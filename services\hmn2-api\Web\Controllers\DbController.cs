﻿using ClosedXML.Excel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NLog.Targets;
using System.Data;
using Web.Helper;
using Web.Helper.Interfaces;
using Web.Models.Controller;
using Web.Models.Service;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;

namespace Web.Controller;

/// <summary>
/// ＤＢ控制器
/// </summary>
[Route("[controller]")]
public class DbController(IDataAccessService dataAccessService,
                          ICredentialService credentialService,
                          IDbHelper dbHelper,
                          IRequestContextService requestContextService,
                          FusionS3HMNContext dbContext,
                          ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly IDbHelper _dbHelper = dbHelper;
    private readonly FusionS3HMNContext _dbContext = dbContext;

    //[HttpGet("tables/download")]
    //public async Task<IActionResult> DownloadTableSchema()
    //{
    //    string? requestUUID = _requestContextService.GetRequestUID();
    //    string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
    //    _logService.Logging("info", logActionName, requestUUID, "Start");

    //    var fetchAllTableSql = _dbHelper.GetAllTablesSql(_dbHelper.CheckDatabaseType(_dbContext));
    //    var fetchAllTableSchemaSql = _dbHelper.GetAllTableStructureSql(_dbHelper.CheckDatabaseType(_dbContext));

    //    var tableList = await _dataAccessService.ExecuteSqlQuery<TableInfo>(fetchAllTableSql);
    //    var tableSchemaList = await _dataAccessService.ExecuteSqlQuery<TableStructure>(fetchAllTableSchemaSql);

    //    tableList = tableList.Select(row => new TableInfo
    //    {
    //        TableName = row.TableName,
    //        TableComment = row.TableComment,
    //        CreateTableScript = _dbHelper.GenerateCreateTableScript(tableSchemaList.Where(t => t.TableName == row.TableName).ToList()),
    //        Fields = tableSchemaList.Where(t => t.TableName == row.TableName).Select(t => new FieldInfo
    //        {
    //            ColumnName = t.ColumnName,
    //            DataType = t.DataType,
    //            Length = t.Length,
    //            Comment = t.Comment,
    //            DefaultValue = t.DefaultValue,
    //            IsNullable = t.IsNullable
    //        }).ToList()
    //    }).OrderBy(t => t.TableName).ToList();

    //    var workbook = new XLWorkbook();

    //    foreach(TableInfo table in tableList)
    //    {
    //        var sheet = workbook.Worksheets.Add(table.TableName);
    //        var mergeRange = sheet.Range("A1:I1");
    //        mergeRange.Merge();
    //        mergeRange.Value = "目錄";

    //        sheet.Cell(2, 1).Value = "檔案名稱";
    //        var mergeRange2 = sheet.Range("B2:I2");
    //        mergeRange2.Merge();
    //        mergeRange2.Value = table.TableName;

    //        sheet.Cell(3, 1).Value = "表格說明";
    //        var mergeRange3 = sheet.Range("B3:I3");
    //        mergeRange3.Merge();
    //        mergeRange3.Value = table.TableComment;

    //        sheet.Cell(4, 1).Value = "欄位說明";
    //        sheet.Cell(4, 2).Value = "欄位名稱";
    //        sheet.Cell(4, 3).Value = "資料型態&長度";
    //        sheet.Cell(4, 4).Value = "PK";
    //        sheet.Cell(4, 5).Value = "FK";
    //        sheet.Cell(4, 6).Value = "必填";
    //        sheet.Cell(4, 7).Value = "預設值";
    //        sheet.Cell(4, 8).Value = "備註";
    //        sheet.Cell(4, 8).Value = "Ref Table";


    //    }

    //    var fileName = "TableSchema_" + DateTime.Now.ToString("yyyyMMddHHmmss") + ".xlsx";
    //}

    [HttpGet("tables/list")]
    public async Task<IActionResult> RetrieveAllTableName()
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        var fetchAllTableSql = _dbHelper.GetAllTablesSql(_dbHelper.CheckDatabaseType(_dbContext));
        var fetchAllTableSchemaSql = _dbHelper.GetAllTableStructureSql(_dbHelper.CheckDatabaseType(_dbContext));

        var tableList = await _dataAccessService.ExecuteSqlQuery<TableInfo>(fetchAllTableSql);
        var tableSchemaList = await _dataAccessService.ExecuteSqlQuery<TableStructure>(fetchAllTableSchemaSql);

        tableList = tableList.Select(row=> new TableInfo
        {
            TableName = row.TableName,
            TableComment = row.TableComment,
            CreateTableScript = _dbHelper.GenerateCreateTableScript(tableSchemaList.Where(t=>t.TableName == row.TableName).ToList()),
            Fields = tableSchemaList.Where(t=>t.TableName == row.TableName).Select(t=> new FieldInfo
            {
                ColumnName = t.ColumnName,
                DataType = t.DataType,
                Length = t.Length,
                Comment = t.Comment,
                DefaultValue = t.DefaultValue,
                IsNullable = t.IsNullable
            }).ToList()
        }).OrderBy(t=>t.TableName).ToList();

        _logService.Logging("info", logActionName, requestUUID, "End");
        returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new { tableList }
        };
        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpGet("tables/schema")]
    public async Task<IActionResult> RetrieveTableInfo()
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        var sql = _dbHelper.GetAllTableStructureSql(_dbHelper.CheckDatabaseType(_dbContext));

        var tableInfoList = await _dataAccessService.ExecuteSqlQuery<TableStructure>(sql);

        var tableList = tableInfoList.Select(row => new TableInfo
        {
            TableName = row.TableName,
            TableComment = row.TableComment,
            CreateTableScript = _dbHelper.GenerateCreateTableScript(tableInfoList.Where(t=>t.TableName == row.TableName).ToList())
        }).Distinct().ToList();

        _logService.Logging("info", logActionName, requestUUID, "End");
        returnModel = new ReturnModel 
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK, 
            result = true, 
            data = new {tableInfoList, tableList}
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpGet("Index")]
    public IActionResult Index()
    {
        return View();
    }
}
