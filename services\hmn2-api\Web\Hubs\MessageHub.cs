﻿using Microsoft.AspNetCore.SignalR;
using Web.Services;
using Web.Services.Interfaces;

namespace Web.Hubs;

public class MessageHub(ICredentialService credentialService, 
    ILogger<MessageHub> logger, 
    SignalRGroupService signalRGroupService) : Hub
{
    private readonly ICredentialService _credentialService = credentialService;
    private readonly ILogger<MessageHub> _logger = logger;
    private readonly SignalRGroupService _signalRGroupService = signalRGroupService;

    // 重寫連接建立時的方法
    public async override Task OnConnectedAsync()
    {
        bool recordGroupSuccess = await RecordGroup(Context.ConnectionId);

        if (recordGroupSuccess)
        {
            _logger.LogInformation($"Client connected, ConnectionId: {Context.ConnectionId}");
            await base.OnConnectedAsync();
        }
        else
        {
            _logger.LogError($"Client connected, Record group failed.");
        }
    }

    public async Task<bool> RecordGroup(string connectionId)
    {
        bool recordSuccess = false;

        try
        {
            var areaCode = Context.GetHttpContext().Request.Query["AreaCode"];
            var user = _credentialService.UserResult;
            var deptCodes = string.Join(",", user.DeptCodeList.OrderBy(e => e));

            if(string.IsNullOrEmpty(areaCode) || string.IsNullOrEmpty(user.AppCode) || string.IsNullOrEmpty(deptCodes))
            {
                _logger.LogError($"Client RecordGroup fail:AppCode:{user.AppCode}, AreaCode:{areaCode}, deptCodes:{deptCodes}");
                throw new Exception("AreaCode, AppCode, DeptCodes are required.");
            }

            string groupId = $"{user.AppCode}/{areaCode}/{deptCodes}";

            // 將ConnectionId加入群組
            await Groups.AddToGroupAsync(Context.ConnectionId, groupId);
            _signalRGroupService.AddGroup(groupId);

            _logger.LogInformation($"Client connected, ConnectionId: {connectionId}, GroupId: {groupId}");

            recordSuccess = true;
        }
        catch (Exception)
        {
            _logger.LogError($"Client connected, ConnectionId: {connectionId}, Record group failed.");
        }

        return recordSuccess;
    }

    // 重寫斷開連接時的方法
    public override Task OnDisconnectedAsync(Exception exception)
    {
        // 如果有例外發生，記錄例外；否則，只記錄斷開連接的信息
        if (exception != null)
        {
            _logger.LogError(exception, "Client disconnected with error: {ConnectionId}", Context.ConnectionId);
        }
        else
        {
            _logger.LogInformation("Client disconnected: {ConnectionId}", Context.ConnectionId);
        }

        return base.OnDisconnectedAsync(exception);
    }
}
