﻿@model Web.Repository.Models.Entities.Area

@{
    ViewData["Title"] = "Edit";
}

<h1>Edit</h1>

<h4>Area</h4>
<hr />
<div class="row">
    <div class="col-md-4">
        <form asp-action="Edit">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="AreaId" class="control-label"></label>
                <input asp-for="AreaId" class="form-control" />
                <span asp-validation-for="AreaId" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="AppCode" class="control-label"></label>
                <input asp-for="AppCode" class="form-control" />
                <span asp-validation-for="AppCode" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="OrganizationCode" class="control-label"></label>
                <input asp-for="OrganizationCode" class="form-control" />
                <span asp-validation-for="OrganizationCode" class="text-danger"></span>
            </div>
            <input type="hidden" asp-for="AreaCode" />
            <div class="form-group">
                <label asp-for="CustomAreaCode" class="control-label"></label>
                <input asp-for="CustomAreaCode" class="form-control" />
                <span asp-validation-for="CustomAreaCode" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="AreaName" class="control-label"></label>
                <input asp-for="AreaName" class="form-control" />
                <span asp-validation-for="AreaName" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="AreaMapPath" class="control-label"></label>
                <input asp-for="AreaMapPath" class="form-control" />
                <span asp-validation-for="AreaMapPath" class="text-danger"></span>
            </div>
            <div class="form-group">
                <input type="submit" value="Save" class="btn btn-primary" />
            </div>
        </form>
    </div>
</div>

<div>
    <a asp-action="Index">Back to List</a>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
