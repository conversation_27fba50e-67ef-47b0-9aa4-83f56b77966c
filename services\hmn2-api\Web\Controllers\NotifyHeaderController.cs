﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Web.Models.Controller.NotifyHeader;
using Web.Models.Controller;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;
using Web.Validation;
using Web.Constant;

namespace Web.Controller;

/// <summary>
/// 通知標頭
/// </summary>
[Route("[controller]")]
[Authorize]
public class NotifyHeaderController(IDataAccessService dataAccessService,
                                   ICredentialService credentialService,
                                   IRequestContextService requestContextService,
                                   ILogService logService) : BaseController(dataAccessService, credentialService, requestContextService, logService)
{   
    /// <summary>
    /// 新增通知標頭檢核
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    [HttpPost("notifyHeaders/validate")]
    [RequestParamListDuplicate("NotifyCode")]
    [RequestParamListNotNullOrEmpty]
    public IActionResult ValidateNotifyHeader([FromBody] List<CreateNotifyHeader> paramList)
    {
        // 進到 controller 代表驗證通過，回傳空的錯誤列表
        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new List<ReturnError>()
        });
    }

    /// <summary>
    /// 新增通知標頭
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    [HttpPost("notifyHeaders")]
    [RequestParamListDuplicate("NotifyCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateNotifyHeader([FromBody] List<CreateNotifyHeader> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        string appCode = _user.AppCode;
        _logService.Logging("info", logActionName, requestUUID, "NotifyHeader Data Validated, start to append.");

        _dataAccessService.BeginTransaction();

        foreach (CreateNotifyHeader nh in paramList)
        {
            NotifyHeader notifyHeader = new NotifyHeader
            {
                AppCode = _user.AppCode,
                AreaCode = nh.AreaCode,
                NotifyCode = nh.NotifyCode,
                NotifyName = nh.NotifyName,
                Enable = nh.Enable == "Y",
                CreateDate = DateTime.Now,
                CreateUserAccount = _user.Account,
                ModifyDate = DateTime.Now
            };

            // 新增 NotifyHeader
            await _dataAccessService.CreateAsync<NotifyHeader>(notifyHeader);

            // 更新 NotifyDetail 列表
            await UpdateNotifyDetailsAsync(nh.AreaCode, nh.NotifyCode, nh.NotifyDetailList);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "NotifyHeader Data append done.");

        returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status201Created,
            result = true
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    /// <summary>
    /// 更新通知明細列表
    /// </summary>
    /// <param name="areaCode"></param>
    /// <param name="fenceCode"></param>
    /// <param name="stationSIDList"></param>
    /// <returns></returns>
    private async Task<List<NotifyDetail>> UpdateNotifyDetailsAsync(string areaCode, string notifyCode, List<CreateNotifyDetail> notifyDetailList)
    {
        List<NotifyDetail> notifyDetails = new List<NotifyDetail>();

        if (notifyDetailList != null)
        {
            // 刪除原本已存在 NotifyCode 的 NotifyDetail 列表
            await _dataAccessService.DeleteAsync<NotifyDetail>(e => e.AppCode == _user.AppCode && e.NotifyCode == notifyCode);

            // 新增 NotifyDetail 到 HMN DB
            notifyDetails.AddRange(notifyDetailList.Select(nd => new NotifyDetail() {
                AppCode = _user.AppCode,
                AreaCode = areaCode,
                NotifyCode = nd.NotifyCode,
                NotifyType = nd.NotifyType,
                Source = nd.Source,
                ContactCode = nd.ContactCode,
                CreateUserAccount = _user.Account,
                CreateDate = DateTime.Now,
                ModifyDate = DateTime.Now
            }).ToList());

            // 批次新增
            if (notifyDetails.Count() > 0)
                await _dataAccessService.CreateRangeAsync(notifyDetails);
        }

        return notifyDetails;
    }

    /// <summary>
    /// 更新通知標頭
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    [HttpPatch("notifyHeaders")]
    [RequestParamListDuplicate("NotifyCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateNotify([FromBody] List<UpdateNotifyHeader> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        _dataAccessService.BeginTransaction();

        foreach (UpdateNotifyHeader param in paramList)
        {
            NotifyHeader notifyHeader = _dataAccessService.Fetch<NotifyHeader>(e => e.AppCode == _user.AppCode && e.NotifyCode == param.NotifyCode).AsTracking().First();

            List<string> updateField = new List<string>();

            if (param.Enable != null)
            {
                updateField.Add("Enable");
                notifyHeader.Enable = param.Enable == "Y";
            }

            if (param.NotifyName != null)
            {
                updateField.Add("NotifyName");
                notifyHeader.NotifyName = param.NotifyName;
            }

            updateField.Add("ModifyDate");
            updateField.Add("ModifyUserAccount");
            notifyHeader.ModifyDate = DateTime.Now;
            notifyHeader.ModifyUserAccount = _user.Account;

            await _dataAccessService.UpdateAsync<NotifyHeader>(notifyHeader, updateField.ToArray());

            // 更新 NotifyDetail 列表
            await UpdateNotifyDetailsAsync(notifyHeader.AreaCode, param.NotifyCode, param.NotifyDetailList);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "NotifyHeader Data update done.");

        return Ok(new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = true
                });
    }

    /// <summary>
    /// 刪除通知標頭
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    [HttpDelete("notifyHeaders")]
    [RequestParamListDuplicate("NotifyCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeleteNotifyHeader([FromBody] List<DeleteNotifyHeader> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 開始刪除通知標頭資料
        _logService.Logging("info", logActionName, requestUUID, "NotifyHeader Data Validated, start to delete.");
        _dataAccessService.BeginTransaction();

        foreach (DeleteNotifyHeader nh in paramList)
        {
            // 刪除通知擋頭
            await _dataAccessService.DeleteAsync<NotifyHeader>(e => e.AppCode == _user.AppCode && e.NotifyCode == nh.NotifyCode);

            // 刪除 NotifyDetail 列表
            await _dataAccessService.DeleteAsync<NotifyDetail>(e => e.AppCode == _user.AppCode && e.NotifyCode == nh.NotifyCode);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "NotifyHeader Data delete done.");

        return Ok(new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = true
                });
    }

    /// <summary>
    /// 查詢通知標頭
    /// </summary>
    /// <param name="queryParam"></param>
    /// <returns></returns>
    [HttpGet("notifyHeaders")]
    public async Task<IActionResult> RetrieveNotifyHeader([FromQuery] RetrieveNotifyHeader queryParam)
    {
        RetrieveNotifyHeader param = queryParam;
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // 排序；如果沒有 sort 條件預設使用 ModifyDate 降冪排序
        string sort = string.IsNullOrEmpty(param.sort) ? "ModifyDate:desc" : param.sort;
        string sortByField = sort.Split(":")[0];
        string sortDirection = sort.Split(":")[1];

        var areaList = _dataAccessService.Fetch<Area>(e => e.AppCode == _user.AppCode);
        var departmentList = _dataAccessService.Fetch<Department>(e => e.AppCode == _user.AppCode);
        var contactDataList = _dataAccessService.Fetch<ContactDatum>(x => x.AppCode == _user.AppCode);
        var notifyHeaderList = _dataAccessService.Fetch<NotifyHeader>(x => x.AppCode == _user.AppCode);
        var notifyDetailList = _dataAccessService.Fetch<NotifyDetail>(x => x.AppCode == _user.AppCode);

        var query = (from a in notifyHeaderList
                    join b in areaList on a.AreaCode equals b.AreaCode into temp
                    from t in temp.DefaultIfEmpty()
                    select new
                    {
                        a.Id,
                        Enable = (a.Enable == true) ? "Y" : "N",
                        a.NotifyCode,
                        a.NotifyName,
                        AreaName = (t == null) ? "" : t.AreaName,
                        a.AreaCode,
                        NotifyDetails = (from nd in notifyDetailList
                            where nd.NotifyCode == a.NotifyCode
                            orderby nd.NotifyType, nd.Source
                            join c in contactDataList on nd.ContactCode equals c.ContactCode into temp2
                            from t2 in temp2.DefaultIfEmpty()
                            join d in departmentList on t2.DeptCode equals d.DeptCode into temp3
                            from t3 in temp3.DefaultIfEmpty()
                            select new
                            {
                                nd.NotifyType,
                                nd.Source,
                                nd.ContactCode,
                                t2.ContactName,
                                ContactContent = nd.NotifyType == "Email" ? t2.Email : nd.NotifyType == "SMS" ? t2.Phone : t2.LineToken,
                                t3.DeptName
                                
                            }).ToList(),
                        a.CreateDate,
                        a.CreateUserAccount,
                        a.ModifyDate,
                        a.ModifyUserAccount
                    })
                   .Where(x => (param.NotifyName == null || x.NotifyName.ToUpper().Contains(param.NotifyName.ToUpper()))
                            && (param.NotifyCode == null || x.NotifyCode.ToUpper().Contains(param.NotifyCode.ToUpper()))
                            && (param.AreaCode == null || x.AreaCode.ToUpper().Contains(param.AreaCode.ToUpper()))
                            && (param.Enable == null || x.Enable == param.Enable)
                            && (string.IsNullOrEmpty(param.NotifyType) || x.NotifyDetails.Any(nd => nd.NotifyType.ToUpper().Contains(param.NotifyType.ToUpper()))));

        // 取得總筆數
        int recordTotal = await query.CountAsync();

        // 根據 sortByField 和 sortDirection 進行動態排序
        query = sortDirection.ToLower() == "desc"
            ? query.OrderByDescending(x => EF.Property<object>(x, sortByField))
            : query.OrderBy(x => EF.Property<object>(x, sortByField));

        // 進行資料庫分頁
        var recordList = size == 0 
            ? await query.ToListAsync() // 如果 size == 0，表示不分頁，直接取回所有資料
            : await query.Skip(skip).Take(size).ToListAsync(); // 分頁查詢

        ReturnModel returnModel = new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = new
                    {
                        recordTotal,
                        recordList
                    }
                };

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);
    }
}