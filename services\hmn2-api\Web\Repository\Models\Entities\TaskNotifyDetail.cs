﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class TaskNotifyDetail
{
    public int Id { get; set; }

    public string AppCode { get; set; } = null!;

    public string AreaCode { get; set; } = null!;

    public int TaskId { get; set; }

    public string NotifyType { get; set; } = null!;

    public string ContactName { get; set; } = null!;
    
    public string NotifyContent { get; set; } = null!;

    public string? NotifyMessage1 { get; set; }
    public string? NotifyMessage2 { get; set; }
    public string? Subject { get; set; }

    public bool? NotifyStatus { get; set; }

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }
}
