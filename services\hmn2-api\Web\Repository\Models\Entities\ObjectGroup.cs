﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class ObjectGroup
{
    public int GroupId { get; set; }

    public string AppCode { get; set; } = null!;

    public string? AreaCode { get; set; }

    public string GroupCode { get; set; } = null!;

    public bool Active { get; set; }

    public bool Enable { get; set; }

    public string GroupName { get; set; } = null!;

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }
}
