﻿using System.ComponentModel.DataAnnotations;
using Web.Constant;
using Web.Validation;

namespace Web.Models.Controller.Auth;

public class Login
{
    [Required]
    public string UserAccount { get; set; } = null!;
    [Required]
    public string UserPassword { get; set; } = null!;
}

public class InAutoLogin
{
    [Required]
    public string token { get; set; } = null!;
}

public class ClaimData
{                       
    public string EnableAlarmVoice { get; set; } = null; //使用者是否啟用告警音效(Y/N)
    public int PositionInterval { get; set; } = 0; //定位時間差
    public int AlarmInterval { get; set; } = 0; //告警音效持續秒數
    public string EventFontSize { get; set; } //即時事件字體大小
    public int BatteryLevel { get; set; } //低電量閥值
}
public class ChangeUserPwd
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "OrginPassword")]
    public string OrginPassword { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "NewPassword")]
    [EqualsPropertyValue("", "", "", "", "ConfirmPassword", false, ErrorMessage = Constants.ErrorCode.Invalid + "NewPassword")]
    public string NewPassword { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ConfirmPassword")]
    [EqualsPropertyValue("", "", "", "", "NewPassword", false, ErrorMessage = Constants.ErrorCode.Invalid + "ConfirmPassword")]
    public string ConfirmPassword { get; set; }
}
