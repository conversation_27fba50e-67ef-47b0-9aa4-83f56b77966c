﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class VideoCapture
{
    public int Id { get; set; }

    public string AppCode { get; set; } = null!;

    public string AreaCode { get; set; } = null!;

    public string DeptCode { get; set; } = null!;

    public string ServiceCode { get; set; } = null!;

    public int PrefixMinute { get; set; }

    public int Suffix { get; set; }

    public string BackupDirectory { get; set; } = null!;

    public string? Account { get; set; }

    public string? Password { get; set; }

    public int? PositionCapture { get; set; }

    public int KeepDays { get; set; }

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }
}
