﻿@model IEnumerable<Web.Repository.Models.Entities.Area>

@{
    ViewData["Title"] = "Index";
}

<h1>Index</h1>

<p>
    <a asp-action="Create">Create New</a>
</p>
<table class="table">
    <thead>
        <tr>
            <th>
                @Html.DisplayNameFor(model => model.AreaId)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.AppCode)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.OrganizationCode)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.CustomAreaCode)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.AreaName)
            </th>
            <th>
                @Html.DisplayNameFor(model => model.AreaMapPath)
            </th>
            <th></th>
        </tr>
    </thead>
    <tbody>
@foreach (var item in Model) {
        <tr>
            <td>
                @Html.DisplayFor(modelItem => item.AreaId)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.AppCode)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.OrganizationCode)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.CustomAreaCode)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.AreaName)
            </td>
            <td>
                @Html.DisplayFor(modelItem => item.AreaMapPath)
            </td>
            <td>
                <a asp-action="Edit" asp-route-id="@item.AreaCode">Edit</a> |
                <a asp-action="Details" asp-route-id="@item.AreaCode">Details</a> |
                <a asp-action="Delete" asp-route-id="@item.AreaCode">Delete</a>
            </td>
        </tr>
}
    </tbody>
</table>
