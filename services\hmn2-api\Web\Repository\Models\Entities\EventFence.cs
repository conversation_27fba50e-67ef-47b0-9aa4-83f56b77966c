﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class EventFence
{
    public int EventFenceId { get; set; }

    public string AppCode { get; set; } = null!;

    public string? AreaCode { get; set; }

    public string EventCode { get; set; } = null!;

    public string FenceCode { get; set; } = null!;

    public bool? Active { get; set; }

    public bool? Enable { get; set; }

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }
}
