﻿using Web.Models.Service.Fusion;
using Web.Models.Service.Monitor;
using Web.Repository.Models.Entities;

namespace Web.Services.Interfaces;

public interface IMonitorService
{
    Task<List<PatchAPIResult>> ClearFusionTask(int taskId, string description);
    Task<List<PatchAPIResult>> ClearFusionTask(List<ClearFusionTaskInput> clearFusionTaskList);
    Task<List<TaskInfo>> GetFollowingTaskList(InFollowingTaskList param);
    Task<List<TaskDatum>> GetFollowingTaskWithoutPerm(List<string> eventCodeList);
    Task<List<TaskInfo>> GetTaskList(InTaskList param);
}