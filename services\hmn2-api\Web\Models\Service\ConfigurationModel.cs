﻿using Web.Models.Service.Fusion;

namespace Web.Models.Service.Configuration;

public class InGetObjectRelationData
{
    public string AppCode { get; set; }
    public class ObjectInfo
    {
        public string ObjectCode { get; set; }
        public string AreaCode { get; set; }
    }
    public List<ObjectInfo> ObjectList { get; set; }
}

public class OutCreateObjectDatum
{
    public List<PostAPIResult> APIResultList { get; set; }
    public List<DBResult> InsertObjectDataResultList { get; set; }
    public List<DBResult> InsertObjectDeviceResultList { get; set; }
    public List<DBResult> InsertObjectDeviceDetailResultList { get; set; }
}

public class OutCreateObjectDevice
{
    public List<PatchAPIResult> APIResultList { get; set; }
    public List<DBResult> InsertObjectDeviceResultList { get; set; }
    public List<DBResult> InsertObjectDeviceDetailResultList { get; set; }
}

public class OutCreateObjectDeviceDetail
{
    public List<DBResult> InsertObjectDeviceDetailResultList { get; set; }
}

public class OutCreateEvents
{
    public List<PostAPIResult> APIResultList { get; set; }
    public List<DBResult> InsertTableResultList { get; set; }
}

public class OutDeleteEvents
{
    public Dictionary<string, PatchAPIResult> APIResultDict { get; set; }
    
    public Dictionary<string, List<DBResult>> DeleteTableResultDict { get; set; }
}

public class OutDeleteDevices
{
    public Dictionary<string, DeleteAPIResult> APIResultDict { get; set; }
    
    public Dictionary<string, List<DBResult>> DeleteTableResultDict { get; set; }
}

public class DBResult
{
    public string code { get; set; }
    public string message { get; set; }
    public string error { get; set; }
    public bool result { get; set; } = false;
}
public class DeviceAllInfo
{
    public int? DeviceId { get; set; }
    public string AppCode { get; set; }
    public string AreaCode { get; set; }
    public string Pid { get; set; }
    public bool? Active { get; set; }
    public bool? Enable { get; set; }
    public string Name { get; set; }
    public string DeviceType { get; set; }
    public string ManageDepartCode { get; set; }
    public string UsageDepartCode { get; set; }
    public string StationSid { get; set; }
    public string MmWaveType { get; set; }
    public string CreateUserAccount { get; set; }
    public DateTime? CreateDate { get; set; }
    public string ModifyUserAccount { get; set; }
    public DateTime? ModifyDate { get; set; }
    public string ManageDepartName { get; set; }
    public string UsageDepartName { get; set; }
    public string AreaName { get; set; }
    public string ObjectCode { get; set; }
    public string ObjectName { get; set; }
    public string ObjectType { get; set; }
    public string GroupCode { get; set; }
    public string EquipmentStatus { get; set; }
    public string Battery { get; set; }
    public string Version { get; set; }
}

public class DevicePositions
{
    public class TaskInfo
    {
        public int? TaskId { get; set; }
        public string ServiceCode { get; set; }
    }
    public int id { get; set; }
    public string pid { get; set; }
    public string name { get; set; }
    public Object @object { get; set; }
    public List<TaskInfo> task { get; set; }
    public position position { get; set; }
    public DateTime modifiesAt { get; set; }
}

public class Object
{
    public string code { get; set; }
    public string name { get; set; }
    public string deptCode { get; set; }
    public string type { get; set; }
    public string typeName { get; set; }
    public string groupCode { get; set; }
}
public class position
{
    public float positionX { get; set; }
    public float positionY { get; set; }
    public station station { get; set; }
    public plane plane { get; set; }
    public List<sector> sector { get; set; }
    public location? location { get; set; }
    public DateTime? latestPositionTime { get; set; }
}
public class location
{
    public string code { get; set; }
    public string name { get; set; }
}
public class plane
{
    public string code { get; set; }
    public string name { get; set; }
    public string mapWidth { get; set; }
    public string mapHeight { get; set; }
    public string positionX { get; set; }
    public string positionY { get; set; }
    public string deptCode { get; set; }
}
public class sector
{
    public string code { get; set; }
    public string name { get; set; }
    public string mapWidth { get; set; }
    public string mapHeight { get; set; }
    public string positionX { get; set; }
    public string positionY { get; set; }
    public string deptCode { get; set; }
}
public class station
{
    public Boolean enable { get; set; }
    public string sid { get; set; }
    public string name { get; set; }
    public string type { get; set; }
    public string regionCode { get; set; }
}

public class InGenerateEventCode
{
    public string AppCode { get; set; }
    public string ServiceCode { get; set; }
    public string ObjectCode { get; set; }
    public string Pid { get; set; }
    public string FenceCode { get; set; }
    public string SddResource { get; set; }
    public string SddComp { get; set; }
}

public class InGenerateEventName
{
    public string ServiceCode { get; set; }
    public string EventCode { get; set; }
    public string EventName { get; set; }
    public string FenceName { get; set; }
}