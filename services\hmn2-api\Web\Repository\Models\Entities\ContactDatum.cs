﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class ContactDatum
{
    public int ContactId { get; set; }

    public string AppCode { get; set; } = null!;

    public string AreaCode { get; set; } = null!;

    public string? DeptCode { get; set; }

    public string Source { get; set; } = null!;

    public string ContactCode { get; set; } = null!;

    public string ContactName { get; set; } = null!;

    public string? Phone { get; set; }

    public string? Email { get; set; }

    public string? LineToken { get; set; }

    public string? CreateUserAccount { get; set; }

    public DateTime? CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }
}
