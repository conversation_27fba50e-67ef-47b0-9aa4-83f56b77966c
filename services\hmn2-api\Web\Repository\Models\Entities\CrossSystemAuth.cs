﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class CrossSystemAuth
{
    public string AuthUuid { get; set; } = null!;

    public string SourceAccount { get; set; } = null!;

    public string TargetPage { get; set; } = null!;

    public DateTime ExpiresAt { get; set; }

    public DateTime CreatedAt { get; set; }

    public DateTime? UpdatedAt { get; set; }
}
