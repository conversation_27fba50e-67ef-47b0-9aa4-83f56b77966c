﻿using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using Web.Models.Controller;
using Web.Services.Interfaces;
using Web.Models.Controller.Device;
using System.Text.Json;
using Web.Models.Service;
using DocumentFormat.OpenXml.InkML;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.ObjectPool;
using Microsoft.Extensions.Options;
using Web.Models.Interface;
using NLog.Web.LayoutRenderers;
using System.Reflection;
using Microsoft.AspNetCore.Http.Json;
using Web.Models.AppSettings;
using Web.Controller;
using static System.Runtime.InteropServices.JavaScript.JSType;
using Web.Services;
using System.Net.Http;
using System.Collections;
using System.ComponentModel.DataAnnotations;
using Web.Validation;

namespace Web.ActionFilter;

public class ValidationActionFilter : IAsyncActionFilter
{
    private readonly ILogService _logService;
    private readonly ICredentialService _credentialService;

    public ValidationActionFilter(ILogService logService, ICredentialService credentialService)
    {
        _logService = logService;
        _credentialService = credentialService;
    }

    public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        if (context.ActionArguments.TryGetValue("paramList", out var paramListObj) && paramListObj is IEnumerable paramListEnumerable)
        {
            // 定义项目类型
            Type itemType = null;

            // 尝试获取泛型类型参数（适用于 List<T>）
            Type paramListType = paramListObj.GetType();
            if (paramListType.IsGenericType && paramListType.GetGenericArguments().Length == 1)
            {
                itemType = paramListType.GetGenericArguments()[0];
            }

            if (itemType != null)
            {
                // 获取所有带有 [Unique] 特性的属性
                var uniqueProperties = itemType.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                                                 .Where(prop => prop.GetCustomAttribute(typeof(UniqueAttribute)) != null)
                                                 .ToList();

                foreach (var prop in uniqueProperties)
                {
                    var duplicates = paramListEnumerable
                        .Cast<object>() // 将元素转换为 object 类型
                        .Select(item => prop.GetValue(item)) // 获取属性值
                        .Where(value => value != null) // 过滤掉 null 值
                        .GroupBy(value => value) // 按值分组
                        .Where(g => g.Count() > 1) // 查找重复值（计数大于1）
                        .Select(g => g.Key) // 获取重复的值
                        .ToList();

                    if (duplicates.Any())
                    {
                        // 获取 [Required] 特性的 ErrorMessage
                        var uniqueAttribute = prop.GetCustomAttribute(typeof(UniqueAttribute)) as UniqueAttribute;

                        // 如果存在重复值，添加模型状态错误
                        context.ModelState.AddModelError(prop.Name, uniqueAttribute?.ErrorMessage?? $"err.unique.param.{prop.Name}");
                    }
                }

                // 如果存在模型状态错误，返回错误响应
                if (!context.ModelState.IsValid)
                {
                    ReturnModel returnModel = new();
                    returnModel.data = context.ModelState;
                    returnModel.authorize = (Authorize)_credentialService.UserResult;
                    context.Result = new BadRequestObjectResult(returnModel);
                    return; // 提前返回，以免执行后续操作
                }
            }
        }

        await next();
    }
}
