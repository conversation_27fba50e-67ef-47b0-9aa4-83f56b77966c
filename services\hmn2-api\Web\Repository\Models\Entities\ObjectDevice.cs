﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class ObjectDevice
{
    public int ObjectDeviceId { get; set; }

    public string AppCode { get; set; } = null!;

    public string? AreaCode { get; set; }

    public string ObjectCode { get; set; } = null!;

    public string Pid { get; set; } = null!;

    /// <summary>
    /// 0--非平躺/1--平躺，預設1
    /// </summary>
    public bool? MmWaveType { get; set; }

    public string? FourColor { get; set; }

    public string? UrgColor { get; set; }

    public string? AbnlColor { get; set; }

    public string? CreateUserAccount { get; set; }

    public DateTime? CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }
}
