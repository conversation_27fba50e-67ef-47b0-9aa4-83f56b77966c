using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using Web.Constant;
using Web.Validation;

namespace Web.Models.Controller.QueryRate;

public class RetrieveQueryRate
{
    public int page { get; set; }
    public int size { get; set; }
    public string? startDate { get; set; }
    public string? endDate { get; set; }
    public int MenuId { get; set; }


    public string? DeptCode { get; set; }
    public string? UserAccount { get; set; }
    public string? ClientIp { get; set; }
    public string? ClientHostName { get; set; }
}


public class PostQueryRate
{
    public string AreaCode { get; set; }
    public string? DeptCode { get; set; }
    public int MenuId { get; set; }
    public string HttpStatus { get; set; }
    public JsonDocument? HttpRequest { get; set; }
}