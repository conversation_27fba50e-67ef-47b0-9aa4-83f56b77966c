﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Web.Repository.Models.Entities;
using Microsoft.EntityFrameworkCore.Storage;
using Web.Repository.Interface;

namespace Web.Repository;

public class UnitOfWork : IUnitOfWork
{
    private readonly FusionS3HMNContext _context;
    private bool disposedValue;
    private IDbContextTransaction? _transaction;

    public UnitOfWork(FusionS3HMNContext context)
    {
        _context = context;
    }


    public async Task BeginTransactionAsync()
    {
        _transaction = await _context.Database.BeginTransactionAsync();
    }

    public async Task<int> CommitAsync()
    {

        if (_transaction == null)
        {
            throw new InvalidOperationException("Transaction have not been started");
        }

        int count = 0;

        try
        {
            count = await _context.SaveChangesAsync();
            await _transaction.CommitAsync();
        }
        catch
        {
            await _transaction.RollbackAsync();
            throw;
        }
        finally
        {
            _transaction.Dispose();
            _transaction = null;
        }

        return count;
    }

    public async Task RollbackAsync()
    {
        if (_transaction == null)
        {
            throw new InvalidOperationException("Transaction have not been started");
        }

        if (_transaction != null)
        {
            await _transaction.RollbackAsync();
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!disposedValue)
        {
            if (disposing)
            {
                _transaction?.Dispose();
                _context.Dispose();
            }

            // TODO: 釋出非受控資源 (非受控物件) 並覆寫完成項
            // TODO: 將大型欄位設為 Null
            disposedValue = true;
        }
    }

    public void Dispose()
    {
        // 請勿變更此程式碼。請將清除程式碼放入 'Dispose(bool disposing)' 方法
        Dispose(disposing: true);
        GC.SuppressFinalize(this);
    }
}
