﻿using Microsoft.AspNetCore.Mvc.Controllers;
using System.Text.Json;
using Web.Services;
using Web.Services.Interfaces;

namespace Web.Middlewares;

public class ReturnHandlingMiddleware(RequestDelegate next
    ,ILogService logService
    //,CredentialService credentialService
    ) :IMiddleware
{
    private readonly RequestDelegate _next = next;
    private readonly ILogService _logService = logService;
    //private readonly CredentialService _credentialService = credentialService;

    public async Task InvokeAsync(HttpContext context, RequestDelegate next)
    {
        var originalBodyStream = context.Response.Body;

        var (controller, action) = GetControllerAndAction(context.GetEndpoint());

        using var responseBody = new MemoryStream();
        context.Response.Body = responseBody;

        string? requestUUID = string.IsNullOrEmpty(context.Request.Headers["x-request-id"]) ? Guid.NewGuid().ToString() : context.Request.Headers["x-request-id"];
        string logActionName = controller + "/" + action;
        _logService.Logging("info", logActionName, requestUUID, "Start1");

        await _next(context);

        _logService.Logging("info", logActionName, requestUUID, "End1");

        responseBody.Seek(0, SeekOrigin.Begin);
        string responseText = await new StreamReader(responseBody).ReadToEndAsync();

        responseBody.Seek(0, SeekOrigin.Begin);
        await responseBody.CopyToAsync(originalBodyStream);

        //if (context.Response.StatusCode == 200 && context.Response.ContentType?.ToLower().Contains("application/json") == true)
        //{
        //    //var responseBodyContent = await FormatResponse(context.Response);
        //    await responseBody.CopyToAsync(originalBodyStream);
        //}
        //else
        //{
        //    await responseBody.CopyToAsync(originalBodyStream);
        //}
    }

    private static (string?, string?) GetControllerAndAction(Endpoint endpoint)
    {
        string? controllerName = null;
        string? actionName = null;

        if (endpoint != null)
        {
            // 獲取 controller 和 action 名稱
            var controllerActionDescriptor = endpoint.Metadata.GetMetadata<ControllerActionDescriptor>();

            if (controllerActionDescriptor != null)
            {
                controllerName = controllerActionDescriptor.ControllerName;
                actionName = controllerActionDescriptor.ActionName;
            }
        }
        return (controllerName, actionName);
    }

    private static async Task<string> FormatResponse(HttpResponse response)
    {
        response.Body.Seek(0, SeekOrigin.Begin);
        var plainBodyText = await new StreamReader(response.Body).ReadToEndAsync();
        response.Body.Seek(0, SeekOrigin.Begin);

        var result = new
        {
            Code = 200,
            Message = "Success",
            Data = JsonSerializer.Deserialize<object>(plainBodyText)
        };

        var jsonResult = JsonSerializer.Serialize(result);
        response.Body = new MemoryStream();
        await response.Body.WriteAsync(System.Text.Encoding.UTF8.GetBytes(jsonResult));
        response.ContentLength = response.Body.Length;

        return jsonResult;
    }
}