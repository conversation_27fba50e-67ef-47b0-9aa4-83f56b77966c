﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using Web.Validation;
using Web.Constant;

namespace Web.Models.Controller.UserProfile;

public class RetrieveUserProfile
{
    public string page { get; set; }
    public string size { get; set; }
    public string AreaCode { get; set; }
    public string UserAccount { get; set; }
}

public class UpdateUserProfile
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "UserAccount")]
    [Exists("", "UserDatum", "UserAccount", ErrorMessage = Constants.ErrorCode.NotFound + "UserAccount")]
    public string UserAccount { get; set; }

    [RegularExpression(@"^(S|M|L|XL)$", ErrorMessage = Constants.ErrorCode.Pattern + "EventFontSize")]
    public string EventFontSize { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "EnableAlarmVoice")]
    public string EnableAlarmVoice { get; set; }

    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "AlarmInterval")]
    [Range(5, 999, ErrorMessage = Constants.ErrorCode.Pattern + "AlarmInterval")]
    public string AlarmInterval { get; set; }

    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "PositionInterval")]
    [RegularExpression(@"^([1-9][0-9]{0,2})$", ErrorMessage = Constants.ErrorCode.Pattern + "PositionInterval")]
    public string PositionInterval { get; set; }

    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "BatteryLevel")]
    [RegularExpression(@"^([0-9]|[1-9][0-9])$", ErrorMessage = Constants.ErrorCode.Pattern + "BatteryLevel")]
    public string BatteryLevel { get; set; }
    
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "PlaneLockedDisplay")]
    public string PlaneLockedDisplay { get; set; }

    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "TempHudMonitorData")]
    [RegularExpression(@"^(8|12|24|48|72)$", ErrorMessage = Constants.ErrorCode.Pattern + "TempHudMonitorData")]
    public string TempHudMonitorData {  get
    ; set; }
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "TempHudContinuousLine")]
    public string TempHudContinuousLine { get; set; }

    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "TempHudTrendInterval")]
    [RegularExpression(@"^(1|5|10|15|30|60)$", ErrorMessage = Constants.ErrorCode.Pattern + "TempHudTrendInterval")]
    public string TempHudTrendInterval { get; set; }

    [RegularExpression(@"^(MEDIAN|MEAN|LAST|RAW)$", ErrorMessage = Constants.ErrorCode.Pattern + "TempHudTrendData")]
    public string TempHudTrendData { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "TempHudTrendThreshold")]
    public string TempHudTrendThreshold { get; set; }
    
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "NormalAlarmInterval")]
    [Range(5, 999, ErrorMessage = Constants.ErrorCode.Pattern + "NormalAlarmInterval")]
    public string NormalAlarmInterval { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "UrgentAudioServiceCode")]
    public string UrgentAudioServiceCode { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "PhysiologyTrendThreshold")]
    public string PhysiologyTrendThreshold { get; set; }

    [RegularExpression(@"^(MEDIAN|MEAN|LAST|RAW)$", ErrorMessage = Constants.ErrorCode.Pattern + "PhysiologyTrendData")]
    public string PhysiologyTrendData { get; set; }

    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "PhysiologyTrendInterval")]
    [RegularExpression(@"^(1|5|10|15|30|60)$", ErrorMessage = Constants.ErrorCode.Pattern + "PhysiologyTrendInterval")]
    public string PhysiologyTrendInterval { get; set; }

    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "PhysiologyMonitor")]
    [RegularExpression(@"^(8|12|24|48|72)$", ErrorMessage = Constants.ErrorCode.Pattern + "PhysiologyMonitor")]
    public string PhysiologyMonitor { get; set; }

    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "LowVoltage")]
    [Range(0, 2.65, ErrorMessage = Constants.ErrorCode.Pattern + "LowVoltage")]
    public string LowVoltage { get; set; }
}
