﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Options;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using System.Security.Cryptography;
using System.Text;
using Web.Models.AppSettings;
using Web.Models.Controller;
using Web.Services.Interfaces;
using DocumentFormat.OpenXml.InkML;
using Web.Models.Interface;
using System.Threading;
using System.Text.Json;

namespace Web.ActionFilter;
public class PaginationActionFilter(IMemoryCache cache, IOptions<AppInfo> appInfo) : IAsyncActionFilter
{
    private readonly IMemoryCache _cache = cache;
    private readonly AppInfo _appInfo = appInfo.Value;
    public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        // 檢查 Parameter Model 是否有繼承 IPaginationRequest ，有的話才進行分頁處理
        if (!ShouldApply(context))
        {
            await next();
            return;
        }

        var (page, size) = GetPaginationParameters(context);

        var resultContext = await next();

        if (resultContext.Result is ObjectResult objectResult)
        {
            var requestUUID = await GetRequestUUID(context);
            objectResult.Value = await ApplyPaginationAsync(objectResult.Value, page, size, requestUUID);
        }
    }

    private static string GetFullActionName(ActionExecutingContext context)
    {
        var httpMethod = context.HttpContext.Request.Method;
        var controllerName = context.ActionDescriptor.RouteValues["controller"];
        var actionName = context.ActionDescriptor.RouteValues["action"];

        var httpGetAttribute = context.ActionDescriptor.EndpointMetadata
            .OfType<HttpGetAttribute>()
            .FirstOrDefault();

        var routeTemplate = httpGetAttribute?.Template ?? actionName;

        var fullActionName = $"{httpMethod}:{controllerName}/{routeTemplate}";
        return fullActionName;
    }

    private bool ShouldApply(ActionExecutingContext context)
    {
        return context.ActionArguments.Values.Any(x => x is IPaginationRequest);
    }

    private (int page, int size) GetPaginationParameters(ActionExecutingContext context)
    {
        int.TryParse(context.HttpContext.Request.Query["page"], out int page);
        int.TryParse(context.HttpContext.Request.Query["size"], out int size);

        return (page, size);
    }

    private async Task<string> GetRequestUUID(ActionExecutingContext context)
    {
        string requestUUID;
        if (context.HttpContext.Request.Headers.TryGetValue(_appInfo.RequestIdHeaderName, out var headerValues) || string.IsNullOrEmpty(headerValues))
        {
            requestUUID = headerValues.ToString();
        }
        else
        {
            throw new Exception("Request ID not found");
        }
        return requestUUID;
    }

    private async Task<object> ApplyPaginationAsync(object data, int page, int size, string requestUUID)
    {
        if (data == null || !(data is ReturnModel returnModel))
            return data;

        if (returnModel.data == null)
            return data;

        var dataType = returnModel.data.GetType();
        var recordListProperty = dataType.GetProperty("recordList");

        if (recordListProperty == null)
            return data;

        var recordList = recordListProperty.GetValue(returnModel.data) as IEnumerable;
        if (recordList == null)
            return data;

        int totalCount = await recordList.GetCountAsync();

        // If size is 0, return all records
        if (size == 0) size = totalCount;

        var recordListHash = GetRecordListHash(recordList);
        var cacheKey = $"PaginatedList_{recordListHash}_{page}_{size}";

        if (!_cache.TryGetValue(cacheKey, out object cachedList))
        {
            var pagedList = await GetPagedListAsync(recordList, recordListProperty.PropertyType, page, size);

            var cacheEntryOptions = new MemoryCacheEntryOptions()
                .SetSlidingExpiration(TimeSpan.FromMinutes(5));
            _cache.Set(cacheKey, pagedList, cacheEntryOptions);

            cachedList = pagedList;
        }

        // Create a new anonymous object with the paged recordList
        var newData = new
        {
            recordTotal = totalCount,
            recordList = cachedList
        };

        // Create a new ReturnModel with the paged data
        var newReturnModel = new ReturnModel
        {
            requestUUID = requestUUID,
            authorize = returnModel.authorize,
            httpStatus = returnModel.httpStatus,
            result = returnModel.result,
            data = newData
        };

        return newReturnModel;
    }

    private string GetRecordListHash(IEnumerable recordList)
    {
        using (var sha256 = SHA256.Create())
        {
            var json = JsonSerializer.Serialize(recordList);
            var inputBytes = Encoding.UTF8.GetBytes(json);
            
            var hashBytes = sha256.ComputeHash(inputBytes);
            return BitConverter.ToString(hashBytes).Replace("-", "").ToLower();
        }
    }

    private async Task<object> GetPagedListAsync(IEnumerable recordList, Type listType, int page, int size)
    {
        return await Task.Run(() =>
        {
            var elementType = listType.GetGenericArguments()[0];
            var genericListType = typeof(List<>).MakeGenericType(elementType);

            var castedList = Cast(recordList, elementType);
            var pagedList = PerformPaging(castedList, page, size);

            var resultList = Activator.CreateInstance(genericListType);
            var addMethod = genericListType.GetMethod("Add");

            foreach (var item in pagedList)
            {
                addMethod.Invoke(resultList, new object[] { item });
            }

            return resultList;
        });
    }

    private static readonly MethodInfo CastMethod = typeof(Enumerable).GetMethod("Cast");

    private static IEnumerable Cast(IEnumerable source, Type elementType)
    {
        return (IEnumerable)CastMethod.MakeGenericMethod(elementType).Invoke(null, new object[] { source });
    }

    private IEnumerable PerformPaging(IEnumerable source, int page, int size)
    {
        var skipMethod = typeof(Enumerable).GetMethods()
            .First(m => m.Name == "Skip" && m.GetParameters().Length == 2)
            .MakeGenericMethod(source.GetType().GetGenericArguments()[0]);

        var takeMethod = typeof(Enumerable).GetMethods()
            .First(m => m.Name == "Take" && m.GetParameters().Length == 2)
            .MakeGenericMethod(source.GetType().GetGenericArguments()[0]);

        var skipped = skipMethod.Invoke(null, new object[] { source, (page - 1) * size });
        return (IEnumerable)takeMethod.Invoke(null, new object[] { skipped, size });
    }
}


public static class EnumerableExtensions
{
    public static async Task<int> GetCountAsync(this IEnumerable enumerable, CancellationToken cancellationToken = default)
    {
        if (enumerable == null)
            return 0;

        if (enumerable is ICollection collection)
            return collection.Count;

        if (enumerable is IReadOnlyCollection<object> readOnlyCollection)
            return readOnlyCollection.Count;

        return await AsyncCountService.GetCountAsync(enumerable, cancellationToken);
    }
}

public static class AsyncCountService
{
    public static async Task<int> GetCountAsync(IEnumerable enumerable, CancellationToken cancellationToken = default)
    {
        const int batchSize = 10000;
        int totalCount = 0;
        var enumerator = enumerable.GetEnumerator();

        try
        {
            while (true)
            {
                cancellationToken.ThrowIfCancellationRequested();

                int batchCount = await Task.Run(() =>
                {
                    int count = 0;
                    for (int i = 0; i < batchSize && enumerator.MoveNext(); i++)
                    {
                        count++;
                    }
                    return count;
                }, cancellationToken);

                if (batchCount == 0) break;

                totalCount += batchCount;

                if (batchCount < batchSize) break;
            }

            return totalCount;
        }
        finally
        {
            (enumerator as IDisposable)?.Dispose();
        }
    }
}