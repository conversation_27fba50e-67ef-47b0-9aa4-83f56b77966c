﻿using System.Collections;
using System.Text.Json;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Web.Models.Controller;
using Web.Models.Service;
using Web.Services;
using Web.Services.Interfaces;

namespace Web.Controller;

public class BaseController(IDataAccessService dataAccessService,
                                    ICredentialService credentialService,
                                    IRequestContextService requestContextService,
                                    ILogService logSercice) : Microsoft.AspNetCore.Mvc.Controller
{
    protected readonly IDataAccessService _dataAccessService = dataAccessService;
    protected readonly ILogService _logService = logSercice;
    protected readonly UserResult _user = credentialService.UserResult;
    protected readonly IRequestContextService _requestContextService = requestContextService;

    public override async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        // 執行 action 並捕捉任何例外
        var resultContext = await next();

        if (resultContext.Exception != null && !resultContext.ExceptionHandled)
        {
            // 印出 Exception.Message 的內容
            Console.WriteLine($"resultContext.Exception.Message: {resultContext.Exception.Message}");

            // 如果有資料存取錯誤，進行交易回滾
            await _dataAccessService.RollbackAsync();

            List<ReturnError> errorList = [];
            errorList.Add(new ReturnError { index = -1, code = "", errors = new List<ErrorDetail> { new ErrorDetail { index = 0, error = "err.internalerror.system", message = resultContext.Exception.Message, innerMsg = resultContext.Exception.InnerException?.Message } } });

            string logActionName = $"{GetType().Name}/{context.ActionDescriptor.DisplayName}";
            string? requestUUID = context.HttpContext.Request.Headers["RequestUUID"];
            _logService.Logging("error", logActionName, requestUUID, JsonSerializer.Serialize(errorList));

            ReturnModel returnModel = new ReturnModel
            {
                authorize = (Authorize)_user,
                requestUUID = requestUUID,
                httpStatus = StatusCodes.Status500InternalServerError,
                result = false,
                data = errorList
            };

            // 回應自訂的 HTTP 500 錯誤訊息
            resultContext.Result = new ObjectResult(returnModel)
            {
                StatusCode = StatusCodes.Status500InternalServerError
            };
            resultContext.ExceptionHandled = true;
        }
    }
}
