﻿namespace Web.Services.Interfaces;

public interface INoticeService
{
    Task<bool> SendMail(string appCode, string subject, string mailto, string mailContent);
    Task<bool> SendLineNotify(string appCode, string message, string to, string accessToken = null, string url = null);
    Task<bool> SendENSMessage(string appCode, string objectCode, int taskId, string taskType, string url, string message, string taskExtra, string toSid = null);
    Task<bool> SendDisplayMessage(string appCode, string objectCode, int taskId, string taskType, string receiverCode, string message, string message2, string taskExtra);
}