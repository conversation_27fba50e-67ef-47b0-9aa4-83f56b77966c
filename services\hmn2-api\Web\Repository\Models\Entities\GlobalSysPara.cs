﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class GlobalSysPara
{
    public int GlobalSysParaId { get; set; }

    public string AppCode { get; set; } = null!;

    public string ParaType { get; set; } = null!;

    public string ParaCode { get; set; } = null!;

    public string ParaValue { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? CreateUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }

    public string? ModifyUserAccount { get; set; }

}
