﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Configuration;
using System.Text.Json;
using Web.Constant;
using Web.Models.Controller;
using Web.Models.Controller.Plane;
using Web.Models.Service;
using Web.Models.Service.Fusion;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;
using Web.Validation;

namespace Web.Controller;

/// <summary>
/// 樓層控制器
/// </summary>
[Route("[controller]")]
[Authorize]
public class PlaneController(IDataAccessService dataAccessService,
                            IPlaneService planeService,
                            ICredentialService credentialService,
                            IRequestContextService requestContextService,
                            ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly IPlaneService _planeService = planeService;

    private const string FileStorageBasePath = "FileStorage";
    
    /// 新增樓層檢核
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    // 有在method 定義route，在Controller記得也要定義route，否則會404
    [HttpPost("planes/validate")]
    [RequestParamListDuplicate("PlaneCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> ValidatePlane([FromBody] List<CreatePlane> paramList)
    {
        // 進到 controller 代表驗證通過，回傳空的錯誤列表
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        string appCode = _user.AppCode;

        // 將 CreatePlane 轉換成 Post Fusion 所要求的格式
        List<PostPlaneInput> fusionPlaneInputList =
            paramList.Select(e => new PostPlaneInput
            {
                code = e.PlaneCode,
                name = e.PlaneName,
                positions = new List<Models.Service.Fusion.position>
                {
                    new Models.Service.Fusion.position
                    {
                        point = "topLeft", //螢幕左上角座標
                        positionX=0,
                        positionY=0
                    },
                    new Models.Service.Fusion.position
                    {
                        point = "bottomRight", //平面圖長寬
                        positionX = (float)(e.MapWidth ?? -1), // 轉換 nullable double 為 float
                        positionY = (float)(e.MapHeight ?? -1) // 轉換 nullable double 為 float
                    },
                    new Models.Service.Fusion.position
                    {
                        point = "topCenter", //平面圖中心點座標
                        positionX = (float)(e.PositionX ?? -1), // 轉換 nullable double 為 float
                        positionY = (float)(e.PositionY ?? -1) // 轉換 nullable double 為 float
                    }
                }
            }).ToList();

        // 新增Fusion Plane
        List<PostAPIResult> fusionPlaneResultList = await _planeService.AddPlaneValidation(fusionPlaneInputList);

        // 判斷每一筆的PostAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> fusionErrorList = _planeService.GetFusionError(fusionPlaneResultList.Select(e => new CommonAPIResult { code = e.code, errors = e.errors, id = e.id }).ToList());

        // 判斷是否有錯誤
        bool addResult = fusionErrorList.Count == 0;

        // 錯誤訊息設定為 Constants.ErrorCode.FusionError
        if (fusionErrorList.Count > 0)
        {
            fusionErrorList.ForEach(e =>
            {
                e.errors.ForEach(ee =>
                {
                    ee.error = Constants.ErrorCode.FusionError;
                });
            });
        }

        returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = addResult ? StatusCodes.Status200OK : StatusCodes.Status400BadRequest,
            result = addResult,
            data = fusionErrorList
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    /// <summary>
    /// 新增樓層
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    // 有在method 定義route，在Controller記得也要定義route，否則會404
    [HttpPost("planes")]
    [RequestParamListDuplicate("PlaneCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreatePlane([FromBody] List<CreatePlane> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        string appCode = _user.AppCode;
        _logService.Logging("info", logActionName, requestUUID, "Plane Data Validated, start to append.");

        // 將 CreatePlane 轉換成 Post Fusion 所要求的格式
        List<PostPlaneInput> fusionPlaneInputList =
            paramList.Select(e => new PostPlaneInput
            {
                code = e.PlaneCode,
                name = e.PlaneName,
                positions = new List<Models.Service.Fusion.position>
                {
                    new Models.Service.Fusion.position
                    {
                        point = "topLeft", //螢幕左上角座標
                        positionX=0,
                        positionY=0
                    },
                    new Models.Service.Fusion.position
                    {
                        point = "bottomRight", //平面圖長寬
                        positionX = (float)(e.MapWidth ?? -1), // 轉換 nullable double 為 float
                        positionY = (float)(e.MapHeight ?? -1) // 轉換 nullable double 為 float
                    },
                    new Models.Service.Fusion.position
                    {
                        point = "topCenter", //平面圖中心點座標
                        positionX = (float)(e.PositionX ?? -1), // 轉換 nullable double 為 float
                        positionY = (float)(e.PositionY ?? -1) // 轉換 nullable double 為 float
                    }
                }
            }).ToList();

        // 新增Fusion Plane
        List<PostAPIResult> fusionPlaneResultList = await _planeService.AddPlane(fusionPlaneInputList);

        // 判斷每一筆的PostAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> fusionErrorList = _planeService.GetFusionError(fusionPlaneResultList.Select(e => new CommonAPIResult { code = e.code, errors = e.errors, id = e.id }).ToList());

        // 取出Fusion新增成功的Plane
        var successPlaneParamList = paramList.Where(e => fusionPlaneResultList.Any(a => a.code == e.PlaneCode && (a.errors == null || a.errors.Count == 0))).ToList();

        _dataAccessService.BeginTransaction();

        // 將新增Fusion成功的 Plane，寫入HMN資料庫
        foreach (CreatePlane p in successPlaneParamList)
        {
            Plane plane = new Plane
            {
                AppCode = _user.AppCode,
                BuildingCode = p.BuildingCode,
                PlaneCode = p.PlaneCode,
                CustomPlaneCode = p.CustomPlaneCode,
                Enable = p.Enable == "Y",
                PlaneNo = p.PlaneNo,
                PlaneName = p.PlaneName,
                MapWidth = p.MapWidth,
                MapHeight = p.MapHeight,
                PositionX = p.PositionX,
                PositionY = p.PositionY,
                CreateDate = DateTime.Now,
                CreateUserAccount = _user.Account,
                ModifyDate = DateTime.Now,
                ModifyUserAccount = _user.Account
            };

            // 新增 Plane
            await _dataAccessService.CreateAsync<Plane>(plane);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "Plane Data append done.");

        // 判斷是否有錯誤
        bool addResult = fusionErrorList.Count == 0;

        // 錯誤訊息設定為 Constants.ErrorCode.FusionError
        if (fusionErrorList.Count > 0)
        {
            fusionErrorList.ForEach(e =>
            {
                e.errors.ForEach(ee =>
                {
                    ee.error = Constants.ErrorCode.FusionError;
                });
            });
        }

        returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = addResult ? StatusCodes.Status201Created : StatusCodes.Status400BadRequest,
            result = addResult,
            data = fusionErrorList
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpPatch("planes")]
    [RequestParamListDuplicate("PlaneCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdatePlane([FromBody] List<UpdatePlane> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 將 CreatePlane 轉換成 Patch Fusion 所要求的格式
        List<PatchPlaneInput> fusionPlaneInputList =
            paramList.Select(e => new PatchPlaneInput
            {
                code = e.PlaneCode,
                name = e.PlaneName,
                enabled = e.Enable == "Y",
                positions = new List<Models.Service.Fusion.position>
                {
                    new Models.Service.Fusion.position
                    {
                        point = "topLeft", //螢幕左上角座標
                        positionX=0,
                        positionY=0
                    },
                    new Models.Service.Fusion.position
                    {
                        point = "bottomRight", //平面圖長寬
                        positionX = (float)(e.MapWidth ?? -1), // 轉換 nullable double 為 float
                        positionY = (float)(e.MapHeight ?? -1) // 轉換 nullable double 為 float
                    },
                    new Models.Service.Fusion.position
                    {
                        point = "topCenter", //平面圖中心點座標
                        positionX = (float)(e.PositionX ?? -1), // 轉換 nullable double 為 float
                        positionY = (float)(e.PositionY ?? -1) // 轉換 nullable double 為 float
                    }
                }
            }).ToList();

        // 更新Fusion Plane
        List<PatchAPIResult> fusionPlaneResultList = await _planeService.PatchPlane(fusionPlaneInputList);

        // 判斷每一筆的PatchAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> fusionErrorList = _planeService.GetFusionError(fusionPlaneResultList.Select(e => new CommonAPIResult { code = e.code, errors = e.errors, id = e.id }).ToList());

        // 取出Fusion更新成功的Plane
        var successPlaneParamList = paramList.Where(e => fusionPlaneResultList.Any(a => a.code == e.PlaneCode && (a.errors == null || a.errors.Count == 0))).ToList();

        _dataAccessService.BeginTransaction();

        // 將更新Fusion成功的 Plane，更新HMN資料庫
        foreach (UpdatePlane p in paramList)
        {
            Plane plane = _dataAccessService.Fetch<Plane>(e => e.AppCode == _user.AppCode && e.PlaneCode == p.PlaneCode).AsTracking().First();
            var planeCode = p.PlaneCode;

            List<string> updateField = new List<string>();

            if (!string.IsNullOrEmpty(p.CustomPlaneCode))
            {
                updateField.Add("CustomPlaneCode");
                plane.CustomPlaneCode = p.CustomPlaneCode;
            }

            if (!string.IsNullOrEmpty(p.PlaneNo))
            {
                updateField.Add("PlaneNo");
                plane.PlaneNo = p.PlaneNo;
            }

            if (!string.IsNullOrEmpty(p.PlaneName))
            {
                updateField.Add("PlaneName");
                plane.PlaneName = p.PlaneName;
            }

            if (!string.IsNullOrEmpty(p.Enable))
            {
                updateField.Add("Enable");
                plane.Enable = p.Enable == "Y";
            }

            if (p.MapWidth != null)
            {
                updateField.Add("MapWidth");
                plane.MapWidth = p.MapWidth;
            }

            if (p.MapHeight != null)
            {
                updateField.Add("MapHeight");
                plane.MapHeight = p.MapHeight;
            }

            if (p.PositionX != null)
            {
                updateField.Add("PositionX");
                plane.PositionX = p.PositionX;
            }

            if (p.PositionY != null)
            {
                updateField.Add("PositionY");
                plane.PositionY = p.PositionY;
            }

            updateField.Add("ModifyDate");
            updateField.Add("ModifyUserAccount");
            plane.ModifyDate = DateTime.Now;
            plane.ModifyUserAccount = _user.Account;

            await _dataAccessService.UpdateAsync<Plane>(plane, updateField.ToArray());
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "Plane Data update done.");

        // 判斷是否有錯誤
        bool updateResult = fusionErrorList.Count == 0;
        
        // 錯誤訊息設定為 Constants.ErrorCode.FusionError
        if (fusionErrorList.Count > 0)
        {
            fusionErrorList.ForEach(e =>
            {
                e.errors.ForEach(ee =>
                {
                    ee.error = Constants.ErrorCode.FusionError;
                });
            });
        }

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = updateResult ? StatusCodes.Status200OK : StatusCodes.Status400BadRequest,
            result = updateResult,
            data = fusionErrorList
        });
    }

    [HttpPut("planes")]
    [RequestParamNotNullOrEmpty]
    public async Task<IActionResult> UpdatePlaneMap([FromBody] UpdatePlaneMap param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        Plane plane = _dataAccessService.Fetch<Plane>(e => e.AppCode == _user.AppCode && e.PlaneCode == param.PlaneCode).AsTracking().First();
        var planeCode = plane.PlaneCode;
        string planeMapPath = plane.PlaneMapPath;

        // 找到 Base64 起始位置
        var base64Index = param.ImageBase64.IndexOf(";base64,");
        if (base64Index >= 0)
        {
            param.ImageBase64 = param.ImageBase64.Substring(base64Index + ";base64,".Length);
        }

        // 將 ImageBase64 儲存到 temp file
        byte[] imageBytes = Convert.FromBase64String(param.ImageBase64);
        string fileExtension = param.ImageFileType.StartsWith(".") ? param.ImageFileType : $".{param.ImageFileType}";
        string tempFileName = $"{Guid.NewGuid()}{fileExtension}";

        // 儲存檔案
        var tempFilePath = Path.Combine(FileStorageBasePath, tempFileName);
        await System.IO.File.WriteAllBytesAsync(tempFilePath, imageBytes);

        // 讀取檔案流
        await using (var fileStream = new FileStream(tempFilePath, FileMode.Open, FileAccess.Read))
        {
            // 建立 IFormFile
            var imageFile = new FormFile(fileStream, 0, fileStream.Length, "Image", tempFileName)
            {
                Headers = new HeaderDictionary(),
                ContentType = "application/octet-stream"
            };

            // 儲存圖片到 Fusion 以及 HMN2
            PutPlaneInput planeImage = new PutPlaneInput
            {
                code = planeCode,
                Image = imageFile
            };

            PutAPIResult fusionPlaneMapResult = await _planeService.PutPlane(planeImage);
            if (!string.IsNullOrEmpty(fusionPlaneMapResult.mapPath))
            {
                // fusion plane map 儲存成功，儲存到 HMN2
                // 取得 mapPath 的檔案名稱
                string fileName = Path.GetFileName(new Uri(fusionPlaneMapResult.mapPath).LocalPath);

                // 確保儲存目錄存在
                var storagePath = Path.Combine(FileStorageBasePath, "PlaneMaps");
                if (!Directory.Exists(storagePath))
                {
                    Directory.CreateDirectory(storagePath);
                }

                storagePath = Path.Combine(storagePath, planeCode);
                if (!Directory.Exists(storagePath))
                {
                    Directory.CreateDirectory(storagePath);
                }

                // 目標檔案路徑
                var destinationFilePath = Path.Combine(storagePath, fileName);

                // 複製檔案
                System.IO.File.Copy(tempFilePath, destinationFilePath, overwrite: true);
                
                planeMapPath = $"/FileStorage/PlaneMaps/{planeCode}/{fileName}";
            }
        }

        // 刪除臨時檔案
        System.IO.File.Delete(tempFilePath);

        // 更新樓層的 planeMapPath 欄位
        List<string> updateField = new List<string>();
        updateField.Add("PlaneMapPath");
        plane.PlaneMapPath = planeMapPath;

        updateField.Add("ModifyDate");
        updateField.Add("ModifyUserAccount");
        plane.ModifyDate = DateTime.Now;
        plane.ModifyUserAccount = _user.Account;

        await _dataAccessService.UpdateAsync<Plane>(plane, updateField.ToArray());

        _logService.Logging("info", logActionName, requestUUID, "Plane Map update done.");

        return Ok(new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = true
                });
    }

    [HttpDelete("planes")]
    [RequestParamListDuplicate("PlaneCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeletePlane([FromBody] List<DeletePlane> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 將 DeletePlane 轉換成 Delete Fusion 所要求的格式
        List<string> deleteFusionPlaneInputs = paramList.Select(e=>e.PlaneCode).ToList();

        // 刪除Fusion Plane
        List<DeleteAPIResult> deleteAPIResult = await _planeService.DeletePlane(string.Join(",", deleteFusionPlaneInputs));

        // 判斷每一筆的DeleteAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> fusionErrorList = _planeService.GetFusionError(deleteAPIResult.Select(e => new CommonAPIResult { code = e.code, sid = e.sid, errors = e.errors, id = e.id }).ToList());

        // 取出Fusion刪除成功的Plane
        var successPlameParamList = paramList.Where(e => deleteAPIResult.Any(a => a.code == e.PlaneCode && (a.errors == null || a.errors.Count == 0))).ToList();

        // 將刪除成功的 Plane，從資料庫中刪除
        _logService.Logging("info", logActionName, requestUUID, "Plane Data Validated, start to delete.");
        _dataAccessService.BeginTransaction();

        foreach (DeletePlane param in successPlameParamList)
        {
            // 刪除樓層
            await _dataAccessService.DeleteAsync<Plane>(e => e.AppCode == _user.AppCode && e.PlaneCode == param.PlaneCode);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "Plane Data delete done.");

        // 判斷是否有錯誤
        bool deleteResult = fusionErrorList.Count == 0;
        
        // 錯誤訊息設定為 Constants.ErrorCode.FusionError
        if (fusionErrorList.Count > 0)
        {
            fusionErrorList.ForEach(e =>
            {
                e.errors.ForEach(ee =>
                {
                    ee.error = Constants.ErrorCode.FusionError;
                });
            });
        }

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = deleteResult ? StatusCodes.Status200OK : StatusCodes.Status400BadRequest,
            result = deleteResult,
            data = fusionErrorList
        });
    }

    [HttpGet("planes")]
    public async Task<IActionResult> RetrievePlane(RetrievePlane queryParam)
    {
        RetrievePlane param = queryParam;
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // 排序；如果沒有 sort 條件預設使用 ModifyDate 降冪排序
        string sort = string.IsNullOrEmpty(param.sort) ? "ModifyDate:desc" : param.sort;
        string sortByField = sort.Split(":")[0];
        string sortDirection = sort.Split(":")[1];

        var buildings = _dataAccessService.Fetch<Building>(e=>e.AppCode == _user.AppCode);
        var planes = _dataAccessService.Fetch<Plane>(e=> e.AppCode == _user.AppCode && buildings.Any(b => b.BuildingCode == e.BuildingCode));

        var query = (from a in planes
             join b in buildings on a.BuildingCode equals b.BuildingCode into temp
             from t in temp.DefaultIfEmpty()
             select new
             {
                t.AreaCode,
                a.PlaneId,
                a.AppCode,
                a.BuildingCode,
                t.BuildingName,
                a.PlaneCode,
                a.CustomPlaneCode,
                Enable = (a.Enable == true) ? "Y" : "N",
                a.PlaneNo,
                a.PlaneName,
                a.PlaneMapPath,
                a.MapWidth,
                a.MapHeight,
                a.PositionX,
                a.PositionY,
                a.CreateUserAccount,
                a.CreateDate,
                a.ModifyUserAccount,
                a.ModifyDate
             })
            .Where(x => (string.IsNullOrEmpty(param.AreaCode) || x.AreaCode == param.AreaCode) // 院區代碼
                     && (string.IsNullOrEmpty(param.BuildingCode) || x.BuildingCode.ToUpper().Contains(param.BuildingCode.ToUpper())) // 棟別代碼
                     && (string.IsNullOrEmpty(param.BuildingName) || x.BuildingName.ToUpper().Contains(param.BuildingName.ToUpper())) // 棟別名稱
                     && (string.IsNullOrEmpty(param.PlaneCode) || x.PlaneCode.ToUpper().Contains(param.PlaneCode.ToUpper())) // 樓層代碼
                     && (string.IsNullOrEmpty(param.CustomPlaneCode) || x.CustomPlaneCode.ToUpper().Contains(param.CustomPlaneCode.ToUpper())) // 客製樓層代碼
                     && (string.IsNullOrEmpty(param.PlaneNo) || x.PlaneNo.ToUpper().Contains(param.PlaneNo.ToUpper())) // 樓層編號
                     && (string.IsNullOrEmpty(param.PlaneName) || x.PlaneName.ToUpper().Contains(param.PlaneName.ToUpper())) // 樓層名稱
                     && (string.IsNullOrEmpty(param.Enable) || x.Enable == param.Enable)
                );

        // 取得總筆數
        int recordTotal = await query.CountAsync();

        // 根據 sortByField 和 sortDirection 進行動態排序
        query = sortDirection.ToLower() == "desc"
            ? query.OrderByDescending(x => EF.Property<object>(x, sortByField))
            : query.OrderBy(x => EF.Property<object>(x, sortByField));

        // 進行資料庫分頁
        var recordList = size == 0 
            ? await query.ToListAsync() // 如果 size == 0，表示不分頁，直接取回所有資料
            : await query.Skip(skip).Take(size).ToListAsync(); // 分頁查詢

        ReturnModel returnModel = new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = new
                    {
                        recordTotal,
                        recordList
                    }
                };

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);
    }

    [HttpGet("planeDatas")]
    [AllowAnonymous]
    public async Task<IActionResult> RetrievePlaneData(RetrievePlane queryParam)
    {
        RetrievePlane param = queryParam;
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // 排序；如果沒有 sort 條件預設使用 ModifyDate 降冪排序
        string sort = string.IsNullOrEmpty(param.sort) ? "ModifyDate:desc" : param.sort;
        string sortByField = sort.Split(":")[0];
        string sortDirection = sort.Split(":")[1];

        var buildings = _dataAccessService.Fetch<Building>(e=>e.AppCode == _user.AppCode);
        var planes = _dataAccessService.Fetch<Plane>(e=>e.AppCode == _user.AppCode && buildings.Any(b => b.BuildingCode == e.BuildingCode));

        var query = (from a in planes
             join b in buildings on a.BuildingCode equals b.BuildingCode into temp
             from t in temp.DefaultIfEmpty()
             select new
             {
                t.AreaCode,
                a.PlaneId,
                a.AppCode,
                a.BuildingCode,
                t.BuildingName,
                a.PlaneCode,
                a.CustomPlaneCode,
                Enable = (a.Enable == true) ? "Y" : "N",
                a.PlaneNo,
                a.PlaneName,
                a.PlaneMapPath,
                a.MapWidth,
                a.MapHeight,
                a.PositionX,
                a.PositionY,
                a.CreateUserAccount,
                a.CreateDate,
                a.ModifyUserAccount,
                a.ModifyDate
             })
            .Where(x => (string.IsNullOrEmpty(param.AreaCode) || x.AreaCode == param.AreaCode) // 院區代碼
                     && (string.IsNullOrEmpty(param.BuildingCode) || x.BuildingCode.ToUpper().Contains(param.BuildingCode.ToUpper())) // 棟別代碼
                     && (string.IsNullOrEmpty(param.BuildingName) || x.BuildingName.Contains(param.BuildingName)) // 棟別名稱
                     && (string.IsNullOrEmpty(param.PlaneCode) || x.PlaneCode.ToUpper().Contains(param.PlaneCode.ToUpper())) // 樓層代碼
                     && (string.IsNullOrEmpty(param.CustomPlaneCode) || x.CustomPlaneCode.ToUpper().Contains(param.CustomPlaneCode.ToUpper())) // 客製樓層代碼
                     && (string.IsNullOrEmpty(param.PlaneNo) || x.PlaneNo.ToUpper().Contains(param.PlaneNo.ToUpper())) // 樓層編號
                     && (string.IsNullOrEmpty(param.PlaneName) || x.PlaneName.Contains(param.PlaneName)) // 樓層名稱
                     && (string.IsNullOrEmpty(param.Enable) || x.Enable == param.Enable)
                );

        // 取得總筆數
        int recordTotal = await query.CountAsync();

        // 根據 sortByField 和 sortDirection 進行動態排序
        query = sortDirection.ToLower() == "desc"
            ? query.OrderByDescending(x => EF.Property<object>(x, sortByField))
            : query.OrderBy(x => EF.Property<object>(x, sortByField));

        // 進行資料庫分頁
        var recordList = size == 0 
            ? await query.ToListAsync() // 如果 size == 0，表示不分頁，直接取回所有資料
            : await query.Skip(skip).Take(size).ToListAsync(); // 分頁查詢

        ReturnModel returnModel = new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = new
                    {
                        recordTotal,
                        recordList
                    }
                };

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);
    }
}
