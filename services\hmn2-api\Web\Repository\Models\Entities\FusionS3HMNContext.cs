﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore;

namespace Web.Repository.Models.Entities;

public partial class FusionS3HMNContext : DbContext
{
    public FusionS3HMNContext(DbContextOptions<FusionS3HMNContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Area> Areas { get; set; }

    public virtual DbSet<Building> Buildings { get; set; }

    public virtual DbSet<Camera> Cameras { get; set; }

    public virtual DbSet<CameraDepartment> CameraDepartments { get; set; }

    public virtual DbSet<CameraStation> CameraStations { get; set; }

    public virtual DbSet<CannedMessage> CannedMessages { get; set; }

    public virtual DbSet<ClientPara> ClientParas { get; set; }

    public virtual DbSet<ContactDatum> ContactData { get; set; }

    public virtual DbSet<ContactDetail> ContactDetails { get; set; }

    public virtual DbSet<Department> Departments { get; set; }
    public virtual DbSet<DepartSector> DepartSectors { get; set; }

    public virtual DbSet<Device> Devices { get; set; }

    public virtual DbSet<EventCannedMessage> EventCannedMessages { get; set; }

    public virtual DbSet<EventFence> EventFences { get; set; }

    public virtual DbSet<EventNotifyContact> EventNotifyContacts { get; set; }

    public virtual DbSet<EventNotifyHeader> EventNotifyHeaders { get; set; }

    public virtual DbSet<EventNotifySchedule> EventNotifySchedules { get; set; }

    public virtual DbSet<EventNotifySetting> EventNotifySettings { get; set; }

    public virtual DbSet<EventNotifyThird> EventNotifyThirds { get; set; }

    public virtual DbSet<ExclusionPeriod> ExclusionPeriods { get; set; }

    public virtual DbSet<Fence> Fences { get; set; }

    public virtual DbSet<FenceAlarmGroup> FenceAlarmGroups { get; set; }

    public virtual DbSet<FenceStation> FenceStations { get; set; }

    public virtual DbSet<FusionLog> FusionLogs { get; set; }

    public virtual DbSet<GlobalSysPara> GlobalSysParas { get; set; }

    public virtual DbSet<Locale> Locales { get; set; }

    public virtual DbSet<LocaleString> LocaleStrings { get; set; }

    public virtual DbSet<Location> Locations { get; set; }

    public virtual DbSet<Menu> Menus { get; set; }

    public virtual DbSet<MenuPermissionType> MenuPermissionTypes { get; set; }

    public virtual DbSet<NotifyDetail> NotifyDetails { get; set; }

    public virtual DbSet<NotifyHeader> NotifyHeaders { get; set; }

    public virtual DbSet<NotifyThird> NotifyThirds { get; set; }

    public virtual DbSet<ObjectDatum> ObjectData { get; set; }

    public virtual DbSet<ObjectDevice> ObjectDevices { get; set; }

    public virtual DbSet<ObjectDeviceDetail> ObjectDeviceDetails { get; set; }

    public virtual DbSet<ObjectEvent> ObjectEvents { get; set; }

    public virtual DbSet<ObjectGroup> ObjectGroups { get; set; }

    public virtual DbSet<ObjectType> ObjectTypes { get; set; }

    public virtual DbSet<Organization> Organizations { get; set; }

    public virtual DbSet<Plane> Planes { get; set; }

    public virtual DbSet<Role> Roles { get; set; }

    public virtual DbSet<RoleMenuPermission> RoleMenuPermissions { get; set; }

    public virtual DbSet<RolePermission> RolePermissions { get; set; }

    public virtual DbSet<ScheduleDepart> ScheduleDeparts { get; set; }

    public virtual DbSet<ScheduleJob> ScheduleJobs { get; set; }

    public virtual DbSet<ScheduleNotify> ScheduleNotifies { get; set; }

    public virtual DbSet<Sector> Sectors { get; set; }

    public virtual DbSet<SectorStation> SectorStations { get; set; }

    public virtual DbSet<Station> Stations { get; set; }

    public virtual DbSet<SysCode> SysCodes { get; set; }
    public virtual DbSet<SysParameters> SysParameters { get; set; }
    public virtual DbSet<SystemEvent> SystemEvents { get; set; }

    public virtual DbSet<QueryRate> QueryRate { get; set; }

    public virtual DbSet<TaskDatum> TaskData { get; set; }

    public virtual DbSet<TaskNotifyDetail> TaskNotifyDetails { get; set; }

    public virtual DbSet<UserClientPara> UserClientParas { get; set; }

    public virtual DbSet<UserDatum> UserData { get; set; }

    public virtual DbSet<UserDeptMonPerm> UserDeptMonPerms { get; set; }

    public virtual DbSet<UserLogonLog> UserLogonLogs { get; set; }

    public virtual DbSet<UserToken> UserTokens { get; set; }

    public virtual DbSet<VideoCapture> VideoCaptures { get; set; }

    public virtual DbSet<VideoCaptureTask> VideoCaptureTasks { get; set; }

    public virtual DbSet<VideoCaptureTaskVideo> VideoCaptureTaskVideos { get; set; }

    public virtual DbSet<VwDeviceInfo> VwDeviceInfos { get; set; }

    public virtual DbSet<VwEventFenceInfo> VwEventFenceInfos { get; set; }

    public virtual DbSet<VwFenceInfo> VwFenceInfos { get; set; }

    public virtual DbSet<VwNotifyDetailInfo> VwNotifyDetailInfos { get; set; }

    public virtual DbSet<VwNotifyHeaderInfo> VwNotifyHeaderInfos { get; set; }

    public virtual DbSet<VwObjectEventInfo> VwObjectEventInfos { get; set; }

    public virtual DbSet<VwObjectGroupInfo> VwObjectGroupInfos { get; set; }

    public virtual DbSet<VwUserDeptMonInfo> VwUserDeptMonInfos { get; set; }

    public virtual DbSet<vwDepartmentInfo> vwDepartmentInfos { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<Area>(entity =>
        {
            entity.HasKey(e => e.AreaId).HasName("Area_pkey");

            entity.ToTable("Area");

            entity.HasIndex(e => e.OrganizationCode, "idxArea_OrganizationCode");

            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaId).ValueGeneratedOnAdd();
            entity.Property(e => e.AreaMapPath).HasMaxLength(200);
            entity.Property(e => e.AreaName).HasMaxLength(50);
            entity.Property(e => e.CustomAreaCode).HasMaxLength(50);
            entity.Property(e => e.OrganizationCode).HasMaxLength(50);
        });

        modelBuilder.Entity<Building>(entity =>
        {
            entity.HasKey(e => e.BuildingId).HasName("Building_pkey");

            entity.ToTable("Building");

            entity.HasIndex(e => e.AreaCode, "idxBuilding_AreaCode");

            entity.Property(e => e.BuildingCode).HasMaxLength(50);
            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.BuildingId).ValueGeneratedOnAdd();
            entity.Property(e => e.BuildingImgName).HasMaxLength(50);
            entity.Property(e => e.BuildingName).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.CustomBuildingCode).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
        });

        modelBuilder.Entity<Camera>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Camera_pkey");

            entity.ToTable("Camera");

            entity.HasIndex(e => e.CameraMAC, "idxCamera_CameraMAC").IsUnique();

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CameraMAC).HasMaxLength(20);
            entity.Property(e => e.CameraName).HasMaxLength(50);
            entity.Property(e => e.CameraVideoPath).HasMaxLength(128);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.IP).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.PathUserName).HasMaxLength(50);
            entity.Property(e => e.PathUserPassword).HasMaxLength(50);
            entity.Property(e => e.StreamURL).HasMaxLength(128);
        });

        modelBuilder.Entity<CameraDepartment>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("CameraDepartment_pkey");

            entity.ToTable("CameraDepartment");

            entity.HasIndex(e => new { e.AppCode, e.CameraMAC, e.DeptCode }, "idxCameraDepartment_DeptCode1").IsUnique();

            entity.HasIndex(e => new { e.AppCode, e.DeptCode, e.CameraMAC }, "idxCameraDepartment_DeptCode2");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CameraMAC).HasMaxLength(20);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.DeptCode).HasMaxLength(50);
        });

        modelBuilder.Entity<CameraStation>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("CameraStation_pkey");

            entity.ToTable("CameraStation");

            entity.HasIndex(e => new { e.AppCode, e.CameraMAC, e.SID }, "idxCameraStation_SID1").IsUnique();

            entity.HasIndex(e => new { e.AppCode, e.SID, e.CameraMAC }, "idxCameraStation_SID2");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CameraMAC).HasMaxLength(20);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.SID).HasMaxLength(20);
        });

        modelBuilder.Entity<CannedMessage>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("CannedMessage_pkey");

            entity.ToTable("CannedMessage");

            entity.Property(e => e.AppCode).HasMaxLength(254);
            entity.Property(e => e.CannedCode).HasMaxLength(50);
            entity.Property(e => e.CannedType).HasMaxLength(2);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.Enable).HasDefaultValue(true);
            entity.Property(e => e.Message).HasMaxLength(512);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.SystemDefault).HasDefaultValue(false);
        });

        modelBuilder.Entity<ClientPara>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ClientPara_pkey");

            entity.ToTable("ClientPara");

            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.DescStringId).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ParaCode).HasMaxLength(50);
            entity.Property(e => e.ParaDesc).HasMaxLength(50);
            entity.Property(e => e.ParaType).HasMaxLength(50);
            entity.Property(e => e.ParaValue).HasMaxLength(50);
        });

        modelBuilder.Entity<ContactDatum>(entity =>
        {
            entity.HasKey(e => e.ContactId).HasName("ContactData_pkey");
            entity.ToTable("ContactData");

            entity.Property(e => e.ContactId).HasDefaultValueSql("nextval('\"ContactData_Id_seq\"'::regclass)");
            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.ContactCode).HasMaxLength(50);
            entity.Property(e => e.ContactName).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.DeptCode).HasMaxLength(50);
            entity.Property(e => e.Email).HasMaxLength(100);
            entity.Property(e => e.LineToken).HasMaxLength(100);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(254);
            entity.Property(e => e.Phone).HasMaxLength(20);
            entity.Property(e => e.Source).HasMaxLength(2);
        });

        modelBuilder.Entity<ContactDetail>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ContactDetail_pkey");

            entity.ToTable("ContactDetail");

            entity.Property(e => e.Id).UseIdentityAlwaysColumn();
            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.ContactCode).HasMaxLength(50);
            entity.Property(e => e.ContactType).HasMaxLength(50);
            entity.Property(e => e.ContactValue).HasMaxLength(100);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
        });

        modelBuilder.Entity<DepartSector>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("DepartSector_pkey");

            entity.ToTable("DepartSector");

            entity.HasIndex(e => new { e.AppCode, e.AreaCode, e.DeptCode, e.SectorCode }, "idxDepartSector_SectorCode").IsUnique();

            entity.HasIndex(e => new { e.AppCode, e.AreaCode, e.DeptCode, e.SectorCode }, "idx_departsector_1");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.DeptCode).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.SectorCode).HasMaxLength(50);
        });

        modelBuilder.Entity<Department>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Department_pkey");

            entity.ToTable("Department");

            entity.HasIndex(e => e.AppCode, "idxDepartment_AppCode");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.BuildingCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.CustomDeptCode).HasMaxLength(50);
            entity.Property(e => e.DeptCode).HasMaxLength(50);
            entity.Property(e => e.DeptEmail).HasMaxLength(50);
            entity.Property(e => e.DeptName).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.OrgType).HasMaxLength(50);
            entity.Property(e => e.PlaneCode).HasMaxLength(20);
            entity.Property(e => e.SectorCode).HasMaxLength(50);
            entity.Property(e => e.Supervisor).HasMaxLength(50);
        });

        modelBuilder.Entity<Device>(entity =>
        {
            entity.HasKey(e => e.DeviceId).HasName("Device_pkey");

            entity.ToTable("Device");

            entity.HasIndex(e => e.AppCode, "idxDevice_AppCode");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.Pid).HasMaxLength(20);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.DeviceId).ValueGeneratedOnAdd();
            entity.Property(e => e.DeviceType).HasMaxLength(50);
            entity.Property(e => e.ManageDepartCode).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.Name).HasMaxLength(20);
            entity.Property(e => e.StationSid).HasMaxLength(50);
            entity.Property(e => e.UsageDepartCode).HasMaxLength(50);
            entity.Property(e => e.mmWaveType).HasMaxLength(50);
        });

        modelBuilder.Entity<EventCannedMessage>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("EventCannedMessage_pkey");

            entity.ToTable("EventCannedMessage");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.CannedCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
        });

        modelBuilder.Entity<EventFence>(entity =>
        {
            entity.HasKey(e => e.EventFenceId).HasName("EventFence_pkey");

            entity.ToTable("EventFence");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.EventCode).HasMaxLength(100);
            entity.Property(e => e.Active).HasDefaultValue(true);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.Enable).HasDefaultValue(true);
            entity.Property(e => e.EventFenceId).ValueGeneratedOnAdd();
            entity.Property(e => e.FenceCode).HasMaxLength(20);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
        });

        modelBuilder.Entity<EventNotifyContact>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("EventNotifyContact_pkey");

            entity.ToTable("EventNotifyContact");

            entity.Property(e => e.Id).ValueGeneratedOnAdd();
            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.NotifyType).HasMaxLength(20);
            entity.Property(e => e.Source).HasMaxLength(2);
            entity.Property(e => e.ContactCode).HasMaxLength(50);
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
        });

        modelBuilder.Entity<EventNotifyHeader>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("EventNotifyHeader_pkey");

            entity.ToTable("EventNotifyHeader");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.NotifyCode).HasMaxLength(20);
        });

        modelBuilder.Entity<EventNotifySchedule>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("EventNotifySchedule_pkey");

            entity.ToTable("EventNotifySchedule");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.EndTime).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.StartTime).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.Weekly).HasMaxLength(50);
        });

        modelBuilder.Entity<EventNotifySetting>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("EventNotifySetting_pkey");

            entity.ToTable("EventNotifySetting");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.ServiceCode).HasMaxLength(50);
            entity.Property(e => e.SddResource).HasMaxLength(50);
            entity.Property(e => e.Description).HasMaxLength(512);
            entity.Property(e => e.BuildingCode).HasMaxLength(50);
            entity.Property(e => e.PlaneCode).HasMaxLength(20);
            entity.Property(e => e.DeptCode).HasMaxLength(50);
            entity.Property(e => e.ObjectType).HasMaxLength(50);
            entity.Property(e => e.GroupCode).HasMaxLength(20);
            entity.Property(e => e.ENSMessage).HasMaxLength(512);
            entity.Property(e => e.DisplayMessage1).HasMaxLength(512);
            entity.Property(e => e.DisplayMessage2).HasMaxLength(512);
            entity.Property(e => e.EmailSubject).HasMaxLength(100);
            entity.Property(e => e.EmailMessage).HasMaxLength(512);
            entity.Property(e => e.NotifyMessage).HasMaxLength(512);
            entity.Property(e => e.SMSMessage).HasMaxLength(512);
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
        });

        modelBuilder.Entity<EventNotifyThird>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("EventNotifyThird_pkey");

            entity.ToTable("EventNotifyThird");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.NotifyType).HasMaxLength(20);
            entity.Property(e => e.ThirdCode).HasMaxLength(20);
        });

        modelBuilder.Entity<Fence>(entity =>
        {
            entity.HasKey(e => e.FenceId).HasName("Fence_pkey");

            entity.ToTable("Fence");

            entity.HasIndex(e => e.AppCode, "idxFence_AppCode");

            entity.Property(e => e.FenceCode).HasMaxLength(50);
            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.CustomFenceCode).HasMaxLength(50);
            entity.Property(e => e.FenceColor).HasMaxLength(50);
            entity.Property(e => e.FenceId).ValueGeneratedOnAdd();
            entity.Property(e => e.FenceName).HasMaxLength(50);
            entity.Property(e => e.FenceType).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.PlaneCode).HasMaxLength(20);
        });

        modelBuilder.Entity<FenceAlarmGroup>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("FenceAlarmGroup_pkey");

            entity.ToTable("FenceAlarmGroup");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.FenceCode).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.Stationsid).HasMaxLength(20);
        });

        modelBuilder.Entity<FenceStation>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("FenceStation_pkey");

            entity.ToTable("FenceStation");

            entity.HasIndex(e => e.AppCode, "idxFenceStation_AppCode");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.FenceCode).HasMaxLength(50);
            entity.Property(e => e.Layer).HasMaxLength(1);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.Stationsid).HasMaxLength(20);
        });

        modelBuilder.Entity<FusionLog>(entity =>
        {
            entity.HasKey(e => e.LogId).HasName("FusionLog_pkey");

            entity.ToTable("FusionLog");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.ClientIP).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.ExecResult).HasMaxLength(24);
            entity.Property(e => e.RequestMethod).HasMaxLength(50);
        });

        modelBuilder.Entity<GlobalSysPara>(entity =>
        {
            entity.HasKey(e => e.GlobalSysParaId).HasName("GlobalSysPara_pkey");

            entity.ToTable("GlobalSysPara");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ParaCode).HasMaxLength(50);
            entity.Property(e => e.ParaType).HasMaxLength(50);
        });

        modelBuilder.Entity<SysParameters>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("SysParameters_pkey");

            entity.ToTable("SysParameters");

            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.DescStringId).HasMaxLength(20);
            entity.Property(e => e.ParaCode).HasMaxLength(50);
            entity.Property(e => e.ParaDesc).HasMaxLength(254);
            entity.Property(e => e.ParaType).HasMaxLength(50);
        });


        modelBuilder.Entity<Locale>(entity =>
        {
            entity.HasKey(e => e.Locale1).HasName("Locale_pkey");

            entity.ToTable("Locale");

            entity.Property(e => e.Locale1)
                .HasMaxLength(20)
                .HasColumnName("Locale");
            entity.Property(e => e.CreatedTime)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.StringId).HasMaxLength(20);
        });

        modelBuilder.Entity<LocaleString>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("LocaleString_pkey");

            entity.ToTable("LocaleString");

            entity.HasIndex(e => new { e.Locale, e.StringId }, "idxLocaleString_Locale").IsUnique();

            entity.Property(e => e.CreatedTime)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.Locale).HasMaxLength(20);
            entity.Property(e => e.StringContent).HasMaxLength(512);
            entity.Property(e => e.StringId).HasMaxLength(20);
        });

        modelBuilder.Entity<Location>(entity =>
        {
            entity.HasKey(e => e.LocId).HasName("Location_pkey");

            entity.ToTable("Location");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.BuildingCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.LocCode).HasMaxLength(50);
            entity.Property(e => e.LocName).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.PlaneCode).HasMaxLength(20);
        });

        modelBuilder.Entity<Menu>(entity =>
        {
            entity.HasKey(e => e.MenuId).HasName("Menu_pkey");

            entity.ToTable("Menu");

            entity.Property(e => e.MenuId).ValueGeneratedNever();
            entity.Property(e => e.ComponentName).HasMaxLength(50);
            entity.Property(e => e.Enabled).HasMaxLength(2);
            entity.Property(e => e.StringId).HasMaxLength(50);
            entity.Property(e => e.Title).HasMaxLength(50);
            entity.Property(e => e.Type).HasMaxLength(10);
            entity.Property(e => e.TypeDesc).HasMaxLength(50);
            entity.Property(e => e.Url).HasMaxLength(50);
            entity.Property(e => e.icon).HasMaxLength(100);
        });

        modelBuilder.Entity<MenuPermissionType>(entity =>
        {
            entity.HasKey(e => e.MenuPermissionId).HasName("MenuPermissionType_pkey");

            entity.ToTable("MenuPermissionType");

            entity.HasIndex(e => new { e.MenuId, e.PermissionType }, "idxMenuPermissionType_MenuPermissionType").IsUnique();

            entity.Property(e => e.Enable).HasDefaultValue(true);
            entity.Property(e => e.PermissionType)
                .HasMaxLength(50)
                .HasComment("Create, Retrieve, Update, Delete");
        });

        modelBuilder.Entity<NotifyDetail>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("NotifyDetail_pkey");

            entity.ToTable("NotifyDetail");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.ContactCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.NotifyCode).HasMaxLength(20);
            entity.Property(e => e.NotifyType).HasMaxLength(20);
            entity.Property(e => e.Source).HasMaxLength(50);
        });

        modelBuilder.Entity<NotifyHeader>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("NotifyHeader_pkey");

            entity.ToTable("NotifyHeader");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.NotifyCode).HasMaxLength(20);
            entity.Property(e => e.NotifyName).HasMaxLength(64);
        });

        modelBuilder.Entity<NotifyThird>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("NotifyThird_pkey");

            entity.ToTable("NotifyThird");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.NotifyType).HasMaxLength(20);
            entity.Property(e => e.ThirdName).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.ThirdCode).HasMaxLength(20);
            entity.Property(e => e.URL_MAC).HasMaxLength(50);
        });

        modelBuilder.Entity<ObjectDatum>(entity =>
        {
            entity.HasKey(e => e.ObjectId).HasName("ObjectData_pkey");
            entity.ToTable("ObjectData");

            entity.HasIndex(e => e.AppCode, "idxObjectData_AppCode");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.EquipmentStatus)
                .HasMaxLength(2)
                .HasComment("設備狀態,******** add");
            entity.Property(e => e.GroupCode).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.Name).HasMaxLength(64);
            entity.Property(e => e.ObjectCode).HasMaxLength(20);
            entity.Property(e => e.ObjectType)
                .HasMaxLength(50)
                .HasComment("1:協助人員; 2:傳送; 3:院內人員;4:設備; 5:病患; 0:空間");
            entity.Property(e => e.Remark)
                .HasMaxLength(512)
                .HasComment("對象備註,******** add");
            entity.Property(e => e.UrgColor).HasMaxLength(50);
            entity.Property(e => e.UsageDepartCode).HasMaxLength(50);
        });

        modelBuilder.Entity<ObjectDevice>(entity =>
        {
            entity.HasKey(e => e.ObjectDeviceId).HasName("ObjectDevice_pkey");

            entity.ToTable("ObjectDevice");

            entity.HasIndex(e => e.AppCode, "idxObjectDevice_AppCode");

            entity.Property(e => e.AbnlColor).HasMaxLength(50);
            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.FourColor).HasMaxLength(50);
            entity.Property(e => e.MmWaveType).HasComment("0--非平躺/1--平躺，預設1");
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.ObjectCode).HasMaxLength(20);
            entity.Property(e => e.Pid).HasMaxLength(20);
            entity.Property(e => e.UrgColor).HasMaxLength(50);
        });

        modelBuilder.Entity<ObjectDeviceDetail>(entity =>
        {
            entity.HasKey(e => e.DeviceDetailId).HasName("ObjectDeviceDetail_pkey");

            entity.ToTable("ObjectDeviceDetail");

            entity.HasIndex(e => new { e.AppCode, e.ObjectCode, e.Pid, e.SddResource, e.SddComp }, "idxObjectDeviceDetail_SddResource").IsUnique();

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.ObjectCode).HasMaxLength(20);
            entity.Property(e => e.Pid).HasMaxLength(20);
            entity.Property(e => e.SddComp).HasMaxLength(10);
            entity.Property(e => e.SddResource).HasMaxLength(50);
        });

        modelBuilder.Entity<ObjectEvent>(entity =>
        {
            entity.HasKey(e => e.ObjectEventId).HasName("ObjectEvent_pkey");

            entity.ToTable("ObjectEvent");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.EventCode).HasMaxLength(100);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.ObjectCode).HasMaxLength(20);
            entity.Property(e => e.ObjectEventId).ValueGeneratedOnAdd();
            entity.Property(e => e.Pid).HasMaxLength(20);
            entity.Property(e => e.SddComp).HasMaxLength(10);
            entity.Property(e => e.SddResource).HasMaxLength(50);
            entity.Property(e => e.ServiceCode).HasMaxLength(50);
        });

        modelBuilder.Entity<ObjectGroup>(entity =>
        {
            entity.HasKey(e => e.GroupId).HasName("ObjectGroup_pkey");

            entity.ToTable("ObjectGroup");

            entity.HasIndex(e => e.AppCode, "idxObjectGroup_AppCode");

            entity.HasIndex(e => e.GroupCode, "idx_objectgroup_1").IsUnique();

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.GroupCode).HasMaxLength(50);
            entity.Property(e => e.GroupName).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
        });

        modelBuilder.Entity<ObjectType>(entity =>
        {
            entity.HasKey(e => e.ObjectTypeId).HasName("ObjectType_pkey");

            entity.ToTable("ObjectType");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.FusionCoreValue)
                .HasMaxLength(50)
                .HasComment("API 要儲存的值");
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.ObjectTypeCode)
                .HasMaxLength(5)
                .HasComment("1:協助人員; 2:傳送; 3:院內人員;4:設備; 5:病患; 0:空間");
            entity.Property(e => e.ObjectTypeDesc).HasMaxLength(254);
        });

        modelBuilder.Entity<Organization>(entity =>
        {
            entity.HasKey(e => e.AppCode).HasName("Organization_pkey");

            entity.ToTable("Organization");

            entity.HasIndex(e => e.OrganizationCode, "idxOrganization_OrganizationCode");

            entity.Property(e => e.OrganizationCode).HasMaxLength(50);
            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.LicenseCode).HasMaxLength(100);
            entity.Property(e => e.LicenseKey).HasMaxLength(100);
            entity.Property(e => e.OrganizationName).HasMaxLength(50);
        });

        modelBuilder.Entity<Plane>(entity =>
        {
            entity.HasKey(e => e.PlaneId).HasName("Plane_pkey");

            entity.ToTable("Plane");

            entity.HasIndex(e => e.BuildingCode, "idxPlane_BuildingCode");

            entity.Property(e => e.PlaneCode).HasMaxLength(20);
            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.BuildingCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.CustomPlaneCode).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.PlaneId).ValueGeneratedOnAdd();
            entity.Property(e => e.PlaneMapPath).HasMaxLength(200);
            entity.Property(e => e.PlaneName).HasMaxLength(50);
            entity.Property(e => e.PlaneNo).HasMaxLength(3);
        });

        modelBuilder.Entity<Role>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Role_pkey");

            entity.ToTable("Role");

            entity.Property(e => e.Id).ValueGeneratedOnAdd();
            entity.Property(e => e.RoleCode).HasMaxLength(50);
            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.RoleDesc).HasMaxLength(50);
            entity.Property(e => e.RoleName).HasMaxLength(50);
        });

        modelBuilder.Entity<RoleMenuPermission>(entity =>
        {
            entity.HasKey(e => e.PermissionId).HasName("RoleMenuPermission_pkey");

            entity.ToTable("RoleMenuPermission");

            entity.HasIndex(e => new { e.AppCode, e.RoleCode, e.MenuId, e.PermissionType }, "idxRoleMenuPermission_MenuPermission").IsUnique();

            entity.HasIndex(e => new { e.RoleCode, e.MenuId }, "idx_rolemenupermission_rolecode_menuid");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.IsAllowed).HasDefaultValue(true);
            entity.Property(e => e.PermissionType).HasMaxLength(50);
            entity.Property(e => e.RoleCode).HasMaxLength(50);
        });

        modelBuilder.Entity<RolePermission>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("RolePermission_pkey");

            entity.ToTable("RolePermission");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.Permission).HasMaxLength(50);
            entity.Property(e => e.RoleCode).HasMaxLength(50);
        });

        modelBuilder.Entity<ScheduleDepart>(entity =>
        {
            entity.HasKey(e => e.ScheduleDepartId).HasName("ScheduleDepart_pkey");

            entity.ToTable("ScheduleDepart");

            entity.HasIndex(e => e.AppCode, "idxScheduleDepart_AppCode");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.UsageDepartCode)
                .HasMaxLength(50)
                .HasComment("A表示全部單位（不過濾單位）");

            entity.HasOne(d => d.ScheduleJob).WithMany(p => p.ScheduleDeparts)
                .HasForeignKey(d => d.ScheduleJobId)
                .HasConstraintName("ScheduleDepart_ScheduleJobId_fkey");
        });

        modelBuilder.Entity<ScheduleJob>(entity =>
        {
            entity.HasKey(e => e.ScheduleJobId).HasName("ScheduleJob_pkey");

            entity.ToTable("ScheduleJob");

            entity.HasIndex(e => e.AppCode, "idxScheduleJob_AppCode");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.JobDesc).HasMaxLength(64);
            entity.Property(e => e.JobType)
                .HasMaxLength(30)
                .HasComment("LowBatter:低電量,PwdCheck:密碼檢查");
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.ScheduleDay)
                .HasMaxLength(95)
                .HasComment("月:01~31,週:0~6,日:1，可多組用半形逗號隔開");
            entity.Property(e => e.ScheduleFreq)
                .HasMaxLength(1)
                .HasComment("M:月,W:週,D:日");
            entity.Property(e => e.ScheduleTime)
                .HasMaxLength(5)
                .HasComment("執行時間（24小時制）");
            entity.Property(e => e.Subject).HasMaxLength(64);
            entity.Property(e => e.Threahold).HasMaxLength(50);
        });

        modelBuilder.Entity<ScheduleNotify>(entity =>
        {
            entity.HasKey(e => e.ScheduleNotifyId).HasName("ScheduleNotify_pkey");

            entity.ToTable("ScheduleNotify");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.ContactCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.Email)
                .HasMaxLength(255)
                .HasComment("當Source為3，則此欄必填");
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.NotifyType)
                .HasMaxLength(64)
                .HasComment("Line,SMS,EMail 對應ContactData欄位");
            entity.Property(e => e.Source)
                .HasMaxLength(50)
                .HasComment("如果資料由ContactData來，記與0,1,2與ContactData一致，如果直接輸入Email則記3");

            entity.HasOne(d => d.ScheduleJob).WithMany(p => p.ScheduleNotifies)
                .HasForeignKey(d => d.ScheduleJobId)
                .HasConstraintName("ScheduleNotify_ScheduleJobId_fkey");
        });

        modelBuilder.Entity<Sector>(entity =>
        {
            entity.HasKey(e => e.SectorId).HasName("Sector_pkey");

            entity.ToTable("Sector");

            entity.Property(e => e.SectorCode).HasMaxLength(50);
            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.CustomSectorCode).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.PlaneCode).HasMaxLength(20);
            entity.Property(e => e.SectorId).ValueGeneratedOnAdd();
            entity.Property(e => e.SectorMapPath).HasMaxLength(200);
            entity.Property(e => e.SectorName).HasMaxLength(50);
        });

        modelBuilder.Entity<SectorStation>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("SectorStation_pkey");

            entity.ToTable("SectorStation");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.SectorCode).HasMaxLength(50);
            entity.Property(e => e.Stationsid).HasMaxLength(20);
        });

        modelBuilder.Entity<Station>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("Station_pkey");

            entity.ToTable("Station");

            entity.HasIndex(e => e.AppCode, "idxStation_AppCode");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.PlaneCode).HasMaxLength(20);
            entity.Property(e => e.RegionCode).HasMaxLength(50);
            entity.Property(e => e.SID).HasMaxLength(50);
            entity.Property(e => e.SpaceType).HasMaxLength(50);
            entity.Property(e => e.StationIP).HasMaxLength(50);
            entity.Property(e => e.StationMac).HasMaxLength(50);
            entity.Property(e => e.StationName).HasMaxLength(50);
            entity.Property(e => e.StationType).HasMaxLength(50);
        });

        modelBuilder.Entity<SysCode>(entity =>
        {
            entity.HasKey(e => new { e.CodeType, e.Code }).HasName("SysCode_pkey");
            entity.ToTable("SysCode");

            entity.HasIndex(e => new { e.CodeType, e.Code }, "idxSysCode").IsUnique();

            entity.Property(e => e.Code).HasMaxLength(50);
            entity.Property(e => e.CodeType)
                .HasMaxLength(50)
                .HasComment("TaskAction:事件狀態/EquipmentStatus:設備狀態");
            entity.Property(e => e.Extend)
                .HasMaxLength(254)
                .HasComment("EquipmentStatus 時此欄位表示顏色");
            entity.Property(e => e.Name).HasMaxLength(50);
            entity.Property(e => e.StringId).HasMaxLength(50);
            entity.Property(e => e.ValueType)
                .HasMaxLength(20)
                .HasComment("String,Boolean,Int,Float");
        });

        modelBuilder.Entity<TaskDatum>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("TaskData_pkey");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.AreaName).HasMaxLength(50);
            entity.Property(e => e.BuildingCode).HasMaxLength(50);
            entity.Property(e => e.BuildingName).HasMaxLength(50);
            entity.Property(e => e.DeptCode).HasMaxLength(50);
            entity.Property(e => e.DeptName).HasMaxLength(50);
            entity.Property(e => e.EventCode).HasMaxLength(100);
            entity.Property(e => e.EventName).HasMaxLength(100);
            entity.Property(e => e.FinishesAt).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifiesAt).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.PlaneCode).HasMaxLength(20);
            entity.Property(e => e.PlaneMapPath).HasMaxLength(200);
            entity.Property(e => e.PlaneName).HasMaxLength(20);
            entity.Property(e => e.ServiceCode).HasMaxLength(50);
            entity.Property(e => e.SponsorDeviceName).HasMaxLength(50);
            entity.Property(e => e.SponsorDevicePid).HasMaxLength(20);
            entity.Property(e => e.SponsorDeviceType).HasMaxLength(50);
            entity.Property(e => e.SponsorLocationCode).HasMaxLength(50);
            entity.Property(e => e.SponsorLocationName).HasMaxLength(50);
            entity.Property(e => e.SponsorObjectCode).HasMaxLength(20);
            entity.Property(e => e.SponsorObjectName).HasMaxLength(50);
            entity.Property(e => e.SponsorObjectType).HasMaxLength(50);
            entity.Property(e => e.SponsorStation).HasMaxLength(20);
            entity.Property(e => e.StartsAt).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.TaskClearMethod).HasMaxLength(254);
            entity.Property(e => e.TaskClearMoreDesc).HasMaxLength(254);
            entity.Property(e => e.TaskHappenReason).HasMaxLength(254);
            entity.Property(e => e.Extra).HasMaxLength(2147483647);
        });

        modelBuilder.Entity<TaskNotifyDetail>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("TaskNotifyDetail_pkey");

            entity.ToTable("TaskNotifyDetail");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.ContactName).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.NotifyContent).HasMaxLength(100);
            entity.Property(e => e.NotifyMessage1).HasMaxLength(512);
            entity.Property(e => e.NotifyMessage2).HasMaxLength(512);
            entity.Property(e => e.NotifyType).HasMaxLength(20);
            entity.Property(e => e.Subject).HasMaxLength(100);
        });

        modelBuilder.Entity<UserClientPara>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("UserClientPara_pkey");

            entity.ToTable("UserClientPara");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.ParaCode).HasMaxLength(50);
            entity.Property(e => e.ParaType).HasMaxLength(50);
            entity.Property(e => e.ParaValue).HasMaxLength(50);
            entity.Property(e => e.UserAccount).HasMaxLength(50);
        });

        modelBuilder.Entity<UserDatum>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("UserData_pkey");
            entity.ToTable("UserData");

            entity.Property(e => e.Id).ValueGeneratedOnAdd();
            entity.Property(e => e.UserAccount).HasMaxLength(50);
            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.DeptCode).HasMaxLength(50);
            entity.Property(e => e.IsAdmin).HasMaxLength(2);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.PwdExpEndDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.PwdExpStartDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.PwdExpType).HasMaxLength(2);
            entity.Property(e => e.RoleCode).HasMaxLength(50);
            entity.Property(e => e.UserAccountDisp).HasMaxLength(50);
            entity.Property(e => e.UserEmail).HasMaxLength(50);
            entity.Property(e => e.UserName).HasMaxLength(50);
            entity.Property(e => e.UserPassword).HasMaxLength(50);
        });

        modelBuilder.Entity<UserDeptMonPerm>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("UserDeptMonPerm_pkey");

            entity.ToTable("UserDeptMonPerm");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.UsageDeptCode).HasMaxLength(50);
            entity.Property(e => e.UserAccount).HasMaxLength(50);
        });

        modelBuilder.Entity<UserLogonLog>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("UserLogonLog_pkey");

            entity.ToTable("UserLogonLog");

            entity.Property(e => e.ClientIp).HasMaxLength(128);
            entity.Property(e => e.CreateDate)
                .HasDefaultValueSql("CURRENT_TIMESTAMP")
                .HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.IsAdmin).HasMaxLength(2);
            entity.Property(e => e.RoleCode).HasMaxLength(50);
            entity.Property(e => e.UserAccount).HasMaxLength(50);
        });

        modelBuilder.Entity<UserToken>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("UserToken_pkey");

            entity.ToTable("UserToken");

            entity.HasIndex(e => e.UserDataId, "idxUserToken_PK").IsUnique();

            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.Token).HasMaxLength(33);
            entity.Property(e => e.TokenDeleted).HasDefaultValue(false);
            entity.Property(e => e.TokenExpiryDate).HasColumnType("timestamp(6) without time zone");
        });

        modelBuilder.Entity<VideoCapture>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("VideoCapture_pkey");

            entity.ToTable("VideoCapture");

            entity.HasIndex(e => new { e.AppCode, e.DeptCode, e.ServiceCode }, "idxVideoCapture_ServiceCode1").IsUnique();

            entity.HasIndex(e => new { e.AppCode, e.ServiceCode, e.DeptCode }, "idxVideoCapture_ServiceCode2");

            entity.Property(e => e.Account).HasMaxLength(50);
            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.BackupDirectory).HasMaxLength(128);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.DeptCode).HasMaxLength(50);
            entity.Property(e => e.Password).HasMaxLength(50);
            entity.Property(e => e.ServiceCode).HasMaxLength(50);
        });

        modelBuilder.Entity<VideoCaptureTask>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("VideoCaptureTask_pkey");

            entity.ToTable("VideoCaptureTask");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.DeptCode).HasMaxLength(50);
            entity.Property(e => e.ServiceCode).HasMaxLength(50);
            entity.Property(e => e.EventCode).HasMaxLength(100);
            entity.Property(e => e.EventName).HasMaxLength(100);
            entity.Property(e => e.ObjectName).HasMaxLength(64);
            entity.Property(e => e.DeviceName).HasMaxLength(20);
            entity.Property(e => e.DeviceType).HasMaxLength(50);
            entity.Property(e => e.TaskStationSid).HasMaxLength(50);
            entity.Property(e => e.BackupDirectory).HasMaxLength(128);
            entity.Property(e => e.Account).HasMaxLength(50);
            entity.Property(e => e.Password).HasMaxLength(50);
            entity.Property(e => e.TaskStartsAt).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
        });

        modelBuilder.Entity<VideoCaptureTaskVideo>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("VideoCaptureTaskVideo_pkey");

            entity.ToTable("VideoCaptureTaskVideo");

            entity.Property(e => e.CameraCode).HasMaxLength(50);
            entity.Property(e => e.CameraName).HasMaxLength(50);
            entity.Property(e => e.CameraVideoFilename).HasMaxLength(64);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
        });

        modelBuilder.Entity<ExclusionPeriod>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("ExclusionPeriod_pkey");

            entity.ToTable("ExclusionPeriod");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.Weekly).HasMaxLength(95);
            entity.Property(e => e.StartTime).HasMaxLength(8);
            entity.Property(e => e.EndTime).HasMaxLength(8);
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
        });

        modelBuilder.Entity<SystemEvent>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("SystemEvent_pkey");

            entity.ToTable("SystemEvent");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.ServiceCode).HasMaxLength(50);
            entity.Property(e => e.EventCode).HasMaxLength(100);
            entity.Property(e => e.EventName).HasMaxLength(100);
            entity.Property(e => e.DeviceType).HasMaxLength(50);
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
        });

        modelBuilder.Entity<QueryRate>(entity =>
        {
            entity.HasKey(e => e.Id).HasName("QueryRate_pkey");

            entity.ToTable("QueryRate");
            // entity.Property(e => e.Uid).HasMaxLength(50);
            entity.Property(e => e.ClientIp).HasMaxLength(50);
            entity.Property(e => e.ClientHostName).HasMaxLength(50);
            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(100);
            entity.Property(e => e.DeptCode).HasMaxLength(100);
            entity.Property(e => e.MenuId).HasMaxLength(100);
            entity.Property(e => e.UserAccount).HasMaxLength(50);
            entity.Property(e => e.HttpStatus).HasMaxLength(50);
            entity.Property(e => e.HttpRequest).HasMaxLength(512);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
        });

        modelBuilder.Entity<VwDeviceInfo>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("VwDeviceInfo");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.AreaName).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.DeviceType).HasMaxLength(50);
            entity.Property(e => e.GroupCode).HasMaxLength(50);
            entity.Property(e => e.ManageDepartCode).HasMaxLength(50);
            entity.Property(e => e.ManageDepartName).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.Name).HasMaxLength(20);
            entity.Property(e => e.ObjectCode).HasMaxLength(20);
            entity.Property(e => e.ObjectName).HasMaxLength(64);
            entity.Property(e => e.ObjectType).HasMaxLength(50);
            entity.Property(e => e.Pid).HasMaxLength(20);
            entity.Property(e => e.StationSid).HasMaxLength(50);
            entity.Property(e => e.UsageDepartCode).HasMaxLength(50);
            entity.Property(e => e.UsageDepartName).HasMaxLength(50);
            entity.Property(e => e.mmWaveType).HasMaxLength(50);
        });

        modelBuilder.Entity<VwEventFenceInfo>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("VwEventFenceInfo");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.EventCode).HasMaxLength(100);
            entity.Property(e => e.FenceCode).HasMaxLength(20);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.ObjectCode).HasMaxLength(20);
        });

        modelBuilder.Entity<VwFenceInfo>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("VwFenceInfo");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.BuildingCode).HasMaxLength(50);
            entity.Property(e => e.BuildingName).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.CustomFenceCode).HasMaxLength(50);
            entity.Property(e => e.FenceCode).HasMaxLength(50);
            entity.Property(e => e.FenceColor).HasMaxLength(50);
            entity.Property(e => e.FenceName).HasMaxLength(50);
            entity.Property(e => e.FenceType).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.PlaneCode).HasMaxLength(20);
            entity.Property(e => e.PlaneName).HasMaxLength(50);
        });

        modelBuilder.Entity<VwNotifyDetailInfo>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("VwNotifyDetailInfo");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.ContactCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.DeptCode).HasMaxLength(50);
            entity.Property(e => e.DeptName).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.Name).HasColumnType("character varying");
            entity.Property(e => e.NotifyCode).HasMaxLength(20);
            entity.Property(e => e.NotifyType).HasMaxLength(20);
            entity.Property(e => e.Source).HasMaxLength(50);
        });

        modelBuilder.Entity<VwNotifyHeaderInfo>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("VwNotifyHeaderInfo");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.AreaName).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.NotifyCode).HasMaxLength(20);
            entity.Property(e => e.NotifyName).HasMaxLength(64);
        });

        modelBuilder.Entity<VwObjectEventInfo>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("VwObjectEventInfo");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.EventCode).HasMaxLength(100);
            entity.Property(e => e.FenceCode).HasMaxLength(20);
            entity.Property(e => e.ObjectCode).HasMaxLength(20);
            entity.Property(e => e.Pid).HasMaxLength(20);
            entity.Property(e => e.SddComp).HasMaxLength(10);
            entity.Property(e => e.SddResource).HasMaxLength(50);
            entity.Property(e => e.ServiceCode).HasMaxLength(50);
        });

        modelBuilder.Entity<VwObjectGroupInfo>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("VwObjectGroupInfo");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.AreaName).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.CustomAreaCode).HasMaxLength(50);
            entity.Property(e => e.GroupCode).HasMaxLength(50);
            entity.Property(e => e.GroupName).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.OrganizationCode).HasMaxLength(50);
            entity.Property(e => e.OrganizationName).HasMaxLength(50);
        });

        modelBuilder.Entity<VwUserDeptMonInfo>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("VwUserDeptMonInfo");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.AreaName).HasMaxLength(50);
            entity.Property(e => e.CustomDeptCode).HasMaxLength(50);
            entity.Property(e => e.DeptCode).HasMaxLength(50);
            entity.Property(e => e.DeptEmail).HasMaxLength(50);
            entity.Property(e => e.DeptName).HasMaxLength(50);
            entity.Property(e => e.OrgType).HasMaxLength(50);
            entity.Property(e => e.SectorCode).HasMaxLength(50);
            entity.Property(e => e.Supervisor).HasMaxLength(50);
            entity.Property(e => e.UserAccount).HasColumnType("character varying");
        });

        modelBuilder.Entity<vwDepartmentInfo>(entity =>
        {
            entity
                .HasNoKey()
                .ToView("vwDepartmentInfo");

            entity.Property(e => e.AppCode).HasMaxLength(50);
            entity.Property(e => e.AreaCode).HasMaxLength(50);
            entity.Property(e => e.AreaName).HasMaxLength(50);
            entity.Property(e => e.BuildingCode).HasMaxLength(50);
            entity.Property(e => e.CreateDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.CreateUserAccount).HasMaxLength(50);
            entity.Property(e => e.CustomDeptCode).HasMaxLength(50);
            entity.Property(e => e.DeptCode).HasMaxLength(50);
            entity.Property(e => e.DeptEmail).HasMaxLength(50);
            entity.Property(e => e.DeptName).HasMaxLength(50);
            entity.Property(e => e.ModifyDate).HasColumnType("timestamp(6) without time zone");
            entity.Property(e => e.ModifyUserAccount).HasMaxLength(50);
            entity.Property(e => e.OrgType).HasMaxLength(50);
            entity.Property(e => e.PlaneCode).HasMaxLength(20);
            entity.Property(e => e.SectorCode).HasMaxLength(50);
            entity.Property(e => e.Supervisor).HasMaxLength(50);
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
