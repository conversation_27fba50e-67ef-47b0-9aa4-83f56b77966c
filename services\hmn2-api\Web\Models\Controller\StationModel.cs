﻿using System.Text.RegularExpressions;
using Web.Services.Interfaces;
using Web.Validation;

using Microsoft.EntityFrameworkCore;
using Web.Models.Controller.Station;
using Web.Repository.Models.Entities;
using System.ComponentModel.DataAnnotations;
using Web.Models.Interface;
using Web.Constant;
using Web.Models.Service.Fusion;
using Microsoft.AspNetCore.Mvc;

namespace Web.Models.Controller.Station;

public class InRetrieveStation
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    public string? AreaCode { get; set; }
    public string? SID { get; set; }
    public string? StationName { get; set; }
    public string? BuildingCode { get; set; }
    public string? PlaneCode { get; set; }
    public string? LocCode { get; set; }
    public string? Enable { get; set; }
    public string? IsConnected { get; set; }
    public string? SceneMode { get; set; }
    public string CompareMode { get; set; }
    public string? StationType { get; set; }
    public string? SpaceType { get; set; }
}

public class InCreateNewStationFromFusion : IValidationRequest
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Enable")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }
    
    private string? sid;

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "SID")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "SID")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "SID")]
    [Unique("", "Station", "SID", true, true, true, ErrorMessage = Constants.ErrorCode.Unique + "SID")]
    public string? SID { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "StationName")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "StationName")]
    public string? StationName { get; set; }
    
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string? AreaCode { get; set; }
    
    [Exists("AreaCode", "Location", "LocCode", ErrorMessage = Constants.ErrorCode.NotFound + "LocCode")]
    public string? LocCode { get; set; }
    
    [Exists("AreaCode", "Plane", "PlaneCode", ErrorMessage = Constants.ErrorCode.NotFound + "PlaneCode")]
    [RequiredWhenPresent("DiffPositionX,DiffPositionY", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PlaneCode")]
    public string? PlaneCode { get; set; }
    
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "AxisX")]
    public double? AxisX { get; set; }
    
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "AxisY")]
    public double? AxisY { get; set; }
    
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "DiffPositionX")]
    public double? DiffPositionX { get; set; }
    
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "DiffPositionY")]
    public double? DiffPositionY { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "StationType")]
    [RegularExpression(@"^(fih-station|fih-smft)$", ErrorMessage = Constants.ErrorCode.Pattern + "StationType")]
    public string StationType { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "SpaceType")]
    [RegularExpression(@"^(room|corridor|publicArea)$", ErrorMessage = Constants.ErrorCode.Pattern + "SpaceType")]
    public string? SpaceType { get; set; }
}

public class InCreateStation:IValidationRequest
{
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string? AreaCode { get; set; }

    [Exists("AreaCode", "Location", "LocCode", ErrorMessage = Constants.ErrorCode.NotFound + "LocCode")]
    public string? LocCode { get; set; }
    
    [Exists("AreaCode", "Plane", "PlaneCode", ErrorMessage = Constants.ErrorCode.NotFound + "PlaneCode")]
    [RequiredWhenPresent("DiffPositionX,DiffPositionY", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PlaneCode")]
    public string? PlaneCode { get; set; }
    
    private string? sid;

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "SID")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "SID")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "SID")]
    [Unique("", "Station", "SID", true, true, true, ErrorMessage = Constants.ErrorCode.Unique + "SID")]
    public string? SID { get=>sid; set { sid = value; } }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "StationName")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "StationName")]
    public string? StationName { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "StationType")]
    [RegularExpression(@"^(fih-station|fih-smft)$", ErrorMessage = Constants.ErrorCode.Pattern + "StationType")]
    public string StationType { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "SpaceType")]
    [RegularExpression(@"^(room|corridor|publicArea)$", ErrorMessage = Constants.ErrorCode.Pattern + "SpaceType")]
    public string? SpaceType { get; set; }

    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "AxisX")]
    public double? AxisX { get; set; }
    
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "AxisY")]
    public double? AxisY { get; set; }
    
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "DiffPositionX")]
    public double? DiffPositionX { get; set; }
    
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "DiffPositionY")]
    public double? DiffPositionY { get; set; }
}

public class InDeleteStation : IValidationRequest
{
    [Exists("", "Station", "SID", true, true, true, ErrorMessage = Constants.ErrorCode.NotFound + "SID")]
    public string SID { get; set; }
}

public class InUpdateStation : IValidationRequest
{
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Exists("AreaCode", "Location", "LocCode", ErrorMessage = Constants.ErrorCode.NotFound + "LocCode")]
    public string? LocCode { get; set; }
    
    [Exists("AreaCode", "Plane", "PlaneCode", ErrorMessage = Constants.ErrorCode.NotFound + "PlaneCode")]
    [RequiredWhenPresent("DiffPositionX,DiffPositionY", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PlaneCode")]
    public string? PlaneCode { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "SID")]
    [Exists("", "Station", "SID", true, true, true, ErrorMessage = Constants.ErrorCode.NotFound + "SID")]
    public string SID { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "StationName")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "StationName")]
    public string? StationName { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "SpaceType")]
    [RegularExpression(@"^(room|corridor|publicArea)$", ErrorMessage = Constants.ErrorCode.Pattern + "SpaceType")]
	public string? SpaceType { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Enable")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }

    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "AxisX")]
    public double? AxisX { get; set; }
    
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "AxisY")]
    public double? AxisY { get; set; }
    
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "DiffPositionX")]
    public double? DiffPositionX { get; set; }
    
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "DiffPositionY")]
    public double? DiffPositionY { get; set; }
}

public class InUpdateStationConfiguration : IValidationRequest
{   
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "SID")]
    [Exists("", "Station", "SID", true, true, true, ErrorMessage = Constants.ErrorCode.NotFound + "SID")]
    [ListDuplicate("Configurations", "resourceId", ErrorMessage = Constants.ErrorCode.Duplicate + "Configurations.resourceId")]
    [RequestNotNullOrEmpty("Configurations", ErrorMessage = Constants.ErrorCode.RequestNullOrEmpty + "Configurations")]
    public string SID { get; set; }

    public List<Configuration>? Configurations { get; set; }
}

public class InBindingPlane
{
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string? AreaCode { get; set; }
    //-------------------------------------
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "SID")]
    [Exists("", "Station", "SID", true, true, true, ErrorMessage = Constants.ErrorCode.NotFound + "SID")]
    public string? SID { set; get; }
    //-------------------------------------
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PlaneCode")]
    [Exists("AreaCode", "Plane", "PlaneCode",ErrorMessage = Constants.ErrorCode.NotFound + "PlaneCode")]
    public string? PlaneCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "DiffPositionX")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "DiffPositionX")]
    public double? DiffPositionX { get; set; }
    //-------------------------------------
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "DiffPositionY")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "DiffPositionY")]
    public double? DiffPositionY { get; set; }
    //-------------------------------------
}
public class InUnBindingPlane
{
    //-------------------------------------
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "SID")]
    [Exists("", "Station", "SID", true, true, true, ErrorMessage = Constants.ErrorCode.NotFound + "SID")]
    public string? SID { set; get; }
    //-------------------------------------
}
public class InCreateNewGuard2FromFusion : IValidationRequest
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Enable")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }
    //-------------------------------------
    private string? sid;

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "SID")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "SID")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "SID")]
    [Unique("", "Station", "SID", true, true, true, ErrorMessage = Constants.ErrorCode.Unique + "SID")]
    public string? SID { get => sid; set { sid = value; Pid = $"{value}{Constants.MMWAVE_PID_SUFFIX}"; } }
    //-------------------------------------
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "StationName")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "StationName")]
    public string? StationName { get; set; }
    //-------------------------------------
    [Unique("", "Device", "Pid", ErrorMessage = Constants.ErrorCode.Unique + "Pid")]
    public string Pid { get; set; }
    //-------------------------------------
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "DeviceName")]
    [StringLength(20, ErrorMessage = Constants.ErrorCode.Length + "DeviceName")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "DeviceName")]
    public string DeviceName { get; set; }
    //-------------------------------------
    [IsUsageDept("AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "UsageDepartCode")]
    public string? UsageDepartCode { get; set; }
    //-------------------------------------
    [IsManagedDept("AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "ManageDepartCode")]
    public string? ManageDepartCode { get; set; }
    //-------------------------------------
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string? AreaCode { get; set; }
    //-------------------------------------
    [Exists("AreaCode", "Building", "BuildingCode", ErrorMessage = Constants.ErrorCode.NotFound + "BuildingCode")]
    public string? BuildingCode { get; set; }
    //-------------------------------------
    [Exists("AreaCode", "Plane", "PlaneCode", ErrorMessage = Constants.ErrorCode.NotFound + "PlaneCode")]
    public string? PlaneCode { get; set; }
    //-------------------------------------
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "AxisX")]
    public double? AxisX { get; set; }
    //-------------------------------------
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "AxisY")]
    public double? AxisY { get; set; }
    //-------------------------------------
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "DiffPositionX")]
    public double? DiffPositionX { get; set; }
    //-------------------------------------
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "DiffPositionY")]
    public double? DiffPositionY { get; set; }
    //-------------------------------------
}

public class InCreateAt3:IValidationRequest
{
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string? AreaCode { get; set; }
    //-------------------------------------
    private string? sid;

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "SID")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "SID")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "SID")]
    [Unique("", "Station", "SID", true, true, true, ErrorMessage = Constants.ErrorCode.Unique + "SID")]
    public string? SID { get=>sid; set { sid = value;Pid = $"{value}{Constants.MMWAVE_PID_SUFFIX}"; } }
    //-------------------------------------
    [Unique("", "Device", "Pid", ErrorMessage = Constants.ErrorCode.Unique + "Pid")]
    public string? Pid { get; set; }
    //-------------------------------------
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "StationName")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "StationName")]
    public string? StationName { get; set; }
    //-------------------------------------
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "DeviceName")]
    [StringLength(20, ErrorMessage = Constants.ErrorCode.Length + "DeviceName")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "DeviceName")]
    public string DeviceName { get; set; }
    //-------------------------------------
    [IsUsageDept("AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "UsageDepartCode")]
    public string? UsageDepartCode { get; set; }
    //-------------------------------------
    [IsManagedDept("AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "ManageDepartCode")]
    public string? ManageDepartCode { get; set; }
}

public class InDeleteSt3 : IValidationRequest
{
    //-------------------------------------
    [Exists("", "Station", "SID", true, true, true, ErrorMessage = Constants.ErrorCode.NotFound + "SID")]
    public string SID { get; set; }
}

public class InRetrieveSt3 : IPaginationRequest
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    public string? AreaCode { get; set; }
    public string? SID { get; set; }
    public string? StationName { get; set; }
    public string? BuildingCode { get; set; }
    public string? PlaneCode { get; set; }
    public string? LocCode { get; set; }
    public string? Enable { get; set; }
    public string? IsConnected { get; set; }
    public string? SceneMode { get; set; }
    //SearchMode, CompareKind, CompareType 這是供全文檢索用的，怕忘記這個欄位叫CompareMode
    public string CompareMode { get; set; }
    public string? StationType { get; set; }
}

public class InUpdateEnable : IValidationRequest
{
    //-------------------------------------
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "SID")]
    [Exists("", "Station", "SID", true, true, true, ErrorMessage = Constants.ErrorCode.NotFound + "SID")]
    public string SID { get; set; }
    //-------------------------------------
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Enable")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }
    //-------------------------------------
}

public class InUpdateSt3 : IValidationRequest
{
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }
    //-------------------------------------
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "SID")]
    [Exists("", "Station", "SID", true, true, true, ErrorMessage = Constants.ErrorCode.NotFound + "SID")]
    public string SID { get; set; }
    //-------------------------------------
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "StationName")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "StationName")]
    public string? StationName { get; set; }
    //-------------------------------------
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "DeviceName")]
    [StringLength(20, ErrorMessage = Constants.ErrorCode.Length + "DeviceName")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "DeviceName")]
    public string DeviceName { get; set; }
    //-------------------------------------
    [IsUsageDept("AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "UsageDepartCode")]
    [CanModifyUsageDept("AreaCode","SID", ErrorMessage = Constants.ErrorCode.Invalid + "UsageDepartCode")]
    public string? UsageDepartCode { get; set; }
    //-------------------------------------
    [IsManagedDept("AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "ManageDepartCode")]
    [CanModifyManageDept("AreaCode", "SID", "UsageDepartCode", ErrorMessage = Constants.ErrorCode.Invalid + "ManageDepartCode")]
    public string? ManageDepartCode { get; set; }
    //-------------------------------------
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Enable")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }
    //-------------------------------------
    //[Required(ErrorMessage = "err.null.param.Configurations")] //Configurations = [] 使用Required會認為是有資料，所以要靠ValidationResourceAttribute來檢查
    //[ValidationResource(ErrorMessage ="err.invalid.param.Configuration")]
    public List<Configuration>? Configurations { get; set; }
}

public class Configuration
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "resourceId")]
    public string resourceId { get; set; }
    //-------------------------------------
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "value")]
    public string value { get; set; }
}

public class InUpdateSt3Configuration : IValidationRequest
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "SID")]
    [Exists("", "Station", "SID", true, true, true, ErrorMessage = Constants.ErrorCode.NotFound + "SID")]
    [ListDuplicate("Configurations", "resourceId", ErrorMessage = Constants.ErrorCode.Duplicate + "Configurations.resourceId")]
    [RequestNotNullOrEmpty("Configurations", ErrorMessage = Constants.ErrorCode.RequestNullOrEmpty + "Configurations")]
    public string SID { get; set; }

    public List<Configuration>? Configurations { get; set; }
}

public class InRetrieveConfigurationsCompatible : IPaginationRequest
{
    public string page { get; set; }
    public string size { get; set; }
    public string AreaCode { get; set; }
    public string SIDs { get; set; }
    public string ResourceIds { get; set; }
    //SearchMode, CompareKind, CompareType 這是供全文檢索用的，怕忘記這個欄位叫CompareMode
    public string CompareMode { get; set; }
}