﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class TaskDatum
{
    public int Id { get; set; }

    public int? TaskId { get; set; }

    public string? AppCode { get; set; }

    public string? AreaCode { get; set; }

    public string? AreaName { get; set; }

    public string? BuildingCode { get; set; }

    public string? BuildingName { get; set; }

    public string? PlaneCode { get; set; }

    public string? PlaneName { get; set; }

    public string? DeptCode { get; set; }

    public string? DeptName { get; set; }

    public string? PlaneMapPath { get; set; }

    public double? MapWidth { get; set; }

    public double? MapHeight { get; set; }

    public double? PositionX { get; set; }

    public double? PositionY { get; set; }

    public int? Action { get; set; }

    public bool? Active { get; set; }

    public string? EventCode { get; set; }

    public string? EventName { get; set; }

    public string? ServiceCode { get; set; }

    public DateTime? StartsAt { get; set; }

    public DateTime? FinishesAt { get; set; }

    public DateTime? ModifiesAt { get; set; }

    public string? SponsorStation { get; set; }

    public double? DiffPositionX { get; set; }

    public double? DiffPositionY { get; set; }

    public string? SponsorLocationCode { get; set; }

    public string? SponsorLocationName { get; set; }

    public string? SponsorObjectCode { get; set; }

    public string? SponsorObjectName { get; set; }

    public string? SponsorObjectType { get; set; }

    public string? SponsorDevicePid { get; set; }

    public string? SponsorDeviceName { get; set; }

    public string? SponsorDeviceType { get; set; }

    public string? TaskHappenReason { get; set; }

    public string? TaskClearMethod { get; set; }

    public string? TaskClearMoreDesc { get; set; }
    public string? Extra { get; set; }
}
