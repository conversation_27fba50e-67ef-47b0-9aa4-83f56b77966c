﻿using AspectCore.DynamicProxy;
using Microsoft.Extensions.Caching.Memory;

namespace Web.Interceptors;

public class CacheInterceptor(IMemoryCache cache, int seconds = 60) : AbstractInterceptorAttribute
{
    private readonly IMemoryCache _cache = cache;
    private readonly TimeSpan _expiration = TimeSpan.FromSeconds(seconds);

    public override async Task Invoke(AspectContext context, AspectDelegate next)
    {
        var cacheKey = $"{context.ImplementationMethod.Name}_{string.Join("_", context.Parameters)}";
        if (_cache.TryGetValue(cacheKey, out var cachedValue))
        {
            context.ReturnValue = cachedValue;
            return;
        }

        await next(context);

        _cache.Set(cacheKey, context.ReturnValue, _expiration);
    }
}
