﻿namespace Web.Interceptors;

using AspectCore.DynamicProxy;
using NLog;
using System;
using System.Threading.Tasks;

public class LoggingInterceptorAttribute : AbstractInterceptorAttribute
{
    private static readonly ILogger logger = LogManager.GetCurrentClassLogger();

    public override async Task Invoke(AspectContext context, AspectDelegate next)
    {
        string methodName = $"{context.ImplementationMethod.DeclaringType.FullName}.{context.ImplementationMethod.Name}";
        logger.Info($"Start {methodName}");

        var startTime = DateTime.Now;
        try
        {
            await next(context);
        }
        finally
        {
            var elapsed = DateTime.Now - startTime;
            logger.Info($"End {methodName} - Duration: {elapsed.TotalMilliseconds}ms");
        }
    }
}