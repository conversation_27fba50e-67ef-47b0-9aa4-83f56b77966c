﻿using System.Text;
using System.Text.Json;
using System.Linq.Expressions;
using System.Reflection;
using Microsoft.EntityFrameworkCore;
using Web.Repository.Interface;
using Web.Repository.Models.Entities;
using System.Collections;
using System.Data;
using System.Diagnostics;
using System.Runtime.CompilerServices;
using Dapper;
using Microsoft.Extensions.Options;
using static Dapper.SqlMapper;
using KellermanSoftware.CompareNetObjects;
using DocumentFormat.OpenXml.Vml.Office;
using System.Collections.Concurrent;
using Web.Services.Interfaces;
using System.Dynamic;
using DocumentFormat.OpenXml.Spreadsheet;
using Web.Services;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using DocumentFormat.OpenXml.InkML;
using Microsoft.EntityFrameworkCore.Metadata;
using System.Linq;
using Microsoft.EntityFrameworkCore.Query.Internal;
using Microsoft.AspNetCore.Http;
using System.Net.Http;
using Web.Models.AppSettings;

namespace Web.Services;

public class CompareResult<T>
{
    public int Count { get; set; }
    public List<T> AddedObjects { get; set; }
    public List<T> DeletedObjects { get; set; }
    public List<T> ModifiedObjects { get; set; }
    public List<object> AddedChildObjects { get; set; }
    public List<object> DeletedChildObjects { get; set; }
    public List<object> ModifiedChildObjects { get; set; }
}

public static class ExpressionHelper
{
    public static string GetQueryString<T>(Expression<Func<T, bool>> predicate)
    {
        var queryString = GetStringRepresentation(predicate.Body);
        return queryString;
    }

    private static string GetStringRepresentation(Expression expression)
    {
        switch (expression.NodeType)
        {
            case ExpressionType.Quote:
                var quoteExp = (UnaryExpression)expression;
                return GetStringRepresentation(quoteExp.Operand);
            case ExpressionType.Lambda:
                var lambdaExp = (LambdaExpression)expression;
                return $"({string.Join(", ", lambdaExp.Parameters.Select(p => p.Name))}) => {GetStringRepresentation(lambdaExp.Body)}";
            case ExpressionType.Call:
                var methodCallExpression = (MethodCallExpression)expression;
                var methodName = methodCallExpression.Method.Name;
                var arguments = string.Join(", ", methodCallExpression.Arguments.Select(GetStringRepresentation));
                return $"{GetMemberName(methodCallExpression.Object)}.{methodName}({arguments})";

            case ExpressionType.AndAlso:
            case ExpressionType.OrElse:
                var binaryExpression = (BinaryExpression)expression;
                return $"({GetStringRepresentation(binaryExpression.Left)}) {GetOperatorSymbol(binaryExpression.NodeType)} ({GetStringRepresentation(binaryExpression.Left).ToLowerInvariant()})";

            case ExpressionType.Equal:
            case ExpressionType.NotEqual:
            case ExpressionType.GreaterThan:
            case ExpressionType.GreaterThanOrEqual:
            case ExpressionType.LessThan:
            case ExpressionType.LessThanOrEqual:
                binaryExpression = (BinaryExpression)expression;
                return $"{GetMemberName(binaryExpression.Left)} {GetOperatorSymbol(binaryExpression.NodeType)} {GetMemberName(binaryExpression.Left).ToLowerInvariant()}";

            case ExpressionType.MemberAccess:
                var memberExpression = (MemberExpression)expression;
                return GetMemberName(memberExpression);

            default:
                throw new NotSupportedException($"The node type '{expression.NodeType}' is not supported.");
        }
    }

    private static string GetMemberName(Expression expression)
    {
        switch (expression)
        {
            case MemberExpression memberExpression:
                return memberExpression.Member.Name;
            case ConstantExpression constantExpression:
                return constantExpression.Value?.ToString() ?? "null";
            case UnaryExpression unaryExpression:
                // 处理一元表达式
                if (unaryExpression.NodeType == ExpressionType.Convert)
                {
                    return GetMemberName(unaryExpression.Operand);
                }
                else
                {
                    throw new NotSupportedException($"The unary expression type '{unaryExpression.NodeType}' is not supported.");
                }
            case MethodCallExpression methodCallExpression:
                // 处理方法调用表达式
                var methodInfo = methodCallExpression.Method;
                var methodName = methodInfo.Name;
                var arguments = string.Join(", ", methodCallExpression.Arguments.Select(GetMemberName));
                return $"{methodName}({arguments})";
            default:
                return $"{(expression==null || expression.GetType()==null ? "Null" : expression.GetType().Name)}ExpressionTypeNotSuppored";
                //throw new NotSupportedException($"The expression type '{expression.GetType().Name}' is not supported.");
        }
    }

    private static string GetOperatorSymbol(ExpressionType nodeType)
    {
        return nodeType switch
        {
            ExpressionType.Equal => "=",
            ExpressionType.NotEqual => "!=",
            ExpressionType.GreaterThan => ">",
            ExpressionType.GreaterThanOrEqual => ">=",
            ExpressionType.LessThan => "<",
            ExpressionType.LessThanOrEqual => "<=",
            ExpressionType.AndAlso => "AND",
            ExpressionType.OrElse => "OR",
            _ => throw new NotSupportedException($"The node type '{nodeType}' is not supported.")
        };
    }
}

public class DataAccessService(IUnitOfWork unitOfWork,
    IRequestContextService requestContextService,
    FusionS3HMNContext dbContext,
    FusionS3HMNContext dbLogContext,
    ILogService logService,
    IDbContextFactory<FusionS3HMNContext> contextFactory) : IDataAccessService
{
    //HACK: 10/28有看到網路討論，有人提到共用DbContext在多個Transaction中有時會出現問題（commit沒有全部commit），但大部份人都回覆沒遇過，這裡先memo，如果未來遇到再來拆成各自使用
    private readonly FusionS3HMNContext _dbContext = dbContext;
    private readonly FusionS3HMNContext _dbLogContext = dbLogContext;
    private readonly IRequestContextService _requestContextService = requestContextService;
    private readonly IUnitOfWork? _unitOfWork = unitOfWork;
    private readonly ILogService _logService = logService;
    private readonly IDbContextFactory<FusionS3HMNContext> _contextFactory = contextFactory;

    private bool _beginTransaction = false;

    private static string? GetMethodName(System.Reflection.MethodBase method)
    {
        string? _methodName = method?.DeclaringType?.FullName;

        if (_methodName.Contains('>') || _methodName.Contains('<'))
        {
            _methodName = _methodName.Split('<', '>')[1];
        }
        else
        {
            _methodName = method?.Name;
        }

        return _methodName;
    }

    private static string ExtractPropertyName<T>(Expression<Func<T, object>> expression)
    {
        if (expression.Body is MemberExpression memberExpression)
        {
            return memberExpression.Member.Name;
        }
        else if (expression.Body is UnaryExpression unaryExpression)
        {
            if (unaryExpression.Operand is MemberExpression operandExpression)
            {
                return operandExpression.Member.Name;
            }
        }
        return expression.Body.ToString().Split('.').Last();
    }

    public async Task<List<dynamic>> ExecuteSqlQuery(string sql, DynamicParameters parameters)
    {
        // 获取底层的数据库连接
        var connection = _dbContext.Database.GetDbConnection();

        // 确保数据库连接是打开的
        if (connection.State != ConnectionState.Open)
        {
            await connection.OpenAsync();
        }

        // 使用 Dapper 执行查询
        return (await connection.QueryAsync<dynamic>(sql, parameters)).AsList();
    }

    public async Task<List<T>> ExecuteSqlQuery<T>(string sql)
    {
        // 获取底层的数据库连接
        var connection = _dbContext.Database.GetDbConnection();

        // 确保数据库连接是打开的
        if (connection.State != ConnectionState.Open)
        {
            await connection.OpenAsync();
        }

        // 使用 Dapper 执行查询
        return (await connection.QueryAsync<T>(sql)).AsList();
    }

    /// <summary>
    /// 使用新的 DbContext 執行查詢
    /// 此方法比較不會產生
    /// System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. 
    /// This is usually caused by different threads concurrently using the same instance of DbContext. 
    /// For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
    /// 
    /// 但此方法會比較要考慮效能問題，因為每次都會建立新的 DbContext 實例，當同時有多個查詢時，會耗費較多資源
    /// 以及每次都會CreateDbContext及CloseDbContext，速度上會比較慢
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="predicate"></param>
    /// <returns></returns>
    public async Task<List<T>> FetchWithNewContext<T>(Expression<Func<T, bool>> predicate = null) where T : class
    {
        //DateTime startTime = DateTime.UtcNow;

        using var context = _contextFactory.CreateDbContext();

        DbSet<T> dbSet = context.Set<T>();

        dynamic result;

        if (predicate != null)
        {
            result = await dbSet.Where(predicate).AsNoTracking().ToListAsync();
        }
        else
        {
            result = await dbSet.AsNoTracking().ToListAsync();
        }

        //TimeSpan executionTime = DateTime.UtcNow - startTime;

        //LogQuery(typeof(T).Name, predicate, executionTime);

        return result;
    }

    public IQueryable<T> Fetch<T>(Expression<Func<T, bool>> predicate = null) where T : class
    {
        //DateTime startTime = DateTime.UtcNow;

        DbSet<T> dbSet = _dbContext.Set<T>();

        dynamic result;

        if (predicate != null)
        {
            result = dbSet.Where(predicate).AsNoTracking();
        }
        else
        {
            result = dbSet.AsNoTracking();
        }

        //TimeSpan executionTime = DateTime.UtcNow - startTime;

        //LogQuery(typeof(T).Name, predicate, executionTime);

        return result;
    }
    public async Task<List<T>> FetchAsync<T>(Expression<Func<T, bool>> predicate = null) where T : class
    {
        using var context = _contextFactory.CreateDbContext();
        var dbSet = context.Set<T>();

        if (predicate != null)
        {
            return await dbSet.Where(predicate)
                            .AsNoTracking()
                            .ToListAsync();
        }

        return await dbSet.AsNoTracking()
                        .ToListAsync();
    }

    public async Task<TResult?> MaxAsync<TEntity, TResult>(Expression<Func<TEntity, TResult?>> selector)
    where TEntity : class
    where TResult : struct
    {
        return await _dbContext.Set<TEntity>()
            .Select(selector)
            .DefaultIfEmpty()
            .MaxAsync();
    }

    // 新的並行查詢方法，返回一個字典
    public async Task<IDictionary<string, object>> ExecuteParallelQueriesAsync(params (string Name, Func<FusionS3HMNContext, Task<object>> Query)[] queries)
    {
        var tasks = queries.Select(q => (q.Name, Task: ExecuteQueryAsync(q.Query))).ToArray();
        await Task.WhenAll(tasks.Select(t => t.Task));

        return tasks.ToDictionary(t => t.Name, t => t.Task.Result);
    }

    private async Task<object> ExecuteQueryAsync(Func<FusionS3HMNContext, Task<object>> query)
    {
        using var context = _contextFactory.CreateDbContext();
        return await query(context);
    }

    public string? ConvertExpressionToSql(LambdaExpression expression)
    {
        Type entityType = expression.Parameters[0].Type;

        var dbSetProperty = _dbContext.GetType().GetProperties()
            .FirstOrDefault(p => p.PropertyType == typeof(DbSet<>).MakeGenericType(entityType)) ?? throw new InvalidOperationException($"DbSet<{entityType.Name}> not found in the DbContext.");

        var dbSet = dbSetProperty.GetValue(_dbContext) as IQueryable;
        var whereMethod = typeof(Queryable).GetMethods()?
            .FirstOrDefault(m => m.Name == "Where" && m.GetParameters().Length == 2)?
            .MakeGenericMethod(entityType);

        var query = whereMethod?.Invoke(null, new object[]{ dbSet, expression }) as IQueryable;

        return query?.ToQueryString().Replace("'", "");
    }

    [Obsolete("This method is deprecated. Use Fetch(Expression<Func<T, bool>> predicate = null) instead.", true)]
    public IQueryable<T> Fetch<T>(string appCode) where T : class
    {
        if (typeof(T).GetProperty("AppCode") != null)
        {
            // 使用反射來創建一個表示篩選條件的運算式
            var parameter = Expression.Parameter(typeof(T), "e");
            var propertyAppCode = Expression.Property(parameter, "AppCode");
            var constantAppCode = Expression.Constant(appCode);
            var equalAppCode = Expression.Equal(propertyAppCode, constantAppCode);
            var lambda = Expression.Lambda<Func<T, bool>>(equalAppCode, parameter);

            return Fetch(lambda);
        }
        else
        {
            throw new InvalidOperationException("The type does not have an AppCode property.");
        }
    }

    [Obsolete("This method is deprecated. Use Fetch(Expression<Func<T, bool>> predicate = null) instead.", true)]
    public IQueryable<T> Fetch<T>(string appCode, string areaCode) where T : class
    {
        if (typeof(T).GetProperty("AppCode") != null && typeof(T).GetProperty("AreaCode") != null)
        {
            // 使用反射來創建一個表示篩選條件的運算式
            var parameter = Expression.Parameter(typeof(T), "e");
            var propertyAppCode = Expression.Property(parameter, "AppCode");
            var constantAppCode = Expression.Constant(appCode);
            var equalAppCode = Expression.Equal(propertyAppCode, constantAppCode);

            var propertyAreaCode = Expression.Property(parameter, "AreaCode");
            var constantAreaCode = Expression.Constant(areaCode);
            var equalAreaCode = Expression.Equal(propertyAreaCode, constantAreaCode);

            var andExp = Expression.AndAlso(equalAppCode, equalAreaCode);
            var lambda = Expression.Lambda<Func<T, bool>>(andExp, parameter);

            return Fetch(lambda);
        }
        else
        {
            throw new InvalidOperationException("The type does not have an AppCode or AreaCode property.");
        }
    }

    [Obsolete("This method is deprecated. Use Fetch(Expression<Func<T, bool>> predicate = null) instead.", true)]
    public IQueryable<T> Fetch<T>(string appCode, IEnumerable<string> areaCodeList) where T : class
    {
        if (typeof(T).GetProperty("AppCode") != null && typeof(T).GetProperty("AreaCode") != null)
        {
            // 使用反射來創建一個表示篩選條件的運算式
            var parameter = Expression.Parameter(typeof(T), "e");
            var propertyAppCode = Expression.Property(parameter, "AppCode");
            var constantAppCode = Expression.Constant(appCode);
            var equalAppCode = Expression.Equal(propertyAppCode, constantAppCode);

            var propertyAreaCode = Expression.Property(parameter, "AreaCode");
            var constantAreaCodeList = Expression.Constant(areaCodeList);

            MethodInfo method = typeof(Enumerable)
                                                .GetMethods()
                                                .Where(x => x.Name == "Contains")
                                                .Single(x => x.GetParameters().Length == 2)
                                                .MakeGenericMethod(typeof(string));

            var containsAreaCode = Expression.Call(method, constantAreaCodeList, propertyAreaCode);

            var andExp = Expression.AndAlso(equalAppCode, containsAreaCode);
            var lambda = Expression.Lambda<Func<T, bool>>(andExp, parameter);

            return Fetch(lambda);
        }
        else
        {
            throw new InvalidOperationException("The type does not have an AppCode or AreaCode property.");
        }
    }

    /// <summary>
    /// 
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="query"></param>
    /// <code>
    /// var query = from a in userDeptPermissions
    ///             join b in departments
    ///             on new { appcode = a.AppCode, areacode = a.AreaCode, deptcode = a.UsageDeptCode } equals
    ///                new { appcode = b.AppCode, areacode = b.AreaCode, deptcode = b.DeptCode }
    ///             select b;
    /// 
    /// var queryExpression = Expression.Lambda&lt;Func&lt;IQueryable&lt;TargetModel&gt;&gt;&gt;(Expression.Constant(query.AsQueryable()));
    /// 
    /// var result = base.ExecuteComplexQuery(queryExpression).Distinct()
    ///                        .OrderByDescending(e =&gt; e.DeptName)
    ///                        .ToList();
    /// </code>
    /// <returns>IQueryable&lt;T&gt; 執行結果</returns>
    public IQueryable<T> ExecuteLINQ<T>(IEnumerable<T> query) where T : class
    {
        IQueryable<T> queryable = query.AsQueryable();

        var queryExpression = Expression.Lambda<Func<IQueryable<T>>>(Expression.Constant(queryable));

        // 编译表达式并执行它
        IQueryable<T> result = queryExpression.Compile().Invoke();

        return result;
    }


    public async Task<int?> CreateAsync<T>(T entity, [CallerMemberName] string callMethodName = null) where T : class
    {
        ArgumentNullException.ThrowIfNull(entity);

        string? requestUUID = Guid.NewGuid().ToString();
        string logActionName = $"{callMethodName ?? "Unknown"}/{GetType().Name}/{GetMethodName(new StackTrace().GetFrame(0)?.GetMethod())}";

        _logService.Logging("info", logActionName, requestUUID, "Start");

        var tableName = _dbContext.Model.FindEntityType(typeof(T)).GetTableName(); // 使用EF Core獲取表名

        _dbContext.Set<T>().Add(entity);

        _logService.Logging("info", logActionName, requestUUID, $"Table: {tableName}, Entity: {JsonSerializer.Serialize(entity, new JsonSerializerOptions { ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.IgnoreCycles, WriteIndented = true })}");
        _logService.Logging("info", logActionName, requestUUID, "End");

        if (!_beginTransaction)
        {
            await _dbContext.SaveChangesAsync();

            return entity.GetType().GetProperty("Id")?.GetValue(entity) as int?;
        }
        else
        {
            return null;
        }
    }

    /// <summary>
    /// 批量新增資料
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="entities"></param>
    /// <param name="callMethodName"></param>
    /// <code>
    /// var newUsers = new List<User>
    /// {
    ///     new User { Name = "Alice", Email = "<EMAIL>", Age = 30 },
    ///     new User { Name = "Bob", Email = "<EMAIL>", Age = 25 },
    ///     new User { Name = "Charlie", Email = "<EMAIL>", Age = 35 }
    /// };
    /// 
    /// int createdCount = await _dataAccessService.CreateRangeAsync(newUsers);
    /// </code>
    /// <returns></returns>
    public async Task<int> CreateRangeAsync<T>(IEnumerable<T> entities, [CallerMemberName] string callMethodName = null) where T : class
    {
        ArgumentNullException.ThrowIfNull(entities);

        string? requestUUID = _requestContextService.GetRequestUID();

        string logActionName = $"{callMethodName ?? "Unknown"}/{GetType().Name}/{nameof(CreateRangeAsync)}";

        _logService.Logging("info", logActionName, requestUUID, "Start");

        var tableName = _dbContext.Model.FindEntityType(typeof(T)).GetTableName();

        _dbContext.Set<T>().AddRange(entities);

        _logService.Logging("info", logActionName, requestUUID, $"Table: {tableName}, Entities Count: {entities.Count()}");

        if (!_beginTransaction)
        {
            int result = await _dbContext.SaveChangesAsync();
            _logService.Logging("info", logActionName, requestUUID, $"Created {result} entities");
            _logService.Logging("info", logActionName, requestUUID, "End");
            return result;
        }
        else
        {
            _logService.Logging("info", logActionName, requestUUID, "Transaction in progress, changes not saved");
            _logService.Logging("info", logActionName, requestUUID, "End");
            return 0;
        }
    }

    /// <summary>
    /// 批量更新資料
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="entities"></param>
    /// <param name="callMethodName"></param>
    /// <param name="updateProperties"></param>
    /// <code>
    /// //假設我們已經有了一些用戶,並要更新他們的年齡
    /// var usersToUpdate = await _dataAccessService.Fetch<User>(u => u.Age < 30).ToListAsync();
    ///
    /// foreach (var user in usersToUpdate)
    /// {
    ///     user.Age += 1;
    /// }
    /// 
    /// int updatedCount = await _dataAccessService.UpdateRangeAsync(usersToUpdate, updateProperties: u => u.Age);
    /// 
    /// // 假設我們要更新特定電子郵件域名用戶的姓名和年齡
    /// var usersToUpdate = await _dataAccessService.Fetch<User>(u => u.Email.EndsWith("@example.com")).ToListAsync();
    /// 
    /// foreach (var user in usersToUpdate)
    /// {
    ///     user.Name = $"Updated_{user.Name}";
    /// user.Age += 5;
    /// }
    /// 
    /// int updatedCount = await _dataAccessService.UpdateRangeAsync(usersToUpdate,
    /// updateProperties: new Expression<Func<User, object>>[]
    /// {
    ///     u => u.Name,
    ///     u => u.Age
    /// });
    /// </code>
    /// <returns></returns>
    public async Task<int> UpdateRangeAsync<T>(IEnumerable<T> entities, [CallerMemberName] string callMethodName = null, params Expression<Func<T, object>>[] updateProperties) where T : class
    {
        ArgumentNullException.ThrowIfNull(entities);

        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = $"{callMethodName ?? "Unknown"}/{GetType().Name}/{nameof(UpdateRangeAsync)}";

        _logService.Logging("info", logActionName, requestUUID, "Start");

        var tableName = _dbContext.Model.FindEntityType(typeof(T)).GetTableName();

        var propertiesToUpdate = new ConcurrentDictionary<string, bool>(
            updateProperties.Select(p => new KeyValuePair<string, bool>(
                ExtractPropertyName(p),
                true
            ))
        );

        foreach (var entity in entities)
        {
            _dbContext.Set<T>().Attach(entity);

            foreach (var property in entity.GetType().GetProperties())
            {
                bool isModified = propertiesToUpdate.ContainsKey(property.Name);
                _dbContext.Entry(entity).Property(property.Name).IsModified = isModified;
            }
        }

        _logService.Logging("info", logActionName, requestUUID, $"Table: {tableName}, Entities Count: {entities.Count()}");

        if (!_beginTransaction)
        {
            int result = await _dbContext.SaveChangesAsync();
            _logService.Logging("info", logActionName, requestUUID, $"Updated {result} entities");
            _logService.Logging("info", logActionName, requestUUID, "End");
            return result;
        }
        else
        {
            _logService.Logging("info", logActionName, requestUUID, "Transaction in progress, changes not saved");
            _logService.Logging("info", logActionName, requestUUID, "End");
            return 0;
        }
    }

    /// <summary>
    /// entity 必須是已經修改後的資料，並且傳入 updatePropertyNames 告知要更新的欄位
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="entity"></param>
    /// <param name="updatePropertyNames"></param>
    /// <returns></returns>
    public async Task<int> UpdateAsync<T>(T entity, [CallerMemberName] string callMethodName = null, params Expression<Func<T, object>>[] updateProperties) where T : class
    {
        ArgumentNullException.ThrowIfNull(entity);

        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = $"{callMethodName ?? "Unknown"}/{GetType().Name}/{GetMethodName(new StackTrace().GetFrame(0)?.GetMethod())}";

        _logService.Logging("info", logActionName, requestUUID, "Start");

        var tableName = _dbContext.Model.FindEntityType(typeof(T)).GetTableName(); // 使用EF Core獲取表名

        _dbContext.Set<T>().Attach(entity);

        var propertiesToUpdate = new ConcurrentDictionary<string, bool>(
            updateProperties.Select(p => new KeyValuePair<string, bool>(
                ExtractPropertyName(p),
                true
            ))
        );

        foreach (var property in entity.GetType().GetProperties())
        {
            bool isModified = propertiesToUpdate.ContainsKey(property.Name);
            _dbContext.Entry(entity).Property(property.Name).IsModified = isModified;
        }

        _logService.Logging("info", logActionName, requestUUID, $"Table: {tableName}, Entity: {JsonSerializer.Serialize(entity, new JsonSerializerOptions { ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.IgnoreCycles, WriteIndented = true })}");
        _logService.Logging("info", logActionName, requestUUID, "End");

        if (!_beginTransaction)
        {
            return await _dbContext.SaveChangesAsync();
        }
        else
        {
            return 0;
        }
    }

    /// <summary>
    /// entity 必須是已經修改後的資料，並且傳入 updatePropertyNames 告知要更新的欄位
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="entity"></param>
    /// <param name="updatePropertyNames"></param>
    /// <returns></returns>
    public async Task<int> UpdateAsync<T>(T entity, string[] updatePropertyNames, [CallerMemberName] string callMethodName = null) where T : class
    {
        ArgumentNullException.ThrowIfNull(entity);

        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = $"{callMethodName ?? "Unknown"}/{GetType().Name}/{GetMethodName(new StackTrace().GetFrame(0)?.GetMethod())}";

        _logService.Logging("info", logActionName, requestUUID, "Start");

        var tableName = _dbContext.Model.FindEntityType(typeof(T)).GetTableName(); // 使用EF Core獲取表名

        _dbContext.Set<T>().Attach(entity);

        _logService.Logging("info", logActionName, requestUUID, $"Table: {tableName}, Entity: {JsonSerializer.Serialize(entity)}");
        _logService.Logging("info", logActionName, requestUUID, "End");

        foreach (var propertyName in updatePropertyNames)
        {
            _dbContext.Entry(entity).Property(propertyName).IsModified = true;
        }

        if (!_beginTransaction)
        {
            return await _dbContext.SaveChangesAsync();
        }
        else
        {
            return 0;
        }
    }

    /// <summary>
    /// entity 必須是已經修改後的資料，會更新所有欄位
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="entity"></param>
    /// <returns></returns>
    public async Task<int> UpdateAsync<T>(T entity, [CallerMemberName] string callMethodName = null) where T : class
    {
        ArgumentNullException.ThrowIfNull(entity);

        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = $"{callMethodName ?? "Unknown"}/{GetType().Name}/{GetMethodName(new StackTrace().GetFrame(0)?.GetMethod())}";

        _logService.Logging("info", logActionName, requestUUID, "Start");

        var tableName = _dbContext.Model.FindEntityType(typeof(T)).GetTableName(); // 使用EF Core獲取表名

        _dbContext.Set<T>().Attach(entity);

        _dbContext.Entry(entity).State = EntityState.Modified;

        _logService.Logging("info", logActionName, requestUUID, $"Table: {tableName}, Entity: {JsonSerializer.Serialize(entity)}");

        _logService.Logging("info", logActionName, requestUUID, "End");

        if (!_beginTransaction)
        {
            return await _dbContext.SaveChangesAsync();
        }
        else
        {
            return 0;
        }
    }

    /// <summary>
    /// 依指定條件更新多筆資料，且能更新多個欄位
    /// Example:
    /// 你想要更新所有名为"John Doe"的用户，将他们的IsActive属性设置为false，Name属性设置为"John Updated"，你可以这样做：
    /// <para>Expression&lt;Func&lt;User, bool&gt;&gt; predicate = u =&gt; u.Name == "John Doe";</para>
    /// <para>Expression&lt;Func&lt;User, User&gt;&gt; updateExpression = u =&gt; new User { IsActive = false, Name = "John Updated" };</para>
    /// <para>int updatedCount = await DataAccessService.UpdateAsync&lt;T&gt;(predicate, updateExpression);</para>
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="predicate"></param>
    /// <param name="updateExpression"></param>
    /// <returns></returns>
    public async Task<int> UpdateAsync<T>(Expression<Func<T, bool>> predicate, Dictionary<string, object> updateValues, [CallerMemberName] string callMethodName = null) where T : class
    {
        // TODO : DepartmentController.UpdateDepartment 有使用到這個方法，但無法正常更新資料
        ArgumentNullException.ThrowIfNull(predicate);
        ArgumentNullException.ThrowIfNull(updateValues);

        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = $"{callMethodName ?? "Unknown"}/{GetType().Name}/{GetMethodName(new StackTrace().GetFrame(0)?.GetMethod())}";

        _logService.Logging("info", logActionName, requestUUID, "Start");

        var tableName = _dbContext.Model.FindEntityType(typeof(T)).GetTableName(); // 使用EF Core獲取表名

        var entities = _dbContext.Set<T>().Where(predicate);

        foreach (var entity in entities)
        {
            foreach (var updateValue in updateValues)
            {
                entity.GetType().GetProperty(updateValue.Key).SetValue(entity, updateValue.Value);
            }

            _dbContext.Entry(entity).State = EntityState.Modified;

            _logService.Logging("info", logActionName, requestUUID, $"Table: {tableName}, Entity: {JsonSerializer.Serialize(entity)}");
        }

        _logService.Logging("info", logActionName, requestUUID, "End");

        if (!_beginTransaction)
        {
            return await _dbContext.SaveChangesAsync();
        }
        else
        {
            return 0;
        }
    }

    /// <summary>
    /// entities 必須是已經修改後的資料(多筆)，會更新所有欄位
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="entities"></param>
    /// <returns></returns>
    public async Task<int> UpdateAsync<T>(IEnumerable<T> entities, [CallerMemberName] string callMethodName = null) where T : class
    {
        ArgumentNullException.ThrowIfNull(entities);

        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = $"{callMethodName ?? "Unknown"}/{GetType().Name}/{GetMethodName(new StackTrace().GetFrame(0)?.GetMethod())}";

        _logService.Logging("info", logActionName, requestUUID, "Start");

        foreach (var entity in entities)
        {
            var tableName = _dbContext.Model.FindEntityType(typeof(T)).GetTableName(); // 使用EF Core獲取表名

            _dbContext.Set<T>().Attach(entity);

            _dbContext.Entry(entity).State = EntityState.Modified;

            _logService.Logging("info", logActionName, requestUUID, $"Table: {tableName}, Entity: {JsonSerializer.Serialize(entity)}");
        }

        _logService.Logging("info", logActionName, requestUUID, "End");

        if (!_beginTransaction)
        {
            return await _dbContext.SaveChangesAsync();
        }
        else
        {
            return 0;
        }
    }

    private object GetPrimaryKeyValue<T>(T entity)
    {
        var keyProperties = _dbContext.Model.FindEntityType(typeof(T)).FindPrimaryKey().Properties;
        if (keyProperties.Count == 0) return null;

        var keyProperty = keyProperties[0];
        return keyProperty.PropertyInfo.GetValue(entity);
    }

    public async Task<CompareResult<T>> UpdateAsync<T>(List<T> entities, [CallerMemberName] string callMethodName = null) where T : class
    {
        ArgumentNullException.ThrowIfNull(entities);

        var updateIds = entities.Select(e =>
        {
            var propertyInfo = e.GetType().GetProperties().FirstOrDefault(p => p.Name.EndsWith("Id"));
            if (propertyInfo == null) return 0;
            return (int)propertyInfo.GetValue(e);
        }).ToList();

        var primaryKeyName = _dbContext.Model.FindEntityType(typeof(T)).FindPrimaryKey().Properties[0].Name;
        var tableName = _dbContext.Model.FindEntityType(typeof(T)).GetTableName();

        var updateIdParameters = updateIds.Select((id, index) => new { Id = id, Index = index }).ToList();

        var sourceEntities = _dbContext.Set<T>()
            .FromSqlRaw($"SELECT * FROM \"{tableName}\" WHERE \"{primaryKeyName}\" IN ({string.Join(",", updateIds)})");

        var navigationProperties = typeof(T).GetProperties()
            .Where(p => p.PropertyType != typeof(string) && p.PropertyType.FullName.StartsWith("System.Collections"));// && p.PropertyType.IsClass

        foreach (var property in navigationProperties)
        {
            sourceEntities = sourceEntities.Include(property.Name);
        }

        var sourceList = sourceEntities.ToList();

        var compareResult = CompareList(sourceList, entities);

        // 新增對象
        foreach (var obj in compareResult.AddedObjects)
        {
            obj.GetType().GetProperty("CreateDate")?.SetValue(obj, DateTime.Now);
            _dbContext.Set<T>().Add(obj);
        }

        // 刪除對象
        foreach (var obj in compareResult.DeletedObjects)
        {
            _dbContext.Set<T>().Remove(obj);
        }

        // 修改對象
        foreach (var obj in compareResult.ModifiedObjects)
        {
            obj.GetType().GetProperty("ModifyDate")?.SetValue(obj, DateTime.Now);
            _dbContext.Entry(obj).State = EntityState.Modified;
        }

        // 新增子對象
        foreach (var childObj in compareResult.AddedChildObjects)
        {
            childObj.GetType().GetProperty("CreateDate")?.SetValue(childObj, DateTime.Now);
            _dbContext.Entry(childObj).State = EntityState.Added;
        }

        // 刪除子對象
        foreach (var childObj in compareResult.DeletedChildObjects)
        {
            _dbContext.Entry(childObj).State = EntityState.Deleted;
        }

        // 修改子對象
        foreach (var childObj in compareResult.ModifiedChildObjects)
        {
            childObj.GetType().GetProperty("ModifyDate")?.SetValue(childObj, DateTime.Now);
            _dbContext.Entry(childObj).State = EntityState.Modified;
        }

        compareResult.Count = _beginTransaction ? 0 : await _dbContext.SaveChangesAsync();

        return compareResult;
    }
    /// <summary>
    /// 獲取當前DbContext Transaction中的所有新增資料數量
    /// </summary>
    /// <returns></returns>
    public int GetPendingAdds() => _dbContext.ChangeTracker.Entries().Count(e => e.State == EntityState.Added);
    /// <summary>
    /// 獲取當前DbContext Transaction中的所有更新資料數量
    /// </summary>
    /// <returns></returns>
    public int GetPendingModifies() => _dbContext.ChangeTracker.Entries().Count(e => e.State == EntityState.Modified);
    /// <summary>
    /// 獲取當前DbContext Transaction中的所有刪除資料數量
    /// </summary>
    /// <returns></returns>
    public int GetPendingDeletes() => _dbContext.ChangeTracker.Entries().Count(e => e.State == EntityState.Deleted);
    public async Task<int> DeleteAsync<T>(Expression<Func<T, bool>> predicate, [CallerMemberName] string callMethodName = null) where T : class
    {
        ArgumentNullException.ThrowIfNull(predicate);

        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = $"{callMethodName ?? "Unknown"}/{GetType().Name}/{GetMethodName(new StackTrace().GetFrame(0)?.GetMethod())}";

        _logService.Logging("info", logActionName, requestUUID, "Start");

        var tableName = _dbContext.Model.FindEntityType(typeof(T)).GetTableName(); // 使用EF Core獲取表名

        var entities = _dbContext.Set<T>().Where(predicate);

        int res = await entities.ExecuteDeleteAsync();

        _logService.Logging("info", logActionName, requestUUID, $"Table: {tableName}, Entity: {JsonSerializer.Serialize(entities, new JsonSerializerOptions { ReferenceHandler = System.Text.Json.Serialization.ReferenceHandler.IgnoreCycles, WriteIndented = true })}");

        _logService.Logging("info", logActionName, requestUUID, "End");

        if (!_beginTransaction)
        {
            await _dbContext.SaveChangesAsync();
        }
        return res;
    }
    
    public async Task<int> DeleteAsync<T>(T entityOrEntities, [CallerMemberName] string callMethodName = null) where T : class
    {
        string logActionName = $"DataAccess.{callMethodName}";
        string? requestUUID = _requestContextService.GetRequestUID();
        _logService.Logging("info", logActionName, requestUUID, "Start");

        Type entityType = typeof(T);
        if (entityOrEntities.GetType().Name == "EntityQueryable`1")
        {
            // 如果是 EntityQueryable<T>，獲取其元素類型
            entityType = entityOrEntities.GetType().GetGenericArguments()[0];
        }

        var dbSetMethod = typeof(DbContext).GetMethod("Set", Type.EmptyTypes).MakeGenericMethod(entityType);
        var dbSet = dbSetMethod.Invoke(_dbContext, null);

        var entityTypeObj = _dbContext.Model.FindEntityType(entityType);

        if (entityType == null)
        {
            _logService.Logging("error", logActionName, requestUUID, $"Entity type {typeof(T).Name} not found in the DbContext model.");
            _logService.Logging("info", logActionName, requestUUID, "End");
            return 0;
        }

        var tableName = entityTypeObj.GetTableName();

        try
        {
            if (entityOrEntities is IEnumerable enumerable)
            {
                var removeRangeMethod = dbSet.GetType().GetMethod("RemoveRange", [typeof(IEnumerable<>).MakeGenericType(entityType) ]);
                removeRangeMethod.Invoke(dbSet, [ enumerable ]);
            }
            else if (entityOrEntities.GetType().Name == "EntityQueryable`1")
            {
                var toListAsyncMethod = entityOrEntities.GetType().GetMethod("ToListAsync");
                var entitiesToDelete = await (Task<IList>)toListAsyncMethod.Invoke(entityOrEntities, new object[] { CancellationToken.None });
                var removeRangeMethod = dbSet.GetType().GetMethod("RemoveRange", [typeof(IEnumerable<>).MakeGenericType(entityType)]);
                removeRangeMethod.Invoke(dbSet, [entitiesToDelete]);
            }
            else
            {
                var removeMethod = dbSet.GetType().GetMethod("Remove");
                removeMethod.Invoke(dbSet, [entityOrEntities]);
            }

            int affectedRows = _beginTransaction ? 0 : await _dbContext.SaveChangesAsync();

            _logService.Logging("info", logActionName, requestUUID, $"Deleted {affectedRows} rows from {tableName}");
            _logService.Logging("info", logActionName, requestUUID, "End");

            return affectedRows;
        }
        catch (Exception ex)
        {
            _logService.Logging("error", logActionName, requestUUID, $"Error deleting from {tableName}: {ex.Message}");
            _logService.Logging("info", logActionName, requestUUID, "End");
            throw;
        }
    }

    public void BeginTransaction()
    {
        _beginTransaction = true;
        _unitOfWork?.BeginTransactionAsync();
    }

    public async Task<int> CommitAsync()
    {
        int count;
        if (_beginTransaction && _unitOfWork != null)
        {
            //count = await _dbContext.SaveChangesAsync();
            count = await _unitOfWork.CommitAsync();
        }
        else
        {
            throw new InvalidOperationException("The transaction has not been started.");
        }

        return count;
    }

    public async Task RollbackAsync()
    {
        if (_beginTransaction && _unitOfWork != null)
        {
            await _unitOfWork.RollbackAsync();
        }
    }

    private CompareResult<T> CompareList<T>(List<T> list1, List<T> list2) where T : class
    {
        var comparer = new CompareLogic();
        var result = new CompareResult<T>
        {
            AddedObjects = new List<T>(),
            DeletedObjects = new List<T>(),
            ModifiedObjects = new List<T>(),
            AddedChildObjects = new List<object>(),
            DeletedChildObjects = new List<object>(),
            ModifiedChildObjects = new List<object>()
        };

        var primaryKeyName = _dbContext.Model.FindEntityType(typeof(T)).FindPrimaryKey().Properties[0].Name;

        // 找出list2中新增的對象
        result.AddedObjects = list2.Where(obj2 => !list1.Any(obj1 => GetPropertyValueToInt(obj1, primaryKeyName) == GetPropertyValueToInt(obj2, primaryKeyName))).ToList();

        // 找出list2中刪除的對象
        result.DeletedObjects = list1.Where(obj1 => !list2.Any(obj2 => GetPropertyValueToInt(obj1, primaryKeyName) == GetPropertyValueToInt(obj2, primaryKeyName))).ToList();

        // 找出相同Id的對象
        var commonObjects = list1.Where(obj1 => list2.Any(obj2 => GetPropertyValueToInt(obj1, primaryKeyName) == GetPropertyValueToInt(obj2, primaryKeyName))).ToList();

        foreach (var obj1 in commonObjects)
        {
            var obj2 = list2.First(obj => GetPropertyValueToInt(obj, primaryKeyName) == GetPropertyValueToInt(obj1, primaryKeyName));

            // 找出修改的對象
            var compareResult = comparer.Compare(obj1, obj2);
            if (!compareResult.AreEqual)
            {
                result.ModifiedObjects.Add(obj2);
            }

            // 遍歷對象的屬性
            foreach (var propertyInfo in obj1.GetType().GetProperties())
            {
                var propertyValue1 = propertyInfo.GetValue(obj1);
                var propertyValue2 = propertyInfo.GetValue(obj2);

                if (propertyValue1 is IEnumerable && !(propertyValue1 is string))
                {
                    var childList1 = ((IEnumerable)propertyValue1).Cast<object>().ToList();
                    var childList2 = ((IEnumerable)propertyValue2).Cast<object>().ToList();

                    if (childList1.FirstOrDefault() == null || childList2.FirstOrDefault() == null) continue;

                    var childPkName = _dbContext.Model.FindEntityType(childList1.First().GetType()).FindPrimaryKey().Properties[0].Name;

                    // 找出新增的子對象
                    var addedChildObjects = childList2.Where(child2 => !childList1.Any(child1 => GetPropertyValueToInt(child1, childPkName) == GetPropertyValueToInt(child2, childPkName))).ToList();
                    result.AddedChildObjects.AddRange(addedChildObjects);

                    // 找出刪除的子對象
                    var deletedChildObjects = childList1.Where(child1 => !childList2.Any(child2 => GetPropertyValueToInt(child1, childPkName) == GetPropertyValueToInt(child2, childPkName))).ToList();
                    result.DeletedChildObjects.AddRange(deletedChildObjects);

                    // 找出修改的子對象
                    var commonChildObjects = childList1.Where(child1 => childList2.Any(child2 => GetPropertyValueToInt(child1, childPkName) == GetPropertyValueToInt(child2, childPkName))).ToList();
                    foreach (var childObj1 in commonChildObjects)
                    {
                        var childObj2 = childList2.First(child => GetPropertyValueToInt(child, childPkName) == GetPropertyValueToInt(childObj1, childPkName));
                        var childResult = comparer.Compare(childObj1, childObj2);
                        if (!childResult.AreEqual)
                        {
                            if (childResult.Differences.Count == 1 && childResult.Differences[0].PropertyName == obj1.GetType().Name) continue;

                            result.ModifiedChildObjects.Add(childObj2);
                        }
                    }
                }
            }
        }

        return result;
    }

    public string GetCompareResultLog<T>(CompareResult<T> compareResult)
    {
        var logBuilder = new StringBuilder();

        logBuilder.AppendLine("Compare Result:");
        logBuilder.AppendLine("Added Objects:");
        foreach (var obj in compareResult.AddedObjects)
        {
            logBuilder.AppendLine($"- {GetObjectDetails(obj)}");
        }

        logBuilder.AppendLine("Deleted Objects:");
        foreach (var obj in compareResult.DeletedObjects)
        {
            logBuilder.AppendLine($"- {GetObjectDetails(obj)}");
        }

        logBuilder.AppendLine("Modified Objects:");
        foreach (var obj in compareResult.ModifiedObjects)
        {
            var originalObj = compareResult.DeletedObjects.FirstOrDefault(o => GetPropertyValue(o, "Id") == GetPropertyValue(obj, "Id"));
            if (originalObj != null)
            {
                var modifiedProperties = GetModifiedProperties(originalObj, obj);
                logBuilder.AppendLine($"- {GetObjectDetails(obj, modifiedProperties)}");
            }
            else
            {
                logBuilder.AppendLine($"- {GetObjectDetails(obj)}");
            }
        }

        logBuilder.AppendLine("Added Child Objects:");
        foreach (var childObj in compareResult.AddedChildObjects)
        {
            logBuilder.AppendLine($"- {GetObjectDetails(childObj)}");
        }

        logBuilder.AppendLine("Deleted Child Objects:");
        foreach (var childObj in compareResult.DeletedChildObjects)
        {
            logBuilder.AppendLine($"- {GetObjectDetails(childObj)}");
        }

        logBuilder.AppendLine("Modified Child Objects:");
        foreach (var childObj in compareResult.ModifiedChildObjects)
        {
            var originalChildObj = compareResult.DeletedChildObjects.FirstOrDefault(o => GetPropertyValue(o, "Id") == GetPropertyValue(childObj, "Id"));
            if (originalChildObj != null)
            {
                var modifiedProperties = GetModifiedProperties(originalChildObj, childObj);
                logBuilder.AppendLine($"- {GetObjectDetails(childObj, modifiedProperties)}");
            }
            else
            {
                logBuilder.AppendLine($"- {GetObjectDetails(childObj)}");
            }
        }

        return logBuilder.ToString();
    }

    private static string GetObjectDetails<T>(T obj, Dictionary<string, string> modifiedProperties = null)
    {
        var propertyInfos = typeof(T).GetProperties();
        var detailsBuilder = new StringBuilder();

        foreach (var propertyInfo in propertyInfos)
        {
            var propertyValue = propertyInfo.GetValue(obj);
            if (modifiedProperties != null && modifiedProperties.ContainsKey(propertyInfo.Name))
            {
                detailsBuilder.Append($"{propertyInfo.Name}: {modifiedProperties[propertyInfo.Name]}, ");
            }
            else
            {
                detailsBuilder.Append($"{propertyInfo.Name}: {propertyValue}, ");
            }
        }

        return detailsBuilder.ToString().TrimEnd(',', ' ');
    }

    private static Dictionary<string, string> GetModifiedProperties<T>(T originalObj, T modifiedObj)
    {
        var modifiedProperties = new Dictionary<string, string>();
        var propertyInfos = typeof(T).GetProperties();

        foreach (var propertyInfo in propertyInfos)
        {
            var originalValue = propertyInfo.GetValue(originalObj);
            var modifiedValue = propertyInfo.GetValue(modifiedObj);

            if (originalValue != null && !originalValue.Equals(modifiedValue))
            {
                modifiedProperties[propertyInfo.Name] = $"{originalValue}->{modifiedValue}";
            }
        }

        return modifiedProperties;
    }

    private static object GetPropertyValue(object obj, string propertyName)
    {
        var propertyInfo = obj.GetType().GetProperty(propertyName);
        if (propertyInfo == null) return null;
        var propertyValue = propertyInfo.GetValue(obj);
        return propertyValue;
    }

    private static int GetPropertyValueToInt(object obj, string propertyName)
    {
        object data = GetPropertyValue(obj, propertyName);
        int result = data == null ? 0 : (int)data;

        return result;
    }
}
