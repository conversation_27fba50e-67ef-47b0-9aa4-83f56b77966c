using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Web.Models.Controller.EventNotifySetting;
using Web.Models.Controller;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;
using Web.Validation;
using Web.Constant;

namespace Web.Controller;

/// <summary>
/// 事件通知設定
/// </summary>
[Route("[controller]")]
[Authorize]
public class EventNotifySettingController(IDataAccessService dataAccessService,
                                   ICredentialService credentialService,
                                   IRequestContextService requestContextService,
                                   ILogService logService) : BaseController(dataAccessService, credentialService, requestContextService, logService)
{
    /// <summary>
    /// 新增事件通知設定檢核
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    [HttpPost("settings/validate")]
    [RequestParamListNotNullOrEmpty]
    public IActionResult ValidateEventNotifySetting([FromBody] List<CreateEventNotifySetting> paramList)
    {
        // 進到 controller 代表驗證通過，回傳空的錯誤列表
        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new List<ReturnError>()
        });
    }

    /// <summary>
    /// 新增事件通知設定
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    [HttpPost("settings")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateEventNotifySetting([FromBody] List<CreateEventNotifySetting> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        string appCode = _user.AppCode;
        _logService.Logging("info", logActionName, requestUUID, "EventNotifySetting Data Validated, start to append.");

        List<int> eventNotifySettingIdList = [];

        foreach (CreateEventNotifySetting ens in paramList)
        {
            EventNotifySetting eventNotifySetting = new EventNotifySetting
            {
                AppCode = _user.AppCode,
                AreaCode = ens.AreaCode,
                ServiceCode = ens.ServiceCode,
                SddResource = ens.SddResource,
                Description = ens.Description,
                BuildingCode = ens.BuildingCode,
                PlaneCode = ens.PlaneCode,
                DeptCode = ens.DeptCode,
                ObjectType = ens.ObjectType,
                GroupCode = ens.GroupCode,
                Enable = ens.Enable == "Y",
                EnableSchedule = ens.EnableSchedule == "Y",
                PositionNotify = ens.PositionNotify == "Y",
                TraceTime = ens.TraceTime,
                ENSMessage = ens.ENSMessage,
                DisplayMessage1 = ens.DisplayMessage1,
                DisplayMessage2 = ens.DisplayMessage2,
                EmailSubject = ens.EmailSubject,
                EmailMessage = ens.EmailMessage,
                NotifyMessage = ens.NotifyMessage,
                SMSMessage = ens.SMSMessage,
                CreateDate = DateTime.Now,
                CreateUserAccount = _user.Account,
                ModifyDate = DateTime.Now
            };

            // 新增 EventNotifySetting
            int? result = await _dataAccessService.CreateAsync<EventNotifySetting>(eventNotifySetting);

            // 如果 result 等於 null 就丟出例外
            if (result == null)
            {
                throw new Exception("Create EventNotifySetting failed");
            }

            int eventNotifySettingId = result.Value;
            eventNotifySettingIdList.Add(eventNotifySettingId);

            _dataAccessService.BeginTransaction();

            // 取得新增的 EventNotifySetting ID

            // 處理排程列表
            if (ens.ScheduleList != null && ens.ScheduleList.Count > 0)
            {
                foreach (var schedule in ens.ScheduleList)
                {
                    EventNotifySchedule eventNotifySchedule = new EventNotifySchedule
                    {
                        AppCode = _user.AppCode,
                        AreaCode = ens.AreaCode,
                        EventNotifySettingId = eventNotifySettingId,
                        Weekly = schedule.Weekly,
                        StartTime = DateTime.Parse(schedule.StartTime),
                        EndTime = DateTime.Parse(schedule.EndTime),
                        CreateDate = DateTime.Now,
                        CreateUserAccount = _user.Account
                    };

                    await _dataAccessService.CreateAsync<EventNotifySchedule>(eventNotifySchedule);
                }
            }

            // 處理聯絡人列表
            if (ens.ContactList != null && ens.ContactList.Count > 0)
            {
                foreach (var contact in ens.ContactList)
                {
                    EventNotifyContact eventNotifyContact = new EventNotifyContact
                    {
                        AppCode = _user.AppCode,
                        AreaCode = ens.AreaCode,
                        EventNotifySettingId = eventNotifySettingId,
                        NotifyType = contact.NotifyType,
                        Source = contact.Source,
                        ContactCode = contact.ContactCode,
                        CreateDate = DateTime.Now,
                        CreateUserAccount = _user.Account
                    };

                    await _dataAccessService.CreateAsync<EventNotifyContact>(eventNotifyContact);
                }
            }

            // 處理第三方通知列表
            if (ens.ThirdList != null && ens.ThirdList.Count > 0)
            {
                foreach (var third in ens.ThirdList)
                {
                    EventNotifyThird eventNotifyThird = new EventNotifyThird
                    {
                        AppCode = _user.AppCode,
                        AreaCode = ens.AreaCode,
                        EventNotifySettingId = eventNotifySettingId,
                        NotifyType = third.NotifyType,
                        ThirdCode = third.ThirdCode,
                        CreateDate = DateTime.Now,
                        CreateUserAccount = _user.Account
                    };

                    await _dataAccessService.CreateAsync<EventNotifyThird>(eventNotifyThird);
                }
            }

            // 處理通知標頭列表
            if (ens.HeaderList != null && ens.HeaderList.Count > 0)
            {
                foreach (var header in ens.HeaderList)
                {
                    EventNotifyHeader eventNotifyHeader = new EventNotifyHeader
                    {
                        AppCode = _user.AppCode,
                        AreaCode = ens.AreaCode,
                        EventNotifySettingId = eventNotifySettingId,
                        NotifyCode = header.NotifyCode,
                        CreateDate = DateTime.Now,
                        CreateUserAccount = _user.Account
                    };

                    await _dataAccessService.CreateAsync<EventNotifyHeader>(eventNotifyHeader);
                }
            }

            await _dataAccessService.CommitAsync();
        }

        _logService.Logging("info", logActionName, requestUUID, "EventNotifySetting Data append done.");

        returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status201Created,
            result = true,
            data = eventNotifySettingIdList
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    /// <summary>
    /// 更新事件通知設定
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    [HttpPatch("settings")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateEventNotifySetting([FromBody] List<UpdateEventNotifySetting> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        _dataAccessService.BeginTransaction();

        foreach (UpdateEventNotifySetting param in paramList)
        {
            EventNotifySetting eventNotifySetting = _dataAccessService.Fetch<EventNotifySetting>(e => e.AppCode == _user.AppCode && e.Id == param.Id).AsTracking().First();

            List<string> updateField = new List<string>();

            if (param.AreaCode != null)
            {
                updateField.Add("AreaCode");
                eventNotifySetting.AreaCode = param.AreaCode;
            }

            if (param.BuildingCode != null)
            {
                updateField.Add("BuildingCode");
                eventNotifySetting.BuildingCode = param.BuildingCode;
            }

            if (param.PlaneCode != null)
            {
                updateField.Add("PlaneCode");
                eventNotifySetting.PlaneCode = param.PlaneCode;
            }

            if (param.DeptCode != null)
            {
                updateField.Add("DeptCode");
                eventNotifySetting.DeptCode = param.DeptCode;
            }

            if (param.ObjectType != null)
            {
                updateField.Add("ObjectType");
                eventNotifySetting.ObjectType = param.ObjectType;
            }

            if (param.GroupCode != null)
            {
                updateField.Add("GroupCode");
                eventNotifySetting.GroupCode = param.GroupCode;
            }

            if (param.Description != null)
            {
                updateField.Add("Description");
                eventNotifySetting.Description = param.Description;
            }

            if (param.Enable != null)
            {
                updateField.Add("Enable");
                eventNotifySetting.Enable = param.Enable == "Y";
            }

            if (param.EnableSchedule != null)
            {
                updateField.Add("EnableSchedule");
                eventNotifySetting.EnableSchedule = param.EnableSchedule == "Y";
            }

            if (param.PositionNotify != null)
            {
                updateField.Add("PositionNotify");
                eventNotifySetting.PositionNotify = param.PositionNotify == "Y";
            }

            if (param.TraceTime != null)
            {
                updateField.Add("TraceTime");
                eventNotifySetting.TraceTime = param.TraceTime;
            }

            if (param.ENSMessage != null)
            {
                updateField.Add("ENSMessage");
                eventNotifySetting.ENSMessage = param.ENSMessage;
            }

            if (param.DisplayMessage1 != null)
            {
                updateField.Add("DisplayMessage1");
                eventNotifySetting.DisplayMessage1 = param.DisplayMessage1;
            }

            if (param.DisplayMessage2 != null)
            {
                updateField.Add("DisplayMessage2");
                eventNotifySetting.DisplayMessage2 = param.DisplayMessage2;
            }

            if (param.EmailSubject != null)
            {
                updateField.Add("EmailSubject");
                eventNotifySetting.EmailSubject = param.EmailSubject;
            }

            if (param.EmailMessage != null)
            {
                updateField.Add("EmailMessage");
                eventNotifySetting.EmailMessage = param.EmailMessage;
            }

            if (param.NotifyMessage != null)
            {
                updateField.Add("NotifyMessage");
                eventNotifySetting.NotifyMessage = param.NotifyMessage;
            }

            if (param.SMSMessage != null)
            {
                updateField.Add("SMSMessage");
                eventNotifySetting.SMSMessage = param.SMSMessage;
            }

            updateField.Add("ModifyDate");
            updateField.Add("ModifyUserAccount");
            eventNotifySetting.ModifyDate = DateTime.Now;
            eventNotifySetting.ModifyUserAccount = _user.Account;

            await _dataAccessService.UpdateAsync<EventNotifySetting>(eventNotifySetting, updateField.ToArray());

            // 處理排程列表
            if (param.ScheduleList != null)
            {
                // 刪除原有的排程
                await _dataAccessService.DeleteAsync<EventNotifySchedule>(e => e.AppCode == _user.AppCode && e.EventNotifySettingId == param.Id);

                // 新增更新的排程
                foreach (var schedule in param.ScheduleList)
                {
                    EventNotifySchedule eventNotifySchedule = new EventNotifySchedule
                    {
                        AppCode = _user.AppCode,
                        AreaCode = eventNotifySetting.AreaCode,
                        EventNotifySettingId = param.Id,
                        Weekly = schedule.Weekly,
                        StartTime = DateTime.Parse(schedule.StartTime),
                        EndTime = DateTime.Parse(schedule.EndTime),
                        CreateDate = DateTime.Now,
                        CreateUserAccount = _user.Account
                    };

                    await _dataAccessService.CreateAsync<EventNotifySchedule>(eventNotifySchedule);
                }
            }

            // 處理聯絡人列表
            if (param.ContactList != null)
            {
                // 刪除原有的聯絡人
                await _dataAccessService.DeleteAsync<EventNotifyContact>(e => e.AppCode == _user.AppCode && e.EventNotifySettingId == param.Id);

                // 新增更新的聯絡人
                foreach (var contact in param.ContactList)
                {
                    EventNotifyContact eventNotifyContact = new EventNotifyContact
                    {
                        AppCode = _user.AppCode,
                        AreaCode = eventNotifySetting.AreaCode,
                        EventNotifySettingId = param.Id,
                        NotifyType = contact.NotifyType,
                        Source = contact.Source,
                        ContactCode = contact.ContactCode,
                        CreateDate = DateTime.Now,
                        CreateUserAccount = _user.Account
                    };

                    await _dataAccessService.CreateAsync<EventNotifyContact>(eventNotifyContact);
                }
            }

            // 處理第三方通知列表
            if (param.ThirdList != null)
            {
                // 刪除原有的第三方通知
                await _dataAccessService.DeleteAsync<EventNotifyThird>(e => e.AppCode == _user.AppCode && e.EventNotifySettingId == param.Id);

                // 新增更新的第三方通知
                foreach (var third in param.ThirdList)
                {
                    EventNotifyThird eventNotifyThird = new EventNotifyThird
                    {
                        AppCode = _user.AppCode,
                        AreaCode = eventNotifySetting.AreaCode,
                        EventNotifySettingId = param.Id,
                        NotifyType = third.NotifyType,
                        ThirdCode = third.ThirdCode,
                        CreateDate = DateTime.Now,
                        CreateUserAccount = _user.Account
                    };

                    await _dataAccessService.CreateAsync<EventNotifyThird>(eventNotifyThird);
                }
            }

            // 處理通知標頭列表
            if (param.HeaderList != null)
            {
                // 刪除原有的通知標頭
                await _dataAccessService.DeleteAsync<EventNotifyHeader>(e => e.AppCode == _user.AppCode && e.EventNotifySettingId == param.Id);

                // 新增更新的通知標頭
                foreach (var header in param.HeaderList)
                {
                    EventNotifyHeader eventNotifyHeader = new EventNotifyHeader
                    {
                        AppCode = _user.AppCode,
                        AreaCode = eventNotifySetting.AreaCode,
                        EventNotifySettingId = param.Id,
                        NotifyCode = header.NotifyCode,
                        CreateDate = DateTime.Now,
                        CreateUserAccount = _user.Account
                    };

                    await _dataAccessService.CreateAsync<EventNotifyHeader>(eventNotifyHeader);
                }
            }
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "EventNotifySetting Data update done.");

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = true
        });
    }

    /// <summary>
    /// 刪除事件通知設定
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    [HttpDelete("settings")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeleteEventNotifySetting([FromBody] List<DeleteEventNotifySetting> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 開始刪除事件通知設定資料
        _logService.Logging("info", logActionName, requestUUID, "EventNotifySetting Data Validated, start to delete.");
        _dataAccessService.BeginTransaction();

        foreach (DeleteEventNotifySetting param in paramList)
        {
            // 刪除相關的排程
            await _dataAccessService.DeleteAsync<EventNotifySchedule>(e => e.AppCode == _user.AppCode && e.EventNotifySettingId == param.Id);

            // 刪除相關的聯絡人
            await _dataAccessService.DeleteAsync<EventNotifyContact>(e => e.AppCode == _user.AppCode && e.EventNotifySettingId == param.Id);

            // 刪除相關的第三方通知
            await _dataAccessService.DeleteAsync<EventNotifyThird>(e => e.AppCode == _user.AppCode && e.EventNotifySettingId == param.Id);

            // 刪除相關的通知標頭
            await _dataAccessService.DeleteAsync<EventNotifyHeader>(e => e.AppCode == _user.AppCode && e.EventNotifySettingId == param.Id);

            // 刪除事件通知設定
            await _dataAccessService.DeleteAsync<EventNotifySetting>(e => e.AppCode == _user.AppCode && e.Id == param.Id);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "EventNotifySetting Data delete done.");

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = true
        });
    }

    /// <summary>
    /// 查詢事件通知設定
    /// </summary>
    /// <param name="param"></param>
    /// <returns></returns>
    [HttpGet("settings")]
    public async Task<IActionResult> RetrieveEventNotifySetting([FromQuery] RetrieveEventNotifySetting param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 建立基本查詢
        var query = _dataAccessService.Fetch<EventNotifySetting>(e => e.AppCode == _user.AppCode);

        // 如果使用者不是管理員且不能查看所有區域，則限制只能查看自己的區域
        if (_user.IsAdmin != "A" && !_user.IsViewAllArea)
        {
            query = query.Where(e => _user.AreaCodeList.Contains(e.AreaCode));
        }

        // 加入查詢條件
        if (!string.IsNullOrEmpty(param.AreaCode))
        {
            query = query.Where(e => e.AreaCode == param.AreaCode);
        }

        if (!string.IsNullOrEmpty(param.ServiceCode))
        {
            query = query.Where(e => e.ServiceCode == param.ServiceCode || ((e.ServiceCode == "Help" || e.ServiceCode == "LongPress") && param.ServiceCode == "Help"));
        }

        if (!string.IsNullOrEmpty(param.SddResource))
        {
            query = query.Where(e => e.SddResource.Contains(param.SddResource));
        }

        if (!string.IsNullOrEmpty(param.Description))
        {
            query = query.Where(e => e.Description.Contains(param.Description));
        }

        if (!string.IsNullOrEmpty(param.BuildingCode))
        {
            query = query.Where(e => e.BuildingCode == param.BuildingCode);
        }

        if (!string.IsNullOrEmpty(param.PlaneCode))
        {
            query = query.Where(e => e.PlaneCode == param.PlaneCode);
        }

        if (!string.IsNullOrEmpty(param.DeptCode))
        {
            query = query.Where(e => e.DeptCode == param.DeptCode);
        }

        if (!string.IsNullOrEmpty(param.ObjectType))
        {
            query = query.Where(e => e.ObjectType == param.ObjectType);
        }

        if (!string.IsNullOrEmpty(param.GroupCode))
        {
            query = query.Where(e => e.GroupCode == param.GroupCode);
        }

        if (!string.IsNullOrEmpty(param.Enable))
        {
            bool enable = param.Enable == "Y";
            query = query.Where(e => e.Enable == enable);
        }

        if (!string.IsNullOrEmpty(param.EnableSchedule))
        {
            bool enableSchedule = param.EnableSchedule == "Y";
            query = query.Where(e => e.EnableSchedule == enableSchedule);
        }

        // 處理 NotifyCode 條件 - 需要 join EventNotifyHeader 表
        if (!string.IsNullOrEmpty(param.NotifyCode))
        {
            var eventNotifyHeaderIds = _dataAccessService.Fetch<EventNotifyHeader>(h =>
                h.AppCode == _user.AppCode &&
                h.NotifyCode == param.NotifyCode)
                .Select(h => h.EventNotifySettingId);

            query = query.Where(e => eventNotifyHeaderIds.Contains(e.Id));
        }

        // 處理 ThirdCode 條件 - 需要 join EventNotifyThird 表
        if (!string.IsNullOrEmpty(param.ThirdCode))
        {
            var eventNotifyThirdIds = _dataAccessService.Fetch<EventNotifyThird>(t =>
                t.AppCode == _user.AppCode &&
                t.ThirdCode == param.ThirdCode)
                .Select(t => t.EventNotifySettingId);

            query = query.Where(e => eventNotifyThirdIds.Contains(e.Id));
        }

        // 處理排序
        if (!string.IsNullOrEmpty(param.sort))
        {
            string[] sortParams = param.sort.Split(':');
            if (sortParams.Length == 2)
            {
                string sortField = sortParams[0];
                string sortDirection = sortParams[1].ToLower();

                switch (sortField)
                {
                    case "Id":
                        query = sortDirection == "desc" ? query.OrderByDescending(e => e.Id) : query.OrderBy(e => e.Id);
                        break;
                    case "AreaCode":
                        query = sortDirection == "desc" ? query.OrderByDescending(e => e.AreaCode) : query.OrderBy(e => e.AreaCode);
                        break;
                    case "ServiceCode":
                        query = sortDirection == "desc" ? query.OrderByDescending(e => e.ServiceCode) : query.OrderBy(e => e.ServiceCode);
                        break;
                    case "SddResource":
                        query = sortDirection == "desc" ? query.OrderByDescending(e => e.SddResource) : query.OrderBy(e => e.SddResource);
                        break;
                    case "Description":
                        query = sortDirection == "desc" ? query.OrderByDescending(e => e.Description) : query.OrderBy(e => e.Description);
                        break;
                    case "CreateDate":
                        query = sortDirection == "desc" ? query.OrderByDescending(e => e.CreateDate) : query.OrderBy(e => e.CreateDate);
                        break;
                    case "ModifyDate":
                        query = sortDirection == "desc" ? query.OrderByDescending(e => e.ModifyDate) : query.OrderBy(e => e.ModifyDate);
                        break;
                    default:
                        query = query.OrderByDescending(e => e.ModifyDate);
                        break;
                }
            }
            else
            {
                query = query.OrderByDescending(e => e.ModifyDate);
            }
        }
        else
        {
            query = query.OrderByDescending(e => e.ModifyDate);
        }

        // 處理分頁
        int page = 1;
        int size = 0;
        if (!string.IsNullOrEmpty(param.page) && int.TryParse(param.page, out int parsedPage))
        {
            page = parsedPage;
        }
        if (!string.IsNullOrEmpty(param.size) && int.TryParse(param.size, out int parsedSize))
        {
            size = parsedSize;
        }

        int recordTotal = await query.CountAsync();
        List<EventNotifySetting> recordList;

        if (size > 0)
        {
            recordList = await query.Skip((page - 1) * size).Take(size).ToListAsync();
        }
        else
        {
            recordList = await query.ToListAsync();
        }

        // 構建返回結果
        var result = new
        {
            recordTotal,
            recordList = recordList.Select(async e =>
            {
                // 獲取相關的排程列表
                var eventNotifyScheduleList = await _dataAccessService.Fetch<EventNotifySchedule>(s => s.AppCode == _user.AppCode && s.EventNotifySettingId == e.Id).ToListAsync();

                // 獲取相關的聯絡人列表
                var eventNotifyContactList = await _dataAccessService.Fetch<EventNotifyContact>(c => c.AppCode == _user.AppCode && c.EventNotifySettingId == e.Id).ToListAsync();
                var contactList = await _dataAccessService.Fetch<ContactDatum>(c => c.AppCode == _user.AppCode).ToListAsync();
                var contactDetailList = await _dataAccessService.Fetch<ContactDetail>(c => c.AppCode == _user.AppCode).ToListAsync();

                // 獲取相關的第三方通知列表
                var eventNotifyThirdList = await _dataAccessService.Fetch<EventNotifyThird>(t => t.AppCode == _user.AppCode && t.EventNotifySettingId == e.Id).ToListAsync();
                var notifyThirdList = await _dataAccessService.Fetch<NotifyThird>(t => t.AppCode == _user.AppCode).ToListAsync();

                // 獲取相關的通知標頭列表
                var eventNotifyHeaderList = await _dataAccessService.Fetch<EventNotifyHeader>(h => h.AppCode == _user.AppCode && h.EventNotifySettingId == e.Id).ToListAsync();
                var notifyHeaderList = await _dataAccessService.Fetch<NotifyHeader>(h => h.AppCode == _user.AppCode).ToListAsync();

                // 獲取院區名稱
                var area = await _dataAccessService.Fetch<Area>(a => a.AppCode == _user.AppCode && a.AreaCode == e.AreaCode).FirstOrDefaultAsync();
                string areaName = area?.AreaName ?? "";

                // 獲取部燜列表
                var deptList = await _dataAccessService.Fetch<Department>(d => d.AppCode == _user.AppCode && d.AreaCode == e.AreaCode).ToListAsync();
                var dept = deptList.FirstOrDefault(d => d.DeptCode == e.DeptCode);
                string deptName = dept?.DeptName ?? "";

                // 獲取棟別名稱
                var buildingList = await _dataAccessService.Fetch<Building>(b => b.AppCode == _user.AppCode).ToListAsync();
                var building = buildingList.FirstOrDefault(b => b.BuildingCode == e.BuildingCode);
                string buildingName = building?.BuildingName ?? "";

                // 獲取樓層名稱
                var planeList = await _dataAccessService.Fetch<Plane>(p => p.AppCode == _user.AppCode).ToListAsync();
                var plane = planeList.FirstOrDefault(p => p.PlaneCode == e.PlaneCode);
                string planeName = plane?.PlaneName ?? "";

                // 獲取物件類型描述
                var objectTypeList = await _dataAccessService.Fetch<ObjectType>(ot => ot.AppCode == _user.AppCode).ToListAsync();
                var objectType = objectTypeList.FirstOrDefault(ot => ot.ObjectTypeCode == e.ObjectType);
                string objectTypeDesc = objectType?.ObjectTypeDesc ?? "";

                // 獲取群組名稱
                var objectGroupList = await _dataAccessService.Fetch<ObjectGroup>(g => g.AppCode == _user.AppCode).ToListAsync();
                var objectGroup = objectGroupList.FirstOrDefault(g => g.GroupCode == e.GroupCode);
                string groupName = objectGroup?.GroupName ?? "";

                return new
                {
                    e.Id,
                    e.AreaCode,
                    AreaName = areaName,
                    e.ServiceCode,
                    e.SddResource,
                    e.Description,
                    e.BuildingCode,
                    BuildingName = buildingName,
                    e.PlaneCode,
                    PlaneName = planeName,
                    e.DeptCode,
                    DeptName = deptName,
                    e.ObjectType,
                    ObjectTypeDesc = objectTypeDesc,
                    e.GroupCode,
                    GroupName = groupName,
                    Enable = e.Enable == true ? "Y" : "N",
                    EnableSchedule = e.EnableSchedule == true ? "Y" : "N",
                    PositionNotify = e.PositionNotify == true ? "Y" : "N",
                    e.TraceTime,
                    e.ENSMessage,
                    e.DisplayMessage1,
                    e.DisplayMessage2,
                    e.EmailSubject,
                    e.EmailMessage,
                    e.NotifyMessage,
                    e.SMSMessage,
                    e.CreateUserAccount,
                    CreateDate = e.CreateDate.ToString("yyyy-MM-ddTHH:mm:ss"),
                    e.ModifyUserAccount,
                    ModifyDate = e.ModifyDate?.ToString("yyyy-MM-ddTHH:mm:ss"),
                    ScheduleList = eventNotifyScheduleList.Select(s => new
                    {
                        Weekly = s.Weekly,
                        StartTime = s.StartTime.ToString("HH:mm"),
                        EndTime = s.EndTime.ToString("HH:mm")
                    }).ToList(),
                    ContactList = (from enc in eventNotifyContactList
                                   join c in contactList on enc.ContactCode equals c.ContactCode into temp
                                   from t in temp.DefaultIfEmpty()
                                   join d in deptList on t.DeptCode equals d.DeptCode into temp3
                                   from t3 in temp3.DefaultIfEmpty()
                                   where t3 != null
                                   join cd in contactDetailList on t.ContactCode equals cd.ContactCode into temp2
                                   from t2 in temp2.DefaultIfEmpty()
                                   where t2.ContactType == enc.NotifyType
                                   select new
                                   {
                                       enc.NotifyType,
                                       enc.Source,
                                       enc.ContactCode,
                                       t2.ContactValue,
                                       t.ContactName,
                                       t.DeptCode,
                                       t3.DeptName
                                   }).ToList(),
                    ThirdList = (from ent in eventNotifyThirdList
                                 join nt in notifyThirdList on ent.ThirdCode equals nt.ThirdCode into temp
                                 from t in temp.DefaultIfEmpty()
                                 where t.NotifyType == ent.NotifyType
                                 select new
                                 {
                                     ent.NotifyType,
                                     ent.ThirdCode,
                                     t.ThirdName,
                                     t.URL_MAC
                                 }).ToList(),
                    HeaderList = (from enh in eventNotifyHeaderList
                                  join nh in notifyHeaderList on enh.NotifyCode equals nh.NotifyCode into temp
                                  from t in temp.DefaultIfEmpty()
                                  select new
                                  {
                                      enh.NotifyCode,
                                      t.Enable,
                                      t.NotifyName
                                  }).ToList()
                };
            })
            .Select(t => t.Result)
            .Where(x => (param.GroupName == null || x.GroupName.ToUpper().Contains(param.GroupName.ToUpper()))
                && (param.DeptName == null || x.DeptName.ToUpper().Contains(param.DeptName.ToUpper()))
                && (param.BuildingName == null || x.BuildingName.ToUpper().Contains(param.BuildingName.ToUpper()))
                && (param.PlaneName == null || x.PlaneName.ToUpper().Contains(param.PlaneName.ToUpper()))
                && (param.NotifyName == null || x.HeaderList.Any(h => h.NotifyName.ToUpper().Contains(param.NotifyName.ToUpper())))
                && (param.ThirdName == null || x.ThirdList.Any(t => t.ThirdName.ToUpper().Contains(param.ThirdName.ToUpper())))
                && (param.ObjectTypeDesc == null || x.ObjectTypeDesc.ToUpper().Contains(param.ObjectTypeDesc.ToUpper()))).ToList()
        };

        _logService.Logging("info", logActionName, requestUUID, "EventNotifySetting Data retrieve done.");

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = result
        });
    }
}

