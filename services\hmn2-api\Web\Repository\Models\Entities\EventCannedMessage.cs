﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class EventCannedMessage
{
    public int Id { get; set; }

    public string AppCode { get; set; } = null!;

    public long TaskId { get; set; }

    public string CannedCode { get; set; } = null!;

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }
}
