﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Web.Models.AppSettings;
using Web.Models.Controller;
using Web.Models.Service;
using Web.Models.Service.Configuration;
using Web.Services.Interfaces;
using Web.Validation;

public class ValidationError
{
    public string NestObject { get; set; }
    public string Index { get; set; }
    public string PropertyName { get; set; }
    public object PropertyValue { get; set; }
    public string ErrorMessage { get; set; }
}

[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public class ValidateModelActionFilter : Attribute, IActionFilter
{
    private readonly UserResult _user;
    private readonly AppInfo _appInfo;

    public ValidateModelActionFilter(IOptions<AppInfo> appInfo, ICredentialService credentialService)
    {
        _appInfo = appInfo.Value;
        _user = credentialService.UserResult;
    }

    public void OnActionExecuting(ActionExecutingContext context)
    {
        // 檢查 ModelState 中是否有 paramList 參數（當 request 為空陣列）或是 ModelState 中是否有 paramList 的錯誤（當 request 為 null）
        if ((context.ActionArguments.TryGetValue("paramList", out var paramListObj) || (context.ModelState.TryGetValue("paramList", out var entry) && entry.Errors.Count > 0)) ||
            (context.ActionArguments.TryGetValue("param", out var paramObj) || (context.ModelState.TryGetValue("param", out var paramEntry) && paramEntry.Errors.Count > 0)) ||
            (context.ActionArguments.TryGetValue("queryParam", out var queryParamObj) || (context.ModelState.TryGetValue("queryParam", out var queryEntry) && queryEntry.Errors.Count > 0)))
        {
            List<ReturnError> errors = new List<ReturnError>();

            var validationErrors = GetValidationErrors(context);

            // 如果沒有錯誤就直接返回
            if (!validationErrors.Any())
            {
                return;
            }

            var groupedErrors = GroupValidationErrorsByIndex(validationErrors);

            foreach (var kvp in groupedErrors)
            {
                var returnError = new ReturnError
                {
                    index = kvp.Key,
                    code = FormatPropertyValue(kvp.Value.FirstOrDefault()?.PropertyValue),
                    errors = kvp.Value.Select((validationError, idx) => new ErrorDetail
                    {
                        index = idx,
                        code = FormatPropertyValue(validationError.PropertyValue),
                        error = validationError.ErrorMessage,
                        message = validationError.ErrorMessage,
                        innerMsg = string.Join("|", new[]
                        {
                            validationError.NestObject ?? string.Empty,
                            validationError.Index ?? string.Empty,
                            validationError.PropertyName ?? string.Empty,
                            validationError.PropertyValue?.ToString() ?? string.Empty
                        })
                    }).ToList()
                };
                
                errors.Add(returnError);
            }

            // 回傳 400 Bad Request 並附上錯誤細節
            ReturnModel returnModel = new()
            {
                requestUUID = GetUUID(context.HttpContext.Request),
                authorize = (Authorize)_user,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = errors
            };

            context.Result = new BadRequestObjectResult(returnModel);
        }
    }

    public void OnActionExecuted(ActionExecutedContext context)
    {
        // 不需要實作
    }

    // Helper method to format PropertyValue
    private string FormatPropertyValue(object propertyValue)
    {
        return propertyValue is IEnumerable enumerable && propertyValue is not string
            ? "[" + string.Join(",", enumerable.Cast<object>()) + "]"
            : propertyValue?.ToString() ?? string.Empty;
    }

    private Dictionary<int, List<ValidationError>> GroupValidationErrorsByIndex(List<ValidationError> errors)
    {
        var groupedErrors = new Dictionary<int, List<ValidationError>>();

        foreach (var error in errors)
        {
            // 提取 index 的第一個數字，去掉 "[" 和 "]"
            if (int.TryParse(error.Index.Split('[')[1].TrimEnd(']'), out int index))
            {
                if (!groupedErrors.ContainsKey(index))
                {
                    groupedErrors[index] = new List<ValidationError>();
                }
                groupedErrors[index].Add(error);
            }
            else
            {
                // 如果解析失敗，可以選擇性處理，或跳過此項目
                Console.WriteLine($"Failed to parse index: {error.Index}");
            }
        }

        return groupedErrors;
    }

    private List<ValidationError> GetValidationErrors(ActionExecutingContext context)
    {
        var errors = new List<ValidationError>();
        foreach (var entry in context.ModelState)
            {
                var key = entry.Key; // 例如 "[0].Locale" 或 "[0].DetailList[0].Locale"
                var errorsForKey = entry.Value.Errors;

                foreach (var error in errorsForKey)
                {
                    Console.WriteLine($"Validation error in field '{key}': {error.ErrorMessage}");

                    // 過濾掉 ASP.NET 預設的 required 驗證錯誤以及 empty request body 驗證錯誤
                     if (error.ErrorMessage.EndsWith("field is required.") || error.ErrorMessage == "A non-empty request body is required.")
                    {
                        continue;
                    }

                    // 解析鍵並提取路徑組件
                    var parsedKey = ParseModelStateKey(key);
                    if (parsedKey != null)
                    {
                        var pathSegments = parsedKey.PathSegments;
                        var propertyName = parsedKey.PropertyName;

                        // 獲取 nestObject 和索引
                        if (TryGetNestedObjectAndIndex(context.ActionArguments, pathSegments, out object nestObject, out string indexPath))
                        {
                            string nestObjectPath = GetNestObjectPath(pathSegments);

                            // 取得屬性值
                            object propertyValue = null;
                            if (TryGetPropertyValue(nestObject, propertyName, out propertyValue))
                            {
                                // 解析 ErrorMessage 如果包含冒號就取冒號前部分；冒號的後部分指定給 PropertyValue 
                                string errorMessage = error.ErrorMessage;
                                string extractedErrorMessage = errorMessage;
                                object extractedPropertyValue = propertyValue;

                                // 檢查 ErrorMessage 是否包含冒號
                                int colonIndex = errorMessage.IndexOf(':');
                                if (colonIndex >= 0)
                                {
                                    // 取冒號前的部分作為 ErrorMessage
                                    extractedErrorMessage = errorMessage.Substring(0, colonIndex).Trim();
                                    // 取冒號後的部分作為 PropertyValue
                                    extractedPropertyValue = errorMessage.Substring(colonIndex + 1).Trim();
                                }
                                
                                errors.Add(new ValidationError
                                {
                                    NestObject = $"ParamList{(string.IsNullOrWhiteSpace(nestObjectPath) ? "" : ".")}{nestObjectPath}",
                                    Index = indexPath,
                                    PropertyName = propertyName,
                                    PropertyValue = extractedPropertyValue,
                                    ErrorMessage = extractedErrorMessage
                                });
                            }
                            else
                            {
                                // 無法找到屬性
                                errors.Add(new ValidationError
                                {
                                    NestObject = $"ParamList{(string.IsNullOrWhiteSpace(nestObjectPath) ? "" : ".")}{nestObjectPath}",
                                    Index = indexPath,
                                    PropertyName = propertyName,
                                    PropertyValue = null,
                                    ErrorMessage = error.ErrorMessage
                                });
                            }
                        }
                        else
                        {
                            // 無法找到 nestObject 或索引
                            errors.Add(new ValidationError
                            {
                                NestObject = null,
                                Index = null,
                                PropertyName = propertyName,
                                PropertyValue = null,
                                ErrorMessage = error.ErrorMessage
                            });
                        }
                    }
                    else
                    {
                        // 無法解析鍵，僅回傳錯誤訊息
                        errors.Add(new ValidationError
                        {
                            NestObject = "ParamList",
                            Index = "[-1]",
                            PropertyName = key,
                            PropertyValue = null,
                            ErrorMessage = error.ErrorMessage
                        });
                    }
                }
            }
        return errors;
    }

    /// <summary>
    /// 從Http Header中取得x-request-id，若不存在則產生一個新的UUID
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private string GetUUID(HttpRequest request)
    {
        if (!request.Headers.TryGetValue(_appInfo.RequestIdHeaderName, out var headerValues) || string.IsNullOrEmpty(headerValues))
        {
            return "";
        }
        else
        {
            return headerValues.ToString();
        }
    }

    /// <summary>
    /// 解析 ModelState 的鍵，提取路徑組件和屬性名稱。
    /// </summary>
    private ParsedKey ParseModelStateKey(string key)
    {
        // 使用正則表達式解析鍵
        // 例如 "[0].Locale" 或 "[0].DetailList[0].Locale"
        var regex = new Regex(@"(\[[0-9]+\]|\w+)");
        var matches = regex.Matches(key);

        if (matches.Count >= 2)
        {
            // 最後一個匹配為屬性名稱
            var propertyName = matches[matches.Count - 1].Value;

            // 路徑部分
            var pathSegments = matches.Cast<Match>().Take(matches.Count - 1).Select(m => m.Value).ToList();

            return new ParsedKey
            {
                PathSegments = pathSegments,
                PropertyName = propertyName
            };
        }

        return null;
    }

    /// <summary>
    /// 遍歷 ActionArguments 以根據路徑獲取 nestObject 和 indexPath。
    /// </summary>
    private bool TryGetNestedObjectAndIndex(IDictionary<string, object> actionArguments, List<string> pathSegments, out object nestObject, out string indexPath)
    {
        nestObject = null;
        indexPath = string.Empty;

        foreach (var arg in actionArguments)
        {
            // 嘗試從每個 ActionArgument 中遍歷路徑
            if (TryTraversePath(arg.Value, pathSegments, out object foundObject, out string foundIndexPath))
            {
                nestObject = foundObject;
                indexPath = foundIndexPath;
                return true;
            }
        }

        return false;
    }

    /// <summary>
    /// 遍歷對象圖，根據路徑獲取目標對象和索引路徑。
    /// </summary>
    private bool TryTraversePath(object currentObject, List<string> pathSegments, out object targetObject, out string indexPath)
    {
        targetObject = null;
        indexPath = string.Empty;

        object obj = currentObject;
        List<string> indices = new List<string>();

        foreach (var segment in pathSegments)
        {
            if (segment.StartsWith("[") && segment.EndsWith("]"))
            {
                // 處理索引，如 "[0]"
                var indexStr = segment.Trim('[', ']');
                if (int.TryParse(indexStr, out int index))
                {
                    if (obj is IList list && index < list.Count)
                    {
                        obj = list[index];
                        indices.Add($"[{index}]");
                    }
                    else
                    {
                        // 索引超出範圍或對象不是清單
                        return false;
                    }
                }
                else
                {
                    // 無效的索引
                    return false;
                }
            }
            else
            {
                // 處理屬性名稱
                var propInfo = obj.GetType().GetProperty(segment, BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
                if (propInfo != null)
                {
                    obj = propInfo.GetValue(obj);
                    if (obj == null)
                    {
                        // 屬性值為 null
                        return false;
                    }
                }
                else
                {
                    // 無法找到屬性
                    return false;
                }
            }
        }

        targetObject = obj;
        indexPath = string.Join("", indices);
        return true;
    }

    /// <summary>
    /// 使用反射從 nestObject 中獲取屬性值。
    /// </summary>
    private bool TryGetPropertyValue(object nestObject, string propertyName, out object propertyValue)
    {
        propertyValue = null;
        if (nestObject == null)
            return false;

        var propertyInfo = nestObject.GetType().GetProperty(propertyName, BindingFlags.Public | BindingFlags.Instance | BindingFlags.IgnoreCase);
        if (propertyInfo != null)
        {
            propertyValue = propertyInfo.GetValue(nestObject);
            return true;
        }

        return false;
    }

    /// <summary>
    /// 根據路徑組件構建 nestObject 路徑。
    /// </summary>
    private string GetNestObjectPath(List<string> pathSegments)
    {
        // 移除所有索引，僅保留屬性名稱
        var path = pathSegments.Where(s => !s.StartsWith("[")).ToList();
        return string.Join(".", path);
    }

    /// <summary>
    /// 結構體來存儲解析後的鍵資訊。
    /// </summary>
    private class ParsedKey
    {
        public List<string> PathSegments { get; set; }
        public string PropertyName { get; set; }
    }
}
