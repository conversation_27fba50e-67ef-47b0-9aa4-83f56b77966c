﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Runtime.CompilerServices;
using System.Text.Json;
using System.Text.RegularExpressions;
using Web.Constant;
using Web.Models.Controller;
using Web.Models.Controller.Role;
using Web.Models.Service;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;
using Web.Validation;

namespace Web.Controller;

/// <summary>
/// 角色控制器
/// </summary>
[Route("[controller]")]
[Authorize]
public class RoleController : BaseController
{
    private readonly MqttBackgroundService _mqttService; // 新增私有欄位

    public RoleController(
        IDataAccessService dataAccessService,
        ICredentialService credentialService,
        IRequestContextService requestContextService,
        ILogService logService,
        MqttBackgroundService mqttService) // 注入 MqttBackgroundService
        : base(dataAccessService, credentialService, requestContextService, logService)
    {
        _mqttService = mqttService; // 將注入的服務指派給私有欄位
    }

    [HttpPatch("roles")]
    [RequestParamListDuplicate("RoleCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateRoles([FromBody] List<InUpdateRole> paramList, [CallerMemberName] string callerName = null)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        string appCode = _user.AppCode;

        List<ReturnError> errors = [];

        var existsRoleList = _dataAccessService.Fetch<Role>().Where(x => x.AppCode == appCode);
        var existsMenuList = _dataAccessService.Fetch<Repository.Models.Entities.Menu>();
        var existsPermissionList = _dataAccessService.Fetch<SysCode>().Where(x => x.CodeType == "PermissionType");

        // 處理輸入的資料
        for (int i = 0; i < paramList.Count; i++)
        {
            var item = paramList[i];

            List<ErrorDetail> errorList = [];

            var role = existsRoleList.FirstOrDefault(x => x.RoleCode == item.RoleCode);

            // 檢查Menus是否不為空
            if (item.Menus != null && item.Menus.Count > 0)
            {
                List<Models.Controller.Role.Menu> menus = item.Menus.ToList();

                // 檢查Menu Id是否存在
                foreach (var menu in item.Menus)
                {
                    var menuData = existsMenuList.FirstOrDefault(x => x.MenuId == menu.MenuId);

                    if (menuData != null)
                    {
                        // 自動加入ParentMenuId
                        if (menuData.ParentMenuId != null && !item.Menus.Any(x => x.MenuId == menuData.ParentMenuId) && !menus.Any(x => x.MenuId == menuData.ParentMenuId))
                        {
                            menus.Add(new Models.Controller.Role.Menu
                            {
                                MenuId = menuData.ParentMenuId.Value,
                                Permissions = []
                            });

                            // 取得ParentMenuId的EnableMenu Id(這是for Alpha的特殊需求，不寫這筆Alpha Menu秀不出來)
                            var enableParantMenu = existsMenuList.FirstOrDefault(x => x.ParentMenuId == menuData.ParentMenuId && x.Type == "EnableMenu");

                            if (enableParantMenu != null && !item.Menus.Any(x => x.MenuId == enableParantMenu.MenuId) && !menus.Any(x => x.MenuId == enableParantMenu.MenuId))
                            {
                                menus.Add(new Models.Controller.Role.Menu
                                {
                                    MenuId = enableParantMenu.MenuId,
                                    Permissions = []
                                });
                            }

                        }

                        // 自動加入EnableMenu Id(這是for Alpha的特殊需求，不寫這筆Alpha Menu秀不出來)
                        var enableMenu = existsMenuList.FirstOrDefault(x => x.ParentMenuId == menuData.MenuId && x.Type == "EnableMenu");

                        if (enableMenu != null && !item.Menus.Any(x => x.MenuId == enableMenu.MenuId) && !menus.Any(x => x.MenuId == enableMenu.MenuId))
                        {
                            menus.Add(new Models.Controller.Role.Menu
                            {
                                MenuId = enableMenu.MenuId,
                                Permissions = []
                            });
                        }
                    }
                }

                item.Menus = menus;
            }
        }

        _logService.Logging("info", logActionName, requestUUID, "Role Data Validated, start append.");
        _dataAccessService.BeginTransaction();

        foreach (InUpdateRole param in paramList)
        {
            // 更新角色
            Role role = _dataAccessService.Fetch<Role>().Where(x => x.AppCode == _user.AppCode && x.RoleCode == param.RoleCode).AsTracking().FirstOrDefault();

            role.RoleName = param.RoleName;
            role.RoleDesc = param.RoleDesc;
            role.Enable = param.Enable == "Y";
            role.ModifyDate = DateTime.Now;

            await _dataAccessService.UpdateAsync<Role>(role, callMethodName: callerName,
                                                             e => e.RoleName,
                                                             e => e.RoleDesc,
                                                             e => e.Enable,
                                                             e => e.ModifyDate);
            if (param.Enable == "N")
            {
                var userList = await _dataAccessService.Fetch<UserDatum>(x => x.AppCode == _user.AppCode)
                    .Where(x => x.RoleCode == role.RoleCode) // 只查詢啟用的用戶
                    .AsTracking()
                    .ToListAsync();

                if (userList.Any())
                {
                    // 更新所有用戶的狀態
                    foreach (var user in userList)
                    {
                        user.Enable = false;
                        user.ModifyDate = DateTime.Now;

                        await _mqttService.NotifyUserAccountDisable(requestUUID, user, "role");
                    }

                    // 並行更新資料庫
                    await Task.WhenAll(userList.Select(u => _dataAccessService.UpdateAsync<UserDatum>(u, callMethodName: callerName, e => e.Enable, e => e.ModifyDate)));
                }
            }


            // 刪除角色舊有的權限
            await _dataAccessService.DeleteAsync<RolePermission>(x => x.AppCode == appCode && x.RoleCode == role.RoleCode);
            await _dataAccessService.DeleteAsync<RoleMenuPermission>(x => x.AppCode == appCode && x.RoleCode == role.RoleCode);

            // 新增角色權限
            foreach (var menu in param.Menus)
            {
                // 新增角色-Menu
                RolePermission rolePermission = new()
                {
                    AppCode = appCode,
                    RoleCode = param.RoleCode,
                    MenuId = menu.MenuId
                };

                await _dataAccessService.CreateAsync(rolePermission);


                // 新增角色-Menu-Persmission
                foreach (var permission in menu.Permissions)
                {
                    RoleMenuPermission roleMenuPermission = new()
                    {
                        AppCode = appCode,
                        RoleCode = param.RoleCode,
                        MenuId = menu.MenuId,
                        PermissionType = permission,
                        IsAllowed = true
                    };

                    await _dataAccessService.CreateAsync(roleMenuPermission);
                }
            }
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "Role Data update done.");

        returnModel = new ReturnModel
        {
            authorize = new Authorize { Account = _user.Account, AppCode = _user.AppCode, AreaCode = _user.AreaCode },
            httpStatus = StatusCodes.Status200OK,
            result = true
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpPost("roles")]
    [RequestParamListDuplicate("RoleCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateRoles([FromBody] List<InCreateRole> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        string appCode = _user.AppCode;

        var existsRoleList = _dataAccessService.Fetch<Role>().Where(x => x.AppCode == appCode);
        var existsMenuList = _dataAccessService.Fetch<Repository.Models.Entities.Menu>();
        var existsPermissionList = _dataAccessService.Fetch<SysCode>().Where(x => x.CodeType == "PermissionType");

        // 驗證輸入的資料
        for (int i = 0; i < paramList.Count; i++)
        {
            var item = paramList[i];

            // 檢查Menus是否不為空
            if (item.Menus != null && item.Menus.Count > 0)
            {
                List<Models.Controller.Role.Menu> menus = item.Menus.ToList();

                // 檢查Menu Id是否存在
                foreach (var menu in item.Menus)
                {
                    var menuData = existsMenuList.FirstOrDefault(x => x.MenuId == menu.MenuId);

                    if (menuData != null)
                    {
                        // 自動加入ParentMenuId
                        if (menuData.ParentMenuId != null && !item.Menus.Any(x => x.MenuId == menuData.ParentMenuId) && !menus.Any(x => x.MenuId == menuData.ParentMenuId))
                        {
                            menus.Add(new Models.Controller.Role.Menu
                            {
                                MenuId = menuData.ParentMenuId.Value,
                                Permissions = []
                            });

                            // 取得ParentMenuId的EnableMenu Id(這是for Alpha的特殊需求，不寫這筆Alpha Menu秀不出來)
                            var enableParantMenu = existsMenuList.FirstOrDefault(x => x.ParentMenuId == menuData.ParentMenuId && x.Type == "EnableMenu");

                            if (enableParantMenu != null && !item.Menus.Any(x => x.MenuId == enableParantMenu.MenuId) && !menus.Any(x => x.MenuId == enableParantMenu.MenuId))
                            {
                                menus.Add(new Models.Controller.Role.Menu
                                {
                                    MenuId = enableParantMenu.MenuId,
                                    Permissions = []
                                });
                            }
                        }

                        // 自動加入EnableMenu Id(這是for Alpha的特殊需求，不寫這筆Alpha Menu秀不出來)
                        var enableMenu = existsMenuList.FirstOrDefault(x => x.ParentMenuId == menuData.MenuId && x.Type == "EnableMenu");

                        if (enableMenu != null && !item.Menus.Any(x => x.MenuId == enableMenu.MenuId) && !menus.Any(x => x.MenuId == enableMenu.MenuId))
                        {
                            menus.Add(new Models.Controller.Role.Menu
                            {
                                MenuId = enableMenu.MenuId,
                                Permissions = []
                            });
                        }
                    }
                }

                item.Menus = menus;
            }
        }

        _logService.Logging("info", logActionName, requestUUID, "Role Data Validated, start append.");
        _dataAccessService.BeginTransaction();

        foreach (InCreateRole param in paramList)
        {
            // 新增角色
            Role role = new()
            {
                AppCode = appCode,
                RoleCode = param.RoleCode,
                RoleName = param.RoleName,
                RoleDesc = param.RoleDesc,
                Enable = param.Enable == "Y",
                CreateDate = DateTime.Now,
                ModifyDate = DateTime.Now
            };

            await _dataAccessService.CreateAsync(role);

            // 刪除角色舊有的權限
            await _dataAccessService.DeleteAsync<RolePermission>(x => x.AppCode == appCode && x.RoleCode == param.RoleCode);
            await _dataAccessService.DeleteAsync<RoleMenuPermission>(x => x.AppCode == appCode && x.RoleCode == param.RoleCode);

            // 新增角色權限
            foreach (var menu in param.Menus)
            {
                // 新增角色-Menu
                RolePermission rolePermission = new()
                {
                    AppCode = appCode,
                    RoleCode = param.RoleCode,
                    MenuId = menu.MenuId
                };

                await _dataAccessService.CreateAsync(rolePermission);

                foreach (var permission in menu.Permissions)
                {
                    // 新增角色-Menu-Persmission
                    RoleMenuPermission roleMenuPermission = new()
                    {
                        AppCode = appCode,
                        RoleCode = param.RoleCode,
                        MenuId = menu.MenuId,
                        PermissionType = permission,
                        IsAllowed = true
                    };

                    await _dataAccessService.CreateAsync(roleMenuPermission);
                }
            }
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "Role Data append done.");

        var returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status201Created,
            result = true
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpGet("roles")]
    public IActionResult RetrieveRoles(InRetrieveRole param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        var roleList = _dataAccessService.Fetch<Role>(x => x.AppCode == _user.AppCode)
                        .OrderByDescending(e => e.ModifyDate)
                        .ThenByDescending(e => e.CreateDate)
                        .Select(role => new
                        {
                            role.AppCode,
                            role.Id,
                            role.RoleCode,
                            role.RoleName,
                            role.RoleDesc,
                            role.CreateDate,
                            role.ModifyDate,
                            Enable = (role.Enable != null && role.Enable == true) ? "Y" : "N"
                        });

        IEnumerable<dynamic> result;

        // 判斷是否有傳入RoleCode，有則查詢Id
        if (param.Id == null && param.RoleCode != null)
        {
            param.Id = roleList.Where(x => x.AppCode == _user.AppCode && x.RoleCode == param.RoleCode).Select(x => x.Id).FirstOrDefault();
        }

        // 判斷是否有傳入Id，有則查詢單筆，無則查詢多筆
        if (param.Id != null && param.Id > 0)
        {
            var roleCode = roleList.FirstOrDefault(x => x.Id == param.Id)?.RoleCode;

            var rolePermissionList = _dataAccessService.Fetch<RolePermission>()
                .Where(x => x.AppCode == _user.AppCode && x.RoleCode == roleCode);

            var roleMenuPermissionList = _dataAccessService.Fetch<RoleMenuPermission>()
                .Where(x => x.AppCode == _user.AppCode && x.RoleCode == roleCode).AsEnumerable();

            var menuList = _dataAccessService.Fetch<Repository.Models.Entities.Menu>()
                .Where(m1 => roleMenuPermissionList.Any(r => r.MenuId == m1.MenuId) || rolePermissionList.Any(r => r.MenuId == m1.MenuId))
                .Where(m1 => m1.Type == "Menu" || m1.Type == "Custom")
                .AsEnumerable();

            result = roleList.Where(x => x.Id == param.Id).Select(x => new RoleDto
            {
                Id = x.Id,
                RoleCode = x.RoleCode,
                RoleName = x.RoleName,
                RoleDesc = x.RoleDesc,
                CreateDate = x.CreateDate,
                ModifyDate = x.ModifyDate,
                Enable = x.Enable,
                Menus = menuList.Select(m2 => new Models.Controller.Role.Menu
                {
                    MenuId = m2.MenuId,
                    Title = m2.Title,
                    TypeDesc = m2.TypeDesc,
                    ParentMenuId = m2.ParentMenuId
                }).ToList()
            }).ToList();

            foreach (RoleDto item in result)
            {
                item.Menus = item.Menus.Select(m => new Models.Controller.Role.Menu
                {
                    MenuId = m.MenuId,
                    Title = m.Title,
                    TypeDesc = m.TypeDesc,
                    ParentMenuId = m.ParentMenuId,
                    Permissions = roleMenuPermissionList.Where(r => r.MenuId == m.MenuId).Select(r => r.PermissionType).ToList()
                }).ToList();
            }
        }
        else
        {
            var rolePermissionList = _dataAccessService.Fetch<RolePermission>(x => x.AppCode == _user.AppCode);

            var roleMenuPermissionList = _dataAccessService.Fetch<RoleMenuPermission>(x => x.AppCode == _user.AppCode);

            var menuList = _dataAccessService.Fetch<Repository.Models.Entities.Menu>(m1 => m1.Type == "Menu" || m1.Type == "Custom");

            result = from role in roleList
                     let menus = rolePermissionList.Where(r => r.RoleCode == role.RoleCode).ToList()
                     where (string.IsNullOrEmpty(param.RoleCode) || role.RoleCode.Contains(param.RoleCode))
                                             && (string.IsNullOrEmpty(param.RoleName) || role.RoleName.Contains(param.RoleName))
                                             && (string.IsNullOrEmpty(param.RoleDesc) || role.RoleDesc.Contains(param.RoleDesc))
                                             && (string.IsNullOrEmpty(param.Enable) || role.Enable == param.Enable)
                     select new RoleDto
                     {
                         Id = role.Id,
                         RoleCode = role.RoleCode,
                         RoleName = role.RoleName,
                         RoleDesc = role.RoleDesc,
                         CreateDate = role.CreateDate,
                         ModifyDate = role.ModifyDate,
                         Enable = role.Enable,
                         Menus = menuList.Where(m1 => menus.Any(r => r.MenuId == m1.MenuId))
                         .Select(m2 => new Models.Controller.Role.Menu
                         {
                             MenuId = m2.MenuId,
                             Title = m2.Title,
                             TypeDesc = m2.TypeDesc,
                             ParentMenuId = m2.ParentMenuId,
                             Permissions = roleMenuPermissionList.Where(r => r.RoleCode == role.RoleCode && r.MenuId == m2.MenuId).Select(r => r.PermissionType).ToList()
                         }).ToList()
                     };
        }

        // 下方使用CountAsync()會有問題，所以先ToList()再Count()，待解決
        var queryList = result.ToList();

        var recordTotal = queryList.Count();

        var recordList = size == 0 ? result.ToList() : queryList.Skip(skip).Take(size).ToList();

        //// 總筆數
        //var recordTotal = await query.CountAsync();

        //// 判斷是否分頁決定回傳資料
        //var recordList = size == 0 ? await query.ToListAsync() : await query.Skip(skip).Take(size).ToListAsync();

        ReturnModel returnModel = new ReturnModel
        {
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal,
                recordList
            },
            authorize = (Authorize)_user
        };

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);
    }

    [HttpDelete("roles")]
    [RequestParamListDuplicate("RoleCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeleteRoles([FromBody] List<InDeleteRole> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        List<ReturnError> errorList = [];

        // 開始刪除角色資料
        _logService.Logging("info", logActionName, requestUUID, "Role Data Validated, start delete.");
        _dataAccessService.BeginTransaction();

        foreach (InDeleteRole param in paramList)
        {
            await _dataAccessService.DeleteAsync<Role>(x => x.AppCode == _user.AppCode && x.RoleCode == param.RoleCode);
            await _dataAccessService.DeleteAsync<RolePermission>(u => u.AppCode == _user.AppCode && u.RoleCode == param.RoleCode);
            await _dataAccessService.DeleteAsync<RoleMenuPermission>(c => c.AppCode == _user.AppCode && c.RoleCode == param.RoleCode);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "User Data delete done.");

        return Ok(new ReturnModel(StatusCodes.Status200OK, true));
    }
}
