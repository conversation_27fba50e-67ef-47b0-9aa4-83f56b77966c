﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class CannedMessage
{
    public int Id { get; set; }

    public string AppCode { get; set; } = null!;

    public string CannedType { get; set; } = null!;

    public string CannedCode { get; set; } = null!;

    public string? Message { get; set; }

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }

    public bool? SystemDefault { get; set; }

    public bool? Enable { get; set; }
}
