using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Configuration;
using System.Text.Json;
using Web.Constant;
using Web.Models.Controller;
using Web.Models.Controller.Sector;
using Web.Models.Service;
using Web.Models.Service.Fusion;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;
using Web.Validation;

namespace Web.Controller;

/// <summary>
/// 次平面控制器
/// </summary>
[Route("[controller]")]
[Authorize]
public class SectorController(IDataAccessService dataAccessService,
                            ICredentialService credentialService,
                            IRequestContextService requestContextService,
                            ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private const string FileStorageBasePath = "FileStorage";
    
    /// 新增次平面檢核
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    // 有在method 定義route，在Controller記得也要定義route，否則會404
    [HttpPost("sectors/validate")]
    [RequestParamListDuplicate("SectorCode")]
    [RequestParamListNotNullOrEmpty]
    public IActionResult ValidateSector([FromBody] List<CreateSector> paramList)
    {
        // 進到 controller 代表驗證通過，回傳空的錯誤列表
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel  returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    /// <summary>
    /// 新增次平面
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    // 有在method 定義route，在Controller記得也要定義route，否則會404
    [HttpPost("sectors")]
    [RequestParamListDuplicate("SectorCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateSector([FromBody] List<CreateSector> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        string appCode = _user.AppCode;
        _logService.Logging("info", logActionName, requestUUID, "Sector Data Validated, start to append.");

        _dataAccessService.BeginTransaction();

        // 將 Sector 寫入 HMN 資料庫
        foreach (CreateSector s in paramList)
        {
            Sector sector = new Sector
            {
                AppCode = _user.AppCode,
                PlaneCode = s.PlaneCode,
                SectorCode = s.SectorCode,
                CustomSectorCode = s.CustomSectorCode,
                Enable = s.Enable == "Y",
                SectorName = s.SectorName,
                MapWidth = s.MapWidth,
                MapHeight = s.MapHeight,
                PositionX = s.PositionX,
                PositionY = s.PositionY,
                CreateDate = DateTime.Now,
                CreateUserAccount = _user.Account,
                ModifyDate = DateTime.Now,
                ModifyUserAccount = _user.Account
            };

            // 新增 Sector
            await _dataAccessService.CreateAsync<Sector>(sector);

            // 將 SIDList 的基站新增至建立的次平面
            List<string> sidList = s.SIDList;
            if (sidList != null && sidList.Count > 0)
            {
                // 將 SIDList 轉成 SectorStation List
                List<SectorStation> sectorStations = sidList.Select(sid => new SectorStation() {
                    AppCode = appCode,
                    SectorCode = s.SectorCode,
                    Stationsid = sid
                }).ToList();

                // 新增 SectorStation 物件
                foreach (SectorStation sectorStation in sectorStations)
                {
                    await _dataAccessService.CreateAsync(sectorStation);
                }
            }
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "Sector Data append done.");

        returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status201Created,
            result = true
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpPatch("sectors")]
    [RequestParamListDuplicate("SectorCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateSector([FromBody] List<UpdateSector> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        _dataAccessService.BeginTransaction();

        // 將更新的 Sector，更新 HMN 資料庫
        foreach (UpdateSector s in paramList)
        {
            Sector sector = _dataAccessService.Fetch<Sector>(e => e.AppCode == _user.AppCode && e.SectorCode == s.SectorCode).AsTracking().First();
            var sectorCode = s.SectorCode;

            List<string> updateField = new List<string>();

            if (!string.IsNullOrEmpty(s.CustomSectorCode))
            {
                updateField.Add("CustomSectorCode");
                sector.CustomSectorCode = s.CustomSectorCode;
            }

            if (!string.IsNullOrEmpty(s.SectorName))
            {
                updateField.Add("SectorName");
                sector.SectorName = s.SectorName;
            }

            if (!string.IsNullOrEmpty(s.Enable))
            {
                updateField.Add("Enable");
                sector.Enable = s.Enable == "Y";
            }

            if (s.MapWidth != null)
            {
                updateField.Add("MapWidth");
                sector.MapWidth = s.MapWidth;
            }

            if (s.MapHeight != null)
            {
                updateField.Add("MapHeight");
                sector.MapHeight = s.MapHeight;
            }

            if (s.PositionX != null)
            {
                updateField.Add("PositionX");
                sector.PositionX = s.PositionX;
            }

            if (s.PositionY != null)
            {
                updateField.Add("PositionY");
                sector.PositionY = s.PositionY;
            }

            updateField.Add("ModifyDate");
            updateField.Add("ModifyUserAccount");
            sector.ModifyDate = DateTime.Now;
            sector.ModifyUserAccount = _user.Account;

            await _dataAccessService.UpdateAsync<Sector>(sector, updateField.ToArray());

            // 將 SIDList 的基站新增至次平面
            List<string> sidList = s.SIDList;
            if (sidList != null)
            {
                // 刪除原本 SectorCode 與 SID 的關聯
                await _dataAccessService.DeleteAsync<SectorStation>(e => e.AppCode == _user.AppCode && e.SectorCode == sector.SectorCode);

                // 將 SIDList 轉成 SectorStation List
                List<SectorStation> sectorStations = sidList.Select(sid => new SectorStation() {
                    AppCode = _user.AppCode,
                    SectorCode = s.SectorCode,
                    Stationsid = sid
                }).ToList();

                // 新增 SectorStation 物件
                foreach (SectorStation sectorStation in sectorStations)
                {
                    await _dataAccessService.CreateAsync(sectorStation);
                }
            }
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "Sector Data update done.");

        return Ok(new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = true
                });
    }

    [HttpPut("sectors")]
    [RequestParamNotNullOrEmpty]
    public async Task<IActionResult> UpdateSectorMap([FromBody] UpdateSectorMap param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        Sector sector = _dataAccessService.Fetch<Sector>(e => e.AppCode == _user.AppCode && e.SectorCode == param.SectorCode).AsTracking().First();
        var sectorCode = param.SectorCode;
        string sectorMapPath = sector.SectorMapPath;

        // 找到 Base64 起始位置
        var base64Index = param.ImageBase64.IndexOf(";base64,");
        if (base64Index >= 0)
        {
            param.ImageBase64 = param.ImageBase64.Substring(base64Index + ";base64,".Length);
        }

        // 將 ImageBase64 儲存到 temp file
        byte[] imageBytes = Convert.FromBase64String(param.ImageBase64);
        string fileExtension = param.ImageFileType.StartsWith(".") ? param.ImageFileType : $".{param.ImageFileType}";
        string tempFileName = $"{Guid.NewGuid()}{fileExtension}";

        // 儲存暫存檔案
        var tempFilePath = Path.Combine(FileStorageBasePath, tempFileName);
        await System.IO.File.WriteAllBytesAsync(tempFilePath, imageBytes);

        // 儲存到 HMN2
        string fileName = $"{Guid.NewGuid()}{fileExtension}";

        // 確保儲存目錄存在
        var storagePath = Path.Combine(FileStorageBasePath, "SectorMaps");
        if (!Directory.Exists(storagePath))
        {
            Directory.CreateDirectory(storagePath);
        }

        storagePath = Path.Combine(storagePath, sectorCode);
        if (!Directory.Exists(storagePath))
        {
            Directory.CreateDirectory(storagePath);
        }

        // 目標檔案路徑
        var destinationFilePath = Path.Combine(storagePath, fileName);

        // 複製檔案
        System.IO.File.Copy(tempFilePath, destinationFilePath, overwrite: true);
        
        sectorMapPath = $"/FileStorage/SectorMaps/{sectorCode}/{fileName}";

        // 刪除臨時檔案
        System.IO.File.Delete(tempFilePath);

        // 更新次平面的 SectorMapPath 欄位
        List<string> updateField = new List<string>();
        updateField.Add("SectorMapPath");
        sector.SectorMapPath = sectorMapPath;

        updateField.Add("ModifyDate");
        updateField.Add("ModifyUserAccount");
        sector.ModifyDate = DateTime.Now;
        sector.ModifyUserAccount = _user.Account;

        await _dataAccessService.UpdateAsync<Sector>(sector, updateField.ToArray());
        
        _dataAccessService.BeginTransaction();

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "Sector Map update done.");

        return Ok(new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = true
                });
    }

    [HttpDelete("sectors")]
    [RequestParamListDuplicate("SectorCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeleteSector([FromBody] List<DeleteSector> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 將刪除成功的 Station，從資料庫中刪除
        _logService.Logging("info", logActionName, requestUUID, "Sector Data Validated, start to delete.");
        _dataAccessService.BeginTransaction();

        foreach (DeleteSector param in paramList)
        {
            // 刪除次平面
            int deleteResult = await _dataAccessService.DeleteAsync<Sector>(e => e.AppCode == _user.AppCode && e.SectorCode == param.SectorCode);
            if (deleteResult > 0)
            {
                // 刪除 SectorCode 與 Station 的關聯
                await _dataAccessService.DeleteAsync<SectorStation>(e => e.AppCode == _user.AppCode && e.SectorCode == param.SectorCode);

                // 刪除 SectorCode 與 Department 的關聯
                await _dataAccessService.DeleteAsync<DepartSector>(e => e.AppCode == _user.AppCode && e.SectorCode == param.SectorCode);
            }
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "Sector Data delete done.");

        return Ok(new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = true
                });
    }

    [HttpGet("sectors")]
    public async Task<IActionResult> RetrieveSector(RetrieveSector queryParam)
    {
        RetrieveSector param = queryParam;
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // 排序；如果沒有 sort 條件預設使用 ModifyDate 降冪排序
        string sort = string.IsNullOrEmpty(param.sort) ? "ModifyDate:desc" : param.sort;
        string sortByField = sort.Split(":")[0];
        string sortDirection = sort.Split(":")[1];

        var buildings = _dataAccessService.Fetch<Building>(e=>e.AppCode == _user.AppCode);
        var planes = _dataAccessService.Fetch<Plane>(e=>e.AppCode == _user.AppCode && buildings.Any(b => b.BuildingCode == e.BuildingCode));
        var sectors = _dataAccessService.Fetch<Sector>(e=>e.AppCode == _user.AppCode && planes.Any(p => p.PlaneCode == e.PlaneCode));
        var sectorStations = _dataAccessService.Fetch<SectorStation>(e=> e.AppCode == _user.AppCode && sectors.Any(s => s.SectorCode == e.SectorCode));
        var stations = _dataAccessService.Fetch<Station>(e=> e.AppCode == _user.AppCode && sectorStations.Any(s => s.Stationsid == e.SID));
        var departSectors = _dataAccessService.Fetch<DepartSector>(e=>e.AppCode == _user.AppCode && sectors.Any(s => s.SectorCode == e.SectorCode));
        var departments = _dataAccessService.Fetch<Department>(e=>e.AppCode == _user.AppCode && departSectors.Any(ds => ds.DeptCode == e.DeptCode));

        var deptCodes = string.IsNullOrWhiteSpace(param.DeptCodes) ? null : param.DeptCodes.Split(",");
        var deptNames = string.IsNullOrWhiteSpace(param.DeptNames) ? null : param.DeptNames.Split(",");
        var stationSids = string.IsNullOrWhiteSpace(param.StationSids) ? null : param.StationSids.Split(",");
        var stationNames = string.IsNullOrWhiteSpace(param.StationNames) ? null : param.StationNames.Split(",");

        var query = (from a in sectors
             join b in planes on a.PlaneCode equals b.PlaneCode into temp
             from t in temp.DefaultIfEmpty()
             join c in buildings on t.BuildingCode equals c.BuildingCode into temp2
             from t2 in temp2.DefaultIfEmpty()
             select new
             {
                t2.AreaCode,
                a.SectorId,
                a.AppCode,
                Departments = (from ds in departSectors
                            where ds.SectorCode == a.SectorCode
                            orderby ds.SectorCode
                            join d in departments on ds.DeptCode equals d.DeptCode into temp3
                            from t3 in temp3.DefaultIfEmpty()
                            select new
                            {
                                t3.DeptCode,
                                t3.DeptName,

                            }).ToList(),
                a.PlaneCode,
                t.CustomPlaneCode,
                t.PlaneName,
                PlaneMapHeight = t.MapHeight,
                PlaneMapWidth = t.MapWidth,
                t.PlaneMapPath,
                t2.BuildingCode,
                t2.BuildingName,
                a.SectorCode,
                a.CustomSectorCode,
                Enable = (a.Enable == true) ? "Y" : "N",
                a.SectorName,
                a.SectorMapPath,
                a.MapWidth,
                a.MapHeight,
                a.PositionX,
                a.PositionY,
                Stations = (from ss in sectorStations
                            where ss.SectorCode == a.SectorCode
                            orderby ss.SectorCode
                            join s in stations on ss.Stationsid equals s.SID into temp4
                            from t4 in temp4.DefaultIfEmpty()
                            select new
                            {
                                t4.SID,
                                t4.StationName,
                                t4.StationType,
                                t4.SpaceType,
                                t4.StationIP,
                                t4.StationMac,
                                t4.PlaneCode,
                                t4.RegionCode,
                                t4.Enable,
                                t4.AxisX,
                                t4.AxisY,
                                t4.DiffPositionX,
                                t4.DiffPositionY

                            }).ToList(),
                a.CreateUserAccount,
                a.CreateDate,
                a.ModifyUserAccount,
                a.ModifyDate
             })
            .Where(x => (string.IsNullOrEmpty(param.AreaCode) || x.AreaCode == param.AreaCode) // 院區代碼
                    && (string.IsNullOrEmpty(param.BuildingCode) || x.BuildingCode.ToUpper().Contains(param.BuildingCode.ToUpper())) // 棟別代碼
                    && (string.IsNullOrEmpty(param.BuildingName) || x.BuildingName.Contains(param.BuildingName)) // 棟別名稱
                    && (string.IsNullOrEmpty(param.PlaneCode) || x.PlaneCode.ToUpper().Contains(param.PlaneCode.ToUpper())) // 樓層代碼
                    && (string.IsNullOrEmpty(param.PlaneName) || x.PlaneName.Contains(param.PlaneName)) // 樓層名稱
                    && (string.IsNullOrEmpty(param.SectorCode) || x.SectorCode.ToUpper().Contains(param.SectorCode.ToUpper())) // 次平面代碼
                    && (string.IsNullOrEmpty(param.CustomSectorCode) || x.CustomSectorCode.ToUpper().Contains(param.CustomSectorCode.ToUpper())) // 客製次平面代碼
                    && (string.IsNullOrEmpty(param.SectorName) || x.SectorName.Contains(param.SectorName)) // 次平面名稱
                    && (string.IsNullOrEmpty(param.Enable) || x.Enable == param.Enable)
                    && (deptCodes == null || x.Departments.Any(d => deptCodes.Any(dc => d.DeptCode.Contains(dc))))
                    && (deptNames == null || x.Departments.Any(d => deptNames.Any(dn => d.DeptName.Contains(dn))))
                    && (stationSids == null || x.Stations.Any(s => stationSids.Any(sid => s.SID.Contains(sid))))
                    && (stationNames == null || x.Stations.Any(s => stationNames.Any(sn => s.StationName.Contains(sn))))
                );

        // 取得總筆數
        int recordTotal = await query.CountAsync();

        // 根據 sortByField 和 sortDirection 進行動態排序
        query = sortDirection.ToLower() == "desc"
            ? query.OrderByDescending(x => EF.Property<object>(x, sortByField))
            : query.OrderBy(x => EF.Property<object>(x, sortByField));

        // 進行資料庫分頁
        var recordList = size == 0 
            ? await query.ToListAsync() // 如果 size == 0，表示不分頁，直接取回所有資料
            : await query.Skip(skip).Take(size).ToListAsync(); // 分頁查詢

        ReturnModel returnModel = new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = new
                    {
                        recordTotal,
                        recordList
                    }
                };

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);
    }
}
