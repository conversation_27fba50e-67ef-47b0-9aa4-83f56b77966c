﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class DepartSector
{
    public int Id { get; set; }

    public string AppCode { get; set; } = null!;

    public string AreaCode { get; set; } = null!;

    public string DeptCode { get; set; } = null!;

    public string SectorCode { get; set; } = null!;

    public string? CreateUserAccount { get; set; }

    public DateTime? CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }
}
