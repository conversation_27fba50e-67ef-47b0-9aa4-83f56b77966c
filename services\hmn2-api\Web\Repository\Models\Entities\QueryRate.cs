using System;
using System.Collections.Generic;
using System.Text.Json;
using System.ComponentModel.DataAnnotations.Schema;

namespace Web.Repository.Models.Entities;

public partial class QueryRate
{
    public int Id { get; set; }

    // public string? Uid { get; set; }
    public string? ClientIp { get; set; }

    public string? ClientHostName { get; set; }

    public string? AppCode { get; set; }

    public string? AreaCode { get; set; }

    public string? UserAccount { get; set; }

    public string? DeptCode { get; set; }

    public int MenuId { get; set; }

    public string? HttpStatus { get; set; }

    public JsonDocument? HttpRequest { get; set; }

    public DateTime CreateDate { get; set; }


}