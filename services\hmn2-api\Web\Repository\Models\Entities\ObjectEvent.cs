﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace Web.Repository.Models.Entities;

public partial class ObjectEvent
{
    public int ObjectEventId { get; set; }

    public string AppCode { get; set; } = null!;

    public string? AreaCode { get; set; }

    public string ObjectCode { get; set; } = null!;

    public string Pid { get; set; } = null!;

    public string ServiceCode { get; set; } = null!;

    public string? SddResource { get; set; }

    public string EventCode { get; set; } = null!;

    public bool Active { get; set; }

    public bool Enable { get; set; }

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }

    public string? SddComp { get; set; }

    [NotMapped]
    public string? EventName { get; set; }
}
