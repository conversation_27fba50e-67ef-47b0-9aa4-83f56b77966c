﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class ObjectType
{
    public int ObjectTypeId { get; set; }

    public string AppCode { get; set; } = null!;

    /// <summary>
    /// 1:協助人員; 2:傳送; 3:院內人員;4:設備; 5:病患; 0:空間
    /// </summary>
    public string ObjectTypeCode { get; set; } = null!;
    
    public bool SystemDefault { get; set; }

    public bool Active { get; set; }

    public bool Enable { get; set; }

    public string Name { get; set; } = null!;

    /// <summary>
    /// API 要儲存的值
    /// </summary>
    public string FusionCoreValue { get; set; } = null!;

    public string? ObjectTypeDesc { get; set; }

    public string? CreateUserAccount { get; set; }

    public DateTime? CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }
}
