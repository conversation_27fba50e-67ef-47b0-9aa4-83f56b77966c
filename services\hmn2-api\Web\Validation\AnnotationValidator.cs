﻿using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Internal;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Linq.Dynamic.Core;
using System.Linq.Expressions;
using Web.Models.Service;
using Web.Repository.Interface;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;
using Web.Constant;
using Web.Models.Controller.Station;
using IEnumerable = System.Collections.IEnumerable;
using Web.Models.Service.Fusion;

namespace Web.Validation;

public class CustomValidator(IDataAccessService dataAccessService, ICredentialService credentialService, IDeviceService deviceService, IStationService stationService, ILangService langService) : ValidationAttribute
{
    private readonly IDataAccessService _dataAccessService = dataAccessService;
    private readonly ICredentialService _credentialService = credentialService;
    private readonly IDeviceService _deviceService = deviceService;
    private readonly IStationService _stationService = stationService;
    private readonly ILangService _langService = langService;

    UserResult _user = credentialService.UserResult;

    private IEnumerable GetParamList(ValidationContext validationContext)
    {
        var httpContextAccessor = validationContext.GetService(typeof(IHttpContextAccessor)) as IHttpContextAccessor;
        if (httpContextAccessor?.HttpContext != null)
        {
            return httpContextAccessor.HttpContext.Items["ParamList"] as IEnumerable;
        }
        return null;
    }

    private bool CheckDuplicateInList(IEnumerable paramList, string columnValue, Type objectType, string areaCode, string columnName)
    {
        var columnProperty = objectType.GetProperty(columnName);
        var areaCodeProperty = objectType.GetProperty("AreaCode");
        if (columnProperty == null)
        {
            return false;
        }

        int count = 0;
        foreach (var item in paramList)
        {
            if (item.GetType() == objectType)
            {
                var itemValue = columnProperty.GetValue(item)?.ToString();
                var itemAreaCode = areaCodeProperty?.GetValue(item)?.ToString();
                if (itemValue == columnValue && itemAreaCode == areaCode)
                {
                    count++;
                    if (count > 1)
                    {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    public async Task<ValidationResult> ValidateUnique(string value, List<string> associatedProperties, ValidationContext validationContext, string tableName, string columnName, bool? isActive, bool? skipAppCode)
    {
        if (value == null)
        {
            return ValidationResult.Success;
        }

        var columnValue = value.ToString();

        var entityType = Type.GetType($"Web.Repository.Models.Entities.{tableName}");
        if (entityType == null)
        {
            throw new ArgumentException($"Entity type not found for table name: {tableName}");
        }
        var method = typeof(DataAccessService).GetMethods()
            .Where(m => m.Name == "Fetch" && m.IsGenericMethodDefinition)
            .FirstOrDefault(m =>
            {
                var parameters = m.GetParameters();
                return parameters.Length == 1 && parameters[0].ParameterType.IsGenericType &&
                       parameters[0].ParameterType.GetGenericTypeDefinition() == typeof(Expression<>) &&
                       parameters[0].ParameterType.GetGenericArguments()[0].GetGenericTypeDefinition() == typeof(Func<,>);
            });

        if (method == null)
        {
            throw new InvalidOperationException("Fetch method not found in DataAccessService");
        }

        var genericMethod = method.MakeGenericMethod(entityType);

        // 動態構建查詢表達式(AreaCode == areaCode && [ColumnName] == columnValue)
        var parameter = Expression.Parameter(entityType, "e");
        var columnProperty = Expression.Property(parameter, columnName);

        // 取得 columnProperty 的類型
        Type propertyType = columnProperty.Type;

        // 轉換 columnValue 的型態以匹配 columnProperty.Type
        object convertedValue = getConvertedValue(propertyType, columnValue);

        var columnCondition = Expression.Equal(columnProperty, Expression.Constant(convertedValue, columnProperty.Type));

        Expression finalCondition = columnCondition;

        // 檢查是否存在 associated properties 中的欄位
        if (associatedProperties != null && associatedProperties.Count() > 0)
        {
            foreach (string ap in associatedProperties)
            {
                string associatedPropertyName = ap.Split(":")[0];
                string associatedPropertyValue = ap.Split(":")[1];
                if (entityType.GetProperty(associatedPropertyName) != null && !string.IsNullOrEmpty(associatedPropertyValue))
                {
                    var associatedProperty = Expression.Property(parameter, associatedPropertyName);
                    object convertedAssociatedPropertyValue = getConvertedValue(associatedProperty.Type, associatedPropertyValue);
                    var associatedCondition = Expression.Equal(associatedProperty, Expression.Constant(convertedAssociatedPropertyValue));
                    finalCondition = Expression.AndAlso(finalCondition, associatedCondition);
                }
            }
        }

        // 檢查是否存在名為 "AppCode" 的欄位
        if (skipAppCode != true && entityType.GetProperty("AppCode") != null)
        {
            // 如果存在，則加入 AppCode == _user.AppCode 的條件
            var xPropertyExpr = Expression.Property(parameter, "AppCode");
            var xCondition = Expression.Equal(xPropertyExpr, Expression.Constant(_user.AppCode));
            finalCondition = Expression.AndAlso(finalCondition, xCondition);
        }

        // 檢查是否存在名為 "Active" 的欄位
        if (isActive != null && entityType.GetProperty("Active") != null)
        {
            // 如果存在，則加入 Active == true 的條件
            var xPropertyExpr = Expression.Property(parameter, "Active");
            var xCondition = Expression.Equal(xPropertyExpr, Expression.Constant(isActive));
            finalCondition = Expression.AndAlso(finalCondition, xCondition);
        }

        var lambdaType = typeof(Func<,>).MakeGenericType(entityType, typeof(bool));
        var lambda = Expression.Lambda(lambdaType, finalCondition, parameter);

        var result = genericMethod.Invoke(dataAccessService, new object?[] { lambda });
        var queryable = result as IQueryable<object>;
        if (queryable == null)
        {
            throw new InvalidOperationException("The result is not an IQueryable<object>");
        }

        // 使用非泛型版本的 FirstOrDefaultAsync
        var entity = await EntityFrameworkQueryableExtensions.FirstOrDefaultAsync(queryable);

        if (entity != null)
        {
            return new ValidationResult(ErrorMessage);
        }

        return ValidationResult.Success;
    }
    public async Task<ValidationResult> ValidateDepartmentByType(string value, string areaCode, string columnName)
    {
        if (string.IsNullOrWhiteSpace(value))
        {
            return ValidationResult.Success;
        }

        Department? department = null;

        if (columnName == "ManagedDept")
        {
            department = await _dataAccessService.Fetch<Department>(e => e.AppCode == _user.AppCode && e.AreaCode == areaCode && e.IsManagedDept == true && e.DeptCode == value.ToString()).FirstOrDefaultAsync();
        }
        else if (columnName == "UsageDept")
        {
            department = await _dataAccessService.Fetch<Department>(e => e.AppCode == _user.AppCode && e.AreaCode == areaCode && e.IsUsageDept == true && e.DeptCode == value.ToString()).FirstOrDefaultAsync();
        }
        else
        {
            throw new Exception("Invalid column name");
        }

        return department == null ? new ValidationResult(ErrorMessage) : ValidationResult.Success;
    }
    public async Task<ValidationResult> ValidateStationRegistered(string value, string areaCode)
    {
        if (value == null)
        {
            return new ValidationResult("err.null.param.sid");
        }

        var columnValue = value.ToString();

        var station = (await _stationService.GetStationList($"sid eq {columnValue}")).FirstOrDefault();

        if (station == null)
        {
            return new ValidationResult("err.notfound.param.sid");
        }

        if (station.registered == false)
        {
            return new ValidationResult("err.connect.param.sid.fusion");
        }

        return ValidationResult.Success;
    }

    private object getConvertedValue(Type targetType, object value)
    {
        // 轉換 columnValue 的型態以匹配 columnProperty.Type
        object convertedValue;
        try
        {
            if ((targetType == typeof(bool) || targetType == typeof(bool?)) && value is string boolStr)
            {
                if (bool.TryParse(boolStr, out bool boolResult))
                {
                    return boolResult;
                }
            }

            // 如果是 nullable type，先取 underlying type
            var underlyingType = Nullable.GetUnderlyingType(targetType) ?? targetType;
            convertedValue = Convert.ChangeType(value, underlyingType);
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"無法將值 '{value}' 轉換為類型 {targetType}.", ex);
        }
        return convertedValue;
    }

    public async Task<ValidationResult> ValidateExists(string value, List<string> associatedProperties, string tableName, string columnName, bool? isActive, string? checkColumnIs, bool containsMatch, bool? skipAppCode)
    {
        if (value == null || value.Length == 0)
        {
            return ValidationResult.Success;
        }

        var columnValue = value.ToString();
        var entityType = Type.GetType($"Web.Repository.Models.Entities.{tableName}");
        if (entityType == null)
        {
            throw new ArgumentException($"Entity type not found for table name: {tableName}");
        }

        var method = typeof(DataAccessService).GetMethods()
            .Where(m => m.Name == "Fetch" && m.IsGenericMethodDefinition)
            .FirstOrDefault(m =>
            {
                var parameters = m.GetParameters();
                return parameters.Length == 1 && parameters[0].ParameterType.IsGenericType &&
                       parameters[0].ParameterType.GetGenericTypeDefinition() == typeof(Expression<>) &&
                       parameters[0].ParameterType.GetGenericArguments()[0].GetGenericTypeDefinition() == typeof(Func<,>);
            });

        if (method == null)
        {
            throw new InvalidOperationException("Fetch method not found in DataAccessService");
        }

        var genericMethod = method.MakeGenericMethod(entityType);

        var parameter = Expression.Parameter(entityType, "e");
        var columnProperty = Expression.Property(parameter, columnName);

        // 取得 columnProperty 的類型
        Type propertyType = columnProperty.Type;

        // 轉換 columnValue 的型態以匹配 columnProperty.Type
        object convertedValue = getConvertedValue(propertyType, columnValue);

        Expression columnCondition;
        if (containsMatch)
        {
            // 取得 Contains 方法
            var containsMethod = typeof(string).GetMethod("Contains", new[] { typeof(string) });

            // 產生 e.ColumnName.Contains(columnValue)
            columnCondition = Expression.Call(columnProperty, containsMethod, Expression.Constant(convertedValue, typeof(string)));
        }
        else
        {
            columnCondition = Expression.Equal(columnProperty, Expression.Constant(convertedValue, columnProperty.Type));
        }
        Expression finalCondition = columnCondition;


        // 檢查是否存在 associated properties 中的欄位
        if (associatedProperties != null && associatedProperties.Count() > 0)
        {
            foreach (string ap in associatedProperties)
            {
                string associatedPropertyName = ap.Split(":")[0];
                string associatedPropertyValue = ap.Split(":")[1];
                if (entityType.GetProperty(associatedPropertyName) != null && !string.IsNullOrEmpty(associatedPropertyValue))
                {
                    var associatedProperty = Expression.Property(parameter, associatedPropertyName);
                    object convertedAssociatedPropertyValue = getConvertedValue(associatedProperty.Type, associatedPropertyValue);
                    var associatedCondition = Expression.Equal(associatedProperty, Expression.Constant(convertedAssociatedPropertyValue));
                    finalCondition = Expression.AndAlso(finalCondition, associatedCondition);
                }
            }
        }

        if (isActive != null && entityType.GetProperty("Active") != null)
        {
            var activeProperty = Expression.Property(parameter, "Active");
            var activeCondition = Expression.Equal(activeProperty, Expression.Constant(isActive));
            finalCondition = Expression.AndAlso(finalCondition, activeCondition);
        }

        if (skipAppCode != true && entityType.GetProperty("AppCode") != null)
        {
            var appCodeExpr = Expression.Property(parameter, "AppCode");
            var appCodeCondition = Expression.Equal(appCodeExpr, Expression.Constant(_user.AppCode));
            finalCondition = Expression.AndAlso(finalCondition, appCodeCondition);
        }

        var lambdaType = typeof(Func<,>).MakeGenericType(entityType, typeof(bool));
        var lambda = Expression.Lambda(lambdaType, finalCondition, parameter);

        var result = genericMethod.Invoke(dataAccessService, new object?[] { lambda });
        var queryable = result as IQueryable<object>;
        if (queryable == null)
        {
            throw new InvalidOperationException("The result is not an IQueryable<object>");
        }

        // 使用非泛型版本的 FirstOrDefaultAsync
        var entity = await EntityFrameworkQueryableExtensions.FirstOrDefaultAsync(queryable);
        if (entity == null)
        {
            return new ValidationResult(ErrorMessage);
        }

        if (checkColumnIs != null)
        {
            string checkColumnName = checkColumnIs.Split(':')[0];
            string expectedColumnValue = checkColumnIs.Split(':')[1].ToUpper();

            // 檢查 entity 的 Enable 屬性值是否為 null
            var checkColumnProperty = entityType.GetProperty(checkColumnName) ?? throw new ArgumentException($"Property with name {checkColumnName} was not found in entity type {entityType.Name}");
            var checkColumnValue = checkColumnProperty.GetValue(entity).ToString().ToUpper();
            if (checkColumnValue == expectedColumnValue)
            {
                return ValidationResult.Success;
            }
            else
            {
                return new ValidationResult(ErrorMessage);
            }
        }

        return ValidationResult.Success;
    }

    public async Task<ValidationResult> ValidateHasValue(List<string> associatedProperties, string tableName, string conditionColumn, string conditionValue, string checkColumn)
    {
        var entityType = Type.GetType($"Web.Repository.Models.Entities.{tableName}") ?? throw new ArgumentException($"Entity type not found for table name: {tableName}");

        var method = typeof(DataAccessService).GetMethods()
            .Where(m => m.Name == "Fetch" && m.IsGenericMethodDefinition)
            .FirstOrDefault(m =>
            {
                var parameters = m.GetParameters();
                return parameters.Length == 1 && parameters[0].ParameterType.IsGenericType &&
                       parameters[0].ParameterType.GetGenericTypeDefinition() == typeof(Expression<>) &&
                       parameters[0].ParameterType.GetGenericArguments()[0].GetGenericTypeDefinition() == typeof(Func<,>);
            }) ?? throw new InvalidOperationException("Fetch method not found in DataAccessService");

        var genericMethod = method.MakeGenericMethod(entityType);

        // 動態構建查詢表達式(AreaCode == areaCode && [conditionColumn] == conditionValue)
        var parameter = Expression.Parameter(entityType, "e");
        var columnProperty = Expression.Property(parameter, conditionColumn);

        // 取得 columnProperty 的類型
        Type propertyType = columnProperty.Type;

        // 轉換 columnValue 的型態以匹配 columnProperty.Type
        object convertedValue = getConvertedValue(propertyType, conditionValue);

        var columnCondition = Expression.Equal(columnProperty, Expression.Constant(convertedValue, columnProperty.Type));

        Expression finalCondition = columnCondition;

        // 檢查是否存在 associated properties 中的欄位
        if (associatedProperties != null && associatedProperties.Count() > 0)
        {
            foreach (string ap in associatedProperties)
            {
                string associatedPropertyName = ap.Split(":")[0];
                string associatedPropertyValue = ap.Split(":")[1];
                if (entityType.GetProperty(associatedPropertyName) != null && !string.IsNullOrEmpty(associatedPropertyValue))
                {
                    var associatedProperty = Expression.Property(parameter, associatedPropertyName);
                    object convertedAssociatedPropertyValue = getConvertedValue(associatedProperty.Type, associatedPropertyValue);
                    var associatedCondition = Expression.Equal(associatedProperty, Expression.Constant(convertedAssociatedPropertyValue));
                    finalCondition = Expression.AndAlso(finalCondition, associatedCondition);
                }
            }
        }

        if (entityType.GetProperty("AppCode") != null)
        {
            var appCodeProperty = Expression.Property(parameter, "AppCode");
            var appCodeCondition = Expression.Equal(appCodeProperty, Expression.Constant(_user.AppCode));
            finalCondition = Expression.AndAlso(finalCondition, appCodeCondition);
        }

        var lambdaType = typeof(Func<,>).MakeGenericType(entityType, typeof(bool));
        var lambda = Expression.Lambda(lambdaType, finalCondition, parameter);

        var result = genericMethod.Invoke(dataAccessService, new object?[] { lambda });
        var queryable = result as IQueryable<object> ?? throw new InvalidOperationException("The result is not an IQueryable<object>");

        // 使用非泛型版本的 FirstOrDefaultAsync
        var entity = await EntityFrameworkQueryableExtensions.FirstOrDefaultAsync(queryable);

        if (entity == null)
        {
            // 沒有符合條件的資料，視為要檢查的欄位值為null
            return new ValidationResult($"novalue");
        }
        else
        {
            // 檢查 entity 的 checkField 屬性值是否為null
            var checkProperty = entityType.GetProperty(checkColumn) ?? throw new ArgumentException($"Property with name {checkColumn} not found in entity type {entityType.Name}");

            var checkValue = checkProperty.GetValue(entity);
            if (checkValue != null)
            {
                return ValidationResult.Success;
            }
            else
            {
                return new ValidationResult($"novalue");
            }
        }
    }

    public async Task<object?> GetTableValue(List<string> associatedProperties, string tableName, string conditionColumn, object conditionValue, string checkColumn)
    {
        var entityType = Type.GetType($"Web.Repository.Models.Entities.{tableName}") ?? throw new ArgumentException($"Entity type not found for table name: {tableName}");

        var method = typeof(DataAccessService).GetMethods()
            .Where(m => m.Name == "Fetch" && m.IsGenericMethodDefinition)
            .FirstOrDefault(m =>
            {
                var parameters = m.GetParameters();
                return parameters.Length == 1 && parameters[0].ParameterType.IsGenericType &&
                       parameters[0].ParameterType.GetGenericTypeDefinition() == typeof(Expression<>) &&
                       parameters[0].ParameterType.GetGenericArguments()[0].GetGenericTypeDefinition() == typeof(Func<,>);
            }) ?? throw new InvalidOperationException("Fetch method not found in DataAccessService");

        var genericMethod = method.MakeGenericMethod(entityType);

        // 動態構建查詢表達式(AreaCode == areaCode && [conditionColumn] == conditionValue)
        var parameter = Expression.Parameter(entityType, "e");
        var columnProperty = Expression.Property(parameter, conditionColumn);

        // 取得 columnProperty 的類型
        Type propertyType = columnProperty.Type;

        // 轉換 columnValue 的型態以匹配 columnProperty.Type
        object convertedValue = getConvertedValue(propertyType, conditionValue);

        var columnCondition = Expression.Equal(columnProperty, Expression.Constant(convertedValue, columnProperty.Type));

        Expression finalCondition = columnCondition;

        // 檢查是否存在 associated properties 中的欄位
        if (associatedProperties != null && associatedProperties.Count() > 0)
        {
            foreach (string ap in associatedProperties)
            {
                string associatedPropertyName = ap.Split(":")[0];
                string associatedPropertyValue = ap.Split(":")[1];
                if (entityType.GetProperty(associatedPropertyName) != null && !string.IsNullOrEmpty(associatedPropertyValue))
                {
                    var associatedProperty = Expression.Property(parameter, associatedPropertyName);
                    object convertedAssociatedPropertyValue = getConvertedValue(associatedProperty.Type, associatedPropertyValue);
                    var associatedCondition = Expression.Equal(associatedProperty, Expression.Constant(convertedAssociatedPropertyValue));
                    finalCondition = Expression.AndAlso(finalCondition, associatedCondition);
                }
            }
        }

        if (entityType.GetProperty("AppCode") != null)
        {
            var appCodeProperty = entityType.GetProperty("AppCode");
            var appCodeExpr = Expression.Property(parameter, "AppCode");
            var appCodeCondition = Expression.Equal(appCodeExpr, Expression.Constant(_user.AppCode));
            finalCondition = Expression.AndAlso(finalCondition, appCodeCondition);
        }

        var lambdaType = typeof(Func<,>).MakeGenericType(entityType, typeof(bool));
        var lambda = Expression.Lambda(lambdaType, finalCondition, parameter);

        var result = genericMethod.Invoke(dataAccessService, new object?[] { lambda });
        var queryable = result as IQueryable<object> ?? throw new InvalidOperationException("The result is not an IQueryable<object>");

        // 使用非泛型版本的 FirstOrDefaultAsync
        var entity = await EntityFrameworkQueryableExtensions.FirstOrDefaultAsync(queryable);

        if (entity == null)
        {
            // 沒有符合條件的資料，視為要檢查的欄位值為null
            return null;
        }
        else
        {
            // 檢查 entity 的 checkField 屬性值是否為null
            var checkProperty = entityType.GetProperty(checkColumn) ?? throw new ArgumentException($"Property with name {checkColumn} not found in entity type {entityType.Name}"); ;

            var checkValue = checkProperty.GetValue(entity);
            if (checkValue != null)
            {
                return checkValue;
            }
            else
            {
                return null;
            }
        }
    }

    public ValidationResult ValidateResource(object value, ValidationContext validationContext)
    {
        var model = (InUpdateSt3)validationContext.ObjectInstance;
        var configurations = model.Configurations;

        if (configurations == null || !configurations.Any())
        {
            return ValidationResult.Success;
        }

        var horizontalMode = configurations.FirstOrDefault(e => e.resourceId == "/10001/0/2");
        var displayMode = configurations.FirstOrDefault(e => e.resourceId == "/10001/0/24");

        if (horizontalMode == null)
        {
            return new ValidationResult("err.null.param.configurations.resource", ["/10001/0/2"]);
        }

        if (displayMode == null)
        {
            return new ValidationResult("err.null.param.configurations.resource", ["/10001/0/24"]);
        }

        if (horizontalMode != null && horizontalMode.value != "1" && horizontalMode.value != "0")
        {
            return new ValidationResult("err.invalid.param.configurations.resource.value");
        }

        if (displayMode != null && displayMode.value != "1" && displayMode.value != "0")
        {
            return new ValidationResult("err.invalid.param.configurations.resource.value");
        }

        if (horizontalMode != null && horizontalMode.value == "0")
        {
            // 檢查configuration中必要的resourceId是否存在
        }

        if (horizontalMode != null && horizontalMode.value == "1")
        {
            // 檢查configuration中必要的resourceId是否存在
        }

        return ValidationResult.Success;
    }

    public List<Models.Service.Fusion.DeviceType> GetDeviceTypeList()
    {
        // 使用 Task.Result 來同步取得非同步方法的結果
        var devTypeList = _deviceService.GetDeviceTypeList().Result;

        return devTypeList;
    }
    
    public List<serviceCode> GetServiceCodeList()
    {
        //由WebAPI取得所有服務列表（用於多國語言的轉換）
        var serviceCodeList = _langService.GetServiceCodes().Result;

        return serviceCodeList;
    }
}