﻿namespace Web.Models.Controller.ObjectDevicePosition
{
    using System;
    using System.Collections.Generic;
    using System.Text;

    public class ObjectDevicePositionResult
    {
        public int ObjectId { get; set; }
        public string AreaCode { get; set; }
        public string AreaName { get; set; }
        public string ObjectCode { get; set; }
        public string ObjectName { get; set; }
        public bool Enable { get; set; }
        public string UsageDepartCode { get; set; }
        public string UsageDepartName { get; set; }
        public string GroupCode { get; set; }
        public string GroupName { get; set; }
        public string ObjectType { get; set; }
        public Device Device { get; set; }
        public Position Position { get; set; }
    }

    public class Device
    {
        public string Pid { get; set; }
        public string Name { get; set; }
    }   

    public class Position
    {
        public float PositionX { get; set; }
        public float PositionY { get; set; }
        public Building Building { get; set; }
        public Plane Plane { get; set; }
        public Location Location { get; set; }
        public Station Station { get; set; }
        public DateTime LastPositionTime { get; set; }
    }

    public class Building 
    {
        public string CustomBuildingCode { get; set; }
        public string BuildingName { get; set; }
    }

    public class Plane
    {
        public string CustomPlaneCode { get; set; }
        public string PlaneName { get; set; }
        public string PlaneMapPath { get; set;}
    }

    public class Location
    {         
        public string LocCode { get; set; }
        public string LocationName { get; set; }
    }

    public class Station
    {
        public string Sid { get; set;}
        public string StatiionName { get; set; }
    }
}
