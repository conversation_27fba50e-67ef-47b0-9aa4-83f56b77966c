<html>

<head>
  <title>Get Device Sample</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <!-- Bootstrap CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/css/bootstrap.min.css" rel="stylesheet"
    integrity="sha384-EVSTQN3/azprG1Anm3QDgpJLIm9Nao0Yz1ztcQTwFspd3yD65VohhpuuCOmLASjC" crossorigin="anonymous">
  <script src="http://ajax.googleapis.com/ajax/libs/jquery/3.6.3/jquery.min.js"></script>
  <script>
    const getResult = (filter) => {
      $("#fullApi").val('');
      $(".result").html("Loading...");

      let host = $("#host").val();
      let api = $("#api").val();

      if(host[host.length - 1] !== "/") host += "/";
      if(api[0] === "/") api = api.substring(1);

      const url = host + api;

      let form = new FormData();
      
      form.append("account", $("#account").val().toUpperCase());
      form.append("passwords", $("#passwords").val().toUpperCase());

      settings = {
        method: "POST",
        processData: false,
        mimeType: "multipart/form-data",
        contentType: false,
        data: form,
        url: `${url}${filter}`
      };

      $.ajax(settings).done(function (response) {
        const obj = JSON.parse(response);
        response = JSON.stringify(obj, null, 2);

        $("#fullApi").val(`${url}${filter}`);

        $(".result").html(`<pre>${response}</pre>`);
      });
    }
  </script>
</head>

<body>
  <div class="row">
    <div class="col-6">
      <label for="host" class="form-label">1.HMN Web 網址</label>
      <input type="text" class="form-control" id="host" value="https://pcrm.fusionnet.io/hmnweb/">
    </div>
    <div class="col-6">
      <label for="api" class="form-label">2.Api 路徑</label>
      <input type="text" class="form-control" id="api" value="api/v1/Objects/GetDevicePosition">
    </div>
  </div>
  <div class="row">
    <div class="col-6">
      <label for="account" class="form-label">3.帳號</label>
      <input type="text" class="form-control" id="account" value="ann">
    </div>
    <div class="col-6">
      <label for="passwords" class="form-label">4.密碼（使用<a href="https://www.ez2o.com/App/Coder/SHA" target="_blank">大寫SHA-1 加密</a>）</label>
      <input type="text" class="form-control" id="passwords" value="3EF19234AD3957B674E5FCE427E53486C24F2C06">
    </div>
  </div>
  <div class="row">
    <div class="col-12">
      <h3>5.查詢語法範例（使用ODATA Protocol）</h3>
    </div>
  </div>
  <div class="row">
    <div class="col-4 text-end"><label>查詢 所有資料</label></div>
    <div class="col-6"><input type="text" class="sample8 form-control" value=""></div>
    <div class="col-2"><button type="button" class="btn btn-primary" onclick="getResult($('.sample8').val())">執行</button></div>
  </div>

  <div class="row">
    <div class="col-4 text-end"><label>查詢 所屬院區代碼 為'FIH' 且 對象編碼 為'1A63_M8_C'</label></div>
    <div class="col-6"><input type="text" class="sample1 form-control" value="?$filter=AreaCode eq 'FIH' and ObjectCode eq '1A63_M8_C'"></div>
    <div class="col-2"><button type="button" class="btn btn-primary" onclick="getResult($('.sample1').val())">執行</button></div>
  </div>

  <div class="row">
    <div class="col-4 text-end"><label>查詢 對象編碼 包含 'M8'</label></div>
    <div class="col-6"><input type="text" class="sample2 form-control" value="?$filter=contains(ObjectName, 'M8')"></div>
    <div class="col-2"><button type="button" class="btn btn-primary" onclick="getResult($('.sample2').val())">執行</button></div>
  </div>

  <div class="row">
    <div class="col-4 text-end"><label>查詢 裝置識別ID 為 'CC:9F:7A:72:73:7C'</label></div>
    <div class="col-6"><input type="text" class="sample3 form-control" value="?$filter=Device/Pid eq 'CC:9F:7A:72:73:7C'"></div>
    <div class="col-2"> <button type="button" class="btn btn-primary"
        onclick="getResult($('.sample3').val())">執行</button> </div>
  </div>

  <div class="row">
    <div class="col-4 text-end"> <label>查詢 所屬院區代碼 為'FIH' 且 對象編碼 為'56AF_3030_B' 或 '3ECC_M7_B'</label> </div>
    <div class="col-6"><input type="text" class="sample4 form-control"
        value="?$filter=AreaCode eq 'FIH' and ObjectCode in ('56AF_3030_B','3ECC_M7_B')"></div>
    <div class="col-2"> <button type="button" class="btn btn-primary"
        onclick="getResult($('.sample4').val())">執行</button> </div>
  </div>

  <div class="row">
    <div class="col-4 text-end"><label>查詢 最後定位時間 小於 '2023-12-13T15:01:44'</label></div>
    <div class="col-6"><input type="text" class="sample5 form-control"
        value="?$filter=Position/LastPositionTime lt 2023-12-13T15:01:44"></div>
    <div class="col-2"> <button type="button" class="btn btn-primary"
        onclick="getResult($('.sample5').val())">執行</button> </div>
  </div>

  <div class="row">
    <div class="col-4 text-end"><label>查詢 裝置識別ID 不為 'CC:9F:7A:72:70:B2'</label></div>
    <div class="col-6"><input type="text" class="sample6 form-control"
        value="?$filter=Device/Pid ne 'CC:9F:7A:72:70:B2'"></div>
    <div class="col-2"> <button type="button" class="btn btn-primary"
        onclick="getResult($('.sample6').val())">執行</button> </div>
  </div>

  <div class="row">
    <div class="col-4 text-end"><label>查詢 對象編碼 不為 '70B2' 且 不為 '555D'</label></div>
    <div class="col-6"><input type="text" class="sample7 form-control"
        value="?$filter=not (ObjectCode eq '70B2' or ObjectCode eq '555D')">
    </div>
    <div class="col-2"> <button type="button" class="btn btn-primary"
        onclick="getResult($('.sample7').val())">執行</button> </div>
  </div>

  <div class="row">
    <div class="col-12">
      <label for="api">6.此次執行的API URL</label>
      <input type="text" class="form-control" id="fullApi" value="" readonly>
    </div>
  </div>
  <div class="row">
    <div class="col-12">
      <label>7.結果</label>
      <p class="result"></p>
    </div>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.0.2/dist/js/bootstrap.bundle.min.js"
    integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM"
    crossorigin="anonymous"></script>
</body>

</html>