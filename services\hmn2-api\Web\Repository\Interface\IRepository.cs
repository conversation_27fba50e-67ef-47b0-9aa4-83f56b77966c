﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Linq.Expressions;

namespace Web.Repository.Interface;
public interface IRepository<T, Tkey> where T : class
{
    // IUnitOfWork UnitOfWork { get; set; }

    void Create(T entity);
    IEnumerable<T> ReadAll();

    IEnumerable<T> Read(Expression<Func<T, bool>> filter);

    void Update(T entity, params Expression<Func<T, object>>[] updateProperties);
    void Update(T entity, string[] updatePropertyNames);
    void Update(T entity);
    void Save(T entity);

    void Delete(T entity);

    void DeleteAll(Expression<Func<T, bool>> filter);
    void BatchUpdate(IEnumerable<T> entities, Expression<Func<T, object>>[] updateProperties);
}
