﻿using Microsoft.AspNetCore.Authentication;
using System.Security.Claims;

namespace Web.Services.Interfaces;

public interface IAuthService
{
    void CreatePersistentUserClaimsIdentity(List<Claim> claims, out ClaimsIdentity claimsIdentity, out AuthenticationProperties authProperties);
    string EncryptPwd(string userPassword);
    Task<List<Claim>> GenerateClaimList(string appCode, string userAccount);
    Task<(bool loginResult, string? appCode)> Login(string userAccount, string userPassword);
    Task<string> GenerateUserToken(string appCode, string userAccount);

}