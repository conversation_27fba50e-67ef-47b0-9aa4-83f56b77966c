﻿using Microsoft.EntityFrameworkCore;
using System.Globalization;
using System.Net;
using System.Text.Json;
using System.Text.Json.Serialization;
using Web.Services.Interfaces;

namespace Web.Services;

/// <summary>
/// 工具服務
/// </summary>
/// <remarks>
/// UtilityService 註冊為 Singleton 服務，所以不能再繼承 BaseService，會有System.InvalidOperationException: Cannot resolve scoped service 'Web.Services.DataAccessService' from root provider.
/// </remarks>
public class UtilityService() : IUtilityService
{
    // public string GetClientIp(HttpContext httpContext)
    // {
    //     //1.HttpContext.Request.HttpContext.Features.Get<IHttpConnectionFeature>().RemoteIpAddress.ToString()
    //     //這種方法使用IHttpConnectionFeature介面來獲取用戶端的IP位址。它從HttpContext.Request.HttpContext.Features集合中獲取IHttpConnectionFeature實例，並使用RemoteIpAddress屬性來獲取用戶端的IP位址。這種方法是較早版本的ASP.NET中常用的方法。
    //     //2.HttpContext.Request.Headers["X-Forwarded-For"].ToString()
    //     //這種方法通過讀取請求頭中的X - Forwarded - For欄位來獲取用戶端的IP位址。在某些情況下，例如當請求經過代理伺服器時，用戶端的IP位址可能會被代理伺服器添加到此請求頭中。這種方法在使用反向代理伺服器時比較常見。
    //     //3.HttpContext.Connection.RemoteIpAddress.ToString()
    //     //這種方法直接從HttpContext.Connection屬性中獲取用戶端的IP位址。HttpContext.Connection表示與當前請求關聯的連接資訊，包括用戶端的IP位址和埠號。這種方法是.NET Core中常用的方法。
    //     return string.IsNullOrEmpty(httpContext.Request.Headers["X-Forwarded-For"]) ?
    //                                         httpContext.Connection.RemoteIpAddress.ToString() :
    //                                         httpContext.Request.Headers["X-Forwarded-For"].ToString();
    // }

    public string GetClientIp(HttpContext httpContext)
    {
        // 嘗試從多種來源獲取客戶端IP
        string ip = string.Empty;

        // 首先檢查 X-Forwarded-For 標頭
        if (!string.IsNullOrEmpty(httpContext.Request.Headers["X-Forwarded-For"]))
        {
            ip = httpContext.Request.Headers["X-Forwarded-For"].ToString().Split(',')[0].Trim();
        }
        // 檢查 X-Real-IP 標頭
        else if (!string.IsNullOrEmpty(httpContext.Request.Headers["X-Real-IP"]))
        {
            ip = httpContext.Request.Headers["X-Real-IP"].ToString();
        }
        // 使用連接的遠程IP地址
        else if (httpContext.Connection.RemoteIpAddress != null)
        {
            ip = httpContext.Connection.RemoteIpAddress.ToString();
        }

        if (!string.IsNullOrEmpty(ip) && ip == "::1")
        {
            ip = "127.0.0.1";
        }

        return !string.IsNullOrEmpty(ip) ? ip : "Unknown";
    }
    public string GetClientHostName(HttpContext httpContext)
    {
        // 獲取用戶端主機名稱
        // 這裡假設用戶端主機名稱存儲在請求標頭中，實際情況可能需要根據具體需求調整
        return httpContext.Request.Headers["X-Forwarded-Host"].ToString() ?? "Unknown";
    }

    public async Task<(int recordTotal, List<object>)> Pagination(IQueryable<object> query, string pageStr, string sizeStr, string sort)
    {
        bool pageParseResult = int.TryParse(pageStr, out int page);
        bool sizeParseResult = int.TryParse(sizeStr, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // 排序；如果有 sort 
        if (!string.IsNullOrWhiteSpace(sort))
        {
            string sortByField = sort.Split(":")[0];
            string sortDirection = sort.Split(":")[1];
            // 根據 sortByField 和 sortDirection 進行動態排序
            query = sortDirection.ToLower() == "desc"
                ? query.OrderByDescending(x => EF.Property<object>(x, sortByField))
                : query.OrderBy(x => EF.Property<object>(x, sortByField));
        }

        // 取得總筆數
        int recordTotal = await query.CountAsync();

        // 進行資料庫分頁
        var recordList = size == 0
            ? await query.ToListAsync() // 如果 size == 0，表示不分頁，直接取回所有資料
            : await query.Skip(skip).Take(size).ToListAsync();

        return (recordTotal, recordList);
    }

    public async Task<string> GetClientHostNameAsync(HttpContext httpContext)
    {
        var ipAddress = httpContext.Connection.RemoteIpAddress;

        if (ipAddress == null)
            return "Unknown";

        try
        {
            var hostEntry = await Dns.GetHostEntryAsync(ipAddress);
            return hostEntry.HostName;
        }
        catch
        {
            return ipAddress.ToString(); // 或回傳 "Unknown"
        }
    }
}
