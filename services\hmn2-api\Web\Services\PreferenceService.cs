﻿using System.Text;
using System.Security.Cryptography;
using System.Text.RegularExpressions;
using Web.Repository.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Mvc;
using Web.Models.Service.Preference;
using System.Linq;
using Web.Services.Interfaces;

namespace Web.Services;

public class SysParaComparer : IEqualityComparer<OutFetchSysParameter>
{
    public bool Equals(OutFetchSysParameter x, OutFetchSysParameter y)
    {
        return x.ParaCode == y.ParaCode;
    }

    public int GetHashCode(OutFetchSysParameter obj)
    {
        return obj.ParaCode.GetHashCode();
    }
}

/// <summary>
/// 提供偏好設定相關服務
/// </summary>
/// <remarks>
/// 此Service 會在產生Cookie 前被呼叫，所以不繼承BaseService
/// </remarks>
/// <param name="provider"></param>
/// <param name="dataAccessService"></param>
public class PreferenceService(IServiceProvider provider,
    IDataAccessService dataAccessService) : IPreferenceService
{
    private readonly IDataAccessService _dataAccessService = dataAccessService;
    /// <summary>
    /// 取得使用者偏好設定
    /// </summary>
    /// <remarks>
    /// 如果使用者沒有設定偏好設定，就會回傳預設的偏好設定，預設的偏好設定是從ClientPara取得
    /// Call 此Method 時可能尚未寫入Claim ，所以appCode及areaCode用傳入的
    /// </remarks>
    /// <param name="appCode"></param>
    /// <param name="areaCode"></param>
    /// <param name="userAccount"></param>
    /// <returns></returns>
    public async Task<List<UserClientPara>> FetchUserClientPara(string appCode, string areaCode, string userAccount)
    {
        var userClientParaList = _dataAccessService
                                        .Fetch<UserClientPara>(e => e.AppCode == appCode && e.AreaCode == areaCode && e.UserAccount == userAccount);

        if (!userClientParaList.Any())
        {
            userClientParaList = _dataAccessService.Fetch<ClientPara>().Select(para => new UserClientPara
            {
                AppCode = appCode,
                AreaCode = areaCode,
                UserAccount = userAccount,
                ParaCode = para.ParaCode,
                ParaValue = para.ParaValue,
                ParaType = para.ParaType
            });
        }

        return await userClientParaList.ToListAsync();
    }

    public async Task<List<SysParameters>> FetchAllGlobalParameter(string appCode)
    {
        // 獲取所有 sysParameters 作為基礎數據
        var sysParameters = await _dataAccessService.Fetch<SysParameters>()
            .ToListAsync();

        // 獲取匹配 appCode 的 GlobalSysPara，並轉為字典以加速查找
        var globalSysParas = await _dataAccessService.Fetch<GlobalSysPara>(e => e.AppCode == appCode)
            .ToDictionaryAsync(gsp => gsp.ParaCode, gsp => gsp);

        // 以 sysParameters 為基礎構建結果
        var result = sysParameters.Select(sp =>
        {
            // 從字典中查找匹配的 GlobalSysPara
            globalSysParas.TryGetValue(sp.ParaCode, out var matchingGlobal);

            return new SysParameters
            {
                ParaType = sp.ParaType,
                ParaCode = sp.ParaCode,
                ParaValue = matchingGlobal != null ? matchingGlobal.ParaValue : sp.ParaValue,
                Editable = sp.Editable,
                DescStringId = sp.DescStringId,
                ParaDesc = sp.ParaDesc
            };
        }).ToList();

        return result;
    }

    public async Task<string> FetchGlobalParameter(string appCode, string paraCode)
    {
        // Query SysParameters table 作為基礎數據
        var sysParameter = await _dataAccessService.Fetch<SysParameters>(e => e.ParaCode == paraCode)
            .FirstOrDefaultAsync();

        // Query GlobalSysPara table 用於覆蓋
        var globalSysPara = await _dataAccessService.Fetch<GlobalSysPara>(e => e.AppCode == appCode && e.ParaCode == paraCode)
            .FirstOrDefaultAsync();

        // 決定使用哪個 ParaValue
        string paraValue = globalSysPara != null ? globalSysPara.ParaValue : sysParameter?.ParaValue ?? "";

        return paraValue ?? "";
    }

    public async Task<List<OutFetchSysParameter>> FetchSysParameter(string appCode)
    {
        var allGlobalParameterList = await FetchAllGlobalParameter(appCode);

        var result = allGlobalParameterList.Select(e => new OutFetchSysParameter
        {
            ParaType = e.ParaType,
            ParaCode = e.ParaCode,
            ParaValue = e.ParaValue,
            Editable = e.Editable ? "Y" : "N",
            DescStringId = e.DescStringId,
            ParaDesc = e.ParaDesc ?? string.Empty,
            Source = "Global"
        }).ToList();

        return result;
    }

    public async Task<string> FetchSysParameter(string appCode, string areaCode, string paraCode)
    {
        var allGlobalParameterList = await FetchAllGlobalParameter(appCode);

        var result = allGlobalParameterList.Select(e => new OutFetchSysParameter
        {
            ParaType = e.ParaType,
            ParaCode = e.ParaCode,
            ParaValue = e.ParaValue,
            Editable = e.Editable ? "Y" : "N",
            DescStringId = e.DescStringId,
            ParaDesc = e.ParaDesc ?? string.Empty,
            Source = "Global"
        });

        string value = result.FirstOrDefault(e => e.ParaCode == paraCode)?.ParaValue ?? "";

        return value;
    }
}
