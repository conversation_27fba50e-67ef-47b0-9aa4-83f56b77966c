﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Web.Controller;
using Web.Models.Controller;
using Web.Models.Service.Monitor;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;
using static Web.Models.Service.Monitor.UntreatedTaskInfo;
using static Web.Models.Service.Monitor.UntreatedTaskInfo.TaskLocationInfo;
using Location = Web.Repository.Models.Entities.Location;

namespace Web.Controllers
{
    /// <summary>
    /// 監控控制器
    /// </summary>
    [Route("[controller]")]
    [Authorize]
    public class MonitorController(IDataAccessService dataAccessService,
                            IMonitorService monitorService,
                            IConfigurationService configurationService,
                            ICredentialService credentialService,
                            IRequestContextService requestContextService,
                            ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
    {
        private readonly IMonitorService _monitorService = monitorService;
        private readonly IConfigurationService _configurationService = configurationService;

        [HttpGet("untreatedTasks")]
        public async Task<IActionResult> RetrieveUntreatedTask(InUntreatedTaskList param)
        {
            string? requestUUID = _requestContextService.GetRequestUID();
            string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
            _logService.Logging("info", logActionName, requestUUID, "Start");

            try
            {
                // 取得即時事件列表
                var followingTaskList = await _monitorService.GetFollowingTaskList(new InFollowingTaskList
                {
                    AppCode = _user.AppCode,
                    AreaCode = param.AreaCode,
                    DeptCodeList = !string.IsNullOrEmpty(param.DeptCodes) ? param.DeptCodes.Split(",") : _user.DeptCodeList
                });

                // 取得裝置位置與定位基站信息
                var deviceList = followingTaskList.Select(x => x.Pid).Distinct().ToList();
                var devicePositionList = (await _configurationService.GetDevicePosition())
                    .Where(x => x.position != null
                        && x.position.station != null
                        && x.position.plane != null
                        && x.position.latestPositionTime != null
                        && deviceList.Contains(x.pid)).ToList();

                var cameraList = _dataAccessService.Fetch<Camera>(x => x.AppCode == _user.AppCode);
                var cameraStationList = _dataAccessService.Fetch<CameraStation>(x => x.AppCode == _user.AppCode);
                var locationList = _dataAccessService.Fetch<Location>(x => x.AppCode == _user.AppCode);

                var untreatedTaskList = followingTaskList.Select(task => new UntreatedTaskInfo
                {
                    Id = task.Id,
                    TaskId = task.TaskId ?? 0,
                    ServiceCode = task.ServiceCode,
                    EventCode = task.EventCode,
                    EventName = task.EventName,
                    StartsAt = task.StartsAt ?? string.Empty,
                    Pid = task.Pid,
                    DeviceName = task.DeviceName,
                    DeviceType = task.DeviceType,
                    ObjectCode = task.ObjectCode,
                    ObjectName = task.ObjectName,
                    ObjectType = task.ObjectType,
                    EventLocation = (task.Station != null) ? new TaskLocationInfo
                    {
                        PlaneCode = (task.Plane != null) ? task.Plane.PlaneCode : string.Empty,
                        PlaneName = (task.Plane != null) ? (task.Plane.PlaneName ?? string.Empty) : string.Empty,
                        StationSid = task.Station.SID ?? string.Empty,
                        StationName = task.Station.StationName ?? string.Empty,
                        StationCameras = (from cs in cameraStationList
                                          join t in cameraList on cs.CameraMAC equals t.CameraMAC into temp
                                          from c in temp.DefaultIfEmpty()
                                          where cs.SID == task.Station.SID
                                          select new StationCamera
                                          {
                                              CameraMac = c.CameraMAC,
                                              CameraName = c.CameraName,
                                              StreamUrl = c.StreamURL
                                          }).ToList(),
                        LocationCode = task.LocationCode,
                        LocationName = task.LocationName
                    } : null,
                    CurrentLocation = (from dp in devicePositionList
                                       where dp.pid == task.Pid
                                       select new TaskLocationInfo
                                       {
                                           PlaneCode = dp.position.plane.code,
                                           PlaneName = dp.position.plane.name,
                                           StationSid = dp.position.station.sid,
                                           StationName = dp.position.station.name,
                                           StationCameras = (from cs in cameraStationList
                                                             join t in cameraList on cs.CameraMAC equals t.CameraMAC into temp
                                                             from c in temp.DefaultIfEmpty()
                                                             where cs.SID == dp.position.station.sid
                                                             select new StationCamera
                                                             {
                                                                 CameraMac = c.CameraMAC,
                                                                 CameraName = c.CameraName,
                                                                 StreamUrl = c.StreamURL
                                                             }).ToList(),
                                           LocationCode = dp.position.station.regionCode,
                                           LocationName = (from l in locationList
                                                           where l.LocCode == dp.position.station.regionCode
                                           select l.LocName).FirstOrDefault()
                                       }).FirstOrDefault()
                }).ToList();

                ReturnModel returnModel = new ReturnModel
                {
                    authorize = new Authorize { Account = _user.Account, AppCode = _user.AppCode, AreaCode = _user.AreaCode },
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = untreatedTaskList
                };

                _logService.Logging("info", logActionName, requestUUID, untreatedTaskList.Count.ToString());
                _logService.Logging("info", logActionName, requestUUID, "End");

                return StatusCode(returnModel.httpStatus, returnModel);
            }
            catch (Exception ex)
            {
                _logService.Logging("error", logActionName, requestUUID, ex.Message);
                return StatusCode(StatusCodes.Status500InternalServerError, new ReturnModel(StatusCodes.Status500InternalServerError, false, ex.Message));
            }
        }

    }
}
