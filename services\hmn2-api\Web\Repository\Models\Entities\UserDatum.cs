﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class UserDatum
{
    public int Id { get; set; }

    public string? AppCode { get; set; }

    public string? AreaCode { get; set; }

    public string UserAccount { get; set; } = null!;

    public string? UserAccountDisp { get; set; }

    public string? UserPassword { get; set; }

    public bool? Enable { get; set; }

    public string? DeptCode { get; set; }

    public string? RoleCode { get; set; }

    public string? UserName { get; set; }

    public string? UserEmail { get; set; }

    public string? IsAdmin { get; set; }

    public bool? IsSupervisor { get; set; }

    public bool? ThirdPartyAuth { get; set; }

    public string? PwdExpType { get; set; }

    public DateTime? PwdExpStartDate { get; set; }

    public DateTime? PwdExpEndDate { get; set; }

    public bool? NeedChangePwd { get; set; }

    public DateTime? CreateDate { get; set; }

    public DateTime? ModifyDate { get; set; }

    public string? CreateUserAccount { get; set; }

    public string? ModifyUserAccount { get; set; }

    public bool? IsSuperAdmin { get; set; }
}
