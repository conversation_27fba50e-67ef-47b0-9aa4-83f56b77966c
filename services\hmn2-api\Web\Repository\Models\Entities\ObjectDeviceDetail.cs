﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class ObjectDeviceDetail
{
    public int DeviceDetailId { get; set; }

    public string AppCode { get; set; } = null!;

    public string AreaCode { get; set; } = null!;

    public string ObjectCode { get; set; } = null!;

    public string Pid { get; set; } = null!;

    public string SddResource { get; set; } = null!;

    public string? SddComp { get; set; }

    public double? Threshold { get; set; }

    public int? StayOvertime { get; set; }

    public int? Duration { get; set; }

    public int? SilentInterval { get; set; }

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }
}
