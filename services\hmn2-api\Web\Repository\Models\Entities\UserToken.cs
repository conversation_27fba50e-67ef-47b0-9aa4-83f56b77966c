﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class UserToken
{
    public int Id { get; set; }

    public int UserDataId { get; set; }

    public string Token { get; set; } = null!;

    public DateTime TokenExpiryDate { get; set; }

    public bool TokenDeleted { get; set; }

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }
}
