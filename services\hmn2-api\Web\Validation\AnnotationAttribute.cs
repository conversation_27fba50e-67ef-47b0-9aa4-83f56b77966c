﻿using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Drawing.Charts;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.EntityFrameworkCore;
using System.Collections;
using System.ComponentModel.DataAnnotations;
using System.Reflection;
using System.Text.Json;
using System.Text.RegularExpressions;
using Web.Constant;
using Web.Models.AppSettings;
using Web.Models.Controller;
using Web.Models.Controller.Object;
using Web.Models.Controller.Station;
using Web.Models.Service;
using Web.Models.Service.Fusion;
using Web.Services.Interfaces;
using static Web.Models.Controller.Station.InUpdateSt3;

namespace Web.Validation;

public class ValidationResourceAttribute : ValidationAttribute
{
    public string? ListPropertyName { get; }

    public ValidationResourceAttribute(string ListPropertyName)
    {
        this.ListPropertyName = ListPropertyName;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        // 確認 ListPropertyName 是存在的屬性
        var listProperty = validationContext.ObjectType.GetProperty(ListPropertyName);
        if (listProperty == null)
        {
            throw new ArgumentException($"Property '{ListPropertyName}' not found on {validationContext.ObjectType.Name}");
        }

        List<Configuration>? liist = listProperty.GetValue(validationContext.ObjectInstance) as List<Configuration>;
        if (liist == null || liist.Count == 0)
        {
            return ValidationResult.Success;
        }

        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;
        ValidationResult result = validator.ValidateResource(value, validationContext);

        // 如果檢查結果ObjectCode有值，則回傳檢查不通過
        if (result != ValidationResult.Success)
        {
            return new ValidationResult(this.ErrorMessage);
        }

        return ValidationResult.Success;
    }
}
public class StationRegisteredAttribute(string areaCodePropertyName) : ValidationAttribute
{
    private readonly string _areaCodePropertyName = areaCodePropertyName;
    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        var columnValue = value?.ToString();

        var areaCodeProperty = validationContext.ObjectType.GetProperty(_areaCodePropertyName) ?? throw new ArgumentException("Property with this name not found");
        var areaCode = areaCodeProperty.GetValue(validationContext.ObjectInstance)?.ToString();

        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;
        ValidationResult result = validator.ValidateStationRegistered(columnValue, areaCode).Result;

        // 如果檢查結果ObjectCode有值，則回傳檢查不通過
        if (result != ValidationResult.Success)
        {
            return new ValidationResult(this.ErrorMessage);
        }

        return ValidationResult.Success;
    }
}
public class CanModifyUsageDeptAttribute(string associatedPropertyNames, string pidPropertyName) : ValidationAttribute
{
    private readonly string _associatedPropertyNames = associatedPropertyNames;
    private readonly string _pidPropertyName = pidPropertyName;

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        if (value == null) return ValidationResult.Success;

        var newValue = value?.ToString() ?? "";
        var pidProperty = validationContext.ObjectType.GetProperty(_pidPropertyName) ?? throw new ArgumentException("Property with this name not found");

        var associatedProperties = new List<string?>();
        if (!string.IsNullOrEmpty(_associatedPropertyNames))
        {
            associatedProperties.AddRange(
                _associatedPropertyNames.Split(",")
                    .Select(associatedPropertyName =>
                    {
                        // 如果已經包含冒號就直接使用 associatedPropertyName
                        if (associatedPropertyName.Contains(":"))
                        {
                            return associatedPropertyName;
                        }
                        else
                        {
                            // 否則使用 associatedPropertyName 取得 associatedPropertyValue 再回傳 $"{associatedPropertyName}:{associatedPropertyValue}"
                            var associatedProperty = validationContext.ObjectType.GetProperty(associatedPropertyName)
                                ?? throw new ArgumentException($"Property with {associatedPropertyName} name not found");
                            var associatedPropertyValue = associatedProperty.GetValue(validationContext.ObjectInstance)?.ToString();

                            // 如果 associatedPropertyValue 為 null 或空字串則返回 null
                            return string.IsNullOrEmpty(associatedPropertyValue) ? null : $"{associatedPropertyName}:{associatedPropertyValue}";
                        }
                    }).Where(x => x != null).ToList()
            );
        }

        var pid = pidProperty.GetValue(validationContext.ObjectInstance)?.ToString();

        if (_pidPropertyName.ToUpper() == "SID" && pid != null)
        {
            pid = $"{pid}{Constants.MMWAVE_PID_SUFFIX}";
        }

        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;
        // 取得Device table Pid = pid 的 UsageDepartCode
        string oldValue = validator.GetTableValue(associatedProperties, "Device", "Pid", pid, "UsageDepartCode").Result?.ToString() ?? "";
        // 檢查Device 是否已綁定對象代碼
        bool hasObjectCode = validator.ValidateHasValue(associatedProperties, "ObjectDevice", "Pid", pid, "ObjectCode").Result == null;

        // 如果裝置已有對象代碼，則不允許修改使用單位
        if (hasObjectCode)
        {
            if (newValue != oldValue)
            {
                return new ValidationResult("err.perm.param.UsageDept");
            }
        }

        return ValidationResult.Success;
    }
}
public class CanModifyManageDeptAttribute(string associatedPropertyNames, string pidColumnPropertyName, string usageDeptPropertyName) : ValidationAttribute
{
    private readonly string _associatedPropertyNames = associatedPropertyNames;
    private readonly string _pidColumnPropertyName = pidColumnPropertyName;
    private readonly string _usageDeptPropertyName = usageDeptPropertyName;

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        if (value == null) return ValidationResult.Success;

        var newValue = value?.ToString();

        var pidColumnProperty = validationContext.ObjectType.GetProperty(_pidColumnPropertyName) ?? throw new ArgumentException("Property with this name not found");
        var usageDeptProperty = validationContext.ObjectType.GetProperty(_usageDeptPropertyName) ?? throw new ArgumentException("Property with this name not found");

        var associatedProperties = new List<string?>();
        if (!string.IsNullOrEmpty(_associatedPropertyNames))
        {
            associatedProperties.AddRange(
                _associatedPropertyNames.Split(",")
                    .Select(associatedPropertyName =>
                    {
                        // 如果已經包含冒號就直接使用 associatedPropertyName
                        if (associatedPropertyName.Contains(":"))
                        {
                            return associatedPropertyName;
                        }
                        else
                        {
                            // 否則使用 associatedPropertyName 取得 associatedPropertyValue 再回傳 $"{associatedPropertyName}:{associatedPropertyValue}"
                            var associatedProperty = validationContext.ObjectType.GetProperty(associatedPropertyName)
                                ?? throw new ArgumentException($"Property with {associatedPropertyName} name not found");
                            var associatedPropertyValue = associatedProperty.GetValue(validationContext.ObjectInstance)?.ToString();

                            // 如果 associatedPropertyValue 為 null 或空字串則返回 null
                            return string.IsNullOrEmpty(associatedPropertyValue) ? null : $"{associatedPropertyName}:{associatedPropertyValue}";
                        }
                    }).Where(x => x != null).ToList()
            );
        }

        var pid = pidColumnProperty.GetValue(validationContext.ObjectInstance)?.ToString() ?? throw new InvalidOperationException("PidColumn is null");
        var usageDept = usageDeptProperty.GetValue(validationContext.ObjectInstance)?.ToString() ?? throw new InvalidOperationException("UsageDept is null");

        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;
        // 檢查Device 是否已綁定對象代碼
        bool hasObjectCode = validator.ValidateHasValue(associatedProperties, "ObjectDevice", "Pid", pid, "ObjectCode").Result == null;

        // 如果裝置已有對象代碼，則不允許修改管理單位
        return hasObjectCode ? new ValidationResult("err.perm.param.UsageDept") : ValidationResult.Success;
    }
}
public class UniqueAttribute : ValidationAttribute
{
    private readonly string _associatedPropertyNames;
    private readonly string _tableName;
    private readonly string _columnName;
    private readonly bool? _isActive;
    private readonly bool? _toUpperCase;
    private readonly bool? _skipAppCode;

    public UniqueAttribute(string associatedPropertyNames, string tableName, string columnName)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _tableName = tableName;
        _columnName = columnName;
    }

    public UniqueAttribute(string associatedPropertyNames, string tableName, string columnName, bool isActive)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _tableName = tableName;
        _columnName = columnName;
        _isActive = isActive;
    }

    public UniqueAttribute(string associatedPropertyNames, string tableName, string columnName, bool isActive, bool toUpperCase)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _tableName = tableName;
        _columnName = columnName;
        _isActive = isActive;
        _toUpperCase = toUpperCase;
    }

    public UniqueAttribute(string associatedPropertyNames, string tableName, string columnName, bool isActive, bool toUpperCase, bool skipAppCode)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _tableName = tableName;
        _columnName = columnName;
        _isActive = isActive;
        _toUpperCase = toUpperCase;
        _skipAppCode = skipAppCode;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        var associatedProperties = new List<string?>();
        if (!string.IsNullOrEmpty(_associatedPropertyNames))
        {
            associatedProperties.AddRange(
                _associatedPropertyNames.Split(",")
                    .Select(associatedPropertyName =>
                    {
                        // 如果已經包含冒號就直接使用 associatedPropertyName
                        if (associatedPropertyName.Contains(":"))
                        {
                            return associatedPropertyName;
                        }
                        else
                        {
                            // 否則使用 associatedPropertyName 取得 associatedPropertyValue 再回傳 $"{associatedPropertyName}:{associatedPropertyValue}"
                            var associatedProperty = validationContext.ObjectType.GetProperty(associatedPropertyName)
                                ?? throw new ArgumentException($"Property with {associatedPropertyName} name not found");
                            var associatedPropertyValue = associatedProperty.GetValue(validationContext.ObjectInstance)?.ToString();

                            // 如果 associatedPropertyValue 為 null 或空字串則返回 null
                            return string.IsNullOrEmpty(associatedPropertyValue) ? null : $"{associatedPropertyName}:{associatedPropertyValue}";
                        }
                    }).Where(x => x != null).ToList()
            );
        }

        if (value is System.Collections.IList list)
        {
            var propertyInfo = validationContext.ObjectType.GetProperty(_columnName);
            if (propertyInfo == null)
            {
                return new ValidationResult($"Property '{_columnName}' not found.");
            }

            var values = list.Cast<object>()
                             .Select(item => propertyInfo.GetValue(item, null))
                             .ToList();

            if (values.Count != values.Distinct().Count())
            {
                return new ValidationResult($"The field '{_columnName}' contains duplicate values.");
            }
        }

        var columnValue = value?.ToString();
        if (_toUpperCase.HasValue && _toUpperCase.Value)
        {
            columnValue = columnValue.ToUpper();
        }

        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;
        return validator.ValidateUnique(columnValue, associatedProperties, validationContext, _tableName, _columnName, _isActive, _skipAppCode).Result;
    }
}

public class UniqueWhenEqualsAttribute : ValidationAttribute
{
    private readonly string _propertyName;
    private readonly string[] _matchedValues;
    private readonly string _associatedPropertyNames;
    private readonly string _tableNames;
    private readonly string _columnNames;
    private readonly bool? _isActive;
    private readonly bool? _toUpperCase;
    private readonly bool? _skipAppCode;

    public UniqueWhenEqualsAttribute(string propertyName, string matchedValues, string associatedPropertyNames, string tableNames, string columnNames, bool isActive, bool toUpperCase)
    {
        _propertyName = propertyName;
        _matchedValues = matchedValues.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
        _associatedPropertyNames = associatedPropertyNames;
        _tableNames = tableNames;
        _columnNames = columnNames;
        _isActive = isActive;
        _toUpperCase = toUpperCase;
    }

    public UniqueWhenEqualsAttribute(string propertyName, string matchedValues, string associatedPropertyNames, string tableNames, string columnNames, bool isActive, bool toUpperCase, bool skipAppCode)
    {
        _propertyName = propertyName;
        _matchedValues = matchedValues.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
        _associatedPropertyNames = associatedPropertyNames;
        _tableNames = tableNames;
        _columnNames = columnNames;
        _isActive = isActive;
        _toUpperCase = toUpperCase;
        _skipAppCode = skipAppCode;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        // 取得 propertyValue
        var propertyValues = ExtractPropertyValues(validationContext.ObjectInstance, validationContext.ObjectType, _propertyName);

        // 如果 propertyValues 不存在任何 value 包含在 _matchedValues 裡則無需驗證
        if (!propertyValues.Any(v => _matchedValues.Contains(v)))
        {
            return ValidationResult.Success;
        }

        var matchedIndex = _matchedValues
            .Select((value, index) => new { value, index })
            .Where(x => propertyValues.Any(v => x.value.Contains(v)))
            .Select(x => x.index)
            .First();
        
        var _tableName = _tableNames.Split(",")[matchedIndex];
        var _columnName = _columnNames.Split(",")[matchedIndex];

        var associatedProperties = new List<string?>();
        if (!string.IsNullOrEmpty(_associatedPropertyNames))
        {
            associatedProperties.AddRange(
                _associatedPropertyNames.Split(",")
                    .Select(associatedPropertyName =>
                    {
                        // 如果已經包含冒號就直接使用 associatedPropertyName
                        if (associatedPropertyName.Contains(":"))
                        {
                            return associatedPropertyName;
                        }
                        else
                        {
                            // 否則使用 associatedPropertyName 取得 associatedPropertyValue 再回傳 $"{associatedPropertyName}:{associatedPropertyValue}"
                            var associatedProperty = validationContext.ObjectType.GetProperty(associatedPropertyName)
                                ?? throw new ArgumentException($"Property with {associatedPropertyName} name not found");
                            var associatedPropertyValue = associatedProperty.GetValue(validationContext.ObjectInstance)?.ToString();

                            // 如果 associatedPropertyValue 為 null 或空字串則返回 null
                            return string.IsNullOrEmpty(associatedPropertyValue) ? null : $"{associatedPropertyName}:{associatedPropertyValue}";
                        }
                    }).Where(x => x != null).ToList()
            );
        }

        // 如果 value 為 null 則略過
        if (value == null)
        {
            return ValidationResult.Success;
        }

        var columnValueStr = value?.ToString();
        if (_toUpperCase.HasValue && _toUpperCase.Value)
        {
            columnValueStr = columnValueStr.ToUpper();
        }

        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;
        foreach (var columnValue in columnValueStr.Split(","))
        {
            ValidationResult result = validator.ValidateUnique(columnValue, associatedProperties, validationContext, _tableName, _columnName, _isActive, _skipAppCode).Result;
            if (result != ValidationResult.Success)
                return new ValidationResult(this.ErrorMessage);
        }

        return ValidationResult.Success;
    }

    private HashSet<string> ExtractPropertyValues(object instance, Type instanceType, string propertyPath)
    {
        var propertyValues = new HashSet<string>(); // 使用 HashSet 避免重複資料
        var propertyParts = propertyPath.Split('.');

        object currentObject = instance;
        Type currentType = instanceType;

        for (int i = 0; i < propertyParts.Length; i++)
        {
            // 取得當前屬性資訊
            var propertyInfo = currentType.GetProperty(propertyParts[i]);
            if (propertyInfo == null)
            {
                throw new ArgumentException($"無法在 {currentType.Name} 找到屬性 '{propertyParts[i]}'");
            }

            // 取得屬性值
            currentObject = propertyInfo.GetValue(currentObject);
            if (currentObject == null)
            {
                return propertyValues;  // 如果屬性值為 null，則直接返回結果
            }

            // 當處理到倒數第二個屬性並且屬性是集合類型時，迭代集合
            if (i == propertyParts.Length - 2 && currentObject is IEnumerable enumerable)
            {
                foreach (var item in enumerable)
                {
                    // 取得集合中每個項目的最終屬性
                    var itemPropertyInfo = item.GetType().GetProperty(propertyParts.Last());
                    if (itemPropertyInfo != null)
                    {
                        var propertyValue = itemPropertyInfo.GetValue(item)?.ToString();
                        if (propertyValue != null)
                        {
                            propertyValues.Add(propertyValue); // 加入唯一的 propertyValue 值
                        }
                    }
                }
                return propertyValues;
            }

            // 更新當前類型為下一層屬性的類型
            currentType = currentObject.GetType();
        }

        // 如果是單一路徑並已取得最終值，直接檢查 currentObject
        if (currentObject is string finalValue && !string.IsNullOrWhiteSpace(finalValue))
        {
            propertyValues.Add(finalValue); // 加入唯一的最終值
        }

        return propertyValues;
    }
}

public class ExistsAttribute : ValidationAttribute
{
    private readonly string _associatedPropertyNames;
    private readonly string _tableName;
    private readonly string _columnName;
    private readonly bool? _isActive;
    private readonly string? _checkColumnIs;
    private readonly bool? _toUpperCase;
    private readonly bool? _containsMatch;
    private readonly bool? _skipAppCode;

    public ExistsAttribute(string associatedPropertyNames, string tableName, string columnName)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _tableName = tableName;
        _columnName = columnName;
    }

    public ExistsAttribute(string associatedPropertyNames, string tableName, string columnName, bool isActive, bool toUpperCase)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _tableName = tableName;
        _columnName = columnName;
        _isActive = isActive;
        _toUpperCase = toUpperCase;
    }

    public ExistsAttribute(string associatedPropertyNames, string tableName, string columnName, string checkColumnIs)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _tableName = tableName;
        _columnName = columnName;
        _checkColumnIs = checkColumnIs;
    }
    
    public ExistsAttribute(string associatedPropertyNames, string tableName, string columnName, bool containsMatch)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _tableName = tableName;
        _columnName = columnName;
        _containsMatch = containsMatch;
    }

    public ExistsAttribute(string associatedPropertyNames, string tableName, string columnName, bool isActive, bool toUpperCase, bool skipAppCode)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _tableName = tableName;
        _columnName = columnName;
        _isActive = isActive;
        _toUpperCase = toUpperCase;
        _skipAppCode = skipAppCode;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        var associatedProperties = new List<string?>();
        if (!string.IsNullOrEmpty(_associatedPropertyNames))
        {
            associatedProperties.AddRange(
                _associatedPropertyNames.Split(",")
                    .Select(associatedPropertyName =>
                    {
                        // 如果已經包含冒號就直接使用 associatedPropertyName
                        if (associatedPropertyName.Contains(":"))
                        {
                            return associatedPropertyName;
                        }
                        else
                        {
                            // 否則使用 associatedPropertyName 取得 associatedPropertyValue 再回傳 $"{associatedPropertyName}:{associatedPropertyValue}"
                            var associatedProperty = validationContext.ObjectType.GetProperty(associatedPropertyName)
                                ?? throw new ArgumentException($"Property with {associatedPropertyName} name not found");
                            var associatedPropertyValue = associatedProperty.GetValue(validationContext.ObjectInstance)?.ToString();

                            // 如果 associatedPropertyValue 為 null 或空字串則返回 null
                            return string.IsNullOrEmpty(associatedPropertyValue) ? null : $"{associatedPropertyName}:{associatedPropertyValue}";
                        }
                    }).Where(x => x != null).ToList()
            );
        }

        // 如果 value 為 null 則略過
        if (value == null)
        {
            return ValidationResult.Success;
        }

        var columnValueStr = value?.ToString();
        if (_toUpperCase.HasValue && _toUpperCase.Value)
        {
            columnValueStr = columnValueStr.ToUpper();
        }

        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;
        foreach (var columnValue in columnValueStr.Split(","))
        {
            ValidationResult result = validator.ValidateExists(columnValue, associatedProperties, _tableName, _columnName, _isActive, _checkColumnIs, _containsMatch??false, _skipAppCode).Result;
            if (result != ValidationResult.Success)
                return new ValidationResult(this.ErrorMessage);
        }

        return ValidationResult.Success;
    }
}

public class ExistsWhenEqualsAttribute : ValidationAttribute
{
    private readonly string _propertyName;
    private readonly string[] _matchedValues;
    private readonly string _associatedPropertyNames;
    private readonly string _tableNames;
    private readonly string _columnNames;
    private readonly bool? _isActive;
    private readonly string _toUpperCases;
    private readonly bool? _skipAppCode;

    public ExistsWhenEqualsAttribute(string propertyName, string matchedValues, string associatedPropertyNames, string tableNames, string columnNames, bool isActive, string toUpperCases)
    {
        _propertyName = propertyName;
        _matchedValues = matchedValues.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
        _associatedPropertyNames = associatedPropertyNames;
        _tableNames = tableNames;
        _columnNames = columnNames;
        _isActive = isActive;
        _toUpperCases = toUpperCases;
    }

    public ExistsWhenEqualsAttribute(string propertyName, string matchedValues, string associatedPropertyNames, string tableNames, string columnNames, bool isActive, string toUpperCases, bool skipAppCode)
    {
        _propertyName = propertyName;
        _matchedValues = matchedValues.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
        _associatedPropertyNames = associatedPropertyNames;
        _tableNames = tableNames;
        _columnNames = columnNames;
        _isActive = isActive;
        _toUpperCases = toUpperCases;
        _skipAppCode = skipAppCode;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        // 取得 propertyValue
        var propertyValues = ExtractPropertyValues(validationContext.ObjectInstance, validationContext.ObjectType, _propertyName);

        // 如果 propertyValues 不存在任何 value 包含在 _matchedValues 裡則無需驗證
        if (!propertyValues.Any(v => _matchedValues.Contains(v)))
        {
            return ValidationResult.Success;
        }

        var matchedIndex = _matchedValues
            .Select((value, index) => new { value, index })
            .Where(x => propertyValues.Any(v => x.value.Contains(v)))
            .Select(x => x.index)
            .First();
        
        var _tableName = _tableNames.Split(",")[matchedIndex];
        var _columnName = _columnNames.Split(",")[matchedIndex];

        var associatedProperties = new List<string?>();
        if (!string.IsNullOrEmpty(_associatedPropertyNames))
        {
            associatedProperties.AddRange(
                _associatedPropertyNames.Split(",")
                    .Select(associatedPropertyName =>
                    {
                        // 如果已經包含冒號就直接使用 associatedPropertyName
                        if (associatedPropertyName.Contains(":"))
                        {
                            return associatedPropertyName;
                        }
                        else
                        {
                            // 否則使用 associatedPropertyName 取得 associatedPropertyValue 再回傳 $"{associatedPropertyName}:{associatedPropertyValue}"
                            var associatedProperty = validationContext.ObjectType.GetProperty(associatedPropertyName)
                                ?? throw new ArgumentException($"Property with {associatedPropertyName} name not found");
                            var associatedPropertyValue = associatedProperty.GetValue(validationContext.ObjectInstance)?.ToString();

                            // 如果 associatedPropertyValue 為 null 或空字串則返回 null
                            return string.IsNullOrEmpty(associatedPropertyValue) ? null : $"{associatedPropertyName}:{associatedPropertyValue}";
                        }
                    }).Where(x => x != null).ToList()
            );
        }

        // 如果 value 為 null 則略過
        if (value == null)
        {
            return ValidationResult.Success;
        }

        bool toUpperCase = bool.Parse(_toUpperCases.Split(",")[matchedIndex]);

        var columnValueStr = value?.ToString();
        if (toUpperCase)
        {
            columnValueStr = columnValueStr.ToUpper();
        }

        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;
        foreach (var columnValue in columnValueStr.Split(","))
        {
            ValidationResult result = validator.ValidateExists(columnValue, associatedProperties, _tableName, _columnName, _isActive, null, false, _skipAppCode).Result;
            if (result != ValidationResult.Success)
                return new ValidationResult(this.ErrorMessage);
        }

        return ValidationResult.Success;
    }

    private HashSet<string> ExtractPropertyValues(object instance, Type instanceType, string propertyPath)
    {
        var propertyValues = new HashSet<string>(); // 使用 HashSet 避免重複資料
        var propertyParts = propertyPath.Split('.');

        object currentObject = instance;
        Type currentType = instanceType;

        for (int i = 0; i < propertyParts.Length; i++)
        {
            // 取得當前屬性資訊
            var propertyInfo = currentType.GetProperty(propertyParts[i]);
            if (propertyInfo == null)
            {
                throw new ArgumentException($"無法在 {currentType.Name} 找到屬性 '{propertyParts[i]}'");
            }

            // 取得屬性值
            currentObject = propertyInfo.GetValue(currentObject);
            if (currentObject == null)
            {
                return propertyValues;  // 如果屬性值為 null，則直接返回結果
            }

            // 當處理到倒數第二個屬性並且屬性是集合類型時，迭代集合
            if (i == propertyParts.Length - 2 && currentObject is IEnumerable enumerable)
            {
                foreach (var item in enumerable)
                {
                    // 取得集合中每個項目的最終屬性
                    var itemPropertyInfo = item.GetType().GetProperty(propertyParts.Last());
                    if (itemPropertyInfo != null)
                    {
                        var propertyValue = itemPropertyInfo.GetValue(item)?.ToString();
                        if (propertyValue != null)
                        {
                            propertyValues.Add(propertyValue); // 加入唯一的 propertyValue 值
                        }
                    }
                }
                return propertyValues;
            }

            // 更新當前類型為下一層屬性的類型
            currentType = currentObject.GetType();
        }

        // 如果是單一路徑並已取得最終值，直接檢查 currentObject
        if (currentObject is string finalValue && !string.IsNullOrWhiteSpace(finalValue))
        {
            propertyValues.Add(finalValue); // 加入唯一的最終值
        }

        return propertyValues;
    }
}

public class IsUsageDeptAttribute(string areaCodePropertyName) : ValidationAttribute
{
    private readonly string _areaCodePropertyName = areaCodePropertyName;

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        var areaCodeProperty = validationContext.ObjectType.GetProperty(_areaCodePropertyName);
        if (areaCodeProperty == null)
        {
            throw new ArgumentException("Property with this name not found");
        }

        var areaCode = areaCodeProperty.GetValue(validationContext.ObjectInstance)?.ToString();
        var columnValue = value?.ToString();

        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;
        return validator.ValidateDepartmentByType(columnValue, areaCode, "UsageDept").Result;
    }
}
public class IsManagedDeptAttribute : ValidationAttribute
{
    private readonly string _areaCodePropertyName;

    public IsManagedDeptAttribute(string areaCodePropertyName)
    {
        _areaCodePropertyName = areaCodePropertyName;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        var areaCodeProperty = validationContext.ObjectType.GetProperty(_areaCodePropertyName);
        if (areaCodeProperty == null)
        {
            throw new ArgumentException("Property with this name not found");
        }

        var areaCode = areaCodeProperty.GetValue(validationContext.ObjectInstance)?.ToString();
        var columnValue = value?.ToString();

        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;
        return validator.ValidateDepartmentByType(columnValue, areaCode, "ManagedDept").Result;
    }
}

public class NumericAttribute : ValidationAttribute
{
    public override bool IsValid(object value)
    {
        if (value == null)
        {
            return true; // 如果允許 null 值，返回 true
        }

        string stringValue = value.ToString();

        // 使用正則表達式檢查
        string pattern = @"^[-+]?[0-9]*\.?[0-9]+$";
        if (Regex.IsMatch(stringValue, pattern))
        {
            return true;
        }

        // 使用 double.TryParse() 進行額外檢查
        return double.TryParse(stringValue, out _);
    }

    public override string FormatErrorMessage(string name)
    {
        return $"err.invalid.param.{name}";
    }
}

[AttributeUsage(AttributeTargets.Parameter | AttributeTargets.Property, AllowMultiple = true)]
public class ListDuplicateAttribute : ValidationAttribute
{
    public string ListPropertyName { get; }
    public string[] PropertyNames { get; }

    public ListDuplicateAttribute(string firstParameter, params string[] additionalParameters)
    {
        if (additionalParameters.Length == 0)
        {
            // 如果只有一個參數，將其視為 PropertyNames
            ListPropertyName = null;
            PropertyNames = new[] { firstParameter };
        }
        else
        {
            // 如果有多個參數，將第一個參數設為 ListPropertyName，其餘作為 PropertyNames
            ListPropertyName = firstParameter;
            PropertyNames = additionalParameters;
        }
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        // 根據 _listPropertyName 判斷要使用的 List 資料來源
        IEnumerable list;
        if (!string.IsNullOrEmpty(ListPropertyName))
        {
            var listProperty = validationContext.ObjectType.GetProperty(ListPropertyName);
            if (listProperty == null)
            {
                throw new ArgumentException($"Property '{ListPropertyName}' not found on {validationContext.ObjectType.Name}");
            }
            list = listProperty.GetValue(validationContext.ObjectInstance) as IEnumerable;
        }
        else
        {
            list = value as IEnumerable;
        }

        if (list == null)
        {
            return ValidationResult.Success; // 如果沒有 List 資料則返回成功
        }

        var items = list.Cast<object>().ToList();
        var duplicatesWithIndices = items
            .Select((item, index) => new { Item = item, Index = index })
            .GroupBy(x => string.Join("|", PropertyNames.Select(pn =>
                x.Item.GetType().GetProperty(pn, BindingFlags.Public | BindingFlags.Instance)
                ?.GetValue(x.Item, null)?.ToString() ?? "")))
            .Where(g => g.Count() > 1 && !string.IsNullOrEmpty(g.Key))
            .Select(g => new
            {
                g.Key,
                Indices = g.Select(x => x.Index).ToList()
            })
            .ToList();

        if (duplicatesWithIndices.Any())
        {
            var duplicateMessages = duplicatesWithIndices
                .Select(d => $"{this.ErrorMessage.Substring(0, this.ErrorMessage.IndexOf("param") + 5)}.{string.Join(".", PropertyNames)}")
                .ToList();

            var errorMessage = string.Join(" ", duplicateMessages);

            // 產生 [index].propertyName 格式的 fieldKey
            var memberNames = duplicatesWithIndices
                .SelectMany(d => d.Indices.Select(index =>
                    string.Join(", ", PropertyNames.Select(property => $"[{index}].{property}"))
                ))
                .ToList();

            return new ValidationResult(errorMessage, memberNames);
        }

        return ValidationResult.Success;
    }
}

[AttributeUsage(AttributeTargets.Parameter | AttributeTargets.Property, AllowMultiple = true)]
public class RequestNotNullOrEmptyAttribute : ValidationAttribute
{
    public string? ListPropertyName { get; }

    public RequestNotNullOrEmptyAttribute()
    {
        ListPropertyName = null;
    }

    public RequestNotNullOrEmptyAttribute(string ListPropertyName)
    {
        this.ListPropertyName = ListPropertyName;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        if (!string.IsNullOrEmpty(ListPropertyName))
        {
            var listProperty = validationContext.ObjectType.GetProperty(ListPropertyName);
            if (listProperty == null)
            {
                throw new ArgumentException($"Property '{ListPropertyName}' not found on {validationContext.ObjectType.Name}");
            }

            var list = listProperty.GetValue(validationContext.ObjectInstance) as IEnumerable;
            if (list == null || !list.GetEnumerator().MoveNext())
            {
                // 確保錯誤綁定到正確的鍵
                var memberName = validationContext.MemberName ?? validationContext.DisplayName;
                return new ValidationResult(ErrorMessage, new[] { memberName });
            }
        }
        else
        {
            // 檢查是否為 null 或空列表
            if (value == null || (value is IEnumerable list && !list.GetEnumerator().MoveNext()))
            {
                // 確保錯誤綁定到正確的鍵
                var memberName = validationContext.MemberName ?? validationContext.DisplayName;
                return new ValidationResult(ErrorMessage, new[] { memberName });
            }
        }

        return ValidationResult.Success;
    }
}

public class ListAllExistsAttribute : ValidationAttribute
{
    private readonly string _associatedPropertyNames;
    private readonly string _tableName;
    private readonly string _columnName;
    private readonly bool? _isActive;
    private readonly string? _checkColumnIs;
    private readonly string _excludeValue;
    private readonly bool? _skipAppCode;

    public ListAllExistsAttribute(string associatedPropertyNames, string tableName, string columnName)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _tableName = tableName;
        _columnName = columnName;
    }

    public ListAllExistsAttribute(string associatedPropertyNames, string tableName, string columnName, bool isActive)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _tableName = tableName;
        _columnName = columnName;
        _isActive = isActive;
    }
    public ListAllExistsAttribute(string associatedPropertyNames, string tableName, string columnName, bool isActive, bool skipAppCode)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _tableName = tableName;
        _columnName = columnName;
        _isActive = isActive;
        _skipAppCode = skipAppCode;
    }

    public ListAllExistsAttribute(string associatedPropertyNames, string tableName, string columnName, bool? isActive, string checkColumnIs)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _tableName = tableName;
        _columnName = columnName;
        _isActive = isActive;
        _checkColumnIs = checkColumnIs;
    }

    public ListAllExistsAttribute(string associatedPropertyNames, string tableName, string columnName, string excludeValue)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _tableName = tableName;
        _columnName = columnName;
        _excludeValue = excludeValue;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        var associatedProperties = new List<string?>();
        if (!string.IsNullOrEmpty(_associatedPropertyNames))
        {
            associatedProperties.AddRange(
                _associatedPropertyNames.Split(",")
                    .Select(associatedPropertyName =>
                    {
                        // 如果已經包含冒號就直接使用 associatedPropertyName
                        if (associatedPropertyName.Contains(":"))
                        {
                            return associatedPropertyName;
                        }
                        else
                        {
                            // 否則使用 associatedPropertyName 取得 associatedPropertyValue 再回傳 $"{associatedPropertyName}:{associatedPropertyValue}"
                            var associatedProperty = validationContext.ObjectType.GetProperty(associatedPropertyName)
                                ?? throw new ArgumentException($"Property with {associatedPropertyName} name not found");
                            var associatedPropertyValue = associatedProperty.GetValue(validationContext.ObjectInstance)?.ToString();

                            // 如果 associatedPropertyValue 為 null 或空字串則返回 null
                            return string.IsNullOrEmpty(associatedPropertyValue) ? null : $"{associatedPropertyName}:{associatedPropertyValue}";
                        }
                    }).Where(x => x != null).ToList()
            );
        }

        // 嘗試將 value 轉換為 List<string> 或從 List<ObjectDevice> 提取出 columnName 屬性值組成 List<string>
        List<string> list = null;

        // 先判斷 value 是 List<string>
        if (value is List<string> stringList)
        {
            list = stringList;
        }
        else if (value is IEnumerable<object> objectList) // 如果 value 是 IEnumerable<object>，則遍歷其每個元素並從中提取出對應 columnName 的屬性值
        {
            list = objectList
                .Select(obj => obj.GetType().GetProperty(_columnName)?.GetValue(obj)?.ToString())
                .Where(value => value != null)
                .ToList();
        }

        // 如果 list 是空的或 value 不是 List<string>，則直接返回 ValidationResult.Success
        if (list == null || !list.Any())
        {
            return ValidationResult.Success;
        }

        // 如果 _excludeValue 不為空或 Null 的話判斷是否只包含 _excludeValue
        if (!string.IsNullOrWhiteSpace(_excludeValue) && list.Contains(_excludeValue) && list.Any(v => v != _excludeValue))
        {
            return new ValidationResult(ErrorMessage);
        }

        // 取得 CustomValidator 實例
        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;
        if (validator == null)
        {
            throw new InvalidOperationException("CustomValidator service not found.");
        }

        // 驗證每個清單項目是否存在於資料庫
        var invalidItems = new List<string>();
        foreach (var item in list)
        {
            // 如果項目等於 "ALL" 就略過
            if (item == "ALL")
                continue;
                
            var result = validator.ValidateExists(item, associatedProperties, _tableName, _columnName, _isActive, _checkColumnIs, false, _skipAppCode).Result;
            if (result != ValidationResult.Success)
            {
                invalidItems.Add(item);
            }
        }

        // 如果有無效項目，回傳錯誤訊息
        if (invalidItems.Any())
        {
            // 使用分號將無效項目拼接成錯誤訊息
            var errorMessage = $"{ErrorMessage}:[{string.Join(", ", invalidItems)}]";
            return new ValidationResult(errorMessage, new[] { validationContext.MemberName });
        }

        return ValidationResult.Success;
    }
}

public class ListAllUniqueAttribute : ValidationAttribute
{
    private readonly string _associatedPropertyNames;
    private readonly string _tableName;
    private readonly string _columnName;
    private readonly bool? _isActive;
    private readonly bool? _toUpperCase;
    private readonly bool? _skipAppCode;

    public ListAllUniqueAttribute(string associatedPropertyNames, string tableName, string columnName)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _tableName = tableName;
        _columnName = columnName;
    }

    public ListAllUniqueAttribute(string associatedPropertyNames, string tableName, string columnName, bool isActive)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _tableName = tableName;
        _columnName = columnName;
        _isActive = isActive;
    }

    public ListAllUniqueAttribute(string associatedPropertyNames, string tableName, string columnName, bool isActive, bool toUpperCase)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _tableName = tableName;
        _columnName = columnName;
        _isActive = isActive;
        _toUpperCase = toUpperCase;
    }

    public ListAllUniqueAttribute(string associatedPropertyNames, string tableName, string columnName, bool isActive, bool toUpperCase, bool skipAppCode)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _tableName = tableName;
        _columnName = columnName;
        _isActive = isActive;
        _toUpperCase = toUpperCase;
        _skipAppCode = skipAppCode;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        var associatedProperties = new List<string?>();
        if (!string.IsNullOrEmpty(_associatedPropertyNames))
        {
            associatedProperties.AddRange(
                _associatedPropertyNames.Split(",")
                    .Select(associatedPropertyName =>
                    {
                        // 如果已經包含冒號就直接使用 associatedPropertyName
                        if (associatedPropertyName.Contains(":"))
                        {
                            return associatedPropertyName;
                        }
                        else
                        {
                            // 否則使用 associatedPropertyName 取得 associatedPropertyValue 再回傳 $"{associatedPropertyName}:{associatedPropertyValue}"
                            var associatedProperty = validationContext.ObjectType.GetProperty(associatedPropertyName)
                                ?? throw new ArgumentException($"Property with {associatedPropertyName} name not found");
                            var associatedPropertyValue = associatedProperty.GetValue(validationContext.ObjectInstance)?.ToString();

                            // 如果 associatedPropertyValue 為 null 或空字串則返回 null
                            return string.IsNullOrEmpty(associatedPropertyValue) ? null : $"{associatedPropertyName}:{associatedPropertyValue}";
                        }
                    }).Where(x => x != null).ToList()
            );
        }

        // 嘗試將 value 轉換為 List<string> 或從 List<ObjectDevice> 提取出 columnName 屬性值組成 List<string>
        List<string> list = null;

        // 先判斷 value 是 List<string>
        if (value is List<string> stringList)
        {
            list = stringList;
        }
        else if (value is IEnumerable<object> objectList) // 如果 value 是 IEnumerable<object>，則遍歷其每個元素並從中提取出對應 columnName 的屬性值
        {
            list = objectList
                .Select(obj => obj.GetType().GetProperty(_columnName)?.GetValue(obj)?.ToString())
                .Where(value => value != null)
                .ToList();
        }

        // 如果 list 是空的或 value 不是 List<string>，則直接返回 ValidationResult.Success
        if (list == null || !list.Any())
        {
            return ValidationResult.Success;
        }

        // 取得 CustomValidator 實例
        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;
        if (validator == null)
        {
            throw new InvalidOperationException("CustomValidator service not found.");
        }

        // 驗證每個清單項目是否已經存在於資料庫
        var invalidItems = new List<string>();
        foreach (var item in list)
        {
            var columnValue = item?.ToString();
            if (_toUpperCase.HasValue && _toUpperCase.Value)
            {
                columnValue = columnValue.ToUpper();
            }

            var result = validator.ValidateUnique(columnValue, associatedProperties, validationContext, _tableName, _columnName, _isActive, _skipAppCode).Result;
            if (result != ValidationResult.Success)
            {
                invalidItems.Add(item);
            }
        }

        // 如果有無效項目，回傳錯誤訊息
        if (invalidItems.Any())
        {
            // 使用分號將無效項目拼接成錯誤訊息
            var errorMessage = $"{ErrorMessage}:[{string.Join(", ", invalidItems)}]";
            return new ValidationResult(errorMessage, new[] { validationContext.MemberName });
        }

        return ValidationResult.Success;
    }
}

public class HasReferenceAttribute(string associatedPropertyNames, string tableNames, string columnNames) : ValidationAttribute
{
    private readonly string _associatedPropertyNames = associatedPropertyNames;
    private readonly string[] _tableNames = tableNames.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
    private readonly string[] _columnNames = columnNames.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
    
    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        if (value == null) return ValidationResult.Success;

        var newValue = value?.ToString();

        var associatedProperties = new List<string?>();
        if (!string.IsNullOrEmpty(_associatedPropertyNames))
        {
            associatedProperties.AddRange(
                _associatedPropertyNames.Split(",")
                    .Select(associatedPropertyName =>
                    {
                        // 如果已經包含冒號就直接使用 associatedPropertyName
                        if (associatedPropertyName.Contains(":"))
                        {
                            return associatedPropertyName;
                        }
                        else
                        {
                            // 否則使用 associatedPropertyName 取得 associatedPropertyValue 再回傳 $"{associatedPropertyName}:{associatedPropertyValue}"
                            var associatedProperty = validationContext.ObjectType.GetProperty(associatedPropertyName)
                                ?? throw new ArgumentException($"Property with {associatedPropertyName} name not found");
                            var associatedPropertyValue = associatedProperty.GetValue(validationContext.ObjectInstance)?.ToString();

                            // 如果 associatedPropertyValue 為 null 或空字串則返回 null
                            return string.IsNullOrEmpty(associatedPropertyValue) ? null : $"{associatedPropertyName}:{associatedPropertyValue}";
                        }
                    }).Where(x => x != null).ToList()
            );
        }

        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;

        // 逐一檢查每個 _tableName 的 _columnName 是否存在 value 的 AppCode 欄位一定存在
        bool hasReference = false;
        for (int i = 0; i < _tableNames.Count(); i++)
        {
            string _tableName = _tableNames[i];
            string _columnName = _columnNames[i];

            hasReference |= validator.ValidateHasValue(associatedProperties, _tableName, _columnName, value.ToString(), "AppCode").Result == null;
        }

        // 如果有存在關聯回傳驗證錯誤
        return hasReference ? new ValidationResult(this.ErrorMessage) : ValidationResult.Success;
    }
}

public class HasReferenceWhenEqualsAttribute(string propertyName, string matchedValues, string associatedPropertyNames, string tableNames, string columnNames) : ValidationAttribute
{
    private readonly string _propertyName = propertyName;
    private readonly string[] _matchedValues = matchedValues.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
    private readonly string _associatedPropertyNames = associatedPropertyNames;
    private readonly string[] _tableNames = tableNames.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
    private readonly string[] _columnNames = columnNames.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
    
    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        if (value == null) return ValidationResult.Success;

        // 取得 propertyValue
        var propertyValues = ExtractPropertyValues(validationContext.ObjectInstance, validationContext.ObjectType, _propertyName);
        
        // 如果 propertyValues 不存在任何 value 包含在 _matchedValues 裡則無需驗證
        if (!propertyValues.Any(v => _matchedValues.Contains(v)))
        {
            return ValidationResult.Success;
        }

        var newValue = value?.ToString();

        var associatedProperties = new List<string?>();
        if (!string.IsNullOrEmpty(_associatedPropertyNames))
        {
            associatedProperties.AddRange(
                _associatedPropertyNames.Split(",")
                    .Select(associatedPropertyName =>
                    {
                        // 如果已經包含冒號就直接使用 associatedPropertyName
                        if (associatedPropertyName.Contains(":"))
                        {
                            return associatedPropertyName;
                        }
                        else
                        {
                            // 否則使用 associatedPropertyName 取得 associatedPropertyValue 再回傳 $"{associatedPropertyName}:{associatedPropertyValue}"
                            var associatedProperty = validationContext.ObjectType.GetProperty(associatedPropertyName)
                                ?? throw new ArgumentException($"Property with {associatedPropertyName} name not found");
                            var associatedPropertyValue = associatedProperty.GetValue(validationContext.ObjectInstance)?.ToString();

                            // 如果 associatedPropertyValue 為 null 或空字串則返回 null
                            return string.IsNullOrEmpty(associatedPropertyValue) ? null : $"{associatedPropertyName}:{associatedPropertyValue}";
                        }
                    }).Where(x => x != null).ToList()
            );
        }

        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;

        // 逐一檢查每個 _tableName 的 _columnName 是否存在 value 的 AppCode 欄位一定存在
        bool hasReference = false;
        for (int i = 0; i < _tableNames.Count(); i++)
        {
            string _tableName = _tableNames[i];
            string _columnName = _columnNames[i];

            hasReference |= validator.ValidateHasValue(associatedProperties, _tableName, _columnName, value.ToString(), "AppCode").Result == null;
        }

        // 如果有存在關聯回傳驗證錯誤
        return hasReference ? new ValidationResult(this.ErrorMessage) : ValidationResult.Success;
    }

    private HashSet<string> ExtractPropertyValues(object instance, Type instanceType, string propertyPath)
    {
        var propertyValues = new HashSet<string>(); // 使用 HashSet 避免重複資料
        var propertyParts = propertyPath.Split('.');

        object currentObject = instance;
        Type currentType = instanceType;

        for (int i = 0; i < propertyParts.Length; i++)
        {
            // 取得當前屬性資訊
            var propertyInfo = currentType.GetProperty(propertyParts[i]);
            if (propertyInfo == null)
            {
                throw new ArgumentException($"無法在 {currentType.Name} 找到屬性 '{propertyParts[i]}'");
            }

            // 取得屬性值
            currentObject = propertyInfo.GetValue(currentObject);
            if (currentObject == null)
            {
                return propertyValues;  // 如果屬性值為 null，則直接返回結果
            }

            // 當處理到倒數第二個屬性並且屬性是集合類型時，迭代集合
            if (i == propertyParts.Length - 2 && currentObject is IEnumerable enumerable)
            {
                foreach (var item in enumerable)
                {
                    // 取得集合中每個項目的最終屬性
                    var itemPropertyInfo = item.GetType().GetProperty(propertyParts.Last());
                    if (itemPropertyInfo != null)
                    {
                        var propertyValue = itemPropertyInfo.GetValue(item)?.ToString();
                        if (propertyValue != null)
                        {
                            propertyValues.Add(propertyValue); // 加入唯一的 propertyValue 值
                        }
                    }
                }
                return propertyValues;
            }

            // 更新當前類型為下一層屬性的類型
            currentType = currentObject.GetType();
        }

        // 如果是單一路徑並已取得最終值，直接檢查 currentObject
        if (currentObject is string finalValue && !string.IsNullOrWhiteSpace(finalValue))
        {
            propertyValues.Add(finalValue); // 加入唯一的最終值
        }

        return propertyValues;
    }
}

public class IsMonitorAlarmColorAttribute(string associatedPropertyNames) : ValidationAttribute
{
    private readonly string _associatedPropertyNames = associatedPropertyNames;

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        if (value == null) return ValidationResult.Success;

        var associatedProperties = new List<string?>();
        if (!string.IsNullOrEmpty(_associatedPropertyNames))
        {
            associatedProperties.AddRange(
                _associatedPropertyNames.Split(",")
                    .Select(associatedPropertyName =>
                    {
                        // 如果已經包含冒號就直接使用 associatedPropertyName
                        if (associatedPropertyName.Contains(":"))
                        {
                            return associatedPropertyName;
                        }
                        else
                        {
                            // 否則使用 associatedPropertyName 取得 associatedPropertyValue 再回傳 $"{associatedPropertyName}:{associatedPropertyValue}"
                            var associatedProperty = validationContext.ObjectType.GetProperty(associatedPropertyName)
                                ?? throw new ArgumentException($"Property with {associatedPropertyName} name not found");
                            var associatedPropertyValue = associatedProperty.GetValue(validationContext.ObjectInstance)?.ToString();

                            // 如果 associatedPropertyValue 為 null 或空字串則返回 null
                            return string.IsNullOrEmpty(associatedPropertyValue) ? null : $"{associatedPropertyName}:{associatedPropertyValue}";
                        }
                    }).Where(x => x != null).ToList()
            );
        }

        var urgColorValue = value?.ToString();

        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;

        // 取得 SysParameters 中的 paraCode = MonitorAlarmColorList 的 paraValue
        string monitorAlarmColorStr = validator.GetTableValue(associatedProperties, "SysParameters", "ParaCode", "MonitorAlarmColorList", "ParaValue").Result?.ToString() ?? "";

        // 如果 paraCode = MonitorAlarmColorList 不存在 SysParameters 則報參數錯誤
        if (string.IsNullOrEmpty(monitorAlarmColorStr))
        {
            throw new ArgumentException("MonitorAlarmColorList ParaCode was not found");
        }

        // 將 MonitorAlarmColorList 對應的 paraValue 字串反序列化為 List<stirng>
        var monitorAlarmColorList = JsonSerializer.Deserialize<List<string>>(monitorAlarmColorStr);

        // 檢查 urgColorValue 是否存在於 MonitorAlarmColorList 監控卡片告警顏色選項
        return !monitorAlarmColorList.Any(e => e == urgColorValue) ? new ValidationResult(this.ErrorMessage) : ValidationResult.Success;
    }
}

public class HasTempHudDeviceTypeAttribute : ValidationAttribute
{
    private readonly string _urgColorPropertyName;
    private readonly string _objectDeviceListPropertyName;

    public HasTempHudDeviceTypeAttribute(string urgColorPropertyName, string objectDeviceListPropertyName)
    {
        _urgColorPropertyName = urgColorPropertyName;
        _objectDeviceListPropertyName = objectDeviceListPropertyName;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        // 確認 UrgColor 屬性是否存在
        var urgColorProperty = validationContext.ObjectType.GetProperty(_urgColorPropertyName);
        if (urgColorProperty == null)
        {
            throw new ArgumentException($"Property '{_urgColorPropertyName}' not found on {validationContext.ObjectType.Name}");
        }

        // 取得 UrgColor 的值
        var urgColor = urgColorProperty.GetValue(validationContext.ObjectInstance)?.ToString();

        // 如果 urgColor 不為空則返回，不需執行後續檢查
        if (!string.IsNullOrEmpty(urgColor))
        {
            return ValidationResult.Success;
        }

        // 確認 ObjectDeviceList 屬性是否存在並取得其值
        var objectDeviceListProperty = validationContext.ObjectType.GetProperty(_objectDeviceListPropertyName);
        if (objectDeviceListProperty == null)
        {
            throw new ArgumentException($"Property '{_objectDeviceListPropertyName}' not found on {validationContext.ObjectType.Name}");
        }

        // 將值轉型為 List<InCreateObjectDevice> 或 List<inUpdateObjectDevice>
        var inCreateObjectDeviceList = objectDeviceListProperty.GetValue(validationContext.ObjectInstance) as IEnumerable<InCreateObjectDevice>;
        var inUpdateObjectDeviceList = objectDeviceListProperty.GetValue(validationContext.ObjectInstance) as IEnumerable<InUpdateObjectDevice>;
        if ((inCreateObjectDeviceList == null || !inCreateObjectDeviceList.Any()) && (inUpdateObjectDeviceList == null || !inUpdateObjectDeviceList.Any()))
        {
            return ValidationResult.Success; // 無需驗證時返回成功
        }

        // 確認 value 是否為有效的 AreaCode
        var areaCode = value as string;
        if (string.IsNullOrEmpty(areaCode))
        {
            return ValidationResult.Success; // 無需驗證時傳回成功
        }

        var associatedProperties = new List<string>();
        associatedProperties.Add($"AreaCode:{areaCode}");

        // 取得 CustomValidator 服務
        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;
        if (validator == null)
        {
            throw new InvalidOperationException("CustomValidator service not found.");
        }

        // 取得 SysParameters 中的 paraCode = TempHudDeviceType 的 paraValue
        string tempHudDeviceTypeStr = validator.GetTableValue(associatedProperties, "SysParameters", "ParaCode", "TempHudDeviceType", "ParaValue").Result?.ToString() ?? "";

        // 如果 paraCode = TempHudDeviceType 不存在 SysParameters 則報參數錯誤
        if (string.IsNullOrEmpty(tempHudDeviceTypeStr))
        {
            throw new ArgumentException("TempHudDeviceType ParaCode was not found");
        }

        // 將 TempHudDeviceType 對應的 paraValue 字串反序列化為 List<stirng>
        var tempHudDeviceTypeList = JsonSerializer.Deserialize<List<string>>(tempHudDeviceTypeStr);

        // 從 Fusion 取得 deviceTypes 列表
        var deviceTypes = validator.GetDeviceTypeList();

        // 驗證是否有綁定 TempHudDeviceType 的 Pid
        var invalidItems = new List<string>();
        if (inCreateObjectDeviceList != null && inCreateObjectDeviceList.Any())
        {
            foreach (var od in inCreateObjectDeviceList)
            {
                if (!string.IsNullOrWhiteSpace(od.Pid))
                {
                    string deviceType = validator.GetTableValue(associatedProperties, "Device", "Pid", od.Pid, "DeviceType").Result?.ToString() ?? "";
                    if (!string.IsNullOrWhiteSpace(deviceType))
                    {
                        var dt = deviceTypes.FirstOrDefault(e => e.type == deviceType);

                        // 檢查DeviceType是否存在TempHudDeviceTypeList
                        if (dt != null && tempHudDeviceTypeList.Any(e => e == dt.type))
                        {
                            invalidItems.Add(od.Pid);
                        }
                    }
                }
            }
        }
        else if (inUpdateObjectDeviceList != null && inUpdateObjectDeviceList.Any())
        {
            foreach (var od in inUpdateObjectDeviceList)
            {
                if (!string.IsNullOrWhiteSpace(od.Pid))
                {
                    string deviceType = validator.GetTableValue(associatedProperties, "Device", "Pid", od.Pid, "DeviceType").Result?.ToString() ?? "";
                    if (!string.IsNullOrWhiteSpace(deviceType))
                    {
                        var dt = deviceTypes.FirstOrDefault(e => e.type == deviceType);

                        // 檢查DeviceType是否存在TempHudDeviceTypeList
                        if (dt != null && tempHudDeviceTypeList.Any(e => e == dt.type))
                        {
                            invalidItems.Add(od.Pid);
                        }
                    }
                }
            }
        }

        // 如果有無效項目，回傳錯誤訊息
        if (invalidItems.Any())
        {
            // 使用分號將無效項目拼接成錯誤訊息
            var errorMessage = $"{ErrorMessage}:[{string.Join(", ", invalidItems)}]";
            return new ValidationResult(errorMessage, new[] { validationContext.MemberName });
        }

        return ValidationResult.Success;
    }
}

[AttributeUsage(AttributeTargets.Parameter | AttributeTargets.Property, AllowMultiple = true)]
public class SetToListAttribute : ValidationAttribute
{
    private readonly string _sourcePropertyName;
    private readonly string[] _listPropertyNames;

    public SetToListAttribute(string sourcePropertyName, params string[] listPropertyNames)
    {
        _sourcePropertyName = sourcePropertyName;
        _listPropertyNames = listPropertyNames;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        // 確認來源屬性是否存在
        var sourceProperty = validationContext.ObjectType.GetProperty(_sourcePropertyName);
        if (sourceProperty == null)
        {
            throw new ArgumentException($"Property '{_sourcePropertyName}' not found on {validationContext.ObjectType.Name}");
        }

        // 取得來源屬性的值
        var sourceValue = sourceProperty.GetValue(validationContext.ObjectInstance)?.ToString();
        if (string.IsNullOrEmpty(sourceValue))
        {
            return ValidationResult.Success; // 如果來源值為空，則不處理
        }

        // 遍歷所有指定的 List 屬性名稱
        foreach (var listPropertyName in _listPropertyNames)
        {
            // 確認 List 屬性是否存在
            var listProperty = validationContext.ObjectType.GetProperty(listPropertyName);
            if (listProperty == null)
            {
                throw new ArgumentException($"Property '{listPropertyName}' not found on {validationContext.ObjectType.Name}");
            }

            // 取得 List 屬性的值
            var listValue = listProperty.GetValue(validationContext.ObjectInstance);
            if (listValue is IEnumerable<object> objectList)
            {
                foreach (var obj in objectList)
                {
                    // 確認每個物件是否具有指定的來源屬性名稱並且可以寫入
                    var targetPropertyInObject = obj.GetType().GetProperty(_sourcePropertyName);
                    if (targetPropertyInObject != null && targetPropertyInObject.CanWrite)
                    {
                        targetPropertyInObject.SetValue(obj, sourceValue);
                    }
                }
            }
        }

        return ValidationResult.Success;
    }
}

public class SetDeviceTypeAttribute : ValidationAttribute
{
    private readonly string _objectDeviceListPropertyName;

    public SetDeviceTypeAttribute(string objectDeviceListPropertyName)
    {
        _objectDeviceListPropertyName = objectDeviceListPropertyName;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        // 印出當前 ValidationContext 的型態資訊
        Console.WriteLine($"ValidationContext Type: {validationContext.ObjectType.FullName}");

        // 確認 ObjectDeviceList 屬性是否存在
        var objectDeviceListProperty = validationContext.ObjectType.GetProperty(_objectDeviceListPropertyName);
        if (objectDeviceListProperty == null)
        {
            throw new ArgumentException($"Property '{_objectDeviceListPropertyName}' not found on {validationContext.ObjectType.Name}");
        }

        // 將值轉型為 List<InCreateObjectDevice> 或 List<inUpdateObjectDevice>
        var inCreateObjectDeviceList = objectDeviceListProperty.GetValue(validationContext.ObjectInstance) as IEnumerable<InCreateObjectDevice>;
        var inUpdateObjectDeviceList = objectDeviceListProperty.GetValue(validationContext.ObjectInstance) as IEnumerable<InUpdateObjectDevice>;
        if ((inCreateObjectDeviceList == null || !inCreateObjectDeviceList.Any()) && (inUpdateObjectDeviceList == null || !inUpdateObjectDeviceList.Any()))
        {
            return ValidationResult.Success; // 無需驗證時返回成功
        }

        // 確認 value 是否為有效的 AreaCode
        var areaCode = value as string;
        if (string.IsNullOrEmpty(areaCode))
        {
            return ValidationResult.Success; // 無需驗證時傳回成功
        }

        var associatedProperties = new List<string>();
        associatedProperties.Add($"AreaCode:{areaCode}");

        // 取得 CustomValidator 實例
        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;
        if (validator == null)
        {
            throw new InvalidOperationException("CustomValidator service not found.");
        }

        // 從 Fusion 取得 deviceTypes 列表
        var deviceTypes = validator.GetDeviceTypeList();

        // 為每個 ObjectDevice 設定 DeviceType
        if (inCreateObjectDeviceList != null && inCreateObjectDeviceList.Any())
        {
            foreach (var od in inCreateObjectDeviceList)
            {
                if (!string.IsNullOrEmpty(od.Pid))
                {
                    // 取得 Pid 對應的 DeviceType 字串
                    string deviceType = validator.GetTableValue(associatedProperties, "Device", "Pid", od.Pid, "DeviceType").Result?.ToString() ?? "";
                    if (!string.IsNullOrWhiteSpace(deviceType))
                    {
                        // 取得 DeviceType 對應的 Fusion Device Type 物件
                        var dt = deviceTypes.FirstOrDefault(e => e.type == deviceType);
                        od.DeviceType = dt;
                    }
                }
            }
        }
        else if (inUpdateObjectDeviceList != null && inUpdateObjectDeviceList.Any())
        {
            foreach (var od in inUpdateObjectDeviceList)
            {
                if (!string.IsNullOrEmpty(od.Pid))
                {
                    // 取得 Pid 對應的 DeviceType 字串
                    string deviceType = validator.GetTableValue(associatedProperties, "Device", "Pid", od.Pid, "DeviceType").Result?.ToString() ?? "";
                    if (!string.IsNullOrWhiteSpace(deviceType))
                    {
                        // 取得 DeviceType 對應的 Fusion Device Type 物件
                        var dt = deviceTypes.FirstOrDefault(e => e.type == deviceType);
                        od.DeviceType = dt;
                    }
                }
            }
        }


        return ValidationResult.Success;
    }
}

public class SetSupportServiceCodesAttribute : ValidationAttribute
{
    private readonly string _deviceTypePropertyName;
    private readonly string _objectEventListPropertyName;

    public SetSupportServiceCodesAttribute(string deviceTypePropertyName, string objectEventListPropertyName)
    {
        _deviceTypePropertyName = deviceTypePropertyName;
        _objectEventListPropertyName = objectEventListPropertyName;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        // 確認 deviceType 屬性是否存在
        var deviceTypeProperty = validationContext.ObjectType.GetProperty(_deviceTypePropertyName);
        if (deviceTypeProperty == null)
        {
            throw new ArgumentException($"Property '{_deviceTypePropertyName}' not found on {validationContext.ObjectType.Name}");
        }

        // 取得 deviceType 的值
        var deviceType = deviceTypeProperty.GetValue(validationContext.ObjectInstance) as DeviceType;

        // 確認 ObjectEventList 屬性是否存在
        var objectEventListProperty = validationContext.ObjectType.GetProperty(_objectEventListPropertyName);
        if (objectEventListProperty == null)
        {
            throw new ArgumentException($"Property '{_objectEventListPropertyName}' not found on {validationContext.ObjectType.Name}");
        }

        // 取得 ObjectEventList 的值 List<InCreateObjectEvent> 或 List<inUpdateObjectEvent>
        var inCreateObjectEventList = objectEventListProperty.GetValue(validationContext.ObjectInstance) as IEnumerable<InCreateObjectEvent>;
        var inUpdateObjectEventList = objectEventListProperty.GetValue(validationContext.ObjectInstance) as IEnumerable<InUpdateObjectEvent>;
        if ((inCreateObjectEventList == null || !inCreateObjectEventList.Any()) && (inUpdateObjectEventList == null || !inUpdateObjectEventList.Any()))
        {
            return ValidationResult.Success; // 無需驗證時返回成功
        }

        // 確認 value 是否為 DeviceType 類型
        if (deviceType == null)
        {
            return ValidationResult.Success; // 無需驗證時傳回成功
        }

        // 更新 ObjectEventList 中的每個 ObjectEvent 的 SupportDataEventServiceCodes
        if (inCreateObjectEventList != null && inCreateObjectEventList.Any())
        {
            foreach (var oe in inCreateObjectEventList)
            {
                oe.SupportDataEventServiceCodes = deviceType.supportDataEvent.Select(e => e.serviceCode).ToList();
            }
        }
        else if (inUpdateObjectEventList != null && inUpdateObjectEventList.Any())
        {
            foreach (var oe in inUpdateObjectEventList)
            {
                oe.SupportDataEventServiceCodes = deviceType.supportDataEvent.Select(e => e.serviceCode).ToList();
            }
        }

        return ValidationResult.Success;
    }
}

public class ExistsDeviceTypeAttribute : ValidationAttribute
{
    private readonly string _deviceTypePropertyName;

    public ExistsDeviceTypeAttribute(string deviceTypePropertyName)
    {
        _deviceTypePropertyName = deviceTypePropertyName;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        // 確認 DeviceType 屬性是否存在
        var deviceTypeProperty = validationContext.ObjectType.GetProperty(_deviceTypePropertyName);
        if (deviceTypeProperty == null)
        {

            throw new ArgumentException($"Property '{_deviceTypePropertyName}' not found on {validationContext.ObjectType.Name}");
        }

        // 取得 DeviceType 的值
        var deviceType = deviceTypeProperty.GetValue(validationContext.ObjectInstance) as DeviceType;

        // 如果 DeviceType 為 null 則回傳驗證錯誤
        if (deviceType == null)
        {
            return new ValidationResult(ErrorMessage);
        }

        return ValidationResult.Success;
    }
}

public class ExistsServiceCodeAttribute : ValidationAttribute
{
    private readonly string _supportDataEventServiceCodesPropertyName;

    public ExistsServiceCodeAttribute(string supportDataEventServiceCodesPropertyName)
    {
        _supportDataEventServiceCodesPropertyName = supportDataEventServiceCodesPropertyName;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        var supportDataEventServiceCodes = new List<string>();
        if (!string.IsNullOrWhiteSpace(_supportDataEventServiceCodesPropertyName))
        {
            // 確認 SupportDataEventServiceCodes 屬性是否存在
            var supportDataEventServiceCodesProperty = validationContext.ObjectType.GetProperty(_supportDataEventServiceCodesPropertyName);
            if (supportDataEventServiceCodesProperty == null)
            {

                throw new ArgumentException($"Property '{_supportDataEventServiceCodesPropertyName}' not found on {validationContext.ObjectType.Name}");
            }

            // 取得 SupportDataEventServiceCodes 的值
            supportDataEventServiceCodes.AddRange(supportDataEventServiceCodesProperty.GetValue(validationContext.ObjectInstance) as List<string>);
        }
        else
        {
            var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;

            // 取得 SysParameters 中的 paraCode = SupportServiceCode 的 paraValue
            var associatedProperties = new List<string?>();
            associatedProperties.Add("ParaType:Event");
            string SupportServiceCodeJsonArrayStr = validator.GetTableValue(associatedProperties, "SysParameters", "ParaCode", "SupportServiceCode", "ParaValue").Result?.ToString() ?? "";
            if (!string.IsNullOrWhiteSpace(SupportServiceCodeJsonArrayStr))
            {
                // 反序列化 SupportServiceCodeJsonArrayStr: List 的每個元素是一個 Json 物件要取得欄位為 ServiceCode 的值
                var supportServiceCodes = JsonSerializer.Deserialize<List<JsonElement>>(SupportServiceCodeJsonArrayStr);
                if (supportServiceCodes != null)
                {
                    supportDataEventServiceCodes.AddRange(supportServiceCodes.Select(e => e.GetProperty("ServiceCode").GetString()));
                }
            }
            else
            {
                // 從 Fusion 取得 serviceCode 列表
                var fusionServiceCodes = validator.GetServiceCodeList();
                supportDataEventServiceCodes.AddRange(fusionServiceCodes.Select(x => x.code).ToList());
            }
        }

        // 確認 value 不為空
        var serviceCode = value?.ToString();
        if (string.IsNullOrEmpty(serviceCode))
        {
            return ValidationResult.Success;
        }

        // 如果 SupportDataEventServiceCodes 為 null 或空或 SupportDataEventServiceCodes 不包含 serviceCode 時回傳驗證錯誤
        if (supportDataEventServiceCodes == null || !supportDataEventServiceCodes.Any() || !supportDataEventServiceCodes.Contains(serviceCode))
        {
            return new ValidationResult(ErrorMessage);
        }

        return ValidationResult.Success;
    }
}

public class RequiredWhenEqualsAttribute : ValidationAttribute
{
    private readonly string _propertyName;
    private readonly string[] _matchedValues;

    public RequiredWhenEqualsAttribute(string propertyName, string matchedValues)
    {
        _propertyName = propertyName;
        _matchedValues = matchedValues.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        // 取得 propertyValue
        var propertyValues = ExtractPropertyValues(validationContext.ObjectInstance, validationContext.ObjectType, _propertyName);

        // 如果 propertyValues 不存在任何 value 包含在 _matchedValues 裡則無需驗證
        if (!propertyValues.Any(v => _matchedValues.Contains(v)))
        {
            return ValidationResult.Success;
        }

        // 如果存在 propertyValues 等於 _matchedValues，則當前欄位必須有值
        if (value == null || (value is string str && string.IsNullOrWhiteSpace(str)))
        {
            // 如果當前欄位的值為空，則回傳錯誤訊息
            return new ValidationResult(ErrorMessage, new[] { validationContext.MemberName });
        }

        return ValidationResult.Success;
    }

    private HashSet<string> ExtractPropertyValues(object instance, Type instanceType, string propertyPath)
    {
        var propertyValues = new HashSet<string>(); // 使用 HashSet 避免重複資料
        var propertyParts = propertyPath.Split('.');

        object currentObject = instance;
        Type currentType = instanceType;

        for (int i = 0; i < propertyParts.Length; i++)
        {
            // 取得當前屬性資訊
            var propertyInfo = currentType.GetProperty(propertyParts[i]);
            if (propertyInfo == null)
            {
                throw new ArgumentException($"無法在 {currentType.Name} 找到屬性 '{propertyParts[i]}'");
            }

            // 取得屬性值
            currentObject = propertyInfo.GetValue(currentObject);
            if (currentObject == null)
            {
                return propertyValues;  // 如果屬性值為 null，則直接返回結果
            }

            // 當處理到倒數第二個屬性並且屬性是集合類型時，迭代集合
            if (i == propertyParts.Length - 2 && currentObject is IEnumerable enumerable)
            {
                foreach (var item in enumerable)
                {
                    // 取得集合中每個項目的最終屬性
                    var itemPropertyInfo = item.GetType().GetProperty(propertyParts.Last());
                    if (itemPropertyInfo != null)
                    {
                        var propertyValue = itemPropertyInfo.GetValue(item)?.ToString();
                        if (propertyValue != null)
                        {
                            propertyValues.Add(propertyValue); // 加入唯一的 propertyValue 值
                        }
                    }
                }
                return propertyValues;
            }

            // 更新當前類型為下一層屬性的類型
            currentType = currentObject.GetType();
        }

        // 如果是單一路徑並已取得最終值，直接檢查 currentObject
        if (currentObject is string finalValue && !string.IsNullOrWhiteSpace(finalValue))
        {
            propertyValues.Add(finalValue); // 加入唯一的最終值
        }

        return propertyValues;
    }
}

public class RequiredWhenPresentAttribute : ValidationAttribute
{
    private readonly string _propertyNames;

    public RequiredWhenPresentAttribute(string propertyNames)
    {
        _propertyNames = propertyNames;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        // 檢查每個 propertyNames 取得值為空或Null的 propertyName
        var emptyPropertyNames = new List<string>();
        if (!string.IsNullOrEmpty(_propertyNames))
        {
            foreach (string propertyName in _propertyNames.Split(","))
            {
                var property = validationContext.ObjectType.GetProperty(propertyName)
                                                ?? throw new ArgumentException($"Property with {propertyName} name not found");
                var propertyValue = property.GetValue(validationContext.ObjectInstance)?.ToString();
                if (string.IsNullOrEmpty(propertyValue))
                {
                    emptyPropertyNames.Add(propertyName);
                }
            }
        }

        // 如果存在 propertyName 的 Value 為空則不需驗證
        if (emptyPropertyNames.Count() > 0)
        {
            return ValidationResult.Success;
        }

        // propertyName 的 Value 皆有值，則當前欄位必須要有值
        if (value == null || (value is string str && string.IsNullOrWhiteSpace(str)))
        {
            // 如果當前欄位的值為空，則回傳錯誤訊息
            return new ValidationResult(ErrorMessage, new[] { validationContext.MemberName });
        }

        return ValidationResult.Success;
    }

    private HashSet<string> ExtractPropertyValues(object instance, Type instanceType, string propertyPath)
    {
        var propertyValues = new HashSet<string>(); // 使用 HashSet 避免重複資料
        var propertyParts = propertyPath.Split('.');

        object currentObject = instance;
        Type currentType = instanceType;

        for (int i = 0; i < propertyParts.Length; i++)
        {
            // 取得當前屬性資訊
            var propertyInfo = currentType.GetProperty(propertyParts[i]);
            if (propertyInfo == null)
            {
                throw new ArgumentException($"無法在 {currentType.Name} 找到屬性 '{propertyParts[i]}'");
            }

            // 取得屬性值
            currentObject = propertyInfo.GetValue(currentObject);
            if (currentObject == null)
            {
                return propertyValues;  // 如果屬性值為 null，則直接返回結果
            }

            // 當處理到倒數第二個屬性並且屬性是集合類型時，迭代集合
            if (i == propertyParts.Length - 2 && currentObject is IEnumerable enumerable)
            {
                foreach (var item in enumerable)
                {
                    // 取得集合中每個項目的最終屬性
                    var itemPropertyInfo = item.GetType().GetProperty(propertyParts.Last());
                    if (itemPropertyInfo != null)
                    {
                        var propertyValue = itemPropertyInfo.GetValue(item)?.ToString();
                        if (propertyValue != null)
                        {
                            propertyValues.Add(propertyValue); // 加入唯一的 propertyValue 值
                        }
                    }
                }
                return propertyValues;
            }

            // 更新當前類型為下一層屬性的類型
            currentType = currentObject.GetType();
        }

        // 如果是單一路徑並已取得最終值，直接檢查 currentObject
        if (currentObject is string finalValue && !string.IsNullOrWhiteSpace(finalValue))
        {
            propertyValues.Add(finalValue); // 加入唯一的最終值
        }

        return propertyValues;
    }
}

public class RequiredWhenAbsentAttribute : ValidationAttribute
{
    private readonly string _propertyNames;

    public RequiredWhenAbsentAttribute(string propertyNames)
    {
        _propertyNames = propertyNames;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        // 檢查每個 propertyNames 取得值為空或Null的 propertyName
        var emptyPropertyNames = new List<string>();
        if (!string.IsNullOrEmpty(_propertyNames))
        {
            foreach (string propertyName in _propertyNames.Split(","))
            {
                var property = validationContext.ObjectType.GetProperty(propertyName)
                                                ?? throw new ArgumentException($"Property with {propertyName} name not found");
                var propertyValue = property.GetValue(validationContext.ObjectInstance)?.ToString();
                if (string.IsNullOrEmpty(propertyValue))
                {
                    emptyPropertyNames.Add(propertyName);
                }
            }
        }

        // 如果有任一 propertyName 的 Value 不為空則不需驗證
        if (emptyPropertyNames.Count() != _propertyNames.Split(",").Count())
        {
            return ValidationResult.Success;
        }

        // propertyName 的 Value 皆為空，則當前欄位必須要有值
        if (value == null || (value is string str && string.IsNullOrWhiteSpace(str)))
        {
            // 如果當前欄位的值為空，則回傳錯誤訊息
            return new ValidationResult(ErrorMessage, new[] { validationContext.MemberName });
        }

        return ValidationResult.Success;
    }

    private HashSet<string> ExtractPropertyValues(object instance, Type instanceType, string propertyPath)
    {
        var propertyValues = new HashSet<string>(); // 使用 HashSet 避免重複資料
        var propertyParts = propertyPath.Split('.');

        object currentObject = instance;
        Type currentType = instanceType;

        for (int i = 0; i < propertyParts.Length; i++)
        {
            // 取得當前屬性資訊
            var propertyInfo = currentType.GetProperty(propertyParts[i]);
            if (propertyInfo == null)
            {
                throw new ArgumentException($"無法在 {currentType.Name} 找到屬性 '{propertyParts[i]}'");
            }

            // 取得屬性值
            currentObject = propertyInfo.GetValue(currentObject);
            if (currentObject == null)
            {
                return propertyValues;  // 如果屬性值為 null，則直接返回結果
            }

            // 當處理到倒數第二個屬性並且屬性是集合類型時，迭代集合
            if (i == propertyParts.Length - 2 && currentObject is IEnumerable enumerable)
            {
                foreach (var item in enumerable)
                {
                    // 取得集合中每個項目的最終屬性
                    var itemPropertyInfo = item.GetType().GetProperty(propertyParts.Last());
                    if (itemPropertyInfo != null)
                    {
                        var propertyValue = itemPropertyInfo.GetValue(item)?.ToString();
                        if (propertyValue != null)
                        {
                            propertyValues.Add(propertyValue); // 加入唯一的 propertyValue 值
                        }
                    }
                }
                return propertyValues;
            }

            // 更新當前類型為下一層屬性的類型
            currentType = currentObject.GetType();
        }

        // 如果是單一路徑並已取得最終值，直接檢查 currentObject
        if (currentObject is string finalValue && !string.IsNullOrWhiteSpace(finalValue))
        {
            propertyValues.Add(finalValue); // 加入唯一的最終值
        }

        return propertyValues;
    }
}

public class EqualsPropertyValueAttribute(string associatedPropertyNames, string tableName, string columnName, string checkColumnName, string checkPropertyName, bool checkColumnValueNullable) : ValidationAttribute
{
    private readonly string _associatedPropertyNames = associatedPropertyNames;
    private readonly string _tableName = tableName;
    private readonly string _columnName = columnName;
    private readonly string _checkColumnName = checkColumnName;
    private readonly string _checkPropertyName = checkPropertyName;
    private readonly bool _checkColumnValueNullable = checkColumnValueNullable;

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        var associatedProperties = new List<string?>();
        if (!string.IsNullOrEmpty(_associatedPropertyNames))
        {
            associatedProperties.AddRange(
                _associatedPropertyNames.Split(",")
                    .Select(associatedPropertyName =>
                    {
                        // 如果已經包含冒號就直接使用 associatedPropertyName
                        if (associatedPropertyName.Contains(":"))
                        {
                            return associatedPropertyName;
                        }
                        else
                        {
                            // 否則使用 associatedPropertyName 取得 associatedPropertyValue 再回傳 $"{associatedPropertyName}:{associatedPropertyValue}"
                            var associatedProperty = validationContext.ObjectType.GetProperty(associatedPropertyName)
                                ?? throw new ArgumentException($"Property with {associatedPropertyName} name not found");
                            var associatedPropertyValue = associatedProperty.GetValue(validationContext.ObjectInstance)?.ToString();

                            // 如果 associatedPropertyValue 為 null 或空字串則返回 null
                            return string.IsNullOrEmpty(associatedPropertyValue) ? null : $"{associatedPropertyName}:{associatedPropertyValue}";
                        }
                    }).Where(x => x != null).ToList()
            );
        }

        var checkProperty = validationContext.ObjectType.GetProperty(_checkPropertyName);
        if (checkProperty == null)
        {
            throw new ArgumentException($"Property with {_checkPropertyName} name not found");
        }
        var checkPropertyValue = checkProperty.GetValue(validationContext.ObjectInstance)?.ToString();

        // 嘗試將 value 轉換為 List<string>
        List<string> columnValues = null;

        // 先判斷 value 是 List<string>
        if (value is List<string> stringList)
        {
            columnValues = stringList;
        }
        else if (value is string stringValue)
        {
            columnValues = new List<string>();
            columnValues.Add(stringValue);
        }

        if (columnValues == null || columnValues.Count() <= 0)
        {
            return ValidationResult.Success;
        }

        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;

        // 逐一檢查每筆 columnValue
        foreach (string columnValue in columnValues)
        {
            // 如果 _tableName, _columnName, _checkColumnName 都為空就判斷 columnValue 是否等於 checkPropertyValue
            if (string.IsNullOrEmpty(_tableName) && string.IsNullOrEmpty(_columnName) && string.IsNullOrEmpty(_checkColumnName))
            {
                // 如果 checkColumnValue 不等於 checkPropertyValue 時驗證錯誤
                if (columnValue != checkPropertyValue)
                {
                    return new ValidationResult(ErrorMessage);
                }
            }
            else
            {
                // 取得 _tableName 中的 _columnName = columnValue 的 _checkColumnName
                object checkColumnValueObj = validator.GetTableValue(associatedProperties, _tableName, _columnName, columnValue, _checkColumnName).Result;
                if (checkColumnValueObj == null)
                {
                    return new ValidationResult(ErrorMessage);
                }

                string checkColumnValue = checkColumnValueObj.ToString();

                // 如果 _checkColumnValueNullable 為 true 且 checkColumnValue 為空或Null 時略過
                if (_checkColumnValueNullable && string.IsNullOrEmpty(checkColumnValue))
                {
                    continue;
                }

                // 如果 checkColumnValue 不等於 checkPropertyValue 時驗證錯誤
                if (checkColumnValue != checkPropertyValue)
                {
                    return new ValidationResult(ErrorMessage);
                }
            }
        }

        return ValidationResult.Success;
    }
}

public class ExistsMenusAttribute : ValidationAttribute
{
    private readonly string _menusPropertyName;

    public ExistsMenusAttribute(string menusPropertyName)
    {
        _menusPropertyName = menusPropertyName;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        // 確認 _menusPropertyName 屬性是否存在
        var menusProperty = validationContext.ObjectType.GetProperty(_menusPropertyName);
        if (menusProperty == null)
        {

            throw new ArgumentException($"Property '{_menusPropertyName}' not found on {validationContext.ObjectType.Name}");
        }

        // 取得 _menusPropertyName 的值
        var menuList = menusProperty.GetValue(validationContext.ObjectInstance) as List<Web.Models.Controller.Role.Menu>;

        // 如果屋m enuList 為空則略過驗證
        if (menuList == null || menuList.Count() <= 0)
        {
            return ValidationResult.Success;
        }

        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;

        // 依序檢查每個 Menu 項目
        var invalidItems = new List<int>();
        foreach (var menu in menuList)
        {
            int index = menuList.IndexOf(menu);

            // 檢查 MenuId 是否為空或 null
            if (!menu.MenuId.HasValue)
            {
                invalidItems.Add(index);
                continue;
            }

            // 檢查 MenuId 是否已經存在於資料庫
            string menuIdStr = validator.GetTableValue(null, "Menu", "MenuId", menu.MenuId.Value, "MenuId").Result?.ToString() ?? "";
            if (menuIdStr == null)
            {
                invalidItems.Add(index);
                continue;
            }

            // 檢查是否有指定權限
            if (menu.Permissions != null && menu.Permissions.Count > 0)
            {
                // 至少要有Retrieve權限
                if (!menu.Permissions.Contains("Retrieve"))
                {
                    menu.Permissions.Add("Retrieve");
                }

                // 檢查Permission是否存在資料庫
                foreach (var permission in menu.Permissions)
                {
                    List<string> associatedProperties = new List<string>();
                    associatedProperties.Add("CodeType:PermissionType");

                    string permissionCode = validator.GetTableValue(associatedProperties, "SysCode", "Code", permission, "Code").Result?.ToString() ?? "";
                    if (permissionCode == null)
                    {
                        invalidItems.Add(index);
                        continue;
                    }
                }
            }
            else
            {
                // 沒有指定權限，檢查此Menu是否為ParentMenu
                if (!menuList.Any(m => m.ParentMenuId == menu.MenuId))
                {
                    invalidItems.Add(index);
                    continue;
                }
            }

            // 檢查是否有重複的MenuId
            if (menuList.Count(x => x.MenuId == menu.MenuId) > 1)
            {
                invalidItems.Add(index);
                continue;
            }
        }

        // 如果有無效項目，回傳錯誤訊息
        if (invalidItems.Any())
        {
            // 使用分號將無效項目拼接成錯誤訊息
            var errorMessage = $"{ErrorMessage}:[{string.Join(", ", invalidItems)}]";
            return new ValidationResult(errorMessage, new[] { validationContext.MemberName });
        }

        return ValidationResult.Success;
    }
}

public class ListPropertyAllExistsAttribute : ValidationAttribute
{
    private readonly string _associatedPropertyNames;
    private readonly string _listPropertyName;
    private readonly string _listObjectColumnPropertyName;
    private readonly string _tableName;
    private readonly string _columnName;
    private readonly bool? _isActive;
    private readonly string? _checkColumnIs;
    private readonly bool? _skipAppCode;

    public ListPropertyAllExistsAttribute(string associatedPropertyNames, string listPropertyName, string listObjectColumnPropertyName, string tableName, string columnName)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _listPropertyName = listPropertyName;
        _listObjectColumnPropertyName = listObjectColumnPropertyName;
        _tableName = tableName;
        _columnName = columnName;
    }

    public ListPropertyAllExistsAttribute(string associatedPropertyNames, string listPropertyName, string listObjectColumnPropertyName, string tableName, string columnName, bool isActive)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _listPropertyName = listPropertyName;
        _listObjectColumnPropertyName = listObjectColumnPropertyName;
        _tableName = tableName;
        _columnName = columnName;
        _isActive = isActive;
    }

    public ListPropertyAllExistsAttribute(string associatedPropertyNames, string listPropertyName, string listObjectColumnPropertyName, string tableName, string columnName, bool isActive, bool skipAppCode)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _listPropertyName = listPropertyName;
        _listObjectColumnPropertyName = listObjectColumnPropertyName;
        _tableName = tableName;
        _columnName = columnName;
        _isActive = isActive;
        _skipAppCode = skipAppCode;
    }

    public ListPropertyAllExistsAttribute(string associatedPropertyNames, string listPropertyName, string listObjectColumnPropertyName, string tableName, string columnName, bool? isActive, string checkColumnIs)
    {
        _associatedPropertyNames = associatedPropertyNames;
        _listPropertyName = listPropertyName;
        _listObjectColumnPropertyName = listObjectColumnPropertyName;
        _tableName = tableName;
        _columnName = columnName;
        _isActive = isActive;
        _checkColumnIs = checkColumnIs;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        var associatedProperties = new List<string?>();
        if (!string.IsNullOrEmpty(_associatedPropertyNames))
        {
            associatedProperties.AddRange(
                _associatedPropertyNames.Split(",")
                    .Select(associatedPropertyName =>
                    {
                        // 如果已經包含冒號就直接使用 associatedPropertyName
                        if (associatedPropertyName.Contains(":"))
                        {
                            return associatedPropertyName;
                        }
                        else
                        {
                            // 否則使用 associatedPropertyName 取得 associatedPropertyValue 再回傳 $"{associatedPropertyName}:{associatedPropertyValue}"
                            var associatedProperty = validationContext.ObjectType.GetProperty(associatedPropertyName)
                                ?? throw new ArgumentException($"Property with {associatedPropertyName} name not found");
                            var associatedPropertyValue = associatedProperty.GetValue(validationContext.ObjectInstance)?.ToString();

                            // 如果 associatedPropertyValue 為 null 或空字串則返回 null
                            return string.IsNullOrEmpty(associatedPropertyValue) ? null : $"{associatedPropertyName}:{associatedPropertyValue}";
                        }
                    }).Where(x => x != null).ToList()
            );
        }

        var listProperty = validationContext.ObjectType.GetProperty(_listPropertyName)
                                ?? throw new ArgumentException($"Property with {_listPropertyName} name not found");
        var listPropertyValue = listProperty.GetValue(validationContext.ObjectInstance);

        // 嘗試將 listPropertyValue 轉換為 List<string> 或從 List<ObjectDevice> 提取出 columnName 屬性值組成 List<string>
        List<string> list = null;

        // 先判斷 listPropertyValue 是 List<string>
        if (listPropertyValue is List<string> stringList)
        {
            list = stringList;
        }
        else if (listPropertyValue is IEnumerable<object> objectList) // 如果 listPropertyValue 是 IEnumerable<object>，則遍歷其每個元素並從中提取出對應 columnName 的屬性值
        {
            list = objectList
                .Select(obj => obj.GetType().GetProperty(_listObjectColumnPropertyName)?.GetValue(obj)?.ToString())
                .Where(value => value != null)
                .ToList();
        }

        // 如果 list 是空的或 value 不是 List<string>，則直接返回 ValidationResult.Success
        if (list == null || !list.Any())
        {
            return ValidationResult.Success;
        }

        // 取得 CustomValidator 實例
        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;
        if (validator == null)
        {
            throw new InvalidOperationException("CustomValidator service not found.");
        }

        // 驗證每個清單項目是否存在於資料庫
        var invalidItems = new List<string>();
        foreach (var item in list)
        {
            var result = validator.ValidateExists(item, associatedProperties, _tableName, _columnName, _isActive, _checkColumnIs, false, _skipAppCode).Result;
            if (result != ValidationResult.Success)
            {
                invalidItems.Add(item);
            }
        }

        // 如果有無效項目，回傳錯誤訊息
        if (invalidItems.Any())
        {
            // 使用分號將無效項目拼接成錯誤訊息
            var errorMessage = $"{ErrorMessage}:[{string.Join(", ", invalidItems)}]";
            return new ValidationResult(errorMessage, new[] { validationContext.MemberName });
        }

        return ValidationResult.Success;
    }
}

public class CheckIsAdminAttribute : ValidationAttribute
{
    private readonly string _deptCodePropertyName;
    private readonly string _userDeptMonPermListPropertyName;

    public CheckIsAdminAttribute(string deptCodePropertyName, string userDeptMonPermListPropertyName)
    {
        _deptCodePropertyName = deptCodePropertyName;
        _userDeptMonPermListPropertyName = userDeptMonPermListPropertyName;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        // 確認 _deptCodePropertyName 屬性是否存在
        var deptCodeProperty = validationContext.ObjectType.GetProperty(_deptCodePropertyName);
        if (deptCodeProperty == null)
        {

            throw new ArgumentException($"Property '{_deptCodePropertyName}' not found on {validationContext.ObjectType.Name}");
        }
        var deptCodePropertyValue = deptCodeProperty.GetValue(validationContext.ObjectInstance)?.ToString();

        // 確認 _userDeptMonPermListPropertyName 屬性是否存在
        var userDeptMonPermListProperty = validationContext.ObjectType.GetProperty(_userDeptMonPermListPropertyName);
        if (userDeptMonPermListProperty == null)
        {

            throw new ArgumentException($"Property '{_userDeptMonPermListPropertyName}' not found on {validationContext.ObjectType.Name}");
        }

        // 取得 _userDeptMonPermListProperty 的值
        var userDeptMonPermList = userDeptMonPermListProperty.GetValue(validationContext.ObjectInstance) as List<Web.Models.Controller.UserData.InUserDeptMonPerm>;

        // 當IsAdmin為Y或N時，檢查UserDeptMonPermList是否為空（至少要有一筆自己部門）
        var isAdmin = value?.ToString();
        if (isAdmin == "Y" || isAdmin == "N")
        {
            if (userDeptMonPermList == null || userDeptMonPermList.Count == 0)
            {
                return new ValidationResult(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "UserDeptMonPermList");
            }
            else
            {
                // 當有指定IsAdmin為N時，檢查UserDeptMonPermList只會有一筆自己部門
                if (isAdmin == "N")
                {
                    if (userDeptMonPermList.Count > 1 || userDeptMonPermList[0].UsageDeptCode != deptCodePropertyValue)
                    {
                        return new ValidationResult(ErrorMessage = Constants.ErrorCode.Invalid + "UserDeptMonPermList");
                    }
                }
            }
        }
        else if (isAdmin == "A")
        {
            // 當有指定IsAdmin為A時，檢查UserDeptMonPermList是否為空
            if (userDeptMonPermList != null && userDeptMonPermList.Count > 0)
            {
                return new ValidationResult(ErrorMessage = Constants.ErrorCode.Invalid + "UserDeptMonPermList");
            }
        }

        return ValidationResult.Success;
    }
}

public class CheckPwdExpTypeAttribute : ValidationAttribute
{
    private readonly string _pwdExpStartDatePropertyName;
    private readonly string _pwdExpEndDatePropertyName;

    public CheckPwdExpTypeAttribute(string pwdExpStartDatePropertyName, string pwdExpEndDatePropertyName)
    {
        _pwdExpStartDatePropertyName = pwdExpStartDatePropertyName;
        _pwdExpEndDatePropertyName = pwdExpEndDatePropertyName;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        // 確認 _pwdExpStartDatePropertyName 屬性是否存在
        var pwdExpStartDateProperty = validationContext.ObjectType.GetProperty(_pwdExpStartDatePropertyName);
        if (pwdExpStartDateProperty == null)
        {

            throw new ArgumentException($"Property '{_pwdExpStartDatePropertyName}' not found on {validationContext.ObjectType.Name}");
        }
        var pwdExpStartDatePropertyValue = pwdExpStartDateProperty.GetValue(validationContext.ObjectInstance)?.ToString();

        // 確認 _pwdExpEndDatePropertyName 屬性是否存在
        var pwdExpEndDateProperty = validationContext.ObjectType.GetProperty(_pwdExpEndDatePropertyName);
        if (pwdExpEndDateProperty == null)
        {

            throw new ArgumentException($"Property '{_pwdExpEndDatePropertyName}' not found on {validationContext.ObjectType.Name}");
        }
        var pwdExpEndDatePropertyValue = pwdExpEndDateProperty.GetValue(validationContext.ObjectInstance)?.ToString();

        // 如果PwdExpType為R（定期），檢查PwdExpStartDate和PwdExpEndDate是否為空
        var pwdExpType = value?.ToString();
        if (pwdExpType == "R")
        {   // 如果PwdExpType為R（定期），檢查PwdExpStartDate和PwdExpEndDate是否為空
            if (pwdExpStartDatePropertyValue == null || pwdExpEndDatePropertyValue == null)
            {
                return new ValidationResult(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PwdExpStartDate");
            }
            else
            {
                // 檢查PwdExpStartDate是否小於PwdExpEndDate
                DateTime pwdExpStartDate = DateTime.Parse(pwdExpStartDatePropertyValue);
                DateTime pwdExpEndDate = DateTime.Parse(pwdExpEndDatePropertyValue);
                if (pwdExpStartDate >= pwdExpEndDate)
                {
                    return new ValidationResult(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PwdExpStartDate");
                }
            }
        }
        // 檢查PwdExpType為0或S時，PwdExpStartDate和PwdExpEndDate不能有值
        else if ((pwdExpType == "S" || pwdExpType == "0") && (pwdExpStartDatePropertyValue != null || pwdExpEndDatePropertyValue != null))
        {
            return new ValidationResult(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PwdExpType");
        }

        return ValidationResult.Success;
    }
}

public class CheckScheduleNotifiesAttribute : ValidationAttribute
{
    private readonly bool _isCreate;

    public CheckScheduleNotifiesAttribute(bool isCreate)
    {
        _isCreate = isCreate;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        var validator = validationContext.GetService(typeof(CustomValidator)) as CustomValidator;
        var invalidItems = new List<string>();

        if (_isCreate)
        {
            // 確認 _scheduleNotifiesPropertyName 屬性是否存在
            var scheduleNotifyListProperty = validationContext.ObjectType.GetProperty("ScheduleNotifyList");
            if (scheduleNotifyListProperty == null)
            {
                
                throw new ArgumentException($"Property 'ScheduleNotifyList' not found on {validationContext.ObjectType.Name}");
            }
            
            // 取得 scheduleNotifyListProperty 的值
            var scheduleNotifyList = scheduleNotifyListProperty.GetValue(validationContext.ObjectInstance) as List<Web.Models.Controller.Schedule.ScheduleNotifyDto>;

            if (scheduleNotifyList == null || scheduleNotifyList.Count() <= 0)
                return new ValidationResult(ErrorMessage=Constants.ErrorCode.NullOrEmpty + "ScheduleNotifyList");

            var associatedProperties = new List<string>();
            associatedProperties.Add("CodeType:NotifyType");

            for (int i = 0; i < scheduleNotifyList.Count; i++)
            {
                Web.Models.Controller.Schedule.ScheduleNotifyDto scheduleNotify = scheduleNotifyList[i];

                // 檢查通知類型是否為空
                if (string.IsNullOrEmpty(scheduleNotify.NotifyType))
                {
                    invalidItems.Add(Constants.ErrorCode.NullOrEmpty + "ScheduleNotifyList[" + i + "].NotifyType");
                }

                // 取得 SysCode table Code = scheduleNotify.NotifyType.ToUpper() 的 record 的 Code
                string codeValue = validator.GetTableValue(associatedProperties, "SysCode", "Code", scheduleNotify.NotifyType.ToUpper(), "Code").Result?.ToString()??"";

                // 檢查通知類型是否為EMAIL,LINE,SMS
                if (string.IsNullOrEmpty(codeValue))
                {
                    invalidItems.Add(Constants.ErrorCode.NotFound + "ScheduleNotifyList[" + i + "].NotifyType");
                }
                else if (scheduleNotify.NotifyType.ToUpper() == "EMAIL" && string.IsNullOrEmpty(scheduleNotify.Email))
                {
                    invalidItems.Add(Constants.ErrorCode.NullOrEmpty + "ScheduleNotifyList[" + i + "].Email");
                }
            }
        } 
        else
        {
            // 確認 _scheduleNotifiesPropertyName 屬性是否存在
            var scheduleNotifiesProperty = validationContext.ObjectType.GetProperty("ScheduleNotifies");
            if (scheduleNotifiesProperty == null)
            {
                
                throw new ArgumentException($"Property 'ScheduleNotifies' not found on {validationContext.ObjectType.Name}");
            }
            
            // 取得 scheduleNotifiesProperty 的值
            var scheduleNotifies = scheduleNotifiesProperty.GetValue(validationContext.ObjectInstance) as List<Web.Models.Controller.Schedule.UpdateScheduleNotify>;

            if (scheduleNotifies == null || scheduleNotifies.Count() <= 0)
                return  ValidationResult.Success;

            var associatedProperties = new List<string>();
            associatedProperties.Add("CodeType:NotifyType");

            for (int i = 0; i < scheduleNotifies.Count; i++)
            {
                Web.Models.Controller.Schedule.UpdateScheduleNotify scheduleNotify = scheduleNotifies[i];

                // 檢查通知類型是否為空
                if (string.IsNullOrEmpty(scheduleNotify.NotifyType))
                {
                    invalidItems.Add(Constants.ErrorCode.NullOrEmpty + "ScheduleNotifies[" + i + "].NotifyType");
                }

                // 取得 SysCode table Code = scheduleNotify.NotifyType.ToUpper() 的 record 的 Code
                string codeValue = validator.GetTableValue(associatedProperties, "SysCode", "Code", scheduleNotify.NotifyType.ToUpper(), "Code").Result?.ToString()??"";

                // 檢查通知類型是否為EMAIL,LINE,SMS
                if (string.IsNullOrEmpty(codeValue))
                {
                    invalidItems.Add(Constants.ErrorCode.NotFound + "ScheduleNotifies[" + i + "].NotifyType");
                }
                else if (scheduleNotify.NotifyType.ToUpper() == "EMAIL" && string.IsNullOrEmpty(scheduleNotify.Email))
                {
                    invalidItems.Add(Constants.ErrorCode.NullOrEmpty + "ScheduleNotifies[" + i + "].Email");
                }
            }
        }

        // 如果有無效項目，回傳錯誤訊息
        if (invalidItems.Any())
        {
            // 使用分號將無效項目拼接成錯誤訊息
            var errorMessage = $"{ErrorMessage}:[{string.Join(", ", invalidItems)}]";
            return new ValidationResult(errorMessage, new[] { validationContext.MemberName });
        }

        return ValidationResult.Success;
    }
}

public class CheckWeeklyStartTimeEndTimeListAttribute : ValidationAttribute
{
    private readonly string _listPropertyName;

    public CheckWeeklyStartTimeEndTimeListAttribute(string listPropertyName = "ExclusionPeriodList")
    {
        _listPropertyName = listPropertyName;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        var invalidItems = new List<string>();

        // 確認指定的屬性是否存在
        var listProperty = validationContext.ObjectType.GetProperty(_listPropertyName);
        if (listProperty == null)
        {
            throw new ArgumentException($"Property '{_listPropertyName}' not found on {validationContext.ObjectType.Name}");
        }
        
        // 取得屬性的值並轉換為 IEnumerable
        var listObj = listProperty.GetValue(validationContext.ObjectInstance);
        if (listObj == null || !(listObj is IEnumerable))
            return ValidationResult.Success;

        // 將物件轉換為列表並處理每個項目
        var list = (listObj as IEnumerable).Cast<object>().ToList();
        if (list.Count <= 0)
            return ValidationResult.Success;

        for (int i = 0; i < list.Count; i++)
        {
            var period = list[i];
            var weeklyProp = period.GetType().GetProperty("Weekly");
            var startTimeProp = period.GetType().GetProperty("StartTime");
            var endTimeProp = period.GetType().GetProperty("EndTime");

            if (weeklyProp == null || startTimeProp == null || endTimeProp == null)
            {
                invalidItems.Add(Constants.ErrorCode.Invalid + $"{_listPropertyName}[{i}] missing required properties");
                continue;
            }

            string weekly = weeklyProp.GetValue(period)?.ToString();
            string startTime = startTimeProp.GetValue(period)?.ToString();
            string endTime = endTimeProp.GetValue(period)?.ToString();

            // 驗證 Weekly 如果為 Null 或空時填入預設值每天 (0,1,2,3,4,5,6)
            if (string.IsNullOrEmpty(weekly))
            {
                weekly = "0,1,2,3,4,5,6";
                weeklyProp.SetValue(period, weekly);
            }

            Regex WeeklyRegex = new(@"^([0-6](,[0-6])*)?$");
            if (!WeeklyRegex.IsMatch(weekly))
            {
                invalidItems.Add(Constants.ErrorCode.Pattern + $"{_listPropertyName}[{i}].Weekly");
            }

            // 驗證 StartTime 不得為空或 Null
            if (string.IsNullOrEmpty(startTime))
            {
                invalidItems.Add(Constants.ErrorCode.NullOrEmpty + $"{_listPropertyName}[{i}].StartTime");
            }

            // 驗證 EndTime 不得為空或 Null
            if (string.IsNullOrEmpty(endTime))
            {
                invalidItems.Add(Constants.ErrorCode.NullOrEmpty + $"{_listPropertyName}[{i}].EndTime");
            }

            if (string.IsNullOrEmpty(startTime) || string.IsNullOrEmpty(endTime))
            {
                continue;
            }

            // 驗證 StartTime 是否符合時間格式
            Regex TimeRegex = new(@"^([01][0-9]|2[0-3]):[0-5][0-9](:[0-5][0-9])?$");
            if (!TimeRegex.IsMatch(startTime))
            {
                invalidItems.Add(Constants.ErrorCode.Pattern + $"{_listPropertyName}[{i}].StartTime");
            }

            // 驗證 EndTime 是否符合時間格式
            if (!TimeRegex.IsMatch(endTime))
            {
                invalidItems.Add(Constants.ErrorCode.Pattern + $"{_listPropertyName}[{i}].EndTime");
            }
        }

        // 如果有無效項目，回傳錯誤訊息
        if (invalidItems.Any())
        {
            // 使用分號將無效項目拼接成錯誤訊息
            var errorMessage = $"{ErrorMessage}:[{string.Join(", ", invalidItems)}]";
            return new ValidationResult(errorMessage, new[] { validationContext.MemberName });
        }

        return ValidationResult.Success;
    }
}

public class RequestParamListDuplicateAttribute : ActionFilterAttribute
{
    private readonly string _propertyNames;

    public RequestParamListDuplicateAttribute(string propertyNames)
    {
        _propertyNames = propertyNames;
    }

    public override void OnActionExecuting(ActionExecutingContext context)
    {
        List<ReturnError> errors = new List<ReturnError>();
        
        var listParam = context.ActionArguments
            .FirstOrDefault(arg => arg.Value is IEnumerable<object>)
            .Value as IEnumerable<object>;

        if (listParam == null)
        {
            return;
        }

        var items = listParam.Cast<object>().ToList();
        var duplicates = items
            .Select((item, index) => new
            {
                Index = index,
                Value = string.Join(",", _propertyNames.Split(",").Select(_propertyName => item?.GetType().GetProperty(_propertyName)?.GetValue(item)).ToList())
            })
            .Where(x => x.Value != null)
            .GroupBy(x => x.Value)
            .Where(g => g.Count() > 1)
            .SelectMany(g => g.Select(x => new { x.Index, Code = g.Key.ToString() }))
            .ToList();

        if (duplicates.Any())
        {
            var duplicateErrors = duplicates
                .Select(d => new ReturnError
                {
                    index = d.Index,
                    code = d.Code,
                    errors = new List<ErrorDetail>
                    {
                        new ErrorDetail
                        {
                            index = 0,
                            code = d.Code,
                            error = $"{Constants.ErrorCode.Duplicate}{_propertyNames}",
                            message = $"{Constants.ErrorCode.Duplicate}{_propertyNames}",
                            innerMsg = $"ParamList|[{d.Index}]|{_propertyNames}|{d.Code}",
                            details = null
                        }
                    }
                })
                .ToList();

            errors.AddRange(duplicateErrors);

            // 回傳 400 Bad Request 並附上錯誤細節
            ReturnModel returnModel = new()
            {
                requestUUID = "",
                authorize = null,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = errors
            };

            context.Result = new BadRequestObjectResult(returnModel);
        }
    }
}

public class RequestParamListNotNullOrEmptyAttribute : ActionFilterAttribute
{
    public override void OnActionExecuting(ActionExecutingContext context)
    {
        List<ReturnError> errors = new List<ReturnError>();
        
        var listParam = context.ActionArguments
            .FirstOrDefault(arg => arg.Value is IEnumerable<object>)
            .Value as IEnumerable<object>;

        if (listParam == null)
        {
            var returnError = new ReturnError
            {
                index = -1,
                code = "",
                errors = new List<ErrorDetail>
                {
                    new ErrorDetail
                    {
                        index = 0,
                        code = "",
                        error = Constants.ErrorCode.NullOrEmpty,
                        message = Constants.ErrorCode.NullOrEmpty,
                        innerMsg = "ParamList|[-1]|paramList|",
                        details = null
                    }
                }
            };
            
            errors.Add(returnError);

            // 回傳 400 Bad Request 並附上錯誤細節
            ReturnModel returnModel = new()
            {
                requestUUID = "",
                authorize = null,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = errors
            };

            context.Result = new BadRequestObjectResult(returnModel);
            return;
        }
    }
}

public class RequestParamNotNullOrEmptyAttribute : ActionFilterAttribute
{
    public override void OnActionExecuting(ActionExecutingContext context)
    {
        List<ReturnError> errors = new List<ReturnError>();
        
        var objectParam = context.ActionArguments
            .FirstOrDefault(arg => arg.Value is object)
            .Value as object;

        if (objectParam == null)
        {
            var returnError = new ReturnError
            {
                index = -1,
                code = "",
                errors = new List<ErrorDetail>
                {
                    new ErrorDetail
                    {
                        index = 0,
                        code = "",
                        error = Constants.ErrorCode.NullOrEmpty,
                        message = Constants.ErrorCode.NullOrEmpty,
                        innerMsg = "Param|[-1]|param|",
                        details = null
                    }
                }
            };
            
            errors.Add(returnError);

            // 回傳 400 Bad Request 並附上錯誤細節
            ReturnModel returnModel = new()
            {
                requestUUID = "",
                authorize = null,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = errors
            };

            context.Result = new BadRequestObjectResult(returnModel);
            return;
        }
    }
}

public class ListAllMatchedAttribute : ValidationAttribute
{
    private readonly string[] _matchedValues;

    public ListAllMatchedAttribute(string matchedValues)
    {
        _matchedValues = matchedValues.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries);
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        // 嘗試將 value 轉換為 List<string> 或從 List<ObjectDevice> 提取出 columnName 屬性值組成 List<string>
        List<string> list = null;

        // 先判斷 value 是 List<string>
        if (value is List<string> stringList)
        {
            list = stringList;
        }

        // 如果 list 是空的或 value 不是 List<string>，則直接返回 ValidationResult.Success
        if (list == null || !list.Any())
        {
            return ValidationResult.Success;
        }

        // 驗證每個清單項目是否都在 _matchedValues
        var invalidItems = new List<string>();
        foreach (var item in list)
        {
            // 如果項目等於 "ALL" 就略過
            if (item == "ALL")
                continue;
                
            // 如果項目不在 _matchedValues 中，則加入無效項目列表
            if (!_matchedValues.Contains(item))
            {
                invalidItems.Add(item);
            }
        }

        // 如果有無效項目，回傳錯誤訊息
        if (invalidItems.Any())
        {
            // 使用分號將無效項目拼接成錯誤訊息
            var errorMessage = $"{ErrorMessage}:[{string.Join(", ", invalidItems)}]";
            return new ValidationResult(errorMessage, new[] { validationContext.MemberName });
        }

        return ValidationResult.Success;
    }
}