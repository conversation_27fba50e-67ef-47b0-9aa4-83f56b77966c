﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using System.Text.Json;
using System.Text.RegularExpressions;
using Web.Models.Controller.ObjectGroup;
using Web.Models.Controller;
using Web.Models.Service;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;
using DocumentFormat.OpenXml.Drawing;
using Web.Validation;
using Web.Constant;
using Web.Models.Controller.Fence;
using Web.Models.Service.Fusion;
using System.Collections;

namespace Web.Controller;

/// <summary>
/// 圍籬
/// </summary>
[Route("[controller]")]
[Authorize]
public class FenceController(IDataAccessService dataAccessService,
                                    IEventService eventService,
                                    ICredentialService credentialService,
                                    IRequestContextService requestContextService,
                                    ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly IEventService _eventService = eventService;

    /// 新增圍籬檢核
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    // 有在method 定義route，在Controller記得也要定義route，否則會404
    [HttpPost("fences/validate")]
    [RequestParamListDuplicate("FenceCode")]
    [RequestParamListNotNullOrEmpty]
    public IActionResult ValidateFence([FromBody] List<CreateFence> paramList)
    {
        // 進到 controller 代表驗證通過，回傳空的錯誤列表
        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new List<ReturnError>()
        });
    }

    /// <summary>
    /// 新增圍籬
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    // 有在method 定義route，在Controller記得也要定義route，否則會404
    [HttpPost("fences")]
    [RequestParamListDuplicate("FenceCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateFence([FromBody] List<CreateFence> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        string appCode = _user.AppCode;
        _logService.Logging("info", logActionName, requestUUID, "Fence Data Validated, start to append.");

        _dataAccessService.BeginTransaction();

        foreach (CreateFence f in paramList)
        {
            Fence fence = new Fence
            {
                AppCode = _user.AppCode,
                AreaCode = f.AreaCode,
                FenceCode = f.FenceCode,
                CustomFenceCode = f.CustomFenceCode,
                Enable = f.Enable == "Y",
                FenceType = f.FenceType,
                FenceName = f.FenceName,
                PlaneCode = f.PlaneCode,
                Width = f.Width,
                Height = f.Height,
                PositionX = f.PositionX,
                PositionY = f.PositionY,
                FenceColor = f.FenceColor,
                RSSIDelta1 = f.RSSIDelta1,
                RSSIDelta2 = f.RSSIDelta2,
                CreateUserAccount = _user.Account,
                CreateDate = DateTime.Now,
                ModifyDate = DateTime.Now
            };

            // 新增 Fence
            await _dataAccessService.CreateAsync<Fence>(fence);

            // 將基站列表的基站關聯至建立的圍籬
            await CreateFenceStationsAsync(fence.AreaCode, fence.FenceCode, f.StationSIDList);
            
            // 將 AlarmGroup 列表的基站關聯至建立的圍籬
            await CreateFenceAlarmGroupsAsync(fence.AreaCode, fence.FenceCode, f.AlarmGroupSIDList);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "Fence Data append done.");

        returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status201Created,
            result = true
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    /// <summary>
    /// 新增圍籬與基站的關聯
    /// </summary>
    /// <param name="areaCode"></param>
    /// <param name="fenceCode"></param>
    /// <param name="stationSIDList"></param>
    /// <returns></returns>
    private async Task<List<FenceStation>> CreateFenceStationsAsync(string areaCode, string fenceCode, List<string> stationSIDList)
    {
        if (stationSIDList != null && stationSIDList.Count > 0)
        {
            // 新增 Fence 與 Station 的關連到 FenceStation
            List<FenceStation> fenceStations = stationSIDList.Select(sid => new FenceStation() {
                AppCode = _user.AppCode,
                AreaCode = areaCode,
                FenceCode = fenceCode,
                Stationsid = sid,
                Layer = "2", // TODO 待確認
                CreateUserAccount = _user.Account,
                CreateDate = DateTime.Now,
                ModifyDate = DateTime.Now
            }).ToList();

            // 批次新增
            await _dataAccessService.CreateRangeAsync(fenceStations);
            return fenceStations;
        }

        return new List<FenceStation>();
    }

    /// <summary>
    /// 新增圍籬與 AlarmGroup 的關聯
    /// </summary>
    /// <param name="areaCode"></param>
    /// <param name="fenceCode"></param>
    /// <param name="alarmGroupSIDList"></param>
    /// <returns></returns>
    private async Task<List<FenceAlarmGroup>> CreateFenceAlarmGroupsAsync(string areaCode, string fenceCode, List<string> alarmGroupSIDList)
    {
        if (alarmGroupSIDList != null && alarmGroupSIDList.Count > 0)
        {
            // 新增 Fence 與 Station 的關連到 FenceAlarmGroup
            List<FenceAlarmGroup> fenceAlarmGroups = alarmGroupSIDList.Select(sid => new FenceAlarmGroup() {
                AppCode = _user.AppCode,
                AreaCode = areaCode,
                FenceCode = fenceCode,
                Stationsid = sid,
                CreateUserAccount = _user.Account,
                CreateDate = DateTime.Now,
                ModifyDate = DateTime.Now
            }).ToList();

            // 批次新增
            await _dataAccessService.CreateRangeAsync(fenceAlarmGroups);
            return fenceAlarmGroups;
        }

        return new List<FenceAlarmGroup>();
    }

    [HttpPatch("fences")]
    [RequestParamListDuplicate("FenceCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateFence([FromBody] List<UpdateFence> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        _dataAccessService.BeginTransaction();

        foreach (UpdateFence param in paramList)
        {
            Fence fence = _dataAccessService.Fetch<Fence>(e => e.AppCode == _user.AppCode && e.AreaCode == param.AreaCode && e.FenceCode == param.FenceCode).AsTracking().First();

            List<string> updateField = new List<string>();
            if (param.CustomFenceCode != null)
            {
                updateField.Add("CustomFenceCode");
                fence.CustomFenceCode = param.CustomFenceCode;
            }

            if (param.Enable != null)
            {
                updateField.Add("Enable");
                fence.Enable = param.Enable == "Y";
            }

            if (param.FenceType != null)
            {
                updateField.Add("FenceType");
                fence.FenceType = param.FenceType;
            }

            if (param.FenceName != null)
            {
                updateField.Add("FenceName");
                fence.FenceName = param.FenceName;
            }

            if (param.PlaneCode != null)
            {
                updateField.Add("PlaneCode");
                fence.PlaneCode = param.PlaneCode;
            }

            if (param.Width != null)
            {
                updateField.Add("Width");
                fence.Width = param.Width;
            }

            if (param.Height != null)
            {
                updateField.Add("Height");
                fence.Height = param.Height;
            }

            if (param.PositionX != null)
            {
                updateField.Add("PositionX");
                fence.PositionX = param.PositionX;
            }

            if (param.PositionY != null)
            {
                updateField.Add("PositionY");
                fence.PositionY = param.PositionY;
            }

            if (param.FenceColor != null)
            {
                updateField.Add("FenceColor");
                fence.FenceColor = param.FenceColor;
            }

            if (param.RSSIDelta1 != null)
            {
                updateField.Add("RSSIDelta1");
                fence.RSSIDelta1 = param.RSSIDelta1;
            }

            if (param.RSSIDelta2 != null)
            {
                updateField.Add("RSSIDelta2");
                fence.RSSIDelta2 = param.RSSIDelta2;
            }

            updateField.Add("ModifyDate");
            updateField.Add("ModifyUserAccount");
            fence.ModifyDate = DateTime.Now;
            fence.ModifyUserAccount = _user.Account;

            await _dataAccessService.UpdateAsync<Fence>(fence, updateField.ToArray());

            // 更新該 FenceCode 與 Station SID 的關聯，先刪除再新增
            List<FenceStation> fenceStationList = new List<FenceStation>();
            if (param.StationSIDList != null)
            {
                // 先刪除原本的關聯
                await _dataAccessService.DeleteAsync<FenceStation>(e => e.AppCode == _user.AppCode && e.AreaCode == fence.AreaCode && e.FenceCode == fence.FenceCode);

                 // 將基站列表的基站關聯至建立的圍籬
                fenceStationList.AddRange(await CreateFenceStationsAsync(fence.AreaCode, fence.FenceCode, param.StationSIDList));
            }
            
            List<FenceAlarmGroup> fenceAlarmGroupList = new List<FenceAlarmGroup>();
            if (param.AlarmGroupSIDList != null)
            {
                // 先刪除原本的關聯
                await _dataAccessService.DeleteAsync<FenceAlarmGroup>(e => e.AppCode == _user.AppCode && e.AreaCode == fence.AreaCode && e.FenceCode == fence.FenceCode);

                // 將 AlarmGroup 列表的基站關聯至建立的圍籬
                fenceAlarmGroupList.AddRange(await CreateFenceAlarmGroupsAsync(fence.AreaCode, fence.FenceCode, param.AlarmGroupSIDList));
            }

            // 如果圍籬有綁定事件，更新 Console Event 參數
            var eventFenceList = _dataAccessService.Fetch<EventFence>(x => x.AppCode == _user.AppCode && x.FenceCode == fence.FenceCode).ToList();
            if (eventFenceList.Any())
            {
                HashSet<string> eventCodeSet = eventFenceList.Select(x => x.EventCode).Distinct().ToHashSet();
                var objectEventList = await _dataAccessService.Fetch<ObjectEvent>(e => e.AppCode == _user.AppCode && eventCodeSet.Contains(e.EventCode)).ToListAsync();
                await UpdateFusionEventsAsync(fence, eventFenceList, objectEventList, fenceStationList, fenceAlarmGroupList);
            }
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "Fence Data update done.");

        return Ok(new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = true
                });
    }

    /// <summary>
    /// 更新圍籬關聯的 Fusion Event 資料
    /// </summary>
    /// <param name="fence"></param>
    /// <param name="eventFenceList"></param>
    /// <returns></returns>
    private async Task UpdateFusionEventsAsync(Fence fence, List<EventFence> eventFenceList, List<ObjectEvent> objectEventList, List<FenceStation> fenceStationList, List<FenceAlarmGroup> fenceAlarmGroupList)
    {
        List<PatchFusionEventInput> patchFusionEventInputs = [];

        foreach (var eventFence in eventFenceList)
        {
            var objectEvent = objectEventList.FirstOrDefault(e=>e.EventCode == eventFence.EventCode);
            PatchFusionEventInput patchFusionEventInput = new ()
            {
                enable = true,
                code = eventFence.EventCode,
                name = fence.FenceName??"",
                serviceCode = objectEvent?.ServiceCode??"",
                sponsorDevice = new PatchFusionEventInput.SponsorDevice
                {
                    devicePids = [objectEvent?.Pid]
                }
            };

           patchFusionEventInput.stationLayers = fenceStationList.Where(e => e.AreaCode == objectEvent.AreaCode && e.FenceCode == fence.FenceCode)
                    .Select(fs => new PatchFusionEventInput.StationLayer
                    {
                        sid = fs.Stationsid,
                        layer = int.Parse(fs.Layer)
                    }).ToList();

            patchFusionEventInput.stationAlertGroups = new PatchFusionEventInput.StationAlertGroups
                {
                    stationSids = fenceAlarmGroupList.Where(e => e.FenceCode == fence.FenceCode).Select(e => e.Stationsid).ToList(),
                };

            patchFusionEventInput.arguments =
                [
                    new (){key = "autoTreated", value = "true"},
                    new (){key = "noSignalAsLeave", value = "true"},
                    new (){key = "rssiDelta1", value = fence.RSSIDelta1.ToString()},
                    new (){key = "rssiDelta2", value = fence.RSSIDelta2.ToString()},
                ];
            
            patchFusionEventInputs.Add(patchFusionEventInput);
        }

        // 取得存在於 existEventCodeSet 的子列表
        var  enablePatchFusionEventInputList = patchFusionEventInputs.ToList();
        if (enablePatchFusionEventInputList.Any())
        {
            // 更新 Fusion Events
            var patchResults = await _eventService.PatchActiveEvents(enablePatchFusionEventInputList);
        }
    }

    [HttpDelete("fences")]
    [RequestParamListDuplicate("FenceCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeleteFence([FromBody] List<DeleteFence> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 開始刪除圍籬資料
        _logService.Logging("info", logActionName, requestUUID, "Fence Data Validated, start to delete.");
        _dataAccessService.BeginTransaction();

        foreach (DeleteFence f in paramList)
        {
            // 刪除圍籬
            int deleteResult = await _dataAccessService.DeleteAsync<Fence>(e => e.AppCode == _user.AppCode && e.AreaCode == f.AreaCode && e.FenceCode == f.FenceCode);
            if (deleteResult > 0)
            {
                // 刪除 FenceStation 的關聯
                await _dataAccessService.DeleteAsync<FenceStation>(e => e.AppCode == _user.AppCode && e.AreaCode == f.AreaCode && e.FenceCode == f.FenceCode);

                // 刪除 FenceAlarmGroup 的關聯
                await _dataAccessService.DeleteAsync<FenceAlarmGroup>(e => e.AppCode == _user.AppCode && e.AreaCode == f.AreaCode && e.FenceCode == f.FenceCode);
            }
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "Fence Data delete done.");

        return Ok(new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = true
                });
    }

    [HttpGet("fences")]
    public async Task<IActionResult> RetrieveFence([FromQuery] RetrieveFence queryParam)
    {
        RetrieveFence param = queryParam;
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // 排序；如果沒有 sort 條件預設使用 ModifyDate 降冪排序
        string sort = string.IsNullOrEmpty(param.sort) ? "ModifyDate:desc" : param.sort;
        string sortByField = sort.Split(":")[0];
        string sortDirection = sort.Split(":")[1];

        var areaList = _dataAccessService.Fetch<Area>(e=>e.AppCode == _user.AppCode);
        var buildingList = _dataAccessService.Fetch<Building>(e=>e.AppCode == _user.AppCode);
        var fenceList = _dataAccessService.Fetch<Fence>(x => x.AppCode == _user.AppCode);
        var planeList = _dataAccessService.Fetch<Plane>(x => x.AppCode == _user.AppCode);
        var stationList = _dataAccessService.Fetch<Station>(x => x.AppCode == _user.AppCode);
        var fenceStationList = _dataAccessService.Fetch<FenceStation>(x => x.AppCode == _user.AppCode);
        var fenceAlarmGroupList = _dataAccessService.Fetch<FenceAlarmGroup>(x => x.AppCode == _user.AppCode);

        var query = (from a in fenceList
             join b in areaList on a.AreaCode equals b.AreaCode into temp
             from t in temp.DefaultIfEmpty()
             join c in planeList on a.PlaneCode equals c.PlaneCode into temp2
             from tt in temp2.DefaultIfEmpty()
             join d in buildingList on tt.BuildingCode equals d.BuildingCode into temp3
             from ttt in temp3.DefaultIfEmpty()
             select new
             {
                 a.FenceId,
                 Enable = (a.Enable == true) ? "Y" : "N",
                 a.FenceCode,
                 a.CustomFenceCode,
                 a.FenceType,                 
                 a.FenceName,
                 AreaName = (t == null) ? "" : t.AreaName,
                 a.AreaCode,
                 a.PlaneCode,
                 tt.PlaneName,
                 ttt.BuildingCode,
                 ttt.BuildingName,
                 a.PositionX,
                 a.PositionY,
                 a.Width,
                 a.Height,
                 a.FenceColor,
                 Stations = (from fs in fenceStationList
                            where fs.FenceCode == a.FenceCode && fs.AreaCode == a.AreaCode
                            orderby fs.FenceCode
                            join s in stationList on fs.Stationsid equals s.SID into temp2
                            from t2 in temp2.DefaultIfEmpty()
                            where t2.SID != null
                            select new
                            {
                                t2.StationName,
                                t2.SID,
                            }).ToList(),
                AlarmGroups = (from fa in fenceAlarmGroupList
                            where fa.FenceCode == a.FenceCode && fa.AreaCode == a.AreaCode
                            orderby fa.FenceCode
                            join s in stationList on fa.Stationsid equals s.SID into temp3
                            from t3 in temp3.DefaultIfEmpty()
                            where t3.SID != null
                            select new
                            {
                                t3.StationName,
                                t3.SID,
                            }).ToList(),
                a.RSSIDelta1,
                a.RSSIDelta2,
                 a.CreateDate,
                 a.CreateUserAccount,
                 a.ModifyDate,
                 a.ModifyUserAccount
             })
            .Where(x => (param.FenceCode == null || x.FenceCode.ToUpper().Contains(param.FenceCode.ToUpper()))
                     && (param.CustomFenceCode == null || x.CustomFenceCode.ToUpper().Contains(param.CustomFenceCode.ToUpper()))
                     && (param.FenceType == null || x.FenceType.ToUpper().Contains(param.FenceType.ToUpper()))
                     && (param.FenceName == null || x.FenceName.Contains(param.FenceName))
                     && (param.AreaCode == null || x.AreaCode.ToUpper().Contains(param.AreaCode.ToUpper()))
                     && (param.Enable == null || x.Enable == param.Enable)
                     && (param.PlaneCode == null || x.PlaneCode.ToUpper().Contains(param.PlaneCode.ToUpper()))
                     && (string.IsNullOrEmpty(param.SID) || x.Stations.Any(s => s.SID.ToUpper().Contains(param.SID.ToUpper())) || x.AlarmGroups.Any(ag => ag.SID.ToUpper().Contains(param.SID.ToUpper())))
                     && (string.IsNullOrEmpty(param.StationName) || x.Stations.Any(s => s.StationName.ToUpper().Contains(param.StationName.ToUpper())) || x.AlarmGroups.Any(ag => ag.StationName.ToUpper().Contains(param.StationName.ToUpper())))
                );

        // 取得總筆數
        int recordTotal = await query.CountAsync();

        // 根據 sortByField 和 sortDirection 進行動態排序
        query = sortDirection.ToLower() == "desc"
            ? query.OrderByDescending(x => EF.Property<object>(x, sortByField))
            : query.OrderBy(x => EF.Property<object>(x, sortByField));

        // 進行資料庫分頁
        var recordList = size == 0 
            ? await query.ToListAsync() // 如果 size == 0，表示不分頁，直接取回所有資料
            : await query.Skip(skip).Take(size).ToListAsync(); // 分頁查詢

        ReturnModel returnModel = new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = new
                    {
                        recordTotal,
                        recordList
                    }
                };

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);
    }
}
