﻿using Microsoft.EntityFrameworkCore;
using System.Runtime.CompilerServices;
using Web.Models.Controller;
using Web.Models.Controller.Schedule;
using Web.Models.Service;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;

namespace Web.Services;

public class AdminService(IDataAccessService dataAccessService,
    ICredentialService credentialService) : IAdminService
{
    private readonly IDataAccessService _dataAccessService = dataAccessService;
    private readonly UserResult _user = credentialService.UserResult;

    public async Task<ReturnModel> DeleteScheule(List<DeleteSchedule> paramList)
    {
        foreach (DeleteSchedule param in paramList)
        {
            var scheduleId = await _dataAccessService.DeleteAsync<ScheduleJob>(s => s.AppCode == _user.AppCode && s.AreaCode == param.AreaCode && s.ScheduleJobId == param.ScheduleJobId);
        }

        return new ReturnModel(StatusCodes.Status200OK, true, new { paramList[0].ScheduleJobId });
    }

    public async Task<ReturnModel> CreateSchedule(List<CreateSchedule> paramList)
    {
        string appCode = _user.AppCode;
        string userAccount = _user.Account;

        var notifyTypeCodeList = _dataAccessService.Fetch<SysCode>(s => s.CodeType == "NotifyType").Select(n => n.Code).ToList();

        CreateSchedule param = paramList.FirstOrDefault();

        ScheduleJob scheduleJob = new()
        {
            AppCode = appCode,
            AreaCode = param.AreaCode,
            JobType = param.JobType,
            JobDesc = param.JobDesc,
            Enable = param.Enable == "Y",
            Threahold = param.Threahold,
            ScheduleFreq = param.ScheduleFreq,
            ScheduleDay = param.ScheduleDay,
            ScheduleTime = param.ScheduleTime,
            Subject = param.Subject,
            ScheduleNotifies = new List<Repository.Models.Entities.ScheduleNotify>(),
            ScheduleDeparts = new List<Repository.Models.Entities.ScheduleDepart>(),
            CreateUserAccount = userAccount,
            CreateDate = DateTime.Now,
            ModifyDate = DateTime.Now
        };

        foreach (var dept in param.DeptList)
        {
            ScheduleDepart scheduleDepart = new()
            {
                AppCode = appCode,
                AreaCode = param.AreaCode,
                UsageDepartCode = dept,
                CreateUserAccount = userAccount,
                CreateDate = DateTime.Now,
                ModifyDate = DateTime.Now
            };

            scheduleJob.ScheduleDeparts.Add(scheduleDepart);
        }


        foreach (var notify in param.ScheduleNotifyList)
        {
            Repository.Models.Entities.ScheduleNotify scheduleNotify = new()
            {
                AppCode = appCode,
                AreaCode = param.AreaCode,
                NotifyType = notify.NotifyType,
                Source = notify.Source,
                ContactCode = notify.ContactCode,
                Email = notify.NotifyType == "EMAIL" ? notify.Email : null,
                CreateUserAccount = userAccount,
                CreateDate = DateTime.Now,
                ModifyDate = DateTime.Now
            };

            scheduleJob.ScheduleNotifies.Add(scheduleNotify);
        }

        int? scheduleJobId = await _dataAccessService.CreateAsync(scheduleJob);

        return new ReturnModel(StatusCodes.Status200OK, true, new { scheduleJobId });
    }

    public async Task<ReturnModel> UpdateSchedule(List<UpdateSchedule> paramList, [CallerMemberName] string callerName = null)
    {
        string appCode = _user.AppCode;
        string userAccount = _user.Account;

        _dataAccessService.BeginTransaction();

        foreach (UpdateSchedule param in paramList)
        {
            var scheduleJob = await _dataAccessService.Fetch<ScheduleJob>(s => s.AppCode == appCode && s.AreaCode == param.AreaCode && s.ScheduleJobId == param.ScheduleJobId).AsTracking().FirstOrDefaultAsync();

            await _dataAccessService.DeleteAsync<ScheduleDepart>(s => s.AppCode == appCode && s.AreaCode == param.AreaCode && s.ScheduleJobId == param.ScheduleJobId);
            await _dataAccessService.DeleteAsync<Repository.Models.Entities.ScheduleNotify>(s => s.AppCode == appCode && s.AreaCode == param.AreaCode && s.ScheduleJobId == param.ScheduleJobId);

            scheduleJob.AreaCode = param.AreaCode;
            scheduleJob.JobType = param.JobType;
            scheduleJob.Enable = param.Enable == "Y";
            scheduleJob.JobDesc = param.JobDesc;
            scheduleJob.Threahold = param.Threahold;
            scheduleJob.ScheduleFreq = param.ScheduleFreq;
            scheduleJob.ScheduleDay = param.ScheduleDay;
            scheduleJob.ScheduleTime = param.ScheduleTime;
            scheduleJob.Subject = param.Subject;

            scheduleJob.ModifyUserAccount = userAccount;
            scheduleJob.ModifyDate = DateTime.Now;

            // 排除 ScheduleDeparts，僅包含需要更新的屬性名稱
            var updatePropertyNames = new[]
            {
                nameof(ScheduleJob.AreaCode),
                nameof(ScheduleJob.JobType),
                nameof(ScheduleJob.Enable),
                nameof(ScheduleJob.JobDesc),
                nameof(ScheduleJob.Threahold),
                nameof(ScheduleJob.ScheduleFreq),
                nameof(ScheduleJob.ScheduleDay),
                nameof(ScheduleJob.ScheduleTime),
                nameof(ScheduleJob.Subject),
                nameof(ScheduleJob.ModifyUserAccount),
                nameof(ScheduleJob.ModifyDate)
            };

            // 呼叫 UpdateAsync 並傳遞屬性名稱陣列
            await _dataAccessService.UpdateAsync(scheduleJob, updatePropertyNames, callerName);

            foreach (var dept in param.ScheduleDeparts)
            {
                ScheduleDepart scheduleDepart = new()
                {
                    AppCode = appCode,
                    AreaCode = param.AreaCode,
                    ScheduleJobId = param.ScheduleJobId,
                    UsageDepartCode = dept,
                    CreateUserAccount = userAccount,
                    CreateDate = DateTime.Now,
                    ModifyDate = DateTime.Now
                };

                await _dataAccessService.CreateAsync(scheduleDepart);
            }


            foreach (var notify in param.ScheduleNotifies)
            {
                Repository.Models.Entities.ScheduleNotify scheduleNotify = new()
                {
                    AppCode = appCode,
                    AreaCode = param.AreaCode,
                    ScheduleJobId = param.ScheduleJobId,
                    NotifyType = notify.NotifyType,
                    Source = notify.Source,
                    ContactCode = notify.ContactCode,
                    Email = notify.NotifyType == "EMAIL" ? notify.Email : null,
                    CreateUserAccount = userAccount,
                    CreateDate = DateTime.Now,
                    ModifyDate = DateTime.Now
                };

                await _dataAccessService.CreateAsync(scheduleNotify);
            }

        }

        await _dataAccessService.CommitAsync();
        
        return new ReturnModel(StatusCodes.Status200OK, true, new { paramList[0].ScheduleJobId });
    }
}
