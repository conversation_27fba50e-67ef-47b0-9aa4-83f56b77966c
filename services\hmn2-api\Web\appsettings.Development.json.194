{
  "DetailedErrors": true,
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.AspNetCore.SignalR": "Warning",
      "Microsoft.AspNetCore.Http.Connections.Internal.Transports.WebSocketsTransport": "Warning",
      "Microsoft.EntityFrameworkCore.ChangeTracking": "Warning",
      "Microsoft.EntityFrameworkCore.Database": "Warning"
    }
  },
  "ConnectionStrings": {
    "FusionS3HMNConnection": "server=************;port=8260;database=FusionS3HMN;uid=postgres;pwd=*************;",
    "AuditedTables": "Department,UserData"
  },
  "FusionNetParam": {
    "ApiUrl": "https://app-fusion-br.fihtdc.com:8080",
    "ApiVersion": "v3",
    "SecretKey": "B4hvLfSBj5FlhxnRCd9oe0Z1hqeCimao"
  },
  "MQTTParam": {
    "HostIp": "app-fusion-br.fihtdc.com",
    "HostPort": "8885", //default: if 域名 9002 else 8201 
    "UseTLS": "Y", //default: if 域名 Y else N 
    "Timeout": 5000,
    "SelfCertificateFile": "C:\\ConsoleSelfCertified\\FusionNetCA.crt",
    "UserName": "",
    "Password": ""
  },
  "MQTTServerParam": {
    "HostPort": "8888",
	"ConnectionBacklog": 100,
	"MaxPendingMessagesPerClient": 1000
  },
  "Cors": {
    "AllowOrigin": "*"
  },
  "AppInfo": {
    "AppCode": "HMN",
    "RequestIdHeaderName": "X-Request-ID"
  },
  "MapInfo": {
    "SectorUrl": "https://pcrm.fusionnet.io/hmnweb/upload/img/sector/",
    "PlaneUrl": ""
  },
  "HMNAlpha": {
    "WebUrl": "http://************/hmnweb/"
  },
  "CameraEtl": {
    "WorkingDirectory": "Etls",
	"WorkingFileName": "fnc-application-hmn-camera-etl-v0.0.1.jar"
  }
}