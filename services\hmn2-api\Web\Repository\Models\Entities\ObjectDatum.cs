﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class ObjectDatum
{
    public int ObjectId { get; set; }

    public string AppCode { get; set; } = null!;

    public string? AreaCode { get; set; }

    /// <summary>
    /// 1:協助人員; 2:傳送; 3:院內人員;4:設備; 5:病患; 0:空間
    /// </summary>
    public string? ObjectType { get; set; }

    public string ObjectCode { get; set; } = null!;

    public bool Active { get; set; }

    public bool Enable { get; set; }

    public string Name { get; set; } = null!;

    public string? GroupCode { get; set; }

    public string? UsageDepartCode { get; set; }

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }

    /// <summary>
    /// 對象備註,******** add
    /// </summary>
    public string? Remark { get; set; }

    /// <summary>
    /// 設備狀態,******** add
    /// </summary>
    public string? EquipmentStatus { get; set; }

    public string? UrgColor { get; set; }
}
