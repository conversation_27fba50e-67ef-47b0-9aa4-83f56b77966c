﻿using DocumentFormat.OpenXml.Office2010.Excel;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Data;
using Web.Constant;
using Web.Models.Controller;
using Web.Models.Controller.Building;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;
using Web.Validation;

namespace Web.Controller;

/// <summary>
/// CameraEtl控制器
/// </summary>
[Route("[controller]")]
[Authorize]
public class VideoCaptureController(IDataAccessService dataAccessService,
                            ICredentialService credentialService,
                            IRequestContextService requestContextService,
                            IUtilityService utilityService,
                            ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly IUtilityService _utilityService = utilityService;

    [HttpGet("tasks")]
    public async Task<IActionResult> RetrieveVideoCaptureTask([FromQuery] RetrieveVideoCaptureTask queryParam)
    {
        RetrieveVideoCaptureTask param = queryParam;
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // 排序；如果沒有 sort 條件預設使用 ModifyDate 降冪排序
        string sort = string.IsNullOrEmpty(param.sort) ? "ModifyDate:desc" : param.sort;
        string sortByField = sort.Split(":")[0];
        string sortDirection = sort.Split(":")[1];

        // 開始查詢事件影像備份任務資料
        var videoCaptureTaskList = _dataAccessService.Fetch<VideoCaptureTask>(x => x.AppCode == _user.AppCode && _user.DeptCodeList.Any(dc => dc == x.DeptCode));
        var taskDataList = _dataAccessService.Fetch<TaskDatum>(x => x.AppCode == _user.AppCode);
        var videoCaptureTaskVideoList = _dataAccessService.Fetch<VideoCaptureTaskVideo>();

        // 處理 param.Id 參數為 idList
        List<int> idList = param.Id?.Split(',').Where(y => int.TryParse(y, out _)).Select(int.Parse).ToList() ?? new List<int>();

        // 處理 param.DeptCode 參數為 deptCodeList
        List<string> deptCodeList = param.DeptCode?.Split(',').ToList() ?? new List<string>();

        // 處理 param.ServiceCode 參數為 serviceCodeList
        List<string> serviceCodeList = param.ServiceCode?.Split(',').ToList() ?? new List<string>();

        // 處理 param.TaskId 參數為 taskIdList
        List<int> taskIdList = param.TaskId?.Split(',').Where(y => int.TryParse(y, out _)).Select(int.Parse).ToList() ?? new List<int>();

        // 處理 param.TaskDataId 參數為 taskDataIdList
        List<int> taskDataIdList = param.TaskDataId?.Split(',').Where(y => int.TryParse(y, out _)).Select(int.Parse).ToList() ?? new List<int>();

        // 處理 param.TaskAction 參數為 taskActionList
        List<int> taskActionList = param.TaskAction?.Split(',').Where(y => int.TryParse(y, out _)).Select(int.Parse).ToList() ?? new List<int>();

        // 處理 param.EventCode 參數為 eventCodeList
        List<string> eventCodeList = param.EventCode?.Split(',').ToList() ?? new List<string>();

        // 處理 param.EventName 參數為 eventNameList
        List<string> eventNameList = param.EventName?.Split(',').ToList() ?? new List<string>();

        // 處理 param.ObjectName 參數為 objectNameList
        List<string> objectNameList = param.ObjectName?.Split(',').ToList() ?? new List<string>();

        // 處理 param.DeviceName 參數為 deviceNameList
        List<string> deviceNameList = param.DeviceName?.Split(',').ToList() ?? new List<string>();

        // 處理 param.DeviceType 參數為 deviceTypeList
        List<string> deviceTypeList = param.DeviceType?.Split(',').ToList() ?? new List<string>();

        // 處理 param.TaskStationSid 參數為 taskStationSidList
        List<string> taskStationSidList = param.TaskStationSid?.Split(',').ToList() ?? new List<string>();

        // 處理 param.TaskStartsAtBetween 參數為 taskStartsAtBegin 與 taskStartsAtEnd
        DateTime? taskStartsAtBegin = null;
        DateTime? taskStartsAtEnd = null;
        if (!string.IsNullOrWhiteSpace(param.TaskStartsAtBetween))
        {
            string[] timeBounds = param.TaskStartsAtBetween.Split(',');
            if (timeBounds.Length > 0 && !string.IsNullOrWhiteSpace(timeBounds[0]))
            {
                taskStartsAtBegin = DateTime.Parse(timeBounds[0]);
            }

            if (timeBounds.Length > 1 && !string.IsNullOrWhiteSpace(timeBounds[1]))
            {
                taskStartsAtEnd = DateTime.Parse(timeBounds[1]);
            }
        }

        var query = (from a in videoCaptureTaskList
                     join b in taskDataList on a.TaskId equals b.TaskId into temp
                     from t in temp.DefaultIfEmpty()
                     select new
                     {
                         a.Id,
                         Active = (a.Active == true) ? "Y" : "N",
                         a.DeptCode,
                         a.ServiceCode,
                         a.TaskId,
                         TaskDataId = t.Id,
                         TaskAction = t.Action,
                         a.EventCode,
                         a.EventName,
                         a.ObjectName,
                         a.DeviceName,
                         a.DeviceType,
                         a.TaskStationSid,
                         a.TaskStartsAt,
                         a.BackupDirectory,
                         a.Account,
                         a.Password,
                         a.TaskStartedFragment,
                         a.FragmentCount,
                         a.TotalVideoCount,
                         a.TaskResult,
                         a.CreateDate,
                         a.ModifyDate
                     })
            .Where(x => (!idList.Any() || idList.Contains(x.Id))
                     && (string.IsNullOrEmpty(param.Active) || x.Active == param.Active)
                     && (!deptCodeList.Any() || deptCodeList.Any(y => x.DeptCode.ToUpper().Contains(y.ToUpper())))
                     && (!serviceCodeList.Any() || serviceCodeList.Any(y => x.ServiceCode.ToUpper().Contains(y.ToUpper())))
                     && (!taskIdList.Any() || taskIdList.Contains(x.TaskId))
                     && (!taskDataIdList.Any() || taskDataIdList.Contains(x.TaskDataId))
                     && (!taskActionList.Any() || (x.TaskAction.HasValue && taskActionList.Contains(x.TaskAction.Value)))
                     && (!eventCodeList.Any() || eventCodeList.Any(y => x.EventCode.ToUpper().Contains(y.ToUpper())))
                     && (!eventNameList.Any() || eventNameList.Any(y => x.EventName.ToUpper().Contains(y.ToUpper())))
                     && (!objectNameList.Any() || objectNameList.Any(y => x.ObjectName.ToUpper().Contains(y.ToUpper())))
                     && (!deviceNameList.Any() || deviceNameList.Any(y => x.DeviceName.ToUpper().Contains(y.ToUpper())))
                     && (!deviceTypeList.Any() || deviceTypeList.Any(y => x.DeviceType.ToUpper().Contains(y.ToUpper())))
                     && (!taskStationSidList.Any() || taskStationSidList.Any(y => x.TaskStationSid.ToUpper().Contains(y.ToUpper())))
                     && ((!taskStartsAtBegin.HasValue || !taskStartsAtEnd.HasValue) || taskStartsAtBegin.Value <= x.TaskStartsAt && x.TaskStartsAt < taskStartsAtEnd.Value)
                     && (param.FragmentCount == null || x.FragmentCount == int.Parse(param.FragmentCount))
                     && (param.TaskStartedFragment == null || x.TaskStartedFragment == int.Parse(param.TaskStartedFragment))
                     && (param.TotalVideoCount == null || x.TotalVideoCount == int.Parse(param.TotalVideoCount))
                     && (param.TaskResult == null || x.TaskResult == int.Parse(param.TaskResult))
                );

        // 取得總筆數
        int recordTotal = await query.CountAsync();

        // 根據 sortByField 和 sortDirection 進行動態排序
        query = sortDirection.ToLower() == "desc"
            ? query.OrderByDescending(x => EF.Property<object>(x, sortByField))
            : query.OrderBy(x => EF.Property<object>(x, sortByField));

        // 進行資料庫分頁
        var recordList = size == 0
            ? await query.ToListAsync() // 如果 size == 0，表示不分頁，直接取回所有資料
            : await query.Skip(skip).Take(size).ToListAsync(); // 分頁查詢

        ReturnModel returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal,
                recordList
            }
        };

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);
    }
    

    [HttpGet("tasks/{videoCaptureTaskIds}/videos")]
    public async Task<IActionResult> RetrieveVideoCaptureTaskVideos([FromRoute] string videoCaptureTaskIds)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 解析 taskIds 字串，將其轉換為 List<int>
        List<int> videoCaptureTaskIdList = videoCaptureTaskIds.Split(',')
                                      .Select(int.Parse)
                                      .ToList();

        // 開始查詢事件影像備份任務錄影檔案資料
        var videoCaptureTaskList = _dataAccessService.Fetch<VideoCaptureTask>(x => x.AppCode == _user.AppCode && _user.DeptCodeList.Any(dc => dc == x.DeptCode));
        var videoCaptureTaskVideoList = _dataAccessService.Fetch<VideoCaptureTaskVideo>();

        var result = (from a in videoCaptureTaskList
                     join b in videoCaptureTaskVideoList on a.Id equals b.VideoCaptureTaskId into temp
                     from v in temp.DefaultIfEmpty()
                     select new
                     {
                        v.Id,
                        v.VideoCaptureTaskId,
                        v.CameraId,
                        v.CameraCode,
                        v.CameraName,
                        v.CameraVideoFilename,
                        v.CameraVideoFileDeleted,
                        VideoFilePath = $"{a.BackupDirectory}\\{a.DeptCode}\\{a.TaskId}\\{v.CameraCode.Replace(":", "-")}\\{v.CameraVideoFilename}",
                        v.Fragment,
                        v.TaskVideoResult,
                        v.ModifyDate
                     })
                     .Where(x => videoCaptureTaskIdList.Contains(x.VideoCaptureTaskId))
                     .OrderBy(x => x.VideoCaptureTaskId)
                     .ThenBy(x => x.CameraName)
                     .ToList();

        // 取得總筆數
        int recordTotal = result.Count();

        // 進行資料庫分頁
        var recordList = result;

        ReturnModel returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal,
                recordList
            }
        };

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);
    }
}


