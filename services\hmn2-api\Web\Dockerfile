# syntax=docker/dockerfile:1

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# 複製專案檔案
COPY ["Web.csproj", "./"]
RUN dotnet restore "Web.csproj"

# 複製所有檔案並建置，但排除 appsettings*.json 檔案
COPY . .
RUN rm -f appsettings*.json
RUN dotnet build "Web.csproj" -c Release -o /app/build

# 發佈應用程式
FROM build AS publish
RUN dotnet publish "Web.csproj" -c Release -o /app/publish
RUN rm -f /app/publish/appsettings*.json

# 最終映像檔
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app
COPY --from=publish /app/publish .

# 創建 FileStorage 目錄
RUN mkdir -p /app/FileStorage
RUN mkdir -p /app/Etls

# 設定環境變數 - 這些可以被 docker compose 覆蓋
ENV ASPNETCORE_URLS=http://+:8111

# 暴露連接埠
EXPOSE 8111
EXPOSE 8112

# 啟動應用程式
ENTRYPOINT ["dotnet", "Web.dll"]
