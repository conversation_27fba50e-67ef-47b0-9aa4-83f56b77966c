﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.OData.Query;
using Newtonsoft.Json;
using System.Linq;
using Web.Models.Controller.ObjectDevicePosition;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;

[ApiController]
[Route("api/v1/Objects")]
public class ObjectDevicePositionController : ControllerBase
{
    private readonly IDataAccessService _dataAccessService;
    private readonly IDeviceService _deviceService;

    public ObjectDevicePositionController(IDataAccessService dataAccessService, IDeviceService deviceService)
    {
        _dataAccessService = dataAccessService;
        _deviceService = deviceService;
    }

    [HttpPost("GetDevicePosition")]
    public async Task<IActionResult> GetDevicePosition([FromForm] string account,
                                                       [FromForm] string passwords,
                                                       [FromQuery] ODataQueryOptions<ObjectDevicePositionResult> queryOptions)
    {
        try
        {
            var signUser = _dataAccessService.Fetch<UserDatum>(x => x.UserAccount == account.ToUpper() && x.Enable == true).FirstOrDefault();
            if (signUser == null || signUser.UserPassword != passwords)
            {
                return Unauthorized();
            }

            var appCode = signUser.AppCode;
            var areaCodeList = _dataAccessService.Fetch<VwUserDeptMonInfo>(x => x.AppCode == appCode && x.UserAccount == account.ToUpper())
                                                  .Select(x => x.AreaCode)
                                                  .Distinct()
                                                  .ToList();

            var areaList = _dataAccessService.Fetch<Area>(e => e.AppCode == appCode && areaCodeList.Contains(e.AreaCode));
            var buildingList = _dataAccessService.Fetch<Web.Repository.Models.Entities.Building>(e => e.AppCode == appCode && areaCodeList.Contains(e.AreaCode));
            var deptList = _dataAccessService.Fetch<Department>(e => e.AppCode == appCode && areaCodeList.Contains(e.AreaCode));
            var objectQuery = _dataAccessService.Fetch<ObjectDatum>(e => e.AppCode == appCode && areaCodeList.Contains(e.AreaCode));
            var deviceQuery = _dataAccessService.Fetch<ObjectDevice>(od => objectQuery.Any(o => o.AppCode == od.AppCode && o.ObjectCode == od.ObjectCode));

            var devicePositionList = await _deviceService.GetDevicePositionsList();
            if (devicePositionList == null)
            {
                return NotFound("No device positions found.");
            }

            var validDevicePositions = devicePositionList.Where(dp => dp.position != null && dp.@object != null && objectQuery.Any(o => o.ObjectCode == <EMAIL>));
            var objectDevicePositionList = validDevicePositions.Select(devicePosition =>
            {
                var @object = objectQuery.FirstOrDefault(o => o.ObjectCode == <EMAIL>);
                var objectDevice = deviceQuery.FirstOrDefault(d => d.Pid == devicePosition.pid);

                if (@object != null && objectDevice != null)
                {
                    return new ObjectDevicePositionResult
                    {
                        ObjectId = <EMAIL>,
                        AreaCode = objectDevice.AreaCode,
                        AreaName = areaList.FirstOrDefault(a => a.AreaCode == @object.AreaCode)?.AreaName,
                        ObjectCode = <EMAIL>,
                        ObjectName = <EMAIL>,
                        Enable = <EMAIL>,
                        UsageDepartCode = @object.UsageDepartCode,
                        UsageDepartName = deptList.FirstOrDefault(d => d.DeptCode == @object.UsageDepartCode)?.DeptName,
                        GroupCode = @object.GroupCode,
                        GroupName = _dataAccessService.Fetch<ObjectGroup>(e => e.AppCode == appCode && areaCodeList.Contains(e.AreaCode))
                                                      .FirstOrDefault(g => g.GroupCode == @object.GroupCode)?.GroupName,
                        ObjectType = @object.ObjectType,
                        Device = new Web.Models.Controller.ObjectDevicePosition.Device
                        {
                            Pid = devicePosition.pid,
                            Name = devicePosition.name,
                        },
                        Position = new Web.Models.Controller.ObjectDevicePosition.Position
                        {
                            PositionX = devicePosition.position.positionX,
                            PositionY = devicePosition.position.positionY,
                            Building = new Web.Models.Controller.ObjectDevicePosition.Building
                            {
                                CustomBuildingCode = devicePosition.position.plane != null ? buildingList.FirstOrDefault(b => b.BuildingCode == devicePosition.position.plane.code)?.CustomBuildingCode : null,
                                BuildingName = devicePosition.position.plane != null ? buildingList.FirstOrDefault(b => b.BuildingCode == devicePosition.position.plane.code)?.BuildingName : null,
                            },
                            Plane = new Web.Models.Controller.ObjectDevicePosition.Plane
                            {
                                CustomPlaneCode = devicePosition.position.plane != null ? _dataAccessService.Fetch<Web.Repository.Models.Entities.Plane>(x => x.AppCode == appCode)
                                                                      .FirstOrDefault(p => p.PlaneCode == devicePosition.position.plane.code)?.CustomPlaneCode : null,
                                PlaneName = devicePosition.position.plane != null ? _dataAccessService.Fetch<Web.Repository.Models.Entities.Plane>(x => x.AppCode == appCode)
                                                              .FirstOrDefault(p => p.PlaneCode == devicePosition.position.plane.code)?.PlaneName : null,
                                PlaneMapPath = devicePosition.position.plane != null ? _dataAccessService.Fetch<Web.Repository.Models.Entities.Plane>(x => x.AppCode == appCode)
                                                                .FirstOrDefault(p => p.PlaneCode == devicePosition.position.plane.code)?.PlaneMapPath : null,
                            },
                            Location = new Web.Models.Controller.ObjectDevicePosition.Location
                            {
                                LocCode = devicePosition.position.station != null ? _dataAccessService.Fetch<Web.Repository.Models.Entities.Location>(e => e.AppCode == appCode && areaCodeList.Contains(e.AreaCode))
                                                            .FirstOrDefault(l => l.LocCode == devicePosition.position.station.sid)?.LocCode : null,
                                LocationName = devicePosition.position.station != null ? _dataAccessService.Fetch<Web.Repository.Models.Entities.Location>(e => e.AppCode == appCode && areaCodeList.Contains(e.AreaCode))
                                                                 .FirstOrDefault(l => l.LocCode == devicePosition.position.station.sid)?.LocName : null,
                            },
                            Station = new Web.Models.Controller.ObjectDevicePosition.Station
                            {
                                Sid = devicePosition.position.station != null ? _dataAccessService.Fetch<Web.Repository.Models.Entities.Station>(e => e.AppCode == appCode && areaCodeList.Contains(e.AreaCode))
                                                         .FirstOrDefault(s => s.SID == devicePosition.position.station.sid)?.SID : null,
                                StatiionName = devicePosition.position.station != null ? _dataAccessService.Fetch<Web.Repository.Models.Entities.Station>(e => e.AppCode == appCode && areaCodeList.Contains(e.AreaCode))
                                                                 .FirstOrDefault(s => s.SID == devicePosition.position.station.sid)?.StationName : null,
                            },
                            LastPositionTime = devicePosition.position.latestPositionTime,
                        },
                    };
                }
                return null;
            }).Where(result => result != null).ToList();

            return Ok(objectDevicePositionList.AsQueryable());
        }
        catch (Exception ex)
        {
            return Problem(ex.Message);
        }
    }
}
