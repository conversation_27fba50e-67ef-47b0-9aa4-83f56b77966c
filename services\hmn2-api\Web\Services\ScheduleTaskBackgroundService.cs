﻿using System;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Web.Models.AppSettings;
using Web.Models.Controller;
using Web.Models.Service.Fusion;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;

public class ScheduleTaskBackgroundService : BackgroundService
{
    private readonly IServiceScopeFactory _serviceScopeFactory;
    
    public ScheduleTaskBackgroundService(IServiceScopeFactory serviceScopeFactory)
    {
        _serviceScopeFactory  = serviceScopeFactory;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            string logActionName = $"{GetType().Name}/ExecuteAsync";

            using (var scope = _serviceScopeFactory.CreateScope())
            {
                var dataAccessService = scope.ServiceProvider.GetRequiredService<IDataAccessService>();
                var logService = scope.ServiceProvider.GetRequiredService<ILogService>();
                var scheduleService = scope.ServiceProvider.GetRequiredService<IScheduleService>();
                var deviceService = scope.ServiceProvider.GetRequiredService<IDeviceService>();
                var noticeService = scope.ServiceProvider.GetRequiredService<INoticeService>();

                try
                {
                    logService.Logging("info", logActionName, "", "Start Schedule Task Background Service");

                    // 取得所有 organization 列表
                    List<Organization> organizations = dataAccessService.Fetch<Organization>().ToList();

                    foreach (Organization org in organizations)
                    {
                        if (org.AppCode == null)
                            continue;
                        string appCode = org.AppCode;

                        // 排程執行邏輯
                        await RunTask(dataAccessService, logService, scheduleService, deviceService, noticeService, appCode);
                    }

                    // 設定多久執行一次，目前每 1 小時執行一次
                    await Task.Delay(TimeSpan.FromHours(1), stoppingToken);

                    logService.Logging("info", logActionName, "", "Schedule Task Background Service Execution Finished");
                }
                catch (Exception ex)
                {
                    logService.Logging("info", logActionName, "", String.Format("Schedule Task Background Service Execution Failed:{0},{1}", ex.ToString(), DateTimeOffset.Now));
                }
            }
        }
    }

    private async Task RunTask(IDataAccessService dataAccessService, ILogService logService, IScheduleService scheduleService, IDeviceService deviceService, INoticeService noticeService, string appCode)
    {
        string logActionName = $"{GetType().Name}/RunTask";

        List<string> returnInnerMessage = new List<string>();

        logService.Logging("info", logActionName, "", $"[{appCode}] Schedule Task Start");

        string jobType = "LowBattery";

        logService.Logging("info", logActionName, "", $"[{appCode}] appCode:{appCode}, jobType:{jobType}");
        returnInnerMessage.Add($"appCode:{appCode}, jobType:{jobType}");

        string Day = DateTime.Now.Day.ToString("00");
        int Week = (int)DateTime.Now.DayOfWeek;
        int Hour = DateTime.Now.Hour;

        logService.Logging("info", logActionName, "", $"[{appCode}] Day:{Day}, Week:{Week}, Hour:{Hour}");
        returnInnerMessage.Add($"Day:{Day}, Week:{Week}, Hour:{Hour}");

        List<ScheduleJob> SchedDayMonth = scheduleService.FetchScheduleJobListByDayOfMonth(appCode, Day, Hour);
        List<ScheduleJob> SchedDayWeek = scheduleService.FetchScheduleJobListByDayOfWeek(appCode, Week, Hour);
        List<ScheduleJob> SchedEveryDay = scheduleService.FetchScheduleJobListByEveryDay(appCode, Hour);

        logService.Logging("info", logActionName, "", $"[{appCode}] SchedDayMonth 筆數:{SchedDayMonth.Count}");
        logService.Logging("info", logActionName, "", $"[{appCode}] SchedDayWeek 筆數:{SchedDayWeek.Count}");
        logService.Logging("info", logActionName, "", $"[{appCode}] SchedEveryDay 筆數:{SchedEveryDay.Count}");

        returnInnerMessage.Add($"SchedDayMonth 筆數:{SchedDayMonth.Count}");
        returnInnerMessage.Add($"SchedDayWeek 筆數:{SchedDayWeek.Count}");
        returnInnerMessage.Add($"SchedEveryDay 筆數:{SchedEveryDay.Count}");

        List<ScheduleJob> RunScheduleList = new List<ScheduleJob>();

        RunScheduleList.AddRange(SchedDayMonth);
        RunScheduleList.AddRange(SchedDayWeek);
        RunScheduleList.AddRange(SchedEveryDay);

        logService.Logging("info", logActionName, "", $"[{appCode}] RunScheduleList 筆數:{RunScheduleList.Count}");
        returnInnerMessage.Add($"RunScheduleList 筆數:{RunScheduleList.Count}");

        // 呼叫 IDataAccessService 的方法
        List<VwDeviceInfo> ObjectDeviceList = dataAccessService.Fetch<VwDeviceInfo>(x => x.AppCode == appCode).ToList();

        var deviceList = dataAccessService.Fetch<Device>(x => x.AppCode == appCode).ToList();

        List<Devices> DeviceCoreList = await GetCoreDeviceList(deviceService, appCode, string.Join(",", deviceList.Select(x => x.Pid)));

        List<dynamic> RunScheduleResult = new List<dynamic>();

        if (RunScheduleList.Count() == 0)
        {
            logService.Logging("info", logActionName, "", $"[{appCode}] 無排程資料，不會寄送Mail，亦不會call console API");
            returnInnerMessage.Add($"無排程資料，不會寄送Mail，亦不會call console API");
        }

        foreach (ScheduleJob schedule in RunScheduleList)
        {
            logService.Logging("info", logActionName, "", $"[{appCode}] ScheduleJobId:{schedule.ScheduleJobId}");
            returnInnerMessage.Add($"ScheduleJobId:{schedule.ScheduleJobId}");

            List<ScheduleDepart> ScheduleDepartList = scheduleService.FetchScheduleDepartList(schedule.ScheduleJobId);
            List<ScheduleNotify> ScheduleNotifyList = scheduleService.FetchScheduleNotifyList(schedule.ScheduleJobId);
            
            // 先取得所有電量低於門檻值的設備
            List<Devices> lowBatteryDeviceList = DeviceCoreList.Where(device => 
                        device.configurations!=null && device.configurations
                            .Any(config =>
                                config.resourceId == "/1035/0/2" &&
                                int.Parse(config.value) < int.Parse(schedule.Threahold))).ToList();

            logService.Logging("info", logActionName, "", $"[{appCode}] 所有電量低於門檻值的設備:{JsonConvert.SerializeObject(lowBatteryDeviceList.Select(t=>t.pid).ToList())}");
            returnInnerMessage.Add($"所有電量低於門檻值的設備:{JsonConvert.SerializeObject(lowBatteryDeviceList.Select(t=>t.pid).ToList())}");

            //簡化[再取得這些設備的部門是否為監控部門]LINQ的寫法 20230516
            List<Devices> monDeptLowBatteryDeviceList = lowBatteryDeviceList.Where(device =>
            {
                var objDevice = ObjectDeviceList.Find(deviceObj => deviceObj.Pid == device.pid);
                return ScheduleDepartList.Any(scheduleDepart =>
                    objDevice != null &&
                    scheduleDepart.AreaCode == objDevice.AreaCode &&
                    (scheduleDepart.UsageDepartCode == "ALL" ||
                        scheduleDepart.UsageDepartCode == objDevice.UsageDepartCode)
                );
            }).ToList();

            logService.Logging("info", logActionName, "", $"[{appCode}] 監控部門:{JsonConvert.SerializeObject(ScheduleDepartList.Select(t=>t.UsageDepartCode).ToList())}");
            returnInnerMessage.Add($"監控部門:{JsonConvert.SerializeObject(ScheduleDepartList.Select(t=>t.UsageDepartCode).ToList())}");

            logService.Logging("info", logActionName, "", $"[{appCode}] 監控部門下電量低於門檻值的設備筆數:{JsonConvert.SerializeObject(monDeptLowBatteryDeviceList.Count)}");
            returnInnerMessage.Add($"監控部門下電量低於門檻值的設備筆數:{JsonConvert.SerializeObject(monDeptLowBatteryDeviceList.Count)}");

            // 最後取得這些設備的資訊
            dynamic lowBatteryInfoList = monDeptLowBatteryDeviceList.Select(device => new {
                        usageDeptName = ObjectDeviceList.Find(deviceObj =>
                            deviceObj.Pid == device.pid).UsageDepartName,
                        objectName = ObjectDeviceList
                            .Find(deviceObj => deviceObj.Pid == device.pid).ObjectName,
                        deviceName = ObjectDeviceList
                            .Find(deviceObj => deviceObj.Pid == device.pid).Name,
                        value = device.configurations?.Find(config => config.resourceId == "/1035/0/2").value,
                        latestReportTime = device.configurations?.Find(config=> config.resourceId == "/1035/0/2").latestReportTime
            }).ToList();

            logService.Logging("info", logActionName, "", $"[{appCode}] Mail Subject:{schedule.Subject}");
            returnInnerMessage.Add($"Mail Subject:{schedule.Subject}");

            string html = "";
            if (lowBatteryInfoList.Count == 0)
            {
                //_dataLog.Logging("info", logActionName, requestUUID, $"ScheduleJobId:{schedule.ScheduleJobId} 排程檢查無低電量資料");

                html = $"{DateTime.Now:yyyyMMddHH} 排程檢查無低電量資料";

                returnInnerMessage.Add($"Mail Body:{schedule.Subject}");
            }
            else
            {
                //_dataLog.Logging("info", logActionName, requestUUID, $"監控部門下電量低於門檻值的設備資訊:{JsonConvert.SerializeObject(lowBatteryInfoList)}");

                List<string> title = new List<string> { "使用單位", "對象名稱", "裝置", "電量", "電量回報時間" };

                html = GetHtmlTable(lowBatteryInfoList, title);

                returnInnerMessage.Add($"Mail Body:為避免特殊字元，請至FusionLog table檢視 Mail Body");
            }

            returnInnerMessage.Add($"Mail Subject:{schedule.Subject}");

            string mailList = string.Join(",", ScheduleNotifyList.Select(p => p.Email).ToList());

            logService.Logging("info", logActionName, "", $"[{appCode}] Mail To:{mailList}");

            returnInnerMessage.Add($"Mail To:{mailList}");

            bool result = await noticeService.SendMail(appCode, schedule.Subject, mailList, html);

            logService.Logging("info", logActionName, "", $"[{appCode}] Send Mail Result:{result}");

            returnInnerMessage.Add($"Send Mail Result:{result}");

            RunScheduleResult.Add(new 
            { 
                schedule.Subject
                ,html
                ,mailList
                ,result
            });
        }

        // ReturnModel returnResult = new ReturnModel(true, "schedule done then close", RunScheduleResult, string.Join(",", returnInnerMessage)) ;

        //_dataLog.Logging("info", logActionName, requestUUID, $"ReturnResult:{JsonConvert.SerializeObject(returnResult)}");

        logService.Logging("info", logActionName, "", "[{appCode}] Schedule Task End");
    }

    private string GetHtmlTable<T>(IEnumerable<T> objects, List<string> tableTitle)
    {
        StringBuilder tableBuilder = new StringBuilder();
        tableBuilder.Append("<table border='1'>");

        // 取得所有屬性名稱
        var properties = typeof(T).GetProperties();
        tableBuilder.Append("<tr>");
        foreach (string title in tableTitle)
        {
            tableBuilder.Append($"<th>{title}</th>");
        }
        tableBuilder.Append("</tr>");

        // 建立每一個物件的 row
        foreach (var obj in objects)
        {
            tableBuilder.Append("<tr>");
            foreach (var property in properties)
            {
                tableBuilder.Append($"<td>{property.GetValue(obj)?.ToString()}</td>");
            }
            tableBuilder.Append("</tr>");
        }

        tableBuilder.Append("</table>");
        return tableBuilder.ToString();
    }

    private async Task<List<Devices>> GetCoreDeviceList(IDeviceService deviceService, string appCode, string pidList)
    {
        var coreDeviceList = await deviceService.GetDeviceList(appCode).ConfigureAwait(false);

        coreDeviceList = coreDeviceList.Where(x => pidList.Contains(x.pid)).ToList();

        return coreDeviceList;
    }
}
