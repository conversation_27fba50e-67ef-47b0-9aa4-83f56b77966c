﻿namespace Web.Exceptions;

using System;
using Web.Models.Controller;

/**
 * 
服務層:Service
控制器:Controller
中間件:Middleware
在 ASP.NET Core 中，异常处理通常有两种方法：在服务层抛出异常并在中间件中处理，或者在控制器中处理异常。下面我们将分别介绍这两种方法的优缺点和适用场景。

HMN2 目前採用方法一

方法一：在服务层抛出异常并在中间件中处理
优点：
集中化的异常处理：所有的异常处理逻辑都集中在中间件中，保持了控制器的简洁性。
减少代码重复：避免在每个控制器中重复编写相同的异常处理逻辑。
一致性：保证所有异常的处理方式一致，响应格式统一。
缺点：
服务层耦合到 HTTP：服务层需要依赖于特定的异常类型，这可能会引入与 HTTP 相关的逻辑，违反了纯业务逻辑的原则。
灵活性较低：在一些特殊情况下，控制器可能需要根据具体情况自定义响应，这种做法可能会限制这种灵活性。
适用场景：
项目规模较大，多个控制器有类似的异常处理逻辑。
需要统一的错误响应格式和处理方式。
希望简化控制器代码，提高代码的可维护性。

方法二：在控制器中处理异常
优点：
职责分离：保持服务层专注于业务逻辑，不涉及 HTTP 相关的逻辑。
灵活性：允许控制器根据不同情况自定义错误处理和响应格式。
更明确的错误处理：控制器可以根据具体业务需求和场景决定如何处理错误，提供更精准的错误信息。
缺点：
代码重复：需要在每个控制器中编写类似的错误处理逻辑，增加了代码量和维护成本。
一致性问题：不同控制器中可能会有不同的错误处理方式，导致响应格式不一致。
适用场景：
项目规模较小，控制器数量不多。
每个控制器对错误处理有不同的需求，不能统一处理。
需要在控制器中根据不同情况自定义响应和错误处理逻辑。

这两种方法在处理异常时有不同的关注点和适用场景，具体选择哪种方法取决于你的项目需求和开发团队的偏好。
 */


public class CustomException : Exception
{
    public ReturnModel returnModel { get; set; }
    public CustomException(string message) : base(message)
    {
    }

    public CustomException(string message, Exception innerException) : base(message, innerException)
    {
    }

    public CustomException(ReturnModel returnModel) : base(returnModel.title)
    {
        this.returnModel = returnModel;
    }
}
