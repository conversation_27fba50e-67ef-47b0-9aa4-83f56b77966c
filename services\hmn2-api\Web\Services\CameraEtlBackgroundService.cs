﻿using Microsoft.Extensions.Options;
using System.Diagnostics;
using Web.Models.AppSettings;
using Web.Services.Interfaces;

namespace Web.Services;

public class CameraEtlBackgroundService : BackgroundService
{
    private readonly CameraEtl _cameraEtl;
    public CameraEtlBackgroundService(IOptions<CameraEtl> cameraEtl)
    {
        _cameraEtl = cameraEtl.Value;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        // 當_cameraEtl.Enable有指定且指定值為N時，不啟動Camera ETL服務
        if (_cameraEtl.Enable != null && _cameraEtl.Enable == "N")
        {
            return;
        }

        // 關閉正在執行的程序
        System.Diagnostics.Process[] processs = System.Diagnostics.Process.GetProcessesByName("java");
        foreach (System.Diagnostics.Process p in processs) 
        { 
            p.Kill();
        }

        // 重新執行程序
        using (var camreaEtlProcess = new Process())
        {            
            camreaEtlProcess.StartInfo.WorkingDirectory = _cameraEtl.WorkingDirectory;
            camreaEtlProcess.StartInfo.UseShellExecute = true;
            camreaEtlProcess.StartInfo.CreateNoWindow = true;
            camreaEtlProcess.StartInfo.WindowStyle = ProcessWindowStyle.Normal;
            camreaEtlProcess.StartInfo.FileName = "java.exe";
            camreaEtlProcess.StartInfo.Arguments = String.Format("-jar \"{0}\"", _cameraEtl.WorkingFileName);
            camreaEtlProcess.Start();
        }        
    }
}
