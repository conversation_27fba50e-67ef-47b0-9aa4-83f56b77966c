﻿using Web.Models.Interface;
using System.ComponentModel.DataAnnotations;
using Web.Constant;
using Web.Validation;

namespace Web.Models.Controller.Camera;

public class Camera
{
    public class InStation
    {
        public string AreaCode { get; set; }
        public string StationSID { get; set; }
    }
    public class InDepartment
    {
        public string AreaCode { get; set; }
        public string DeptCode { get; set; }
    }
}

public class InCreateCamera
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "CameraMAC")]
    [Unique("", "Camera", "CameraMAC", ErrorMessage = Constants.ErrorCode.Unique + "CameraMAC")]
    [StringLength(20, ErrorMessage = Constants.ErrorCode.Length + "CameraMAC")]
    [RegularExpression(@"^([0-9A-Fa-f]{2}([-:]))([0-9A-Fa-f]{2}\2){4}[0-9A-Fa-f]{2}$", ErrorMessage = Constants.ErrorCode.Pattern + "CameraMAC")]
    [ListPropertyAllExists("AreaCode", "StationList", "StationSID", "Station", "SID", ErrorMessage = Constants.ErrorCode.NotFound + "StationList")]
    public string CameraMAC { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "CameraName")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "CameraName")]
    [ListPropertyAllExists("", "DepartmentList", "DeptCode", "Department", "DeptCode", ErrorMessage = Constants.ErrorCode.NotFound + "DepartmentList")]
    public string CameraName { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "IP")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "IP")]
    [RegularExpression(@"^((25[0-5]|2[0-4][0-9]|1?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|1?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|1?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|1?[0-9][0-9]?))$|^([0-9a-fA-F]{1,4}:){1,7}([0-9a-fA-F]{1,4})$", ErrorMessage = Constants.ErrorCode.Pattern + "IP")] // 檢查是否符合 IPv4 或 IPv6 格式
    public string IP { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "StreamURL")]
    [StringLength(128, ErrorMessage = Constants.ErrorCode.Length + "StreamURL")]
    public string StreamURL { get; set; }
    
    [StringLength(128, ErrorMessage = Constants.ErrorCode.Length + "CameraVideoPath")]
    public string? CameraVideoPath { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "PathUserName")]
    [RequiredWhenPresent("PathUserPassword", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PathUserName")]
    public string? PathUserName { get; set; }
    
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "PathUserPassword")]
    [RequiredWhenPresent("PathUserName", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PathUserPassword")]
    public string? PathUserPassword { get; set; }

    public List<Camera.InStation> StationList { get; set; }
    
    public List<Camera.InDepartment> DepartmentList { get; set; }
}

public class InUpdateCamera
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "CameraMAC")]
    [Exists("AreaCode", "Camera", "CameraMAC", ErrorMessage = Constants.ErrorCode.NotFound + "CameraMAC")]
    [StringLength(20, ErrorMessage = Constants.ErrorCode.Length + "CameraMAC")]
    [RegularExpression(@"^([0-9A-Fa-f]{2}([-:]))([0-9A-Fa-f]{2}\2){4}[0-9A-Fa-f]{2}$", ErrorMessage = Constants.ErrorCode.Pattern + "CameraMAC")]
    [ListPropertyAllExists("AreaCode", "StationList", "StationSID", "Station", "SID", ErrorMessage = Constants.ErrorCode.NotFound + "StationList")]
    public string CameraMAC { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "CameraName")]
    [ListPropertyAllExists("", "DepartmentList", "DeptCode", "Department", "DeptCode", ErrorMessage = Constants.ErrorCode.NotFound + "DepartmentList")]
    public string CameraName { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "IP")]
    [RegularExpression(@"^((25[0-5]|2[0-4][0-9]|1?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|1?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|1?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|1?[0-9][0-9]?))$|^([0-9a-fA-F]{1,4}:){1,7}([0-9a-fA-F]{1,4})$", ErrorMessage = Constants.ErrorCode.Pattern + "IP")] // 檢查是否符合 IPv4 或 IPv6 格式
    public string IP { get; set; }

    [StringLength(128, ErrorMessage = Constants.ErrorCode.Length + "StreamURL")]
    public string StreamURL { get; set; }

    [StringLength(128, ErrorMessage = Constants.ErrorCode.Length + "CameraVideoPath")]
    public string? CameraVideoPath { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "PathUserName")]
    [RequiredWhenPresent("PathUserPassword", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PathUserName")]
    public string? PathUserName { get; set; }
    
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "PathUserPassword")]
    [RequiredWhenPresent("PathUserName", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PathUserPassword")]
    public string? PathUserPassword { get; set; }

    public List<Camera.InStation> StationList { get; set; }

    public List<Camera.InDepartment> DepartmentList { get; set; }
}

public class InRetrieveCamera : IPaginationRequest
{
    public string page { get; set; }
    public string size { get; set; }
    public string AreaCode { get; set; }
    public string DeptCode { get; set; }
    public string CameraMAC { get; set; }
    public string CameraName { get; set; }
    public string StationSID { get; set; }
}

public class InDeleteCamera
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "CameraMAC")]
    [Exists("AreaCode", "Camera", "CameraMAC", ErrorMessage = Constants.ErrorCode.NotFound + "CameraMAC")]
    [StringLength(20, ErrorMessage = Constants.ErrorCode.Length + "CameraMAC")]
    [RegularExpression(@"^([0-9A-Fa-f]{2}([-:]))([0-9A-Fa-f]{2}\2){4}[0-9A-Fa-f]{2}$", ErrorMessage = Constants.ErrorCode.Pattern + "CameraMAC")]
    public string CameraMAC { get; set; }
}

public class InCreateVideoCapture
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; } = null!;

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "DeptCode")]
    [Exists("AreaCode", "Department", "DeptCode", ErrorMessage = Constants.ErrorCode.NotFound + "DeptCode")]
    public string DeptCode { get; set; } = null!;

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ServiceCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "ServiceCode")]
    [Unique("AreaCode,DeptCode", "VideoCapture", "ServiceCode", ErrorMessage = Constants.ErrorCode.Unique + "ServiceCode")]
    public string ServiceCode { get; set; } = null!;

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PrefixMinute")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "PrefixMinute")]
    [RegularExpression(@"^([1-9]|1[0-5])$", ErrorMessage = Constants.ErrorCode.Pattern + "PrefixMinute")]
    public string PrefixMinute { get; set; } = null!;

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Suffix")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "Suffix")]
    [RegularExpression(@"^([1-9]|1[0-5])$", ErrorMessage = Constants.ErrorCode.Pattern + "Suffix")]
    public string Suffix { get; set; } = null!;
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "BackupDirectory")]
    [StringLength(128, ErrorMessage = Constants.ErrorCode.Length + "BackupDirectory")]
    public string BackupDirectory { get; set; } = null!;

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "Account")]
    [RequiredWhenPresent("Password", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Account")]
    public string? Account { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "Password")]
    [RequiredWhenPresent("Account", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Password")]
    public string? Password { get; set; }
    
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "PositionCapture")]
    public string? PositionCapture { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "KeepDays")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "KeepDays")]
    [RegularExpression(@"^([1-9]|[1-8][0-9]|90)$", ErrorMessage = Constants.ErrorCode.Pattern + "KeepDays")]
    public string KeepDays { get; set; } = null!;
}

public class InRetrieveVideoCapture
{
    public string page { get; set; }
    public string size { get; set; }
    public string AreaCode { get; set; } = null!;
    public string DeptCode { get; set; } = null!;
    public string ServiceCode { get; set; } = null!;
}

public class InDeleteVideoCapture
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; } = null!;

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "DeptCode")]
    [Exists("AreaCode", "Department", "DeptCode", ErrorMessage = Constants.ErrorCode.NotFound + "DeptCode")]
    public string DeptCode { get; set; } = null!;

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ServiceCode")]
    [Exists("AreaCode,DeptCode", "VideoCapture", "ServiceCode", ErrorMessage = Constants.ErrorCode.Unique + "ServiceCode")]
    public string? ServiceCode { get; set; } = null;
}

public class InRetrieveTaskVideoCaptureCamera
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    public string UserDataId { get; set; }
    public string IdList { get; set; }
    public string TaskStartsAtBetween { get; set; }
    public string TaskServiceCodeList { get; set; }
    public string TaskAction { get; set; }
    public string TaskSponsorObjectName { get; set; }
    public string TaskSponsorDeviceType { get; set; }
}

public class InRetrieveGetCamera
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
}