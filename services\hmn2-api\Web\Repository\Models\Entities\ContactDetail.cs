﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class ContactDetail
{
    public int Id { get; set; }

    public string AppCode { get; set; } = null!;

    public string ContactCode { get; set; } = null!;

    public string ContactType { get; set; } = null!;

    public string ContactValue { get; set; } = null!;

    public string? CreateUserAccount { get; set; }

    public DateTime? CreateDate { get; set; }
}