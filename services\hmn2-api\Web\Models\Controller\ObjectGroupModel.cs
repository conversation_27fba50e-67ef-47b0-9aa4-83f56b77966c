﻿
using System.ComponentModel.DataAnnotations;
using Web.Validation;
using Web.Constant;

namespace Web.Models.Controller.ObjectGroup;

public class RetrieveObjectGroup
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    public string GroupCode { get; set; }
    public string GroupName { get; set; }
    public string AreaCode { get; set; }

    public string Active { get; set; }
    public string Enable { get; set; }
    public string ObjectCode { get; set; }
    public string ObjectName { get; set; }
    public string ObjectUsageDepartCode { get; set; }
}

public class CreateObjectGroup
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "GroupCode")]
    [Unique("", "ObjectGroup", "GroupCode", true, ErrorMessage = Constants.ErrorCode.Unique + "GroupCode")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "GroupCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "GroupCode")]
    public string GroupCode { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "GroupName")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "GroupName")]
    public string GroupName { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Enable")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }
    
    [ListAllExists("AreaCode", "ObjectDatum", "ObjectCode", ErrorMessage = Constants.ErrorCode.NotFound + "ObjectCodeList")]
    public List<string> ObjectCodeList { get; set; }
}

public class UpdateObjectGroup
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "GroupCode")]
    [Exists("", "ObjectGroup", "GroupCode", ErrorMessage = Constants.ErrorCode.NotFound + "GroupCode")]
    [HasReferenceWhenEquals("Enable", "N", "AreaCode", "ObjectDatum", "GroupCode", ErrorMessage = Constants.ErrorCode.Reference + "GroupCode")]
    public string GroupCode { get; set; }
    
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "GroupName")]
    public string GroupName { get; set; }
    
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }
    
    [ListAllExists("AreaCode", "ObjectDatum", "ObjectCode", ErrorMessage = Constants.ErrorCode.NotFound + "ObjectCodeList")]
    public List<string> ObjectCodeList { get; set; }
}

public class DeleteObjectGroup
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "GroupCode")]
    [Exists("", "ObjectGroup", "GroupCode", ErrorMessage = Constants.ErrorCode.NotFound + "GroupCode")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "GroupCode")]
    [HasReference("", "ObjectDatum", "GroupCode", ErrorMessage = Constants.ErrorCode.Reference + "GroupCode")]
    public string GroupCode { get; set; }
}
