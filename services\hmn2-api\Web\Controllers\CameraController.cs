﻿using Dapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Linq.Expressions;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.Json;
using System.Text.RegularExpressions;
using Web.Constant;
using Web.Models.Controller;
using Web.Models.Controller.Camera;
using Web.Models.Service;
using Web.Models.Service.Fusion;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;
using Web.Validation;
using Camera = Web.Repository.Models.Entities.Camera;

namespace Web.Controller;

public class TaskDataInfo
{
    /// <summary>
    /// 索引
    /// </summary>
    public int? Id { get; set; }
    /// <summary>
    /// 任務索引
    /// </summary>
    public int? TaskId { get; set; }
    /// <summary>
    /// 任務處理情形
    /// </summary>
    public int? Action { get; set; }
    /// <summary>
    /// 任務狀態
    /// </summary>
    public bool? Active { get; set; }
    /// <summary>
    /// 服務代碼
    /// </summary>
    public string? ServiceCode { get; set; }
    /// <summary>
    /// 事件代碼
    /// </summary>
    public string? EventCode { get; set; }
    /// <summary>
    /// 事件名稱
    /// </summary>
    public string? EventName { get; set; }
    /// <summary>
    /// 對象代碼
    /// </summary>
    public string? SponsorObjectCode { get; set; }
    /// <summary>
    /// 對象名稱
    /// </summary>
    public string? SponsorObjectName { get; set; }
    /// <summary>
    /// 對象類別
    /// </summary>
    public string? SponsorObjectType { get; set; }
    /// <summary>
    /// 裝置代碼
    /// </summary>
    public string? SponsorDevicePid { get; set; }
    /// <summary>
    /// 裝置名稱
    /// </summary>
    public string SponsorDeviceName { get; set; }
    /// <summary>
    /// 裝置類別
    /// </summary>
    public string? SponsorDeviceType { get; set; }
    /// <summary>
    /// 發生時間
    /// </summary>
    public string? StartsAt { get; set; }
}

public class CameraInfo
{
    /// <summary>
    /// 索引
    /// </summary>
    public int? Id { get; set; }
    /// <summary>
    /// 攝影機Mac
    /// </summary>
    public string? CameraMac { get; set; }
    /// <summary>
    /// 攝影機Ip
    /// </summary>
    public string? CameraIp { get; set; }
    /// <summary>
    /// 攝影機名稱
    /// </summary>
    public string? CameraName { get; set; }
    /// <summary>
    /// 攝影機串流網址
    /// </summary>
    public string? StreamUrl { get; set; }
    public bool? IsAlive { get; set; }
}

/// <summary>
/// 任務影片資訊
/// </summary>
public class TaskVideoInfo
{
    /// <summary>
    /// 索引
    /// </summary>
    public int? Id { get; set; }
    /// <summary>
    /// 分段
    /// </summary>
    public int? Fragment { get; set; }
    /// <summary>
    /// 檔案名稱
    /// </summary>
    public string? FileName { get; set; }
}

public class TaskVideoCameraInfo
{
    /// <summary>
    /// 索引
    /// </summary>
    public int? Id { get; set; }
    /// <summary>
    /// 任務資訊
    /// </summary>
    public TaskDataInfo? Task { get; set; }
    /// <summary>
    /// 任務發生影片分段
    /// </summary>
    public int? TaskStartsAtVideoFragment { get; set; }
    /// <summary>
    /// 攝影機資訊
    /// </summary>
    public CameraInfo? Camera { get; set; }
    /// <summary>
    /// 擷取前綴時間
    /// </summary>
    public string? CapturePrefixTime { get; set; }
    /// <summary>
    /// 擷取後綴時間
    /// </summary>
    public string? CaptureSuffixTime { get; set; }
    /// <summary>
    /// 擷取狀態
    /// </summary>
    public string? CaptureStatus { get; set; }
    /// <summary>
    /// 影片目錄路徑
    /// </summary>
    public string? VideoDirectoryPath { get; set; }
    /// <summary>
    /// 影片目錄帳號
    /// </summary>
    public string? VideoDirectoryAccount { get; set; }
    /// <summary>
    /// 影片目錄密碼
    /// </summary>
    public string? VideoDirectoryPassword { get; set; }
    /// <summary>
    /// 影片集合
    /// </summary>
    public List<TaskVideoInfo>? Videos { get; set; }
}

/// <summary>
/// Camera 控制器
/// </summary>
[Route("[controller]")]
[Authorize]
public class CameraController(IDataAccessService dataAccessService,
                            ICredentialService credentialService,
                            IPreferenceService preferenceService,
                            ICameraService cameraService,
                            IRequestContextService requestContextService,
                            ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly IPreferenceService _preferenceService = preferenceService;
    private readonly ICameraService _cameraService = cameraService;

    /// <summary>
    /// 新增攝影機
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    /// <spec>
    /// Jira: https://tpe-jira2.fihtdc.com/browse/FSN-6159
    /// 新增攝影機功能在Spec. V1.009中提到的相關檢查與動作
    /// https://tpe-jira2.fihtdc.com/secure/attachment/2144159/screenshot-1.png
    /// https://tpe-jira2.fihtdc.com/secure/attachment/2144160/screenshot-2.png
    /// </spec>
    [HttpPost("cameras")]
    [RequestParamListDuplicate("CameraMAC")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateCamera([FromBody] List<InCreateCamera> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;
        string appCode = _user.AppCode;

        List<ReturnError> errors = [];

        // 同步跟DB取得資料
        var results = await _dataAccessService.ExecuteParallelQueriesAsync(
            ("C", async context => await context.Cameras.Where(e => e.AppCode == appCode).ToListAsync()),
            ("A", async context => await context.Areas.Where(e => e.AppCode == appCode).ToListAsync()),
            ("S", async context => await context.Stations.Where(e => e.AppCode == appCode).ToListAsync()),
            ("D", async context => await context.Departments.Where(e => e.AppCode == appCode).ToListAsync()),
            ("CD", async context => await context.CameraDepartments.Where(e => e.AppCode == appCode).ToListAsync()),
            ("CS", async context => await context.CameraStations.Where(e => e.AppCode == appCode).ToListAsync())
        );

        // 解包結果
        var cameras = (List<Camera>)results["C"];
        var areas = (List<Area>)results["A"];
        var stations = (List<Station>)results["S"];
        var departments = (List<Department>)results["D"];
        var cameraDepartments = (List<CameraDepartment>)results["CD"];
        var cameraStations = (List<CameraStation>)results["CS"];

        _logService.Logging("info", logActionName, requestUUID, "Camera Data Validated, start append.");
        _dataAccessService.BeginTransaction();

        List<AddCameraInput> addCameraInputs = [];

        foreach (InCreateCamera item in paramList)
        {
            AddCameraInput addCameraInput = new()
            {
                code = item.CameraMAC,
                name = item.CameraName,
                stationSids = item.StationList?.Select(e => e.StationSID).ToList() ?? [],
                configuration = new FusionCamera.Configuration
                {
                    ip = item.IP,
                    cameraVideoPath = item.CameraVideoPath,
                    streamUrl = item.StreamURL,
                    username = item.PathUserName,
                    userAuth = item.PathUserPassword
                }
            };

            addCameraInputs.Add(addCameraInput);

            Camera camera = new()
            {
                AppCode = appCode,
                AreaCode = item.AreaCode,
                CameraMAC = item.CameraMAC,
                CameraName = item.CameraName,
                CameraVideoPath = item.CameraVideoPath,
                IP = item.IP,
                StreamURL = item.StreamURL,
                PathUserName = item.PathUserName,
                PathUserPassword = item.PathUserPassword,
                CreateUserAccount = _user.Account,
                CreateDate = DateTime.Now,
                ModifyDate = DateTime.Now
            };

            if (item.StationList != null && item.StationList.Count > 0)
            {
                foreach (var station in item.StationList)
                {
                    CameraStation cameraStation = new()
                    {
                        AppCode = appCode,
                        AreaCode = station.AreaCode,
                        CameraMAC = item.CameraMAC,
                        SID = station.StationSID,
                        CreateUserAccount = _user.Account,
                        CreateDate = DateTime.Now
                    };

                    await _dataAccessService.CreateAsync(cameraStation);
                }
            }

            if (item.DepartmentList != null && item.DepartmentList.Count > 0)
            {
                foreach (var department in item.DepartmentList)
                {
                    CameraDepartment cameraDepartment = new()
                    {
                        AppCode = appCode,
                        AreaCode = department.AreaCode,
                        CameraMAC = item.CameraMAC,
                        DeptCode = department.DeptCode,
                        CreateUserAccount = _user.Account,
                        CreateDate = DateTime.Now
                    };

                    await _dataAccessService.CreateAsync(cameraDepartment);
                }
            }

            await _dataAccessService.CreateAsync(camera);
        }

        List<PostAPIResult> addCameraResults = await _cameraService.AddCamera(addCameraInputs);

        // 判斷每一筆的PostAPIResult中，errors.Count > 0，則回傳錯誤
        List<ReturnError> addCameraErrors = [];
        for (int i = 0; i < addCameraResults.Count; i++)
        {
            if (addCameraResults[i].errors != null && addCameraResults[i].errors.Count > 0)
            {
                var returnError = new ReturnError
                {
                    index = i,
                    code = addCameraResults[i].code,
                    errors = []
                };

                foreach(var error in addCameraResults[i].errors)
                {
                    returnError.errors.Add(new ErrorDetail
                    {
                        index = returnError.errors.Count + 1,
                        error = $"err.{error.error}.cameras.Camera.fusion"
                    });
                }
            }
        }

        if (addCameraErrors.Count > 0)
        {
            await _dataAccessService.RollbackAsync();
            _logService.Logging("error", logActionName, requestUUID, JsonSerializer.Serialize(addCameraErrors));
            returnModel = new ReturnModel
            {
                authorize = (Authorize)_user,
                requestUUID = requestUUID,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = addCameraErrors
            };
        }
        else
        {
            await _dataAccessService.CommitAsync();
            _logService.Logging("info", logActionName, requestUUID, "Camera Data Appended.");
            returnModel = new ReturnModel
            {
                authorize = (Authorize)_user,
                requestUUID = requestUUID,
                httpStatus = StatusCodes.Status201Created,
                result = true,
            };
        }

        _logService.Logging("info", logActionName, requestUUID, "End");

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    /// <summary>
    /// 查詢攝影機
    /// </summary>
    /// <param name="param"></param>
    /// <spec>
    /// Jira: https://tpe-jira2.fihtdc.com/browse/FSN-6159
    /// 列表畫面欄位及需求 https://tpe-jira2.fihtdc.com/secure/thumbnail/2145473/_thumb_2145473.png
    /// 查詢視窗欄位及需求 https://tpe-jira2.fihtdc.com/secure/thumbnail/2145476/_thumb_2145476.png
    /// </spec>
    /// <returns></returns>
    [HttpGet("cameras")]
    public async Task<IActionResult> RetrieveCamera(InRetrieveCamera param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        var stations = await _dataAccessService.Fetch<Station>(e => e.AppCode == _user.AppCode).ToListAsync();
        var cameras = await _dataAccessService.Fetch<Camera>(e => e.AppCode == _user.AppCode && (string.IsNullOrWhiteSpace(param.AreaCode) ? true : e.AreaCode == param.AreaCode)).ToListAsync();
        var cameraStations = await _dataAccessService.Fetch<CameraStation>(e => e.AppCode == _user.AppCode).ToListAsync();
        var cameraDepartments = await _dataAccessService.Fetch<CameraDepartment>(e => e.AppCode == _user.AppCode).ToListAsync();
        var departments = await _dataAccessService.Fetch<Department>(e => e.AppCode == _user.AppCode && e.AreaCode == param.AreaCode).ToListAsync();
        var locations = await _dataAccessService.Fetch<Location>(e => e.AppCode == _user.AppCode).ToListAsync();

        if (!string.IsNullOrEmpty(param.DeptCode))
        {
            cameraDepartments = cameraDepartments.Where(e => e.DeptCode == param.DeptCode).ToList();
            cameras = cameras.Where(e => cameraDepartments.Any(c => c.CameraMAC == e.CameraMAC)).ToList();
        }

        if (!string.IsNullOrEmpty(param.StationSID))
        {
            cameraStations = cameraStations.Where(e => param.StationSID.ToLower().Contains(e.SID.ToLower())).ToList();
            cameras = cameras.Where(e => cameraStations.Any(c => c.CameraMAC == e.CameraMAC)).ToList();
        }

        IEnumerable<dynamic> result;

        var step1 = cameras.Where(camera =>
                       (string.IsNullOrEmpty(param.CameraMAC) || camera.CameraMAC.Contains(param.CameraMAC))
                    && (string.IsNullOrEmpty(param.CameraName) || camera.CameraName.Contains(param.CameraName)));

        var cameraCodes = string.Join(",", step1.Select(e => e.CameraMAC).ToList());

        List<GetCameraOutput> fusionCameraList = await _cameraService.GetCameraList($"code in {cameraCodes}");

        var step1AsEnumerable = step1.AsEnumerable();

        result = step1AsEnumerable.Select(camera =>
        {
            var cameraConnection = fusionCameraList.FirstOrDefault(e => e.code == camera.CameraMAC);

            var stationList = cameraStations
                .Join(stations,
                    cs => new { cs.AreaCode, cs.SID },
                    s => new { s.AreaCode, s.SID },
                    (cs, s) => new { CameraStation = cs, Station = s })
                .Where(x => x.CameraStation.CameraMAC == camera.CameraMAC)
                .Select(x =>
                {
                    var regionCode = x.Station.RegionCode;
                    var regionName = locations
                        .Where(l => l.LocCode == regionCode)
                        .Select(l => l.LocName)
                        .FirstOrDefault();

                    //Console.WriteLine($"Debug: CameraMAC={camera.CameraMAC}, RegionCode={regionCode}, RegionName={regionName}");

                    return new
                    {
                        x.CameraStation.AreaCode,
                        x.CameraStation.SID,
                        LocCode = regionCode,
                        LocName = regionName
                    };
                })
                .ToList();

            var departmentList = cameraDepartments
                .Join(departments,
                    cd => new { cd.AreaCode, cd.DeptCode },
                    d => new { d.AreaCode, d.DeptCode },
                    (cd, d) => new { CameraDepartment = cd, Department = d })
                .Where(x => x.CameraDepartment.CameraMAC == camera.CameraMAC)
                .Select(x => new
                {
                    x.Department.Id,
                    x.CameraDepartment.AreaCode,
                    x.CameraDepartment.DeptCode,
                    x.Department.DeptName
                })
                .ToList();

            return new
            {
                camera.AreaCode,
                camera.CameraMAC,
                camera.CameraName,
                camera.IP,
                camera.CameraVideoPath,
                camera.StreamURL,
                camera.PathUserName,
                camera.PathUserPassword,
                camera.CreateUserAccount,
                camera.CreateDate,
                camera.ModifyDate,
                IsAlive = cameraConnection?.connection?.isAlive ?? false,
                StationList = stationList,
                DepartmentList = departmentList
            };
        }).ToList();

        returnModel = new ReturnModel
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                //recordTotal,
                recordList = result
            }
        };

        return Ok(returnModel);
    }

    /// <summary>
    /// 更新攝影機
    /// </summary>
    /// <param name="paramList"></param>
    /// <param name="callerName"></param>
    /// <spec>
    /// Jira: https://tpe-jira2.fihtdc.com/browse/FSN-6159
    /// 修改畫面欄位及需求 https://tpe-jira2.fihtdc.com/secure/thumbnail/2145635/_thumb_2145635.png
    /// </spec>
    /// <returns></returns>
    [HttpPatch("cameras")]
    [RequestParamListDuplicate("CameraMAC")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateCameras([FromBody] List<InUpdateCamera> paramList, [CallerMemberName] string callerName = null)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        string appCode = _user.AppCode;

        List<ReturnError> errors = [];

        var results = await _dataAccessService.ExecuteParallelQueriesAsync(
            ("C", async context => await context.Cameras.Where(e => e.AppCode == appCode).ToListAsync()),
            ("A", async context => await context.Areas.Where(e => e.AppCode == appCode).ToListAsync()),
            ("D", async context => await context.Departments.Where(e => e.AppCode == appCode).ToListAsync())
        );

        // 解包結果
        var cameras = (List<Camera>)results["C"];
        var areas = (List<Area>)results["A"];
        var departments = (List<Department>)results["D"];

        _logService.Logging("info", logActionName, requestUUID, "Camera Data Validated, start update.");
        _dataAccessService.BeginTransaction();

        // 將要更新的Camera資料轉換成Fusion Camera的格式
        List<PatchCameraInput> patchCameraInputs = [];
        foreach (InUpdateCamera item in paramList)
        {
            PatchCameraInput patchCameraInput = new()
            {
                code = item.CameraMAC,
                name = item.CameraName,
                enable = true,
                stationSids = item.StationList?.Select(e => e.StationSID.Trim()).ToList() ?? [],
                configuration = new FusionCamera.Configuration
                {
                    ip = item.IP,
                    cameraVideoPath = item.CameraVideoPath,
                    streamUrl = item.StreamURL,
                    username = item.PathUserName,
                    userAuth = item.PathUserPassword
                }
            };

            patchCameraInputs.Add(patchCameraInput);
        }

        // 更新Fusion Camera  
        List<PatchAPIResult> patchCameraResults=await _cameraService.PatchCamera(patchCameraInputs);

        // 判斷每一筆的PatchAPIResult中，errors.Count > 0，則回傳錯誤
        List<ReturnError> patchCameraErrors = [];
        List<string> patchCameraSuccess = [];
        for (int i = 0; i < patchCameraResults.Count; i++)
        {
            if (patchCameraResults[i].errors != null && patchCameraResults[i].errors.Count > 0)
            {
                var returnError = new ReturnError
                {
                    index = i,
                    code = patchCameraResults[i].code,
                    errors = []
                };

                foreach (var error in patchCameraResults[i].errors)
                {
                    returnError.errors.Add(new ErrorDetail
                    {
                        index = returnError.errors.Count + 1,
                        error = $"err.{error.error}.cameras.Camera.fusion"
                    });
                }
            }
            else
            {
                patchCameraSuccess.Add(patchCameraResults[i].code);
            }
        }

        foreach (InUpdateCamera item in paramList)
        {
            if (patchCameraSuccess.Contains(item.CameraMAC))
            {
                // 更新攝影機
                var camera = await _dataAccessService.Fetch<Camera>().Where(x => x.CameraMAC == item.CameraMAC).AsTracking().FirstOrDefaultAsync();

                camera.CameraName = item.CameraName;
                camera.IP = item.IP;
                camera.StreamURL = item.StreamURL;
                camera.CameraVideoPath = item.CameraVideoPath;
                camera.PathUserName = item.PathUserName;
                camera.PathUserPassword = item.PathUserPassword;

                camera.ModifyUserAccount = _user.Account;
                camera.ModifyDate = DateTime.Now;

                await _dataAccessService.UpdateAsync<Camera>(camera, callMethodName: callerName,
                                                                 e => e.CameraName,
                                                                 e => e.IP,
                                                                 e => e.StreamURL,
                                                                 e => e.PathUserName,
                                                                 e => e.CameraVideoPath,
                                                                 e => e.PathUserPassword,
                                                                 e => e.ModifyUserAccount,
                                                                 e => e.ModifyDate);

                // 刪除攝影機-Station及攝影機-Department
                await _dataAccessService.DeleteAsync<CameraStation>(e => e.AppCode == appCode && e.CameraMAC == item.CameraMAC);
                await _dataAccessService.DeleteAsync<CameraDepartment>(e => e.AppCode == appCode && e.CameraMAC == item.CameraMAC);

                if (item.StationList != null && item.StationList.Count > 0)
                {
                    foreach (var station in item.StationList)
                    {
                        CameraStation cameraStation = new()
                        {
                            AppCode = appCode,
                            AreaCode = station.AreaCode,
                            CameraMAC = item.CameraMAC,
                            SID = station.StationSID,
                            CreateUserAccount = _user.Account,
                            CreateDate = DateTime.Now
                        };

                        await _dataAccessService.CreateAsync(cameraStation);
                    }
                }

                if (item.DepartmentList != null && item.DepartmentList.Count > 0)
                {
                    foreach (var department in item.DepartmentList)
                    {
                        CameraDepartment cameraDepartment = new()
                        {
                            AppCode = appCode,
                            AreaCode = department.AreaCode,
                            CameraMAC = item.CameraMAC,
                            DeptCode = department.DeptCode,
                            CreateUserAccount = _user.Account,
                            CreateDate = DateTime.Now
                        };

                        await _dataAccessService.CreateAsync(cameraDepartment);
                    }
                }
            }
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "Camera Data update done.");

        returnModel = new ReturnModel
        {
            authorize = new Authorize { Account = _user.Account, AppCode = _user.AppCode, AreaCode = _user.AreaCode },
            httpStatus = StatusCodes.Status200OK,
            result = true
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpDelete("cameras")]
    [RequestParamListDuplicate("CameraMAC")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeleteCamera([FromBody] List<InDeleteCamera> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        string appCode = _user.AppCode;

        List<ReturnError> errors = [];

        // 同時跟DB取得Camera, CameraDepartment, CameraStation資料
        var results = await _dataAccessService.ExecuteParallelQueriesAsync(
            ("C", async context => await context.Cameras.Where(e => e.AppCode == appCode).ToListAsync()),
            ("CD", async context => await context.CameraDepartments.Where(e => e.AppCode == appCode).ToListAsync()),
            ("CS", async context => await context.CameraStations.Where(e => e.AppCode == appCode).ToListAsync())
        );

        // 解包結果
        var cameras = (List<Camera>)results["C"];
        var cameraDepartments = (List<CameraDepartment>)results["CD"];
        var cameraStations = (List<CameraStation>)results["CS"];

        _logService.Logging("info", logActionName, requestUUID, "Camera Data Validated, start update.");
        
        var macs = string.Join(",", paramList.Select(e => e.CameraMAC).ToList());

        var fusionResult = await _cameraService.DeleteCamera(macs);

        List<string> fusionDeleteSuccessList = [];
        List<string> fusionDeleteFailList = [];
        List<string> hmnDeleteFailList = [];

        _dataAccessService.BeginTransaction();

        try
        {
            foreach (var result in fusionResult)
            {
                if (result.errors == null || result.errors.Count == 0)
                {
                    await _dataAccessService.DeleteAsync<Camera>(e => e.AppCode == appCode && e.CameraMAC == result.code);
                    await _dataAccessService.DeleteAsync<CameraStation>(e => e.AppCode == appCode && e.CameraMAC == result.code);
                    await _dataAccessService.DeleteAsync<CameraDepartment>(e => e.AppCode == appCode && e.CameraMAC == result.code);
                }
                else
                {
                    fusionDeleteFailList.Add(result.code);
                }
            }

            await _dataAccessService.CommitAsync();
        }
        catch (Exception)
        {
            await _dataAccessService.RollbackAsync();
            hmnDeleteFailList = paramList.Select(e => e.CameraMAC).ToList();
        }

        _logService.Logging("info", logActionName, requestUUID, "Camera Data update done.");

        if (fusionDeleteFailList.Count > 0)
        {
            _logService.Logging("error", logActionName, requestUUID, $"Camera delete fail in fusion:{JsonSerializer.Serialize(fusionDeleteFailList)}");
        }

        if (hmnDeleteFailList.Count > 0)
        {
            _logService.Logging("error", logActionName, requestUUID, $"Camera delete fail in hmn:{JsonSerializer.Serialize(hmnDeleteFailList)}");
        }

        if (fusionDeleteFailList.Count > 0 || hmnDeleteFailList.Count > 0)
        {
            returnModel = new ReturnModel
            {
                authorize = (Authorize)_user,
                requestUUID = requestUUID,
                httpStatus = StatusCodes.Status500InternalServerError,
                result = false,
                data = new
                {
                    fusionDeleteFailList,
                    hmnDeleteFailList
                }
            };
        }
        else
        {
            returnModel = new ReturnModel
            {
                authorize = (Authorize)_user,
                requestUUID = requestUUID,
                httpStatus = StatusCodes.Status200OK,
                result = true
            };
        }

        return StatusCode(returnModel.httpStatus, returnModel);
    }
    /// <summary>
    /// 查詢事件錄影
    /// </summary>
    /// <param name="param"></param>
    /// <spec>
    /// Jira: https://tpe-jira2.fihtdc.com/browse/FSN-6192
    /// 列表畫面欄位及需求:無獨立查詢視窗欄位及需求，參考新增事件錄影功能畫面
    /// 無查詢視窗欄位及需求
    /// </spec>
    /// <returns></returns>
    [HttpGet("videoCapture")]
    public async Task<IActionResult> RetrieveVideoCapture(InRetrieveVideoCapture param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        // 檢查參數是否為空
        if (param == null)
        {
            _logService.Logging("error", logActionName, requestUUID, "err.null.videoCapture.Camera.param");

            returnModel = new ReturnModel
            {
                requestUUID = requestUUID,
                authorize = (Authorize)_user,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = "err.null.videoCapture.Camera.param"
            };
            return StatusCode(returnModel.httpStatus, returnModel);
        }
        else
        {
            // 檢查AreaCode是否為空
            if (string.IsNullOrEmpty(param.AreaCode) || string.IsNullOrWhiteSpace(param.AreaCode))
            {
                _logService.Logging("error", logActionName, requestUUID, "err.null.videoCapture.Camera.param.AreaCode");

                returnModel = new ReturnModel
                {
                    requestUUID = requestUUID,
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status400BadRequest,
                    result = false,
                    data = "err.null.cameras.Camera.param.AreaCode"
                };
                return StatusCode(returnModel.httpStatus, returnModel);
            }
            else
            {
                // 檢查AreaCode是否存在
                var area = await _dataAccessService.Fetch<Area>(e => e.AppCode == _user.AppCode && e.AreaCode == param.AreaCode).FirstOrDefaultAsync();
                if (area == null)
                {
                    _logService.Logging("error", logActionName, requestUUID, "err.notfound.cameras.Camera.param.AreaCode");

                    returnModel = new ReturnModel
                    {
                        requestUUID = requestUUID,
                        authorize = (Authorize)_user,
                        httpStatus = StatusCodes.Status404NotFound,
                        result = false,
                        data = "err.notfound.cameras.Camera.param.AreaCode"
                    };
                    return StatusCode(returnModel.httpStatus, returnModel);
                }
            }

            // 檢查DeptCode是否為空
            if (string.IsNullOrEmpty(param.AreaCode) || string.IsNullOrWhiteSpace(param.AreaCode))
            {
                _logService.Logging("error", logActionName, requestUUID, "err.null.videoCapture.Camera.param.DeptCode");

                returnModel = new ReturnModel
                {
                    requestUUID = requestUUID,
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status400BadRequest,
                    result = false,
                    data = "err.null.videoCapture.Camera.param.DeptCode"
                };
                return StatusCode(returnModel.httpStatus, returnModel);
            }
            // 如果有DeptCode，檢查DeptCode是否存在
            else
            {
                var dept = await _dataAccessService.Fetch<Department>(e => e.AppCode == _user.AppCode && e.AreaCode == param.AreaCode && e.DeptCode == param.DeptCode).FirstOrDefaultAsync();

                if (dept == null)
                {
                    _logService.Logging("error", logActionName, requestUUID, "err.notfound.videoCapture.Camera.param.DeptCode");

                    returnModel = new ReturnModel
                    {
                        requestUUID = requestUUID,
                        authorize = (Authorize)_user,
                        httpStatus = StatusCodes.Status404NotFound,
                        result = false,
                        data = "err.notfound.videoCapture.Camera.param.DeptCode"
                    };
                    return StatusCode(returnModel.httpStatus, returnModel);
                }
            }

            _logService.Logging("info", logActionName, requestUUID, $"param:{JsonSerializer.Serialize(param)}");
        }

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        var result = _dataAccessService.Fetch<VideoCapture>(e => e.AppCode == _user.AppCode && e.AreaCode == param.AreaCode && e.DeptCode == param.DeptCode);

        // 下方使用CountAsync()會有問題，所以先ToList()再Count()，待解決
        var queryList = result.ToList();

        var recordTotal = queryList.Count();

        var recordList = size == 0 ? result.ToList() : queryList.Skip(skip).Take(size).ToList();

        returnModel = new ReturnModel
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal,
                recordList
            }
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }
    /// <summary>
    /// 新增事件錄影
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    /// <spec>
    /// Jira: https://tpe-jira2.fihtdc.com/browse/FSN-6192
    /// 新增事件錄影功能在Spec. V1.010中提到的相關檢查與動作
    /// https://tpe-jira2.fihtdc.com/secure/thumbnail/2148579/_thumb_2148579.png
    /// VideoCapture Table Not Null fields and length
    /// https://tpe-jira2.fihtdc.com/secure/thumbnail/2148600/_thumb_2148600.png
    /// </spec>
    [HttpPost("videoCapture")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateVideoCapture([FromBody] List<InCreateVideoCapture> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;
        string appCode = _user.AppCode;

        string serviceCodesStr = await _preferenceService.FetchSysParameter(_user.AppCode, _user.AreaCode, "VideoCaptureServiceCode");
        var serviceCodes = JsonSerializer.Deserialize<List<string>>(serviceCodesStr);

        _logService.Logging("info", logActionName, requestUUID, "VideoCapture Data Validated, start append.");
        _dataAccessService.BeginTransaction();

        // 取出paramList 中每一筆的 AreaCode, DeptCode 並去除重複後，將 VideoCapture 資料庫中依照 AreaCode, DeptCode 取出的資料刪除
        var areaDeptList = paramList.Select(e => new { e.AreaCode, e.DeptCode }).Distinct().ToList();
        foreach (var areaDept in areaDeptList)
        {
            await _dataAccessService.DeleteAsync<VideoCapture>(e => e.AppCode == appCode && e.AreaCode == areaDept.AreaCode && e.DeptCode == areaDept.DeptCode);
        }

        // 將要新增的 VideoCapture 資料寫入資料庫
        foreach (var item in paramList)
        {
            VideoCapture videoCapture = new()
            {
                AppCode = appCode,
                AreaCode = item.AreaCode,
                DeptCode = item.DeptCode,
                ServiceCode = item.ServiceCode,
                PrefixMinute = int.Parse(item.PrefixMinute),
                Suffix = int.Parse(item.Suffix),
                BackupDirectory = item.BackupDirectory,
                Account = item.Account,
                Password = item.Password,
                KeepDays = int.Parse(item.KeepDays),
                PositionCapture = int.Parse(item.PositionCapture),
                CreateUserAccount = _user.Account,
                CreateDate = DateTime.Now
            };

            await _dataAccessService.CreateAsync(videoCapture);
        }

        // 提交交易，如果交易失敗，則回滾並回傳錯誤訊息
        int updateCount = 0;
        try
        {
            updateCount = await _dataAccessService.CommitAsync();
        }
        catch (Exception ex)
        {
            string errMsg;
            if (ex.Message.Contains("An error occurred while saving the entity changes. See the inner exception for details.") && ex.InnerException != null)
            {
                errMsg = ex.InnerException.ToString();
            }
            else
            {
                errMsg = ex.ToString();
            }
            _logService.Logging("error", logActionName, requestUUID, $"err.invalid.videoCapture.Camera.Update:{errMsg}");

            return BadRequest(new ReturnModel
            {
                authorize = (Authorize)_user,
                requestUUID = requestUUID,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                title = "err.invalid.videoCapture.Camera.Update",
                innerMsg = errMsg
            });
        }

        // 紀錄傳入及更新筆數
        _logService.Logging("info", logActionName, requestUUID, $"paramList.Count:{paramList.Count}, Update Data:{updateCount}");

        bool updateResult = updateCount > 0;

        returnModel = new()
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = StatusCodes.Status201Created,
            result = updateResult
        };

        _logService.Logging("info", logActionName, requestUUID, "End");

        return StatusCode(returnModel.httpStatus, returnModel);
    }
    [HttpDelete("videoCapture")]
    public async Task<IActionResult> DeleteVideoCapture([FromBody, RequestNotNullOrEmpty(ErrorMessage = Constants.ErrorCode.RequestNullOrEmpty)] List<InDeleteVideoCapture> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        string appCode = _user.AppCode;

        var videoCaptures = _dataAccessService.Fetch<VideoCapture>(e => e.AppCode == _user.AppCode);

        // 驗證通過，開始刪除
        _logService.Logging("info", logActionName, requestUUID, "Camera Data Validated, start delete.");

        // 啟用Transaction
        _dataAccessService.BeginTransaction();

        // 刪除VideoCapture資料
        for (int i = 0; i < paramList.Count; i++)
        {
            var param = paramList[i];

            // 如果ServiceCode是否為空，則刪除指定的AreaCode, DeptCode所有VideoCapture
            if (string.IsNullOrWhiteSpace(param.ServiceCode))
            {
                await _dataAccessService.DeleteAsync<VideoCapture>(e => e.AppCode == appCode && e.AreaCode == param.AreaCode && e.DeptCode == param.DeptCode);
            }
            else
            {
                await _dataAccessService.DeleteAsync<VideoCapture>(e => e.AppCode == appCode && e.AreaCode == param.AreaCode && e.DeptCode == param.DeptCode && e.ServiceCode == param.ServiceCode);
            }
        }

        try
        {
            await _dataAccessService.CommitAsync();

            returnModel = new ReturnModel
            {
                authorize = (Authorize)_user,
                requestUUID = requestUUID,
                httpStatus = StatusCodes.Status200OK,
                result = true
            };
        }
        catch (Exception e)
        {
            await _dataAccessService.RollbackAsync();

            // 如果Commit失敗，回傳錯誤訊息
            returnModel = new ReturnModel
            {
                authorize = (Authorize)_user,
                requestUUID = requestUUID,
                httpStatus = StatusCodes.Status500InternalServerError,
                data = e.ToString(),
                result = false
            };

            _logService.Logging("error", logActionName, requestUUID, e.ToString());
        }

        _logService.Logging("info", logActionName, requestUUID, "Camera Data update done.");

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpGet("GetVideoCaptureCameras")]
    public async Task<IActionResult> RetrieveTaskVideoCaptureCamera([FromQuery] InRetrieveTaskVideoCaptureCamera queryParam)
    {
        InRetrieveTaskVideoCaptureCamera param = queryParam;
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size; // 起始資料列索引值(略過幾筆)

        // 排序；如果沒有 sort 條件預設使用 ModifyDate 降冪排序
        string sort = string.IsNullOrEmpty(param.sort) ? "Task.StartsAt:desc" : param.sort;
        string sortByField = sort.Split(":")[0];
        string sortDirection = sort.Split(":")[1];

        var _userId = param.UserDataId != null ? int.Parse(param.UserDataId) : _dataAccessService
            .Fetch<UserDatum>(e => e.AppCode == _user.AppCode && e.UserAccount == _user.Account)
            .Select(e => e.Id)
            .FirstOrDefault();

        List<TaskVideoCameraInfo> taskVideoCameraInfoList = new List<TaskVideoCameraInfo>();

        string _command = "" +
            "SELECT " +
            "   X.\"Id\", X.\"DepartmentId\", X.\"TaskDataId\", X.\"TaskId\", X.\"Action\", X.\"Active\", X.\"ServiceCode\", X.\"EventCode\", X.\"EventName\", X.\"SponsorObjectCode\", X.\"SponsorObjectName\", X.\"SponsorObjectType\", X.\"SponsorDevicePid\", X.\"SponsorDeviceName\", X.\"SponsorDeviceType\", X.\"StartsAt\", X.\"CapturePrefixTime\", X.\"CaptureSuffixTime\", " +
            "   X.\"CaptureStatus\", X.\"CameraId\", X.\"CameraName\", X.\"CameraMac\", X.\"CameraIp\", X.\"CameraStreamUrl\", " +
            "   X.\"TaskVideoDirectoryPath\", X.\"TaskVideoDirectoryAccount\", X.\"TaskVideoDirectoryPassword\", " +
            "   STRING_AGG('Id@:@'||Y.\"Id\"||'@,@Fragment@:@'||Y.\"Fragment\"||'@,@FileName@:@'||Y.\"FileName\", '@;@' ORDER BY Y.\"Fragment\" Asc) as \"TaskVideos\", " +
            "   COALESCE(MAX(CASE WHEN SUBSTRING(Y.\"FileName\", 5, 14) < X.\"CompStartsAt\" THEN Y.\"Fragment\" ELSE 0 END), 0) as \"TaskStartsAtVideoFragment\" " +
            "FROM (" +
            "   SELECT D.\"Id\", B.\"DepartmentId\", B.\"TaskDataId\", C.\"TaskId\", C.\"Action\", C.\"Active\", C.\"ServiceCode\", C.\"EventCode\", C.\"EventName\", C.\"SponsorObjectCode\", C.\"SponsorObjectName\", C.\"SponsorObjectType\", C.\"SponsorDevicePid\", C.\"SponsorDeviceName\", C.\"SponsorDeviceType\", CAST(C.\"StartsAt\" as varchar) as \"StartsAt\", " +
            "   CAST(C.\"StartsAt\" + ('-'||B.\"PrefixMinute\"||' minutes')::interval as varchar) as \"CapturePrefixTime\", " +
            "   CAST(C.\"StartsAt\" + (B.\"SuffixMinute\"||' minutes')::interval as varchar) as \"CaptureSuffixTime\", " +
            "   D.\"CaptureStatus\", D.\"CameraId\", E.\"CameraName\", E.\"CameraMAC\" as \"CameraMac\", E.\"IP\" as \"CameraIp\", E.\"StreamURL\" as \"CameraStreamUrl\", " +
            "   B.\"BackupDirectory\" as \"TaskVideoDirectoryPath\", B.\"Account\" as \"TaskVideoDirectoryAccount\", B.\"Password\"  as \"TaskVideoDirectoryPassword\", " +
            "   TO_CHAR(C.\"StartsAt\", 'yyyymmddhh24miss') as \"CompStartsAt\" " +
            "   FROM \"UserData\" M " +
            "   LEFT JOIN \"Department\" A on A.\"AppCode\" = M.\"AppCode\" and A.\"AreaCode\" = M.\"AreaCode\" and A.\"DeptCode\" = M.\"DeptCode\" " +
            "   LEFT JOIN \"TaskVideoCapture\" B on B.\"DepartmentId\" = A.\"Id\" and B.\"CaptureStatus\" in ('00', '20') and B.\"VideoDeleted\" = false " +
            "   LEFT JOIN \"TaskData\" C on C.\"Id\" = B.\"TaskDataId\" " +
            "   LEFT JOIN \"TaskVideoCamera\" D on D.\"TaskVideoCaptureId\" = B.\"Id\" " +
            "   LEFT JOIN \"Camera\" E on E.\"Id\" = D.\"CameraId\" " +
            "   WHERE M.\"Id\" =  @UserDataId " +
            "   AND A.\"Id\" is Not Null " +
            "   AND B.\"Id\" is Not Null " +
            "   AND C.\"Id\" is Not Null " +
            "   AND D.\"Id\" is Not Null " +
            "   AND E.\"Id\" is Not Null " +
            ") X " +
            "LEFT JOIN \"TaskVideo\" Y on Y.\"TaskVideoCameraId\" = X.\"Id\" " +
            "GROUP BY " +
            "   X.\"Id\", X.\"DepartmentId\", X.\"TaskDataId\", X.\"TaskId\", X.\"Action\", X.\"Active\", X.\"ServiceCode\", X.\"EventCode\", X.\"EventName\", X.\"SponsorObjectCode\", X.\"SponsorObjectName\", X.\"SponsorObjectType\", X.\"SponsorDevicePid\", X.\"SponsorDeviceName\", X.\"SponsorDeviceType\", X.\"StartsAt\", X.\"CapturePrefixTime\", X.\"CaptureSuffixTime\", " +
            "   X.\"CaptureStatus\", X.\"CameraId\", X.\"CameraName\", X.\"CameraMac\", X.\"CameraIp\", X.\"CameraStreamUrl\", " +
            "   X.\"TaskVideoDirectoryPath\", X.\"TaskVideoDirectoryAccount\", X.\"TaskVideoDirectoryPassword\" " +
            "ORDER BY X.\"Id\" Asc, X.\"CameraId\" Asc ";

        // 建立 DynamicParameters 並加入參數
        var dbParam = new DynamicParameters();
        dbParam.Add("@@UserDataId", _userId, DbType.Int32);

        List<dynamic> result = await _dataAccessService.ExecuteSqlQuery(_command, dbParam);
        if (result.Any())
        {
            foreach (var row in result)
            {
                var data = (IDictionary<string, object>)row;

                // 處理子物件清單 - CaptureVideos
                List<TaskVideoInfo> taskVideoInfos = new List<TaskVideoInfo>();
                List<Dictionary<string, string>> taskVideos = convertStringToObjectsList(data["TaskVideos"]?.ToString() ?? "");
                foreach (var item in taskVideos)
                {
                    taskVideoInfos.Add(new TaskVideoInfo()
                    {
                        Id = Int32.Parse(item["Id"].ToString()),
                        Fragment = Int32.Parse(item["Fragment"].ToString()),
                        FileName = item["FileName"].ToString()
                    });
                };
                // 新增至查詢結果物件清單
                taskVideoCameraInfoList.Add(new TaskVideoCameraInfo
                {
                    
                    Id = Int32.Parse(data["Id"]?.ToString() ?? "-1"),
                    Task = new TaskDataInfo() {
                        Id = Int32.Parse(data["TaskDataId"]?.ToString() ?? "-1"),
                        TaskId = Int32.Parse(data["TaskId"]?.ToString() ?? "-1"),
                        Action = Int32.Parse(data["Action"]?.ToString() ?? "-1"),
                        Active = Convert.ToBoolean(data["Active"]),
                        ServiceCode = data["ServiceCode"]?.ToString() ?? "",
                        EventCode = data["EventCode"]?.ToString() ?? "",
                        EventName = data["EventName"]?.ToString() ?? "",
                        SponsorObjectCode = data["SponsorObjectCode"]?.ToString() ?? "",
                        SponsorObjectName = data["SponsorObjectName"]?.ToString() ?? "",
                        SponsorObjectType = data["SponsorObjectType"]?.ToString() ?? "",
                        SponsorDevicePid = data["SponsorDevicePid"]?.ToString() ?? "",
                        SponsorDeviceName = data["SponsorDeviceName"]?.ToString() ?? "",
                        SponsorDeviceType = data["SponsorDeviceType"]?.ToString() ?? "",
                        StartsAt = data["StartsAt"]?.ToString() ?? "",
                    },
                    TaskStartsAtVideoFragment = Int32.Parse(data["TaskStartsAtVideoFragment"]?.ToString() ?? "-1"),
                    Camera = new CameraInfo
                    {
                        Id = Int32.Parse(data["CameraId"]?.ToString() ?? "-1"),
                        CameraMac = data["CameraMac"]?.ToString() ?? "",
                        CameraIp = data["CameraIp"]?.ToString() ?? "",
                        CameraName = data["CameraName"]?.ToString() ?? "",
                        StreamUrl = data["CameraStreamUrl"]?.ToString() ?? ""
                    },                            
                    CapturePrefixTime = data["CapturePrefixTime"]?.ToString() ?? "",
                    CaptureSuffixTime = data["CaptureSuffixTime"]?.ToString() ?? "",
                    CaptureStatus = data["CaptureStatus"]?.ToString() ?? "",
                    VideoDirectoryPath = Path.Combine(new string[] { (data["TaskVideoDirectoryPath"]?.ToString() ?? "").Replace("\\", Path.DirectorySeparatorChar.ToString()), data["DepartmentId"]?.ToString() ?? "", data["TaskId"]?.ToString() ?? "", data["CameraId"]?.ToString() ?? "" }),
                    VideoDirectoryAccount = data["TaskVideoDirectoryAccount"]?.ToString() ?? "",
                    VideoDirectoryPassword = data["TaskVideoDirectoryPassword"]?.ToString() ?? "",
                    Videos = taskVideoInfos
                });
            }
        }

        // LINQ 過濾查詢條件
        // 解析 param.IdList 為一個整數集合
        var idList = param.IdList?.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                .Select(id => int.Parse(id))
                                .ToHashSet() 
                ?? new HashSet<int>();

        var serviceCodeList = param.TaskServiceCodeList?.Split(',', StringSplitOptions.RemoveEmptyEntries)
                                .ToHashSet() 
                ?? new HashSet<string>();
                
        taskVideoCameraInfoList = taskVideoCameraInfoList
            .Where(x => 
                idList.Count == 0 || (x.Id.HasValue && idList.Contains(x.Id.Value)))
            .Where(x =>
                param.TaskStartsAtBetween == null || (x.Task != null && x.Task.StartsAt != null && filterByTaskStartsAtBetween(param.TaskStartsAtBetween, x)))
            .Where(x => 
                serviceCodeList.Count == 0 || (x.Task != null && serviceCodeList.Contains(x.Task.ServiceCode)))
            .Where(x => 
                param.TaskAction == null || (x.Task != null && int.TryParse(param.TaskAction, out int taskAction) && x.Task.Action == taskAction))
            .Where(x => 
                param.TaskSponsorObjectName == null || (x.Task != null && x.Task.SponsorObjectName.Contains(param.TaskSponsorObjectName)))
            .Where(x => 
                param.TaskSponsorDeviceType == null || (x.Task != null && x.Task.SponsorDeviceType == param.TaskSponsorDeviceType))
            .ToList();

        // 取得總筆數
        int recordTotal = taskVideoCameraInfoList.Count();

        // 根據 sortByField 和 sortDirection 進行動態排序
        taskVideoCameraInfoList = SortByField(taskVideoCameraInfoList, sortByField, sortDirection.ToLower() == "desc").ToList();

        // 進行分頁
        var pagedResult = size == 0
            ? taskVideoCameraInfoList.ToList() // 不分頁，直接返回所有資料
            : taskVideoCameraInfoList.Skip(skip).Take(size).ToList();

        ReturnModel returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal,
                recordList = pagedResult
            }
        };

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);
    }

    private bool filterByTaskStartsAtBetween(string taskStartsAtBetween, TaskVideoCameraInfo x)
    {
        if (taskStartsAtBetween == null)
        {
            // 不過濾，直接保留所有項目
            return true;
        }

        string[] timeBounds = taskStartsAtBetween.Split(',');
        DateTime taskStartsAt = DateTime.Parse(x.Task.StartsAt);

        if (taskStartsAtBetween.StartsWith(","))
        {
            // 開頭是逗號，篩選小於條件的資料
            DateTime upperBound = DateTime.Parse(timeBounds[1]);
            return taskStartsAt < upperBound;
        }
        else if (taskStartsAtBetween.EndsWith(",") || timeBounds.Length == 1)
        {
            // 結尾是逗號，或條件不包含逗號，篩選大於條件的資料
            DateTime lowerBound = DateTime.Parse(timeBounds[0]);
            return taskStartsAt > lowerBound;
        }
        else if (timeBounds.Length == 2)
        {
            // 包含兩個條件，篩選介於兩者之間的資料
            DateTime lowerBound = DateTime.Parse(timeBounds[0]);
            DateTime upperBound = DateTime.Parse(timeBounds[1]);
            return taskStartsAt > lowerBound && taskStartsAt < upperBound;
        }

        return false; // 預設不符合條件的情況
    }

    public static IEnumerable<T> SortByField<T>(IEnumerable<T> list, string sortBy, bool isDescending)
    {
        var param = Expression.Parameter(typeof(T), "x");
        Expression property = param;

        // 支援嵌套屬性，解析點號分隔的屬性
        foreach (var member in sortBy.Split('.'))
        {
            property = Expression.Property(property, member);
        }

        // 建立 Lambda 表達式
        var lambda = Expression.Lambda<Func<T, object>>(Expression.Convert(property, typeof(object)), param);

        // 使用 OrderBy 或 OrderByDescending 排序
        return isDescending ? list.AsQueryable().OrderByDescending(lambda) : list.AsQueryable().OrderBy(lambda);
    }

    private List<Dictionary<string, string>> convertStringToObjectsList(string objectsListString, string rowSplitString = "@;@", string columnSplitString = "@,@", string valueSplitString = "@:@")
    {
        List<Dictionary<string, string>> objectsList = new List<Dictionary<string, string>>();
        try
        {
            string[] dataRows = objectsListString.Split(rowSplitString);
            foreach (string dataRow in dataRows)
            {
                bool haveData = false;
                Dictionary<string, string> dataObject = new Dictionary<string, string>();
                string[] dataColumns = dataRow.Split(columnSplitString);
                foreach (string dataColumn in dataColumns)
                {
                    string[] dataValues = dataColumn.Split(valueSplitString);
                    if (dataValues.Length == 2)
                    {
                        haveData = true;
                        dataObject.Add(dataValues[0], dataValues[1]);
                    }
                }
                if (haveData)
                {
                    objectsList.Add(dataObject);
                }
            }
        }
        catch (Exception e)
        {
            return new List<Dictionary<string, string>>();
        }
        return objectsList;
    }

    [HttpGet("GetCameras")]
    public async Task<IActionResult> GetCameras([FromQuery] InRetrieveGetCamera queryParam)
    {
        InRetrieveGetCamera param = queryParam;
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size; // 起始資料列索引值(略過幾筆)

        // 排序；如果沒有 sort 條件預設使用 ModifyDate 降冪排序
        string sort = string.IsNullOrEmpty(param.sort) ? "CameraName:desc" : param.sort;
        string sortByField = sort.Split(":")[0];
        string sortDirection = sort.Split(":")[1];

        ReturnModel returnModel;

        var userDeptMonList = _dataAccessService.Fetch<VwUserDeptMonInfo>(x => x.AppCode == _user.AppCode && x.UserAccount == _user.Account.ToUpper());
        var cameraDepartmentList = _dataAccessService.Fetch<CameraDepartment>(x => x.AppCode == _user.AppCode);
        var cameraList = _dataAccessService.Fetch<Camera>(x => x.AppCode == _user.AppCode);

        var query = _user.IsAdmin != "A" ? (from m in userDeptMonList
                        join t1 in cameraDepartmentList on new { m.AreaCode, m.DeptCode } equals new { t1.AreaCode, t1.DeptCode } into temp
                        from cd in temp.DefaultIfEmpty()
                        join t2 in cameraList on new { cd.AppCode, cd.AreaCode, cd.CameraMAC } equals new { t2.AppCode, t2.AreaCode, t2.CameraMAC } into temp2
                        from c in temp2.DefaultIfEmpty()
                        where c != null
                        select new CameraInfo
                        {
                            Id = c.Id,
                            CameraMac = c.CameraMAC,
                            CameraIp = c.IP,
                            CameraName = c.CameraName,
                            StreamUrl = c.StreamURL
                        })
                    :   (from c in cameraList
                        where c != null
                        select new CameraInfo
                        {
                            Id = c.Id,
                            CameraMac = c.CameraMAC,
                            CameraIp = c.IP,
                            CameraName = c.CameraName,
                            StreamUrl = c.StreamURL
                        });

        var result = query.ToList().GroupBy(c => c.Id).Select(g => g.FirstOrDefault());

        // 取得總筆數
        int recordTotal = result.Count();

        // 根據 sortByField 和 sortDirection 進行動態排序
        result = SortByField(result, sortByField, sortDirection.ToLower() == "desc").ToList();

        // 進行分頁
        var pagedResult = size == 0
            ? result.ToList() // 不分頁，直接返回所有資料
            : result.Skip(skip).Take(size).ToList();

        // 呼叫 Fusion Cameras API 取得 Camera connection 狀態
        var cameraCodes = string.Join(",", pagedResult.Select(e => e.CameraMac).ToList());
        List<GetCameraOutput> fusionCameraList = await _cameraService.GetCameraList($"code in {cameraCodes}");
        foreach (CameraInfo c in pagedResult)
        {
            var cameraConnection = fusionCameraList.FirstOrDefault(e => e.code == c.CameraMac);
            c.IsAlive = cameraConnection?.connection?.isAlive ?? false;
        }

        returnModel = new ReturnModel
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal,
                recordList = pagedResult
            }
        };

        return Ok(returnModel);
    }
}
