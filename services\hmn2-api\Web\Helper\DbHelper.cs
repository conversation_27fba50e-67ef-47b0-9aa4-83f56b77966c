﻿using Dapper;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using System.Data.Common;
using System.Data.SqlClient;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using static System.Runtime.InteropServices.Marshalling.IIUnknownCacheStrategy;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Web.Helper.Interfaces;

namespace Web.Helper;

public interface ITrackable
{
    Guid TrackingId { get; }
}
public class TableInfo
{
    public string TableName { get; set; }
    public string? TableComment { get; set; }
    public string? CreateTableScript { get; set; }
    public string? AlterTableAddIndexScript { get; set; }
    public List<FieldInfo>? Fields { get; set; }
}

public class FieldInfo
{
    public string ColumnName { get; set; }
    public string DataType { get; set; }
    public int? Length { get; set; }
    public bool IsPrimaryKey { get; set; }
    public bool IsNullable { get; set; }
    public string DefaultValue { get; set; }
    public string Comment { get; set; }
}

public class TableStructure
{
    public string TableName { get; set; }
    public string TableComment { get; set; }
    public string ColumnName { get; set; }
    public string DataType { get; set; }
    public int? Length { get; set; }
    public bool IsNullable { get; set; }
    public string DefaultValue { get; set; }
    public string Comment { get; set; }
    public bool IsPrimaryKey { get; set; }
}

public class DbHelper : IDbHelper
{
    public string CheckDatabaseType(DbContext dbContext)
    {
        //var extension = dbContext.GetService<IDbContextOptionsExtension>();
        var providerName = dbContext.Database.ProviderName.ToLower();// extension.GetType().Name.ToLower();
        string result;

        if (providerName.Contains("sqlserver"))
        {
            result = "sqlserver";
        }
        else if (providerName.Contains("sqlite"))
        {
            result = "sqlite";
        }
        else if (providerName.Contains("mysql"))
        {
            result = "mysql";
        }
        else if (providerName.Contains("postgresql"))
        {
            result = "postgresql";
        }
        else if (providerName.Contains("inmemory"))
        {
            result = "inmemory";
        }
        else if (providerName.Contains("cosmos"))
        {
            result = "cosmos";
        }
        else if (providerName.Contains("oracle"))
        {
            result = "oracle";
        }
        else
        {
            result = "other";
        }

        return result;
    }

    public string GetAllTablesSql(string dbType)
    {
        string sql = string.Empty;

        if (dbType == "sqlserver")
        {
            throw new Exception("SQL Server Not implemented yet");
        }
        else if (dbType == "mysql")
        {
            throw new Exception("MySQL Not implemented yet");
        }
        else if (dbType == "postgresql")
        {
            sql = @"SELECT table_name as ""TableName"",pg_catalog.obj_description(pgc.oid, 'pg_class') as ""TableComment"" 
                    FROM information_schema.tables t INNER JOIN pg_catalog.pg_class pgc ON t.table_name = pgc.relname 
                    WHERE t.table_schema = 'public' AND t.table_type = 'BASE TABLE';";
        }
        else if (dbType == "sqlite")
        {
            throw new Exception("SQLite Not implemented yet");
        }

        return sql;
    }

    public string GetAllTableStructureSql(string dbType)
    {
        if (dbType == "postgresql")
        {
            return
                @"
                SELECT 
                    c.table_name as ""TableName"",
                    c.column_name as ""ColumnName"",
                    c.data_type as ""DataType"",
                    c.character_maximum_length as ""Length"",
                    CASE WHEN c.is_nullable = 'YES' THEN true ELSE false END as ""IsNullable"",
                    c.column_default as ""DefaultValue"",
                    pgd.description as ""Comment"",
                    pgd_table.description as ""TableComment"",
                    CASE WHEN kcu.column_name IS NOT NULL THEN true ELSE false END as ""IsPrimaryKey""
                FROM 
                    information_schema.columns c
                    JOIN information_schema.tables t ON c.table_catalog = t.table_catalog AND c.table_schema = t.table_schema AND c.table_name = t.table_name
                    LEFT JOIN pg_catalog.pg_statio_all_tables st ON c.table_schema = st.schemaname AND c.table_name = st.relname
                    LEFT JOIN pg_catalog.pg_description pgd ON (pgd.objoid = st.relid AND pgd.objsubid = c.ordinal_position)
                    LEFT JOIN pg_catalog.pg_description pgd_table ON (pgd_table.objoid = st.relid AND pgd_table.objsubid = 0)
                    LEFT JOIN information_schema.key_column_usage kcu ON c.table_catalog = kcu.table_catalog AND c.table_schema = kcu.table_schema AND c.table_name = kcu.table_name AND c.column_name = kcu.column_name
                    LEFT JOIN information_schema.table_constraints tc ON kcu.constraint_catalog = tc.constraint_catalog AND kcu.constraint_schema = tc.constraint_schema AND kcu.constraint_name = tc.constraint_name AND tc.constraint_type = 'PRIMARY KEY'
                WHERE 
                    c.table_schema = 'public'
                    AND t.table_type = 'BASE TABLE';
                ";
        }

        return string.Empty;
    }

    public string GenerateCreateTableScript(List<TableStructure> tableStructures)
    {
        var createTableStatements = tableStructures
            .GroupBy(ts => ts.TableName)
            .Select(group =>
            {
                var tableName = group.Key;
                var tableComment = group.FirstOrDefault()?.TableComment;
                var columnComments = string.Join("<br> ", group.Where(ts => !string.IsNullOrEmpty(ts.Comment)).Select(ts => $@"COMMENT ON COLUMN ""public"".""{tableName}"".""{ts.ColumnName}"" IS '{ts.Comment}';"));
                var columnDefinitions = group
                    .Select(ts =>
                    {
                        var columnName = ts.ColumnName;
                        var dataType = ts.DataType;
                        var length = ts.Length.HasValue ? $"({ts.Length.Value})" : "";
                        var nullable = ts.IsNullable ? "" : " NOT NULL";
                        var defaultValue = string.IsNullOrEmpty(ts.DefaultValue) ? "" : $" DEFAULT {ts.DefaultValue}";
                        var comment = string.IsNullOrEmpty(ts.Comment) ? "" : $" /* {ts.Comment} */";

                        return $@"&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;""{columnName}"" {dataType}{length}{nullable}{defaultValue}";
                    })
                    .Aggregate((current, next) => $@"{current},<br>{next}");

                var primaryKeyColumns = group
                    .Where(ts => ts.IsPrimaryKey)
                    .Select(ts => ts.ColumnName)
                    .ToList();

                var primaryKeyConstraint = primaryKeyColumns.Any()
                    ? $@",<br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;CONSTRAINT ""PK_{tableName}"" PRIMARY KEY (""{string.Join("\",\" ", primaryKeyColumns)}"")"
                    : "";

                var createTableStatement = $@"CREATE TABLE ""{tableName}"" (<br>{columnDefinitions}{primaryKeyConstraint}<br>);";
                var commentStatement = (string.IsNullOrEmpty(tableComment) ? "" : @$"<br>COMMENT ON TABLE ""{tableName}"" IS '{tableComment}';") +
                                       (string.IsNullOrEmpty(columnComments) ? "" : $@"<br>{columnComments}");

                return createTableStatement + commentStatement;
            })
            .ToList();

        return string.Join(@"<br><br>", createTableStatements);
    }

    public DynamicParameters GetParameters(string sql, object model)
    {
        DynamicParameters parameters = new DynamicParameters();

        // 获取所有参数
        var matches = Regex.Matches(sql, @"@\w+");

        foreach (Match match in matches.Cast<Match>())
        {
            var parameterName = match.Value;
            var parameterValue = model.GetType().GetProperty(parameterName[1..]).GetValue(model);

            parameters.Add(parameterName, parameterValue);
        }

        return parameters;
    }

    public List<TableInfo> ExtractTableMetadata(DbContext dbContext, List<string>? entityNameList)
    {
        ArgumentNullException.ThrowIfNull(dbContext);

        var model = dbContext.GetService<IDesignTimeModel>().Model;
        var newEntityTypes = model.GetEntityTypes();

        if (entityNameList != null)
        {
            newEntityTypes = newEntityTypes.Where(t => entityNameList.Contains(t.Name));
        }

        var tableInfoList = new List<TableInfo>();

        foreach (var entityType in newEntityTypes)
        {
            var tableInfo = new TableInfo
            {
                TableName = entityType.GetTableName(),
                TableComment = entityType.GetComment(),
                CreateTableScript = GenerateCreateTableSql(entityType),
                AlterTableAddIndexScript = GenerateAlterTableSql(entityType),
                Fields = new List<FieldInfo>()
            };

            var properties = entityType.GetProperties();

            foreach (var property in properties)
            {
                var fieldInfo = new FieldInfo
                {
                    ColumnName = property.Name,
                    DataType = property.ClrType.Name,
                    Length = property.GetMaxLength(),
                    IsPrimaryKey = property.IsPrimaryKey(),
                    IsNullable = property.IsNullable,
                    DefaultValue = property.GetDefaultValueSql(),
                    Comment = property.GetComment()
                };

                tableInfo.Fields.Add(fieldInfo);
            }

            tableInfoList.Add(tableInfo);
        }

        return tableInfoList;
    }

    public string GenerateCreateTableSql(IEntityType entityType)
    {
        var tableName = entityType.GetTableName();
        var properties = entityType.GetProperties();

        var sqlBuilder = new StringBuilder();
        sqlBuilder.AppendLine($"CREATE TABLE {tableName} (");

        foreach (var property in properties)
        {
            var columnName = property.GetColumnName();
            var dataType = GetSqlDataType(property.ClrType);
            var maxLength = property.GetMaxLength();
            var isPrimaryKey = property.IsPrimaryKey();
            var isNullable = property.IsNullable;
            var defaultValue = property.GetDefaultValueSql();
            var comment = property.GetComment();

            sqlBuilder.Append($"    {columnName} {dataType}");
            if (maxLength.HasValue)
            {
                sqlBuilder.Append($"({maxLength})");
            }
            if (isPrimaryKey)
            {
                sqlBuilder.Append(" PRIMARY KEY");
            }
            if (!isNullable)
            {
                sqlBuilder.Append(" NOT NULL");
            }
            if (!string.IsNullOrEmpty(defaultValue))
            {
                sqlBuilder.Append($" DEFAULT {defaultValue}");
            }
            if (!string.IsNullOrEmpty(comment))
            {
                sqlBuilder.Append($" -- {comment}");
            }

            sqlBuilder.AppendLine(",");
        }

        sqlBuilder.Remove(sqlBuilder.Length - 3, 1); // Remove trailing comma
        sqlBuilder.AppendLine(");");

        return sqlBuilder.ToString();
    }

    private string GenerateAlterTableSql(IEntityType entityType)
    {
        var tableName = entityType.GetTableName();
        var indexes = entityType.GetIndexes();

        var sqlBuilder = new StringBuilder();

        foreach (var index in indexes)
        {
            var indexName = index.GetDatabaseName();
            var columnNames = index.Properties.Select(p => p.GetColumnName());

            sqlBuilder.AppendLine($"CREATE INDEX {indexName} ON {tableName} ({string.Join(", ", columnNames)});");
        }

        return sqlBuilder.ToString();
    }

    private string GetSqlDataType(Type type)
    {
        var underlyingType = Nullable.GetUnderlyingType(type);
        if (underlyingType != null)
        {
            type = underlyingType;
        }

        if (type == typeof(int))
        {
            return "INTEGER";
        }
        else if (type == typeof(long))
        {
            return "BIGINT";
        }
        else if (type == typeof(short))
        {
            return "SMALLINT";
        }
        else if (type == typeof(byte))
        {
            return "SMALLINT";
        }
        else if (type == typeof(bool))
        {
            return "BOOLEAN";
        }
        else if (type == typeof(decimal))
        {
            return "NUMERIC(18,2)";
        }
        else if (type == typeof(double))
        {
            return "DOUBLE PRECISION";
        }
        else if (type == typeof(float))
        {
            return "REAL";
        }
        else if (type == typeof(DateTime))
        {
            return "TIMESTAMP";
        }
        else if (type == typeof(byte[]))
        {
            return "BYTEA";
        }
        else if (type == typeof(string))
        {
            return "TEXT";
        }
        else if (type == typeof(TimeSpan))
        {
            return "INTERVAL";
        }
        else if (type == typeof(DateTimeOffset))
        {
            return "TIMESTAMP WITH TIME ZONE";
        }
        else if (type == typeof(Guid))
        {
            return "UUID";
        }
        else
        {
            throw new NotSupportedException($"Unsupported data type: {type.Name}");
        }
    }

    //public void PrintMissingData(string step, IEnumerable<dynamic> before, IEnumerable<dynamic> after)
    //{
    //    var beforeIds = before.Select(x => (Guid)x.TrackingId).ToHashSet();
    //    var afterIds = after.Select(x => (Guid)x.TrackingId).ToHashSet();

    //    var missingIds = beforeIds.Except(afterIds);

    //    Console.WriteLine($"Data filtered out in {step} join:");
    //    int count = 0;
    //    foreach (var id in missingIds)
    //    {
    //        count++;
    //        var missingItem = before.First(x => x.TrackingId == id);
    //        Console.WriteLine($"  {count}. Missing - TrackingId: {id}, Object Code: {<EMAIL>}, Device PID: {missingItem.device.pid}, " +
    //                          $"ToStation SID: {missingItem.toStation.sid}, FromStation SID: {missingItem.fromStation.sid}, " +
    //                          $"Plane Code: {missingItem.plane.code}");
    //    }

    //    Console.WriteLine($"Total items filtered out: {missingIds.Count()}");
    //    Console.WriteLine($"Before count: {before.Count()}, After count: {after.Count()}, Difference: {before.Count() - after.Count()}");
    //    Console.WriteLine();
    //}

    public static void PrintMissingData<TBefore, TAfter>(string step, IEnumerable<TBefore> before, IEnumerable<TAfter> after)
    {
        var beforeIds = before.Select(x => ((dynamic)x).TrackingId).Cast<Guid>().ToHashSet();
        var afterIds = after.Select(x => ((dynamic)x).TrackingId).Cast<Guid>().ToHashSet();

        var missingIds = beforeIds.Except(afterIds);

        Console.WriteLine($"Data filtered out in {step}:");
        Console.WriteLine($"Before count: {beforeIds.Count}, After count: {afterIds.Count}");
        Console.WriteLine($"Number of items filtered out: {missingIds.Count()}");

        if (missingIds.Any())
        {
            Console.WriteLine("Sample of filtered out items:");
            foreach (var id in missingIds.Take(5))
            {
                var missingItem = before.First(x => ((dynamic)x).TrackingId == id);
                Console.WriteLine($"  TrackingId: {id}");
                // 你可以在這裡添加更多的詳細信息，具體取決於你的數據結構
            }
        }

        Console.WriteLine();
    }
}
