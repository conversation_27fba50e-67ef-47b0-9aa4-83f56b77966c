﻿using System.ComponentModel.DataAnnotations;
using Web.Constant;
using Web.Validation;

namespace Web.Models.Controller.Sector;

public class RetrieveSector
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    public string AreaCode { get; set; }
    public string BuildingCode { get; set; }
    public string BuildingName { get; set; }
    public string PlaneCode { get; set; }
    public string PlaneName { get; set; }
    public string SectorCode { get; set; }
    public string CustomSectorCode { get; set; }
    public string SectorName { get; set; }
    public string Enable { get; set; }
    public string DeptCodes {  get; set; }
    public string DeptNames {  get; set; }
    public string StationSids {  get; set; }
    public string StationNames {  get; set; }
}

public class CreateSector
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PlaneCode")]
    [Exists("", "Plane", "PlaneCode", ErrorMessage = Constants.ErrorCode.NotFound + "PlaneCode")]
    public string PlaneCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "SectorCode")]
    [Unique("", "Sector", "SectorCode", ErrorMessage = Constants.ErrorCode.Unique + "SectorCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "SectorCode")]
    public string SectorCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "CustomSectorCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "CustomSectorCode")]
    public string CustomSectorCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "SectorName")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "SectorName")]
    public string SectorName { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "MapWidth")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "MapWidth")]
    public double? MapWidth { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "MapHeight")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "MapHeight")]
    public double? MapHeight { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PositionX")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "PositionX")]
    public double? PositionX { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PositionY")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "PositionY")]
    public double? PositionY { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Enable")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }

    [ListAllExists("PlaneCode", "Station", "SID", ErrorMessage = Constants.ErrorCode.NotFound + "SIDList")]
    [EqualsPropertyValue("PlaneCode", "Station", "SID", "PlaneCode", "PlaneCode", true, ErrorMessage = Constants.ErrorCode.Invalid + "SIDList.SID")]
    public List<string> SIDList { get; set; }
}

public class UpdateSector
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "SectorCode")]
    [Exists("", "Sector", "SectorCode", ErrorMessage = Constants.ErrorCode.NotFound + "SectorCode")]
    [HasReferenceWhenEquals("Enable", "N", "", "Department", "SectorCode", ErrorMessage = Constants.ErrorCode.Reference + "SectorCode")]
    public string SectorCode { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "CustomSectorCode")]
    public string CustomSectorCode { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "SectorName")]
    public string SectorName { get; set; }

    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "MapWidth")]
    public double? MapWidth { get; set; }

    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "MapHeight")]
    public double? MapHeight { get; set; }

    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "PositionX")]
    public double? PositionX { get; set; }

    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "PositionY")]
    public double? PositionY { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }

    [ListAllExists("PlaneCode", "Station", "SID", ErrorMessage = Constants.ErrorCode.NotFound + "SIDList")]
    [EqualsPropertyValue("PlaneCode", "Station", "SID", "PlaneCode", "PlaneCode", true, ErrorMessage = Constants.ErrorCode.Invalid + "SIDList.SID")]
    public List<string> SIDList { get; set; }
}

public class UpdateSectorMap
{   
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "SectorCode")]
    [Exists("", "Sector", "SectorCode", ErrorMessage = Constants.ErrorCode.NotFound + "SectorCode")]
    public string SectorCode { get; set; }

     [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ImageBase64")]
     public string ImageBase64 { get; set; } 

     [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ImageFileType")]
    public string ImageFileType { get; set; } 
}

public class DeleteSector
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "SectorCode")]
    [Exists("", "Sector", "SectorCode", ErrorMessage = Constants.ErrorCode.NotFound + "SectorCode")]
    [HasReference("", "Department", "SectorCode", ErrorMessage = Constants.ErrorCode.Reference + "SectorCode")]
    public string SectorCode { get; set; }

}