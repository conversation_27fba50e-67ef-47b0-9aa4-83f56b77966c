﻿using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Data;
using Web.Models.Controller;
using Web.Models.Controller.Area;
using Web.Models.Controller.Log;
using Web.Models.Service;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;
using Web.Validation;

namespace Web.Controller;

/// <summary>
/// Area控制器
/// </summary>
[Route("[controller]")]
[Authorize]
public class AreaController(IDataAccessService dataAccessService,
                            ICredentialService credentialService,
                            IRequestContextService requestContextService,
                            IUtilityService utilityService,
                            ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly IUtilityService _utilityService = utilityService;

    [HttpPost("areas")]
    [RequestParamListDuplicate("AreaCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateArea([FromBody]List<InCreateArea> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string appCode = _user.AppCode;

        bool addResult = false;

        List<Area> hmnInputList =
            paramList.Select(e => new Area
            {
                OrganizationCode = appCode,     //TODO: 未來這裡要拿掉
                AppCode = appCode,
                AreaCode = e.AreaCode,
                AreaName = e.AreaName,
                CustomAreaCode = e.CustomAreaCode
            }).ToList();

        int createCount = await _dataAccessService.CreateRangeAsync(hmnInputList);

        addResult = createCount == hmnInputList.Count;

        ReturnModel returnModel = new()
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = addResult ? StatusCodes.Status201Created : StatusCodes.Status400BadRequest,
            result = addResult
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }
    [HttpGet("areas")]
    public async Task<IActionResult> RetrieveArea(InRetrieveArea param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();

        var query = _dataAccessService.Fetch<Area>(e => e.AppCode == _user.AppCode && (param.AreaCode == null  || param.AreaCode.Contains(e.AreaCode)));

        (int recordTotal, List<object> recordList) =
            await _utilityService.Pagination(query, param.page, param.size, param.sort);

        ReturnModel returnModel = new()
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal,
                recordList
            }
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }
    [HttpPatch("areas")]
    [RequestParamListDuplicate("AreaCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateArea([FromBody] List<InUpdateArea> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string appCode = _user.AppCode;

        bool hmnResult = false;

        string[] codes = paramList.Select(e => e.AreaCode.ToUpper()).ToArray();

        // 取出此次要更新的HMN 資料
        List<Area> hmnDataInputList =
            await _dataAccessService.Fetch<Area>(e => e.AppCode == appCode && codes.Contains(e.AreaCode)).AsTracking().ToListAsync();

        // 更新成功筆數
        int updateCount = 0;

        // 寫入資料庫
        foreach (var oldData in hmnDataInputList)
        {
            var newData = paramList.FirstOrDefault(e => e.AreaCode.ToUpper() == oldData.AreaCode);

            oldData.AreaName = newData.AreaName;
            oldData.CustomAreaCode = newData.CustomAreaCode;

            updateCount = updateCount + await _dataAccessService.UpdateAsync(oldData);
        }

        // 比對更新筆數是否與參數筆數相同
        hmnResult = updateCount == paramList.Count;

        ReturnModel returnModel = new()
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = hmnResult ? StatusCodes.Status200OK : StatusCodes.Status400BadRequest,
            result = hmnResult
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }
}
