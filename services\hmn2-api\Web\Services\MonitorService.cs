﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Globalization;
using System.Reflection.Emit;
using System.Text.Json;
using Web.Models.Service.Fusion;
using Web.Models.Controller.Task;
using Web.Models.Service.Configuration;
using Web.Models.Service.Monitor;
using Web.Models.Service.Preference;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;

namespace Web.Services;

public class MonitorService(ITaskService taskService,
    ILangService langService,
    IPreferenceService preferenceService,
    IDataAccessService dataAccessService,
    ILogService logService) : IMonitorService
{
    private readonly IDataAccessService _dataAccessService = dataAccessService;
    private readonly IPreferenceService _preferenceService = preferenceService;
    private readonly ITaskService _taskService = taskService;
    private readonly ILangService _langService = langService;
    private readonly ILogService _logService = logService;

    public async Task<List<PatchAPIResult>> ClearFusionTask(int taskId, string description)
    {
        var resolveTaskList = new List<dynamic>
        {
            new { id = taskId, action = 30, description }
        };

        var fusionResult = await _taskService.PatchTask(resolveTaskList).ConfigureAwait(false);

        return fusionResult;
    }

    public async Task<List<PatchAPIResult>> ClearFusionTask(List<ClearFusionTaskInput> clearFusionTaskList)
    {
        var resolveTaskList = new List<dynamic>(clearFusionTaskList.Select(x => new {id = x.TaskId, Action = 30, description = x.Description}).ToList());
        var fusionResult = await _taskService.PatchTask(resolveTaskList).ConfigureAwait(false);

        return fusionResult;
    }

    public async Task<List<TaskInfo>> GetTaskList(InTaskList param)
    {
        var appCode = param.AppCode;
        List<TaskInfo> taskList = [];

        try
        {
            var taskDataList = from a in _dataAccessService.Fetch<TaskDatum>(e=>e.AppCode == appCode)
                               where (param.Id == 0 || a.Id == param.Id)
                                  && (param.TaskId == 0 || a.TaskId == param.TaskId)
                                  && (param.AreaCode == null || a.AreaCode == param.AreaCode)
                                  && (param.TaskAction == null || a.Action == Convert.ToInt32(param.TaskAction))
                                  && (param.StartDate == null || a.StartsAt >= DateTime.ParseExact(param.StartDate, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal))
                                  && (param.EndDate == null || a.StartsAt <= DateTime.ParseExact(param.EndDate, "yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal))
                               select a;

            //由WebAPI取得所有服務列表（用於多國語言的轉換）
            var serviceList = await _langService.GetServiceCodes().ConfigureAwait(false);

            // 服務代碼列表
            var serviceCodeList = serviceList.ToDictionary(s => s.code, s => s.langs?.nameId ?? "");

            // 對象列表
            var objectList = _dataAccessService.Fetch<ObjectDatum>(e => e.AppCode == appCode);

            // 單位列表
            var deptList = _dataAccessService.Fetch<Department>(e => e.AppCode == appCode);

            // 棟別列表
            var buildingList = _dataAccessService.Fetch<Building>(e => e.AppCode == appCode);

            // 樓層列表
            var planeList = _dataAccessService.Fetch<Plane>(e => e.AppCode == appCode);

            // 取得所有裝置的基站編號
            var taskSidList = await taskDataList.Where(x => x.SponsorStation != null).Select(x => x.SponsorStation).ToListAsync();

            // 基站列表
            var stationList = _dataAccessService.Fetch<Station>(e => e.AppCode == appCode);

            // 取得所有基站所在的次平面代碼
            var sectorStationList = _dataAccessService.Fetch<SectorStation>(s => s.AppCode == appCode && taskSidList.Contains(s.Stationsid));

            // 取得所有基站的次平面的資料
            var sectorList = _dataAccessService.Fetch<Sector>(s => s.AppCode == appCode && sectorStationList.Any(ss => ss.SectorCode == s.SectorCode));

            // 取得所有次平面的部門對應資料
            var departSectorList = _dataAccessService.Fetch<DepartSector>(d => d.AppCode == appCode && sectorList.Any(s => s.SectorCode == d.SectorCode));

            // 取得所有事件解除的資訊
            var eventCannedMessageList = _dataAccessService.Fetch<EventCannedMessage>(e => e.AppCode == appCode);

            var query1 = from a in taskDataList
                         select a;

            //_logService.Logging("Info", "MonitorService.GetTaskList", "", $"taskDataList count: {query1.ToList().Count()}");

            var query2 = from a in query1
                         join e in objectList on a.SponsorObjectCode equals e.ObjectCode into objectJoin
                         from oj in objectJoin.DefaultIfEmpty()
                         select new { a, oj };


            //_logService.Logging("Info", "MonitorService.GetTaskList", "", $"After objectList join count: {query2.ToList().Count()}");

            var query3 = from q in query2
                         join p in planeList on q.a.PlaneCode equals p.PlaneCode into planeJoin
                         from pj in planeJoin.DefaultIfEmpty()
                         select new { q.a, q.oj, pj };

            //_logService.Logging("Info", "MonitorService.GetTaskList", "", $"After planeList join count: {query3.ToList().Count()}");

            var query4 = from q in query3
                         join s in stationList on q.a.SponsorStation equals s.SID into stationJoin
                         from st in stationJoin.DefaultIfEmpty()
                         select new { q.a, q.oj, q.pj, st };

            //_logService.Logging("Info", "MonitorService.GetTaskList", "", $"After stationList join count: {query4.ToList().Count()}");

            var query5 = from q in query4
                         join dept in deptList on q.a.DeptCode equals dept.DeptCode into deptJoin
                         from dept in deptJoin.DefaultIfEmpty()
                         select new { q.a, q.oj, q.pj, q.st, dept };

            //_logService.Logging("Info", "MonitorService.GetTaskList", "", $"After deptList join count: {query5.ToList().Count()}");

            var query6 = from q in query5
                         join building in buildingList on q.a.BuildingCode equals building.BuildingCode into buildingJoin
                         from building in buildingJoin.DefaultIfEmpty()
                         let sectors = sectorList.Where(s => sectorStationList.Any(ss => ss.Stationsid == q.a.SponsorStation && ss.SectorCode == s.SectorCode)).ToList()
                         select new { q.a, q.oj, q.pj, q.st, q.dept, building, sectors };

            //_logService.Logging("Info", "MonitorService.GetTaskList", "", $"After buildingList join count: {query6.ToList().Count()}");

            var query7 = from q in query6
                         where (param.ServiceCode == null || q.a.ServiceCode == param.ServiceCode || (q.a.ServiceCode == "LongPress" && param.ServiceCode == "Help"))
                             && (param.BuildingCode == null || (q.a.BuildingCode != null && q.a.BuildingCode.ToUpper().Contains(param.BuildingCode.ToUpper())))
                             && (param.PlaneCode == null || (q.a.PlaneCode != null && q.a.PlaneCode.ToUpper().Contains(param.PlaneCode)))
                             && (param.LocCode == null || (q.a.SponsorLocationCode != null && q.a.SponsorLocationCode.ToUpper() == param.LocCode.ToUpper()))
                             && (param.DeptCode == null || (q.a.DeptCode != null && param.DeptCode.ToUpper().Contains(q.a.DeptCode.ToUpper())) || (q.a.DeptCode == null))
                             && (param.DevicePid == null || (q.a.SponsorDevicePid != null && q.a.SponsorDevicePid.ToUpper().Contains(param.DevicePid.ToUpper())))
                             && (param.DeviceType == null || (q.a.SponsorDeviceType != null && q.a.SponsorDeviceType.ToUpper().Contains(param.DeviceType.ToUpper())))
                             && (param.DeviceName == null || (q.a.SponsorDeviceName != null && q.a.SponsorDeviceName.ToUpper().Contains(param.DeviceName.ToUpper())))
                             && (param.ObjectCode == null || (q.a.SponsorObjectCode != null && q.a.SponsorObjectCode.ToUpper().Contains(param.ObjectCode.ToUpper())))
                             && (param.ObjectName == null || (q.a.SponsorObjectName != null && q.a.SponsorObjectName.ToUpper().Contains(param.ObjectName.ToUpper())))
                             && (param.ObjectType == null || (q.a.SponsorObjectType != null && q.a.SponsorObjectType.ToUpper().Contains(param.ObjectType.ToUpper())))
                             && (param.ObjectGroup == null || (q.oj.GroupCode != null && q.oj.GroupCode.ToUpper().Contains(param.ObjectGroup.ToUpper())))
                         orderby q.a.StartsAt descending
                         select new TaskInfo
                         {
                             Id = q.a.Id,
                             TaskId = q.a.TaskId,
                             Action = q.a.Action,
                             BuildingCode = q.a.BuildingCode ?? "",
                             BuildingName = q.building.BuildingName ?? "",
                             DeptCode = q.a.DeptCode ?? "",
                             DeptName = q.dept.DeptName,
                             EventCode = q.a.EventCode ?? "",
                             EventName = q.a.EventName ?? "",
                             FinishesAt = q.a.FinishesAt.HasValue ? q.a.FinishesAt.Value.ToString("yyyy-MM-ddTHH:mm:ss") : null,
                             Plane = q.pj,
                             ServiceCode = q.a.ServiceCode ?? "",
                             ServiceName = serviceCodeList.GetValueOrDefault(q.a.ServiceCode, q.a.ServiceCode) ?? q.a.ServiceCode ?? "",
                             LocationName = q.a.SponsorLocationName ?? "",
                             LocationCode = q.a.SponsorLocationCode ?? "",
                             ObjectCode = q.a.SponsorObjectCode ?? "",
                             ObjectName = q.a.SponsorObjectName ?? "",
                             ObjectType = q.a.SponsorObjectType ?? "",
                             GroupCode = q.oj.GroupCode ?? "",
                             Pid = q.a.SponsorDevicePid ?? "",
                             DeviceName = q.a.SponsorDeviceName ?? "",
                             DeviceType = q.a.SponsorDeviceType ?? "",
                             EventCannedMessages = eventCannedMessageList.Where(ec => ec.TaskId == q.a.TaskId).ToList(),
                             Sector = q.sectors.Select(s => new sector
                             {
                                 code = s.SectorCode,
                                 name = s.SectorName,
                                 mapHeight = s.MapHeight.ToString(),
                                 mapWidth = s.MapWidth.ToString(),
                                 positionX = s.PositionX.ToString(),
                                 positionY = s.PositionY.ToString(),
                                 deptCode = departSectorList.Where(ds => ds.SectorCode == s.SectorCode).Select(ds => ds.DeptCode).FirstOrDefault()
                             }).ToList(),
                             Station = q.st,
                             StartsAt = q.a.StartsAt.HasValue ? q.a.StartsAt.Value.ToString("yyyy-MM-ddTHH:mm:ss") : null
                         };

            //_logService.Logging("Info", "MonitorService.GetTaskList", "", $"Final query count: {query7.ToList().Count()}");

            var query = query7;

            var sql = query.ToQueryString();
            taskList = await query.ToListAsync();
        }
        catch (Exception e)
        {
            _logService.Logging("Error", "MonitorService.GetTaskList", "", JsonSerializer.Serialize(param));
            _logService.Logging("Error", "MonitorService.GetTaskList", "", e.ToString());
        }

        return taskList;
    }

    public async Task<List<TaskDatum>> GetFollowingTaskWithoutPerm(List<string> eventCodeList)
    {
        var taskList = _dataAccessService.Fetch<TaskDatum>(e=>eventCodeList.Contains(e.EventCode) && e.Action == 10);

        return await taskList.ToListAsync();
    }

    public async Task<List<TaskInfo>> GetFollowingTaskList(InFollowingTaskList param)
    {
        string appCode = param.AppCode;
        IEnumerable<string> deptCodeList = param.DeptCodeList;
        _ = bool.TryParse(await _preferenceService.FetchGlobalParameter(appCode, "HelpNotifyAllWeb"), out bool isHelpNotifyAllWeb);

        var query = _dataAccessService.Fetch<DepartSector>(e => e.AppCode == appCode)
            .Where(ds => deptCodeList.Contains(ds.DeptCode))
            .Select(ds => new { ds.SectorCode, ds.DeptCode });

        var managedSectorCodeList = query.Select(x => x.SectorCode).ToList();
        var managedDeptList = deptCodeList;

        var managedStationList = _dataAccessService.Fetch<SectorStation>(e => e.AppCode == appCode)
            .Where(ss => managedSectorCodeList.Contains(ss.SectorCode ?? ""))
        .Select(ss => ss.Stationsid)
        .ToList();

        var managedObjectList = _dataAccessService.Fetch<ObjectDatum>(e => e.AppCode == appCode)
            .Where(o => managedDeptList.Contains(o.UsageDepartCode))
            .Select(o => o.ObjectCode)
            .ToList();

        string eventNotifyConfJson = await _preferenceService.FetchGlobalParameter(appCode, "EventNotify");
        List<EventNotifyPreference>? eventNotifyConf = string.IsNullOrEmpty(eventNotifyConfJson) ? [] : JsonSerializer.Deserialize<List<EventNotifyPreference>>(eventNotifyConfJson);

        var followTaskList = await this.GetTaskList(new InTaskList
        {
            AppCode = param.AppCode,
            AreaCode = param.AreaCode,
            TaskAction = "10"
        });

        var filteredTaskList = followTaskList.Where(t =>
        {
            // 如果是求救事件或長按事件
            if (t.ServiceCode.ToLower() == "help" || t.ServiceCode.ToLower() == "longpress")
            {
                // 如果是HelpNotifyAllWeb，則一律通知
                if (isHelpNotifyAllWeb)
                {
                    return true;
                }
                else
                {   //否則過濾掉非自己的事件
                    return managedStationList.Contains(t.Station?.SID) || managedObjectList.Contains(t.ObjectCode);
                }
            }

            var serviceCode = t.ServiceCode.ToLower();

            var notifyConf = eventNotifyConf.FirstOrDefault(e => e.ServiceCode.ToLower() == serviceCode);

            if (notifyConf == null)
            {   // 如果沒有設定通知條件，則一律通知
                return true;
            }
            else
            {   // 如果有設定通知條件，則依照設定通知(TaskSectorDept==true 通知發生單位, ObjectDept==true 通知權責單位)
                return (notifyConf.ObjectDept && managedObjectList.Contains(t.ObjectCode)) || (notifyConf.TaskSectorDept && managedStationList.Contains(t.Station?.SID ?? ""));
            }
        });

        return filteredTaskList.OrderByDescending(f => f.StartsAt).ToList();
    }
}
