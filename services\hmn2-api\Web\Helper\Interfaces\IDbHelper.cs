﻿using Dapper;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

namespace Web.Helper.Interfaces;

public interface IDbHelper
{
    string CheckDatabaseType(DbContext dbContext);
    List<TableInfo> ExtractTableMetadata(DbContext dbContext, List<string>? entityNameList);
    string GenerateCreateTableScript(List<TableStructure> tableStructures);
    string GenerateCreateTableSql(IEntityType entityType);
    string GetAllTablesSql(string dbType);
    string GetAllTableStructureSql(string dbType);
    DynamicParameters GetParameters(string sql, object model);
}