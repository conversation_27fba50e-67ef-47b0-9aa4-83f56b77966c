﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using System.Text.Json;
using System.Text.RegularExpressions;
using Web.Models.Controller.ObjectGroup;
using Web.Models.Controller;
using Web.Models.Service;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;
using DocumentFormat.OpenXml.Drawing;
using Web.Validation;
using Web.Constant;

namespace Web.Controller;

/// <summary>
/// 對象群組
/// </summary>
[Route("[controller]")]
[Authorize]
public class ObjectGroupController(IDataAccessService dataAccessService,
                                    ICredentialService credentialService,
                                    IRequestContextService requestContextService,
                                    ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{   
    /// 新增對象群組檢核
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    // 有在method 定義route，在Controller記得也要定義route，否則會404
    [HttpPost("objectGroups/validate")]
    [RequestParamListDuplicate("GroupCode")]
    [RequestParamListNotNullOrEmpty]
    public IActionResult ValidateObjectGroup([FromBody] List<CreateObjectGroup> paramList)
    {
        // 進到 controller 代表驗證通過，回傳空的錯誤列表
        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new List<ReturnError>()
        });
    }

    /// <summary>
    /// 新增對象群組
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    // 有在method 定義route，在Controller記得也要定義route，否則會404
    [HttpPost("objectGroups")]
    [RequestParamListDuplicate("GroupCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateObjectGroup([FromBody] List<CreateObjectGroup> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        string appCode = _user.AppCode;
        _logService.Logging("info", logActionName, requestUUID, "ObjectGroup Data Validated, start to append.");

        _dataAccessService.BeginTransaction();

        foreach (CreateObjectGroup og in paramList)
        {
            ObjectGroup objectGroup = new ObjectGroup
            {
                AppCode = _user.AppCode,
                AreaCode = og.AreaCode,
                GroupCode = og.GroupCode,
                GroupName = og.GroupName,
                Active = true,
                Enable = og.Enable == "Y",
                CreateDate = DateTime.Now,
                CreateUserAccount = _user.Account,
                ModifyDate = DateTime.Now
            };

            // 新增 ObjectGroup
            await _dataAccessService.CreateAsync<ObjectGroup>(objectGroup);

            // 將對象列表的對象新增至建立的對象群組
            List<string> objectCodeList = og.ObjectCodeList;
            if (objectCodeList != null && objectCodeList.Count > 0)
            {
                // 取出 ObjectCode 對應的 ObjectDatum 並更新其 GroupCode
                var objectsToUpdate = _dataAccessService.Fetch<ObjectDatum>(e => e.AppCode == appCode && e.AreaCode == og.AreaCode && objectCodeList.Contains(e.ObjectCode)).AsTracking().ToList();

                if (objectsToUpdate.Any())
                {
                    foreach (var obj in objectsToUpdate)
                    {
                        obj.GroupCode = objectGroup.GroupCode; // 更新 GroupCode
                        obj.ModifyDate = DateTime.Now;         // 更新修改日期
                        obj.ModifyUserAccount = _user.Account; // 更新修改者
                    }

                    // 批量更新物件
                    await _dataAccessService.UpdateAsync(objectsToUpdate);
                }
            }
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "ObjectGroup Data append done.");

        returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status201Created,
            result = true
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpPatch("objectGroups")]
    [RequestParamListDuplicate("GroupCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateObjectGroup([FromBody] List<UpdateObjectGroup> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        _dataAccessService.BeginTransaction();

        foreach (UpdateObjectGroup param in paramList)
        {
            ObjectGroup objectGroup = _dataAccessService.Fetch<ObjectGroup>(e => e.AppCode == _user.AppCode && e.GroupCode == param.GroupCode).AsTracking().First();
            var groupCode = param.GroupCode;

            List<string> updateField = new List<string>();

            if (param.Enable != null)
            {
                updateField.Add("Enable");
                objectGroup.Enable = param.Enable == "Y";
            }

            if (param.GroupName != null)
            {
                updateField.Add("GroupName");
                objectGroup.GroupName = param.GroupName;
            }

            updateField.Add("ModifyDate");
            updateField.Add("ModifyUserAccount");
            objectGroup.ModifyDate = DateTime.Now;
            objectGroup.ModifyUserAccount = _user.Account;

            await _dataAccessService.UpdateAsync<ObjectGroup>(objectGroup, updateField.ToArray());

            // 如果 ObjectCodeList 為 null 就不更新對象資料
            if (param.ObjectCodeList == null)
                continue;

            // 更新原本屬於該 GroupCode 的對象物件
            var objectsToReset = _dataAccessService.Fetch<ObjectDatum>(e => e.AppCode == _user.AppCode &&  e.GroupCode == groupCode).AsTracking().ToList();
            if (objectsToReset.Any())
            {
                foreach (var o in objectsToReset)
                {
                    o.GroupCode = null;
                    o.ModifyDate = DateTime.Now;
                    o.ModifyUserAccount = _user.Account;
                }
                await _dataAccessService.UpdateAsync(objectsToReset);
            }

             // 將對象列表的對象新增至對象群組
            List<string> objectCodeList = param.ObjectCodeList;
            if (objectCodeList != null && objectCodeList.Count > 0 && objectGroup.Active)
            {
                var objectsToUpdate = _dataAccessService.Fetch<ObjectDatum>(e => e.AppCode == _user.AppCode && objectCodeList.Contains(e.ObjectCode)).AsTracking().ToList();
                if (objectsToUpdate.Any())
                {
                    foreach (var o in objectsToUpdate)
                    {
                        o.GroupCode = groupCode;
                        o.ModifyDate = DateTime.Now;
                        o.ModifyUserAccount = _user.Account;
                    }
                    await _dataAccessService.UpdateAsync(objectsToUpdate);
                }
            }
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "ObjectGroup Data update done.");

        return Ok(new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = true
                });
    }

    [HttpDelete("objectGroups")]
    [RequestParamListDuplicate("GroupCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeleteObjectGroup([FromBody] List<DeleteObjectGroup> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 開始刪除對象群組資料
        _logService.Logging("info", logActionName, requestUUID, "ObjectGroup Data Validated, start to delete.");
        _dataAccessService.BeginTransaction();

        foreach (DeleteObjectGroup og in paramList)
        {
            // ObjectGroup objectGroup = _dataAccessService.Fetch<ObjectGroup>(e => e.AppCode == _user.AppCode && e.GroupCode == og.GroupCode).AsTracking().First();

            // List<string> updateField = new List<string>();
            // updateField.Add("Active");
            // objectGroup.Active = false;

            // updateField.Add("Enable");
            // objectGroup.Enable = false;

            // updateField.Add("ModifyDate");
            // updateField.Add("ModifyUserAccount");
            // objectGroup.ModifyDate = DateTime.Now;
            // objectGroup.ModifyUserAccount = _user.Account;

            // await _dataAccessService.UpdateAsync<ObjectGroup>(objectGroup, updateField.ToArray());

            // 刪除 ObjectGroup
            await _dataAccessService.DeleteAsync<ObjectGroup>(e=>e.AppCode==_user.AppCode && e.GroupCode == og.GroupCode);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "ObjectGroup Data delete done.");

        return Ok(new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = true
                });
    }

    [HttpGet("objectGroups")]
    public async Task<IActionResult> RetrieveObjectGroup([FromQuery] RetrieveObjectGroup queryParam)
    {
        RetrieveObjectGroup param = queryParam;
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // 排序；如果沒有 sort 條件預設使用 ModifyDate 降冪排序
        string sort = string.IsNullOrEmpty(param.sort) ? "ModifyDate:desc" : param.sort;
        string sortByField = sort.Split(":")[0];
        string sortDirection = sort.Split(":")[1];

        var areaList = _dataAccessService.Fetch<Area>(e=>e.AppCode == _user.AppCode);
        var objectGroupList = _dataAccessService.Fetch<ObjectGroup>(x => x.AppCode == _user.AppCode);
        var objectList = _dataAccessService.Fetch<ObjectDatum>(x => x.AppCode == _user.AppCode);

        var query = (from a in objectGroupList
             join b in areaList on a.AreaCode equals b.AreaCode into temp
             from t in temp.DefaultIfEmpty()
             select new
             {
                 a.GroupId,
                 Enable = (a.Enable == true) ? "Y" : "N",
                 Active = (a.Active == true) ? "Y" : "N",
                 a.GroupCode,
                 a.GroupName,
                 AreaName = (t == null) ? "" : t.AreaName,
                 a.AreaCode,
                 Objects = (from obj in objectList
                            where obj.GroupCode == a.GroupCode
                            orderby obj.ObjectCode
                            select new
                            {
                                obj.Name,
                                obj.ObjectCode,
                                obj.UsageDepartCode
                            }).ToList(),
                 a.CreateDate,
                 a.CreateUserAccount,
                 a.ModifyDate,
                 a.ModifyUserAccount
             })
            .Where(x => (param.GroupName == null || x.GroupName.ToUpper().Contains(param.GroupName.ToUpper()))
                     && (param.GroupCode == null || x.GroupCode.ToUpper().Contains(param.GroupCode.ToUpper()))
                     && (param.AreaCode == null || x.AreaCode.ToUpper().Contains(param.AreaCode.ToUpper()))
                     && (x.Active == (string.IsNullOrEmpty(param.Active) ? "Y" : param.Active))
                     && (param.Enable == null || x.Enable == param.Enable)
                     && (string.IsNullOrEmpty(param.ObjectCode) || x.Objects.Any(o => o.ObjectCode.ToUpper().Contains(param.ObjectCode.ToUpper())))
                     && (string.IsNullOrEmpty(param.ObjectName) || x.Objects.Any(o => o.Name.ToUpper().Contains(param.ObjectName.ToUpper())))
                     && (string.IsNullOrEmpty(param.ObjectUsageDepartCode) || x.Objects.Any(o => o.UsageDepartCode == param.ObjectUsageDepartCode))
                );

        // 取得總筆數
        int recordTotal = await query.CountAsync();

        // 根據 sortByField 和 sortDirection 進行動態排序
        query = sortDirection.ToLower() == "desc"
            ? query.OrderByDescending(x => EF.Property<object>(x, sortByField))
            : query.OrderBy(x => EF.Property<object>(x, sortByField));

        // 進行資料庫分頁
        var recordList = size == 0 
            ? await query.ToListAsync() // 如果 size == 0，表示不分頁，直接取回所有資料
            : await query.Skip(skip).Take(size).ToListAsync(); // 分頁查詢

        ReturnModel returnModel = new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = new
                    {
                        recordTotal,
                        recordList
                    }
                };

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);
    }
}
