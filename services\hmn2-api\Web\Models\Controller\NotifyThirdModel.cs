using System.ComponentModel.DataAnnotations;
using Web.Validation;
using Web.Constant;
using System.Text.Json.Serialization;

namespace Web.Models.Controller.NotifyThird;

public class RetrieveNotifyThird
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    public string AreaCode { get; set; }
    public string ThirdCode { get; set; }
    public string ThirdName { get; set; }
    public string NotifyType { get; set; }
}

public class CreateNotifyThird
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "NotifyType")]
    [RegularExpression(@"^(ENS|Display)$", ErrorMessage = Constants.ErrorCode.Pattern + "NotifyType")]
    public string NotifyType { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ThirdCode")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "ThirdCode")]
    [StringLength(20, ErrorMessage = Constants.ErrorCode.Length + "ThirdCode")]
    [Unique("", "NotifyThird", "ThirdCode", ErrorMessage = Constants.ErrorCode.Unique + "ThirdCode")]
    public string ThirdCode { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ThirdName")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "ThirdName")]
    public string ThirdName { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "URL_MAC")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "URL_MAC")]
    [UniqueWhenEquals("NotifyType", "Display", "NotifyType:Display", "NotifyThird", "URL_MAC", false, false, ErrorMessage = Constants.ErrorCode.Unique + "URL_MAC")]
    public string URL_MAC { get; set; }
}

public class UpdateNotifyThird
{   
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ThirdCode")]
    [Exists("", "NotifyThird", "ThirdCode", ErrorMessage = Constants.ErrorCode.NotFound + "ThirdCode")]
    public string ThirdCode { get; set; }
    
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "ThirdName")]
    public string? ThirdName { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "URL_MAC")]
    public string? URL_MAC { get; set; }
}

public class DeleteNotifyThird
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ThirdCode")]
    [Exists("", "NotifyThird", "ThirdCode", ErrorMessage = Constants.ErrorCode.NotFound + "ThirdCode")]
    [HasReference("", "EventNotifyThird", "ThirdCode", ErrorMessage = Constants.ErrorCode.Reference + "ThirdCode")]
    public string ThirdCode { get; set; }
}