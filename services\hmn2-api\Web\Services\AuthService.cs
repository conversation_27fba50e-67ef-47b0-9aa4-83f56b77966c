﻿using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using System.Security.Cryptography;
using Web.Repository.Models.Entities;
using System.Text;
using Web.Models.Controller.Auth;
using Web.Services.Interfaces;
using Web.Repository.Interface;
using System.Data.SqlTypes;

namespace Web.Services;
/// <summary>
/// 提供認證相關服務
/// </summary>
/// <remarks>
/// 此Service 會在產生Cookie 前被呼叫，所以不繼承BaseService
/// </remarks>
/// <param name="dataAccessService"></param>
/// <param name="preferenceService"></param>
public class AuthService(IDataAccessService dataAccessService, IPreferenceService preferenceService) : IAuthService
{
    private readonly IDataAccessService _dataAccessService = dataAccessService;
    private readonly IPreferenceService _preferenceService = preferenceService;

    public async Task<(bool loginResult, string? appCode)> Login(string userAccount, string userPassword)
    {
        bool result = false;

        if (userPassword == null)
        {
            return (false, null);
        }

        var user = await _dataAccessService.Fetch<UserDatum>(x => x.UserAccount == userAccount && x.Enable == true).FirstOrDefaultAsync();

        if (user == null)
        {
            return (false, null);
        }

        var validResult = EncryptPwd(userPassword) == user.UserPassword;

        return (validResult, user.AppCode);
    }

    public string EncryptPwd(string userPassword)
    {
        SHA1 sha1 = new SHA1CryptoServiceProvider();//建立SHA1物件
        byte[] bytes_in = Encoding.UTF8.GetBytes(userPassword);//將待加密字串轉為byte型別
        byte[] bytes_out = sha1.ComputeHash(bytes_in);//Hash運算
        sha1.Dispose();//釋放當前例項使用的所有資源
        String result = BitConverter.ToString(bytes_out);//將運算結果轉為string型別
        result = result.Replace("-", "").ToUpper();//替換並轉為大寫
        return result;
    }

    public async Task<List<Claim>> GenerateClaimList(string appCode, string userAccount)
    {
        ClaimData? claimData = null;
        UserDatum? user = null;

        // 取得使用者所屬單位是否為資材管理單位
        var isManagedDept = false;

        // 使用者偏好設定
        List<UserClientPara> userClientParaList;

        // 使用者有權限的單位（DeptCode以逗號分隔）
        string concatenatedDeptCodes = "";

        // 取得使用者資訊 TODO:: 這裡要跟Ann 確認是否考慮到多院區/AppCode的情況
        user = await _dataAccessService.Fetch<UserDatum>(u => u.UserAccount == userAccount).FirstOrDefaultAsync();

        /**
         * ******** 與Ann討論後，定義IsAdmin= A 為管理所有院區的單位；代表目前HMN2 沒有可以設定管理所屬院區的所有單位功能
         */
        if (user.IsSuperAdmin == true)
        {
            user.IsAdmin = "A";
            user.AreaCode = _dataAccessService.Fetch<Area>(x => x.AppCode == appCode).Select(x => x.AreaCode).FirstOrDefault();
            user.DeptCode = _dataAccessService.Fetch<Department>(x => x.AppCode == appCode && x.AreaCode == user.AreaCode).Select(x => x.DeptCode).FirstOrDefault() ?? "";
        }

        // 取得使用者有權限的單位（DeptCode以逗號分隔）
        if (user?.IsAdmin == "A")
        {
            concatenatedDeptCodes = string.Join(",", await _dataAccessService.Fetch<Department>(x => x.AppCode == appCode).Select(x => x.DeptCode).ToListAsync());
        }
        else if (user?.IsAdmin == "Y")
        {
            concatenatedDeptCodes = string.Join(",", await _dataAccessService.Fetch<UserDeptMonPerm>(x => x.AppCode == appCode && x.AreaCode == user.AreaCode && x.UserAccount == userAccount).Select(x => x.UsageDeptCode).ToListAsync());
        }
        else
        {
            concatenatedDeptCodes = user?.DeptCode ?? "";
        }

        // 取得使用者偏好設定
        userClientParaList = await _preferenceService.FetchUserClientPara(appCode, user.AreaCode, userAccount);

        // 取得使用者所屬單位是否為資材管理單位
        isManagedDept = await _dataAccessService.Fetch<Department>(e => e.AppCode == appCode && e.AreaCode == user.AreaCode && e.DeptCode == user.DeptCode).Select(x => x.IsManagedDept).FirstOrDefaultAsync() ?? false;

        claimData = new()
        {
            EnableAlarmVoice = userClientParaList.Where(x => x.ParaCode == "EnableAlarmVoice").Select(x => x.ParaValue).FirstOrDefault() ?? "Y",
            AlarmInterval = userClientParaList.Where(x => x.ParaCode == "AlarmInterval").Select(x => x.ParaValue).FirstOrDefault() == null ? 0 : Int32.Parse(userClientParaList.Where(x => x.ParaCode == "AlarmInterval").Select(x => x.ParaValue).FirstOrDefault()),
            BatteryLevel = userClientParaList.Where(x => x.ParaCode == "BatteryLevel").Select(x => x.ParaValue).FirstOrDefault() == null ? 0 : Int32.Parse(userClientParaList.Where(x => x.ParaCode == "BatteryLevel").Select(x => x.ParaValue).FirstOrDefault()),
            PositionInterval = userClientParaList.Where(x => x.ParaCode == "PositionInterval").Select(x => x.ParaValue).FirstOrDefault() == null ? 0 : Int32.Parse(userClientParaList.Where(x => x.ParaCode == "PositionInterval").Select(x => x.ParaValue).FirstOrDefault()),
            EventFontSize = userClientParaList.Where(x => x.ParaCode == "EventFontSize").Select(x => x.ParaValue).FirstOrDefault() ?? "16",
        };

        string isSuperAdmin = "N";

        if (user.IsSuperAdmin != null)
        {
            isSuperAdmin = user.IsSuperAdmin == true ? "Y" : "N";
        }

        var claims = new List<Claim> {
            new("Id", user.Id.ToString()),
            new("UserAccount", user.UserAccount),
            new("UserName", user.UserName ?? ""),
            new("AppCode", appCode),
            new("AreaCode", user.AreaCode??""),
            new("DeptCode", user.DeptCode??""),
            new("RoleCode", user.RoleCode??""),
            new("IsAdmin", user.IsAdmin?.ToString()??""),
            new("IsSuperAdmin", isSuperAdmin),
            new("IsManagedDept", isManagedDept ? "Y" : "N"),
            // 由于 Claim 的构造函数不接受数组，所以我们需要转换为字符串
            // 假设 DeptCodeList 是 string[]，可以使用 String.Join 转换成逗号分隔的字符串
            new("DeptCodeList", concatenatedDeptCodes),
            new("EnableAlarmVoice", claimData.EnableAlarmVoice),
            new("AlarmInterval", claimData.AlarmInterval.ToString()),
            new("BatteryLevel", claimData.BatteryLevel.ToString()),
            new("PositionInterval", claimData.PositionInterval.ToString()),
            new("EventFontSize", claimData.EventFontSize)
        };
        return claims;
    }

    public void CreatePersistentUserClaimsIdentity(List<Claim> claims, out ClaimsIdentity claimsIdentity, out AuthenticationProperties authProperties)
    {
        claimsIdentity = new ClaimsIdentity(claims, CookieAuthenticationDefaults.AuthenticationScheme);

        authProperties = new AuthenticationProperties
        {
            // AllowRefresh: 當設置為 true 時，允許身份認證票據（cookie）在它快要過期時自動刷新。這意味著，如果用戶在票據快過期時仍然活躍，系統可以給予用戶一個新的票據，從而避免讓用戶重新登入。
            //AllowRefresh = true,// 在Program.cs中已設置了option.ExpireTimeSpan = TimeSpan.FromMinutes(52560000);//365天*1440*100年
            // ExpiresUtc: 設置身份認證票據的過期時間。在這個例子中，票據被設置為自當前 UTC 時間起的 30 分鐘後過期。過了這個時間，用戶將需要重新認證。
            //ExpiresUtc = DateTimeOffset.UtcNow.AddMinutes(30),
            // IsPersistent: 設置票據是否為「持久化的」，也就是說，即使用戶關閉了瀏覽器，票據仍然存在，用戶下次打開瀏覽器時，仍然可以使用這個票據進行登入。
            IsPersistent = true,
        };
    }

    public async Task<string> GenerateUserToken(string appCode, string userAccount)
    {
        UserDatum user = _dataAccessService.Fetch<UserDatum>(x => x.AppCode.Equals(appCode) && x.UserAccount.Equals(userAccount.ToUpper()) && x.Enable == true).FirstOrDefault();

        if (user == null)
            return string.Empty;

        DateTime now = DateTime.Now;
        String token = String.Join("", Guid.NewGuid().ToString().Split("-"));
        UserToken userToken = _dataAccessService.Fetch<UserToken>(x => x.UserDataId == user.Id).FirstOrDefault();

        if (userToken == null)
        {
            userToken = new UserToken
            {
                UserDataId = user.Id,
                Token = token,
                TokenExpiryDate = now.AddDays(1),
                TokenDeleted = false,
                CreateUserAccount = this.GetType().Name,
                ModifyUserAccount = this.GetType().Name,
                CreateDate = now,
                ModifyDate = now
            };
            await _dataAccessService.CreateAsync(userToken);
        }
        else
        {
            if (DateTime.Compare((DateTime)userToken.TokenExpiryDate, now) < 0)
                userToken.Token = token;
            else
                token = userToken.Token;
            userToken.TokenExpiryDate = now.AddDays(1);
            userToken.TokenDeleted = false;
            userToken.ModifyUserAccount = this.GetType().Name;
            userToken.ModifyDate = now;
            await _dataAccessService.UpdateAsync(userToken);
        }

        return token;
    }
}
