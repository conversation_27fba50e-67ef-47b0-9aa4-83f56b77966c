﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class SysCode
{
    /// <summary>
    /// TaskAction:事件狀態/EquipmentStatus:設備狀態
    /// </summary>
    public string CodeType { get; set; } = null!;

    public string Code { get; set; } = null!;

    /// <summary>
    /// String,<PERSON><PERSON>an,Int,Float
    /// </summary>
    public string ValueType { get; set; } = null!;

    public string Name { get; set; } = null!;

    public string? StringId { get; set; }

    /// <summary>
    /// EquipmentStatus 時此欄位表示顏色
    /// </summary>
    public string? Extend { get; set; }

    public bool? Enable { get; set; }

    public int? Sort { get; set; }
}
