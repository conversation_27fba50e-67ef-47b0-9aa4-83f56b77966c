﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class NotifyHeader
{
    public int Id { get; set; }

    public string AppCode { get; set; } = null!;

    public string AreaCode { get; set; } = null!;

    public string NotifyCode { get; set; } = null!;

    public bool Enable { get; set; }

    public string NotifyName { get; set; } = null!;

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }
}
