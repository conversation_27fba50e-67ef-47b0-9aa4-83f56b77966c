﻿@page
@{
    ViewData["Title"] = "DB Info";
}
<h1>@ViewData["Title"]</h1>

<p>DB資訊</p>
<div id="app">
    <div class="row">
        <div class="col-12">
            <button class="btn btn-primary download" v-on:click="store.onPageLoaded()">Reload</button>
        </div>
    </div>
    <table class="table table-striped">
        <thead>
            <tr>
                <th>Table Name</th>
                <th>Description</th>
                <th>Create Script</th>
            </tr>
        </thead>
        <tbody>
            <tr v-for="table in store.allTableList">
                <td>{{ table.TableName }}</td>
                <td>{{ table.TableComment }}</td>
                <td v-html="table.CreateTableScript"></td>
            </tr>
        </tbody>
    </table>
</div>

<script>
    const { createApp, onMounted, ref, reactive, computed } = Vue;
    const { createPinia, defineStore } = Pinia;

    const useStore = defineStore({
        id: 'main',
        state: () => ({
            message: '',
            allTableList: []
        }),
        actions: {
            async onPageLoaded() {
                let returnData = await swd.get("/Db/tables/list");
                let tableList = swd.resolve_ajax_pack(returnData, "tableList");

                this.allTableList = tableList;
            },
            async onMounted() {
                await this.onPageLoaded();
            },
            async onDownload() {
                swd.downloadFile("/Db/tables/download", "db_schema.xlsx");
            }
        }
    });

    const app = createApp({
        setup() {
            const store = useStore();

            onMounted(async () => {
                await store.onMounted();
            });
            
            return { store };
        }
    });

    app.use(createPinia());
    app.mount('#app');
</script>