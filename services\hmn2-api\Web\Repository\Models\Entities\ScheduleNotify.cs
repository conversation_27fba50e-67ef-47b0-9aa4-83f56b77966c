﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class ScheduleNotify
{
    public int ScheduleNotifyId { get; set; }

    public string AppCode { get; set; } = null!;

    public string AreaCode { get; set; } = null!;

    public int ScheduleJobId { get; set; }

    /// <summary>
    /// Line,SMS,EMail 對應ContactData欄位
    /// </summary>
    public string NotifyType { get; set; } = null!;

    /// <summary>
    /// 如果資料由ContactData來，記與0,1,2與ContactData一致，如果直接輸入Email則記3
    /// </summary>
    public string? Source { get; set; }

    public string? ContactCode { get; set; }

    /// <summary>
    /// 當Source為3，則此欄必填
    /// </summary>
    public string? Email { get; set; }

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }

    public virtual ScheduleJob ScheduleJob { get; set; } = null!;
}
