﻿
using Web.Models.Service.Fusion;

namespace Web.Models.Service.Field;

public class OutGetStationList
{
    public string AreaCode { get; set; }
    public string BuildingCode { get; set; }
    public string BuildingName { get; set; }
    public List<ResourceConfiguration> Configurations { get; set; }
    public string DeviceName { get; set; }
    public double DiffPositionX { get; set; }
    public double DiffPositionY { get; set; }
    public string Enable {  get; set; }
    public string Ip { get; set; }
    public string IsConnected { get; set; }
    public string LocCode { get; set; }
    public string LocName { get; set; }
    public string ManageDepartCode { get; set; }
    public string ManageDepartName { get; set; }
    public string ObjectCode { get; set; }
    public string ObjectName { get; set; }
    public string Pid { get; set; }
    public string PlaneCode { get; set; }
    public string PlaneName { get; set; }
    public double PlanePositionX { get; set; }
    public double PlanePositionY { get; set; }
    public string PlaneMapPath { get; set; }
    public double PlaneMapWidth { get; set; }
    public double PlaneMapHeight { get; set; }
    public bool Registered { get; set; }
    public string SceneMode { get; set; }
    public string SID { get; set; }
    public string StationName { get; set; }
    public string StationType { get; set; }
    public string SpaceType { get; set; }
    public string SystemVersion { get; set; }
    public string UsageDepartCode { get; set; }
    public string UsageDepartName { get; set; }
    public DateTime? ModifyDate { get; set; }
    public List<string> SectorCodeList { get; set; }
    public List<string> FenceCodeList { get; set; }
}

public class InGetStationList
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    public string? AreaCode { get; set; }
    public string? SID { get; set; }
    public string? StationName { get; set; }
    public string? StationType { get; set; }
    public string? SpaceType { get; set; }
    public string? Enable { get; set; }
    public string? IsConnected { get; set; }
    public string? PlaneCode { get; set; }
    public string? LocCode { get; set; }
    public string? BuildingCode { get; set; }
}