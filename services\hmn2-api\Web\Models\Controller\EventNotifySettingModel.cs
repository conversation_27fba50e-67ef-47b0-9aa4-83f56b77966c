using System.ComponentModel.DataAnnotations;
using Web.Validation;
using Web.Constant;
using System.Text.Json.Serialization;

namespace Web.Models.Controller.EventNotifySetting;

public class RetrieveEventNotifySetting
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    public string AreaCode { get; set; }
    public string ServiceCode { get; set; }
    public string SddResource { get; set; }
    public string Description { get; set; }
    public string BuildingCode { get; set; }
    public string PlaneCode { get; set; }
    public string DeptCode { get; set; }
    public string ObjectType { get; set; }
    public string GroupCode { get; set; }
    public string Enable { get; set; }
    public string EnableSchedule { get; set; }
    public string NotifyCode { get; set; }
    public string ThirdCode { get; set; }
    public string DeptName { get; set; }
    public string BuildingName { get; set; }
    public string PlaneName { get; set; }
    public string ObjectTypeDesc { get; set; }
    public string GroupName { get; set; }
    public string ThirdName { get; set; }
    public string NotifyName { get; set; }
}

public class EventNotifyScheduleItem
{
    public string Weekly { get; set; }

    public string StartTime { get; set; }

    public string EndTime { get; set; }
}

public class EventNotifyContactItem
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "NotifyType")]
    [RegularExpression(@"^(Line|Email|SMS)$", ErrorMessage = Constants.ErrorCode.Pattern + "NotifyType")]
    public string NotifyType { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Source")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "Source")]
    [Range(0, 2, ErrorMessage = Constants.ErrorCode.Pattern + "Source")]
    public string Source { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ContactCode")]
    [Exists("", "ContactDatum", "ContactCode", ErrorMessage = Constants.ErrorCode.NotFound + "ContactCode")]
    public string ContactCode { get; set; }
}

public class EventNotifyThirdItem
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "NotifyType")]
    [RegularExpression(@"^(ENS|Display)$", ErrorMessage = Constants.ErrorCode.Pattern + "NotifyType")]
    public string NotifyType { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ThirdCode")]
    [Exists("", "NotifyThird", "ThirdCode", ErrorMessage = Constants.ErrorCode.NotFound + "ThirdCode")]
    public string ThirdCode { get; set; }
}

public class EventNotifyHeaderItem
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "NotifyCode")]
    [Exists("", "NotifyHeader", "NotifyCode", ErrorMessage = Constants.ErrorCode.NotFound + "NotifyCode")]
    public string NotifyCode { get; set; }
}

public class CreateEventNotifySetting
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ServiceCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "ServiceCode")]
    [ExistsServiceCode("", ErrorMessage = Constants.ErrorCode.NotFound + "ServiceCode")]
    public string ServiceCode { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "SddResource")]
    public string SddResource { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Description")]
    [StringLength(100, ErrorMessage = Constants.ErrorCode.Length + "Description")]
    public string Description { get; set; }

    [Exists("", "Building", "BuildingCode", ErrorMessage = Constants.ErrorCode.NotFound + "BuildingCode")]
    public string BuildingCode { get; set; }

    [Exists("", "Plane", "PlaneCode", ErrorMessage = Constants.ErrorCode.NotFound + "PlaneCode")]
    public string PlaneCode { get; set; }

    [Exists("AreaCode", "Department", "DeptCode", ErrorMessage = Constants.ErrorCode.NotFound + "DeptCode")]
    public string DeptCode { get; set; }

    [Exists("AreaCode", "ObjectType", "ObjectTypeCode", "Enable:true", ErrorMessage = Constants.ErrorCode.NotFound + "ObjectType")]
    public string ObjectType { get; set; }

    [Exists("AreaCode", "ObjectGroup", "GroupCode", ErrorMessage = Constants.ErrorCode.NotFound + "GroupCode")]
    public string GroupCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Enable")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "EnableSchedule")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "EnableSchedule")]
    [CheckWeeklyStartTimeEndTimeList("ScheduleList", ErrorMessage = Constants.ErrorCode.Invalid + "ScheduleList")]
    public string EnableSchedule { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PositionNotify")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "PositionNotify")]
    public string PositionNotify { get; set; }

    [RequiredWhenEquals("PositionNotify", "Y", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "TraceTime")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "TraceTime")]
    [Range(1, 60, ErrorMessage = Constants.ErrorCode.Pattern + "TraceTime")]
    public int? TraceTime { get; set; }

    [StringLength(500, ErrorMessage = Constants.ErrorCode.Length + "ENSMessage")]
    public string ENSMessage { get; set; }

    [StringLength(500, ErrorMessage = Constants.ErrorCode.Length + "DisplayMessage1")]
    public string DisplayMessage1 { get; set; }

    [StringLength(500, ErrorMessage = Constants.ErrorCode.Length + "DisplayMessage2")]
    public string DisplayMessage2 { get; set; }

    [StringLength(100, ErrorMessage = Constants.ErrorCode.Length + "EmailSubject")]
    public string EmailSubject { get; set; }

    [StringLength(1000, ErrorMessage = Constants.ErrorCode.Length + "EmailMessage")]
    public string EmailMessage { get; set; }

    [StringLength(500, ErrorMessage = Constants.ErrorCode.Length + "NotifyMessage")]
    public string NotifyMessage { get; set; }

    [StringLength(500, ErrorMessage = Constants.ErrorCode.Length + "SMSMessage")]
    public string SMSMessage { get; set; }

    public List<EventNotifyScheduleItem> ScheduleList { get; set; }
    public List<EventNotifyContactItem> ContactList { get; set; }
    public List<EventNotifyThirdItem> ThirdList { get; set; }
    public List<EventNotifyHeaderItem> HeaderList { get; set; }
}

public class UpdateEventNotifySetting
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Id")]
    [Exists("", "EventNotifySetting", "Id", ErrorMessage = Constants.ErrorCode.NotFound + "Id")]
    public int Id { get; set; }

    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [StringLength(100, ErrorMessage = Constants.ErrorCode.Length + "Description")]
    public string Description { get; set; }

    [Exists("", "Building", "BuildingCode", ErrorMessage = Constants.ErrorCode.NotFound + "BuildingCode")]
    public string BuildingCode { get; set; }

    [Exists("", "Plane", "PlaneCode", ErrorMessage = Constants.ErrorCode.NotFound + "PlaneCode")]
    public string PlaneCode { get; set; }

    [Exists("AreaCode", "Department", "DeptCode", ErrorMessage = Constants.ErrorCode.NotFound + "DeptCode")]
    public string DeptCode { get; set; }

    [Exists("AreaCode", "ObjectType", "ObjectTypeCode", "Enable:true", ErrorMessage = Constants.ErrorCode.NotFound + "ObjectType")]
    public string ObjectType { get; set; }

    [Exists("AreaCode", "ObjectGroup", "GroupCode", ErrorMessage = Constants.ErrorCode.NotFound + "GroupCode")]
    public string GroupCode { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "EnableSchedule")]
    [CheckWeeklyStartTimeEndTimeList("ScheduleList", ErrorMessage = Constants.ErrorCode.Invalid + "ScheduleList")]
    public string EnableSchedule { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "PositionNotify")]
    public string PositionNotify { get; set; }

    [RequiredWhenEquals("PositionNotify", "Y", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "TraceTime")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "TraceTime")]
    [Range(1, 60, ErrorMessage = Constants.ErrorCode.Pattern + "TraceTime")]
    public int? TraceTime { get; set; }

    [StringLength(500, ErrorMessage = Constants.ErrorCode.Length + "ENSMessage")]
    public string ENSMessage { get; set; }

    [StringLength(500, ErrorMessage = Constants.ErrorCode.Length + "DisplayMessage1")]
    public string DisplayMessage1 { get; set; }

    [StringLength(500, ErrorMessage = Constants.ErrorCode.Length + "DisplayMessage2")]
    public string DisplayMessage2 { get; set; }

    [StringLength(100, ErrorMessage = Constants.ErrorCode.Length + "EmailSubject")]
    public string EmailSubject { get; set; }

    [StringLength(1000, ErrorMessage = Constants.ErrorCode.Length + "EmailMessage")]
    public string EmailMessage { get; set; }

    [StringLength(500, ErrorMessage = Constants.ErrorCode.Length + "NotifyMessage")]
    public string NotifyMessage { get; set; }

    [StringLength(500, ErrorMessage = Constants.ErrorCode.Length + "SMSMessage")]
    public string SMSMessage { get; set; }

    public List<EventNotifyScheduleItem> ScheduleList { get; set; }
    public List<EventNotifyContactItem> ContactList { get; set; }
    public List<EventNotifyThirdItem> ThirdList { get; set; }
    public List<EventNotifyHeaderItem> HeaderList { get; set; }
}

public class DeleteEventNotifySetting
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Id")]
    [Exists("", "EventNotifySetting", "Id", ErrorMessage = Constants.ErrorCode.NotFound + "Id")]
    public int Id { get; set; }
}