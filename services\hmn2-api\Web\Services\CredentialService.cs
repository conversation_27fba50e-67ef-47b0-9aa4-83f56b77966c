﻿using System.Data;
using Microsoft.EntityFrameworkCore;
using Web.Models.Service;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;

namespace Web.Services;

public class CredentialService(IHttpContextAccessor httpContextAccessor, IUtilityService utilityService, IDataAccessService dataAccessService) : ICredentialService
{
    private readonly HttpContext _httpContext = httpContextAccessor?.HttpContext;
    private readonly IUtilityService _utilityService = utilityService;
    private readonly IDataAccessService _dataAccessService = dataAccessService;

    public UserResult _userResult = new();

    public UserResult UserResult
    {
        get
        {
            if (_userResult.Account == null || _userResult.Account == "")
            {
                _userResult.Account = _httpContext?.User.Claims.FirstOrDefault(x => x.Type == "UserAccount")?.Value ?? "";
                _userResult.UserName = _httpContext?.User.Claims.FirstOrDefault(x => x.Type == "UserName")?.Value ?? "";
                _userResult.AppCode = _httpContext?.User.Claims.FirstOrDefault(x => x.Type == "AppCode")?.Value ?? "";
                _userResult.DeptCode = _httpContext?.User.Claims.FirstOrDefault(x => x.Type == "DeptCode")?.Value ?? "";
                _userResult.EnableAlarmVoice = _httpContext?.User.Claims.FirstOrDefault(x => x.Type == "EnableAlarmVoice")?.Value == "Y";
                _userResult.AlarmInterval = _httpContext?.User.Claims.FirstOrDefault(x => x.Type == "AlarmInterval")?.Value == null ? 0 : Int32.Parse(_httpContext.User.Claims.FirstOrDefault(x => x.Type == "AlarmInterval")?.Value);
                _userResult.FontSize = _httpContext?.User.Claims.FirstOrDefault(x => x.Type == "EventFontSize")?.Value ?? "";
                _userResult.BatteryLevel = _httpContext?.User.Claims.FirstOrDefault(x => x.Type == "BatteryLevel")?.Value == null ? 0 : Int32.Parse(_httpContext.User.Claims.FirstOrDefault(x => x.Type == "BatteryLevel")?.Value);

                string managedDeptFlag = _httpContext?.User.Claims.FirstOrDefault(x => x.Type == "IsManagedDept")?.Value ?? "";
                //如果 managedDeptFlag 為 null，就會回傳 false，否則就會比較 managedDeptFlag 是否等於 "Y"
                _userResult.IsManagedDept = managedDeptFlag?.Equals("Y") ?? false;

                //DeptCodeList 在每一支Service constructor 都會再重新讀取一次，以確定登入者權限都是最新的
                string? deptCodeData = _httpContext?.User.Claims.FirstOrDefault(x => x.Type == "DeptCodeList")?.Value;
                _userResult.DeptCodeList = deptCodeData == null ? "".Split(",") : deptCodeData.Split(',').ToArray<string>();

                string? areaCode = _httpContext?.User.Claims.FirstOrDefault(x => x.Type == "AreaCode")?.Value;
                _userResult.AreaCode = areaCode ?? "";

                //取得裝置所在位置時會用到
                string positionInterval = _httpContext.User.Claims.FirstOrDefault(x => x.Type == "PositionInterval")?.Value ?? "";
                _userResult.PositionInterval = string.IsNullOrEmpty(positionInterval) ? 0 : Int32.Parse(positionInterval);

                //AreaCodeList 在每一支Service constructor 都會再重新讀取一次，以確定登入者權限都是最新的
                _userResult.AreaCodeList = _dataAccessService.Fetch<Department>(x => x.AppCode == _userResult.AppCode && _userResult.DeptCodeList.Contains(x.DeptCode)).Select(x => x.AreaCode).Distinct().ToList();

                //取得用戶端的IP位址
                _userResult.ClientIP = _utilityService.GetClientIp(_httpContext);
                _userResult.IsAdmin = _httpContext?.User?.Claims.FirstOrDefault(x => x.Type == "IsAdmin")?.Value ?? "";
                _userResult.IsViewAllArea = _userResult.IsAdmin == "A";
                _userResult.IsSuperAdmin = _httpContext?.User?.Claims.FirstOrDefault(x => x.Type == "IsSuperAdmin")?.Value == "Y";

                //Ann 要求如果是超級管理員，則預設為管理所有院區的單位
                if (_userResult.IsSuperAdmin)
                {
                    _userResult.IsViewAllArea = true;
                    _userResult.IsAdmin = "A";
                }

                //Ann 要求如果是管理所有單位，則預設為管理所有院區的單位（不論這個院區是否有單位）@20240624 by GM
                if (_userResult.IsViewAllArea)
                {
                    _userResult.AreaCodeList = _dataAccessService.Fetch<Area>(x => x.AppCode == _userResult.AppCode).Select(x => x.AreaCode).ToList();
                    _userResult.DeptCodeList = _dataAccessService.Fetch<Department>(x => x.AppCode == _userResult.AppCode).Select(x => x.DeptCode).Distinct().ToArray();
                }
            }

            return _userResult;
        }
    }

    #region private GetUseageDeptListCrossArea 取得登入者有權限檢視的使用單位列表（不分院區）
    /// <summary>
    /// 取得登入者有權限檢視的使用單位列表（不分院區）
    /// </summary>
    /// <returns></returns>
    private List<VwUserDeptMonInfo> GetUseageDeptListCrossArea()
    {
        //由EF取得USER有讀取權限的部門資料列表
        List<VwUserDeptMonInfo> userDeptMonPermList;

        if (UserResult.IsViewAllArea)
        {
            var departmentList = _dataAccessService.Fetch<Department>(e => e.AppCode == UserResult.AppCode);

            userDeptMonPermList =
            [
                .. departmentList
                                .Select(e => new VwUserDeptMonInfo()
                                {

                                    AppCode = e.AppCode,
                                    AreaCode = e.AreaCode,
                                    UserAccount = UserResult.Account,
                                    SectorCode = e.SectorCode,
                                    DeptCode = e.DeptCode,
                                    CustomDeptCode = e.CustomDeptCode,
                                    DeptName = e.DeptName
                                }),
            ];

        }
        else
        {
            userDeptMonPermList =
            [
                .. _dataAccessService.Fetch<VwUserDeptMonInfo>(e => e.AppCode == UserResult.AppCode &&
                                                                                    e.UserAccount == UserResult.Account &&
                                                                                    e.IsUsageDept == true)
                                                                        .OrderBy(e => e.DeptCode),
            ];
        }

        //如果自已部門不存在UserDeptMomPerm table中，則將之寫入userDeptMonPermList 以供前端顯示
        if (!userDeptMonPermList.Any(x => x.AppCode == UserResult.AppCode
                                          && x.AreaCode == UserResult.AreaCode
                                          && x.DeptCode == UserResult.DeptCode))
        {
            var department = _dataAccessService.Fetch<Department>(e => e.AppCode == UserResult.AppCode && e.AreaCode == UserResult.AreaCode && e.DeptCode == UserResult.DeptCode).FirstOrDefault();

            //自己所屬部門為使用單位
            if (department != null && department.IsUsageDept == true)
            {
                userDeptMonPermList.Add(new VwUserDeptMonInfo()
                {
                    AppCode = UserResult.AppCode,
                    AreaCode = UserResult.AreaCode,
                    UserAccount = UserResult.Account,
                    SectorCode = department.SectorCode,
                    DeptCode = UserResult.DeptCode,
                    CustomDeptCode = department.CustomDeptCode,
                    DeptName = department.DeptName
                });
            }
        }

        return userDeptMonPermList;
    }
    #endregion

    #region private FetchAreaByDept 取得所有deptList中部門的所屬院區
    /// <summary>
    /// 取得所有deptList中部門的所屬院區
    /// </summary>
    /// <param name="appCode"></param>
    /// <param name="deptList"></param>
    /// <returns></returns>
    private List<string> FetchAreaByDept(string appCode, IEnumerable<string> deptList)
    {
        return
        [
            .. _dataAccessService.Fetch<Department>(e => e.AppCode == appCode && deptList.Contains(e.DeptCode))
                                            .Select(e => e.AreaCode).Distinct(),
        ];
    }
    #endregion
}
