﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class EventNotifySchedule
{
    public int Id { get; set; }

    public string AppCode { get; set; } = null!;

    public string AreaCode { get; set; } = null!;

    public int EventNotifySettingId { get; set; }

    public string Weekly { get; set; } = null!;

    public DateTime StartTime { get; set; }

    public DateTime EndTime { get; set; }

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }
}
