﻿using Microsoft.EntityFrameworkCore;
using Web.Models.Service.Field;
using Web.Repository.Models.Entities;
using Web.Models.Service;
using Web.Helper;
using Web.Models.Service.Fusion;
using Web.Controller;
using Web.Services.Interfaces;
using System.Reflection.Emit;
using System.Linq.Expressions;

namespace Web.Services;

public class FieldService(IConfiguration configuration,
    IStationService stationService,
    ICredentialService credentialService,
    IDataAccessService dataAccessService) : IFieldService
{
    private readonly IConfiguration _configuration = configuration;
    private readonly IStationService _stationService = stationService;
    private readonly IDataAccessService _dataAccessService = dataAccessService;

    private readonly UserResult _user = credentialService.UserResult;

    public async Task<List<OutGetStationList>> GetStationList(InGetStationList param)
    {
        var fusionStationList = await _stationService.GetStationList();

        var buildingList = await _dataAccessService.Fetch<Building>(b => b.AppCode == _user.AppCode).ToListAsync();
        var planeList = await _dataAccessService.Fetch<Plane>(p => p.AppCode == _user.AppCode).ToListAsync();
        var locationList = await _dataAccessService.Fetch<Location>(e => e.AppCode == _user.AppCode).ToListAsync();
        var sectorStationList = await _dataAccessService.Fetch<SectorStation>(e => e.AppCode == _user.AppCode).ToListAsync();
        var fenceStationList = await _dataAccessService.Fetch<FenceStation>(e => e.AppCode == _user.AppCode).ToListAsync();
        var deptList = await _dataAccessService.Fetch<Department>(e => e.AppCode == _user.AppCode).ToListAsync();
        var objectList = await _dataAccessService.Fetch<ObjectDatum>(e => e.AppCode == _user.AppCode).ToListAsync();
        var objectDeviceList = await _dataAccessService.Fetch<ObjectDevice>(e => e.AppCode == _user.AppCode).ToListAsync();
        var deviceList = await _dataAccessService.Fetch<Device>(e => e.AppCode == _user.AppCode && !string.IsNullOrWhiteSpace(e.StationSid)).ToListAsync();
        var stationList = await _dataAccessService.Fetch<Station>(e => e.AppCode == _user.AppCode).Select(e => new Station
        {
            Id = e.Id,
            AppCode = e.AppCode ?? _user.AppCode,
            AreaCode = e.AreaCode ?? "",
            SID = e.SID ?? "",
            Enable = e.Enable ?? false,
            StationName = e.StationName ?? "",
            StationMac = e.StationMac ?? "",
            StationType = e.StationType ?? "",
            SpaceType = e.SpaceType ?? "",
            StationIP = e.StationIP ?? "",
            PlaneCode = e.PlaneCode ?? "",
            RegionCode = e.RegionCode ?? "",
            AxisX = e.AxisX,
            AxisY = e.AxisY,
            DiffPositionX = e.DiffPositionX,
            DiffPositionY = e.DiffPositionY,
            CreateUserAccount = e.CreateUserAccount,
            CreateDate = e.CreateDate,
            ModifyUserAccount = e.ModifyUserAccount,
            ModifyDate = e.ModifyDate
        }).ToListAsync();

        //可以考慮將IQueryable 先進行Join，再進行Where，最後再Join FusionStation
        var stations = fusionStationList
            .Join(stationList,
                f => f.sid,
                s => s.SID,
                (fusionStation, station) => new { fusionStation, station })
            .Select(x =>
            {
                var planeCode = x.fusionStation.plane?.code ?? "";
                var plane = planeList.FirstOrDefault(e => e.PlaneCode == planeCode);

                var location = locationList.FirstOrDefault(e => e.LocCode == x.station.RegionCode);

                var buildingCode = plane?.BuildingCode ?? "";
                var building = buildingList.FirstOrDefault(e => e.BuildingCode == buildingCode);

                var sectorCodeList = sectorStationList.Where(ss => ss.Stationsid == x.station.SID).Select(ss => ss.SectorCode).ToList();
                var fenceCodeList = fenceStationList.Where(fs => fs.Stationsid == x.station.SID).Select(fs => fs.FenceCode).ToList();

                var device = deviceList.FirstOrDefault(e => e.StationSid == x.station.SID);

                var objectDevice = objectDeviceList.FirstOrDefault(e => e.Pid == device?.Pid);
                var obj = objectList.FirstOrDefault(e => e.ObjectCode == objectDevice?.ObjectCode);

                var manageDeptCode = device?.ManageDepartCode ?? "";
                var manageDept = deptList.FirstOrDefault(e => e.DeptCode == manageDeptCode);

                var usageDeptCode = device?.UsageDepartCode ?? "";
                var usageDept = deptList.FirstOrDefault(e => e.DeptCode == usageDeptCode);

                return new { x.fusionStation, x.station, plane, location, building, sectorCodeList, fenceCodeList, device, obj, manageDept, usageDept };
            })
            .Where(x =>
                (string.IsNullOrWhiteSpace(param.SID) || x.station.SID.ToLower().Contains(param.SID.ToLower())) &&
                (string.IsNullOrWhiteSpace(param.AreaCode) || x.station.AreaCode.ToUpper() == param.AreaCode) &&
                (string.IsNullOrWhiteSpace(param.StationName) || x.station.StationName.ToLower().Contains(param.StationName.ToLower())) &&
                (string.IsNullOrWhiteSpace(param.Enable) || x.station.Enable == (param.Enable == "Y")) &&
                (string.IsNullOrWhiteSpace(param.IsConnected) || x.fusionStation.connection.isConnected == (param.IsConnected == "Y")) &&
                (string.IsNullOrWhiteSpace(param.PlaneCode) || x.station.PlaneCode == param.PlaneCode) &&
                (string.IsNullOrWhiteSpace(param.LocCode) || x.station.RegionCode == param.LocCode) &&
                (string.IsNullOrWhiteSpace(param.SpaceType) || x.station.SpaceType == param.SpaceType) &&
                (string.IsNullOrWhiteSpace(param.StationType) || param.StationType.Split(',').Contains(x.station.StationType)) // 當 param.StationType 是逗點分隔或是只有一種都可適用
            )
            .Select(x => new OutGetStationList
            {
                AreaCode = x.station.AreaCode,
                BuildingCode = x.plane?.BuildingCode ?? "",
                BuildingName = x.building?.BuildingName ?? "",
                Configurations = x.fusionStation.configurations ?? [],
                DeviceName = x.device?.Name ?? "",
                DiffPositionX = x.station.DiffPositionX ?? 0,
                DiffPositionY = x.station.DiffPositionY ?? 0,
                Enable = (x.station.Enable ?? false) ? "Y" : "N",
                Ip = x.fusionStation.connection?.ip ?? "",
                IsConnected = x.fusionStation.connection.isConnected ? "Y" : "N",
                LocCode = x.station.RegionCode ?? "",
                LocName = x.location?.LocName ?? "",
                ManageDepartCode = x.manageDept?.DeptCode??"",
                ManageDepartName = x.manageDept?.DeptName ?? "",
                ObjectCode = x.obj?.ObjectCode ?? "",
                ObjectName = x.obj?.Name ?? "",
                Pid = x.device?.Pid ?? "",
                PlaneCode = x.plane?.PlaneCode??"",
                PlaneName = x.plane?.PlaneName ?? "",
                PlanePositionX = x.plane?.PositionX ?? 0,
                PlanePositionY = x.plane?.PositionY ?? 0,
                PlaneMapPath = x.plane?.PlaneMapPath ?? "",
                PlaneMapWidth = x.plane?.MapWidth ?? 0,
                PlaneMapHeight = x.plane?.MapHeight ?? 0,
                Registered = x.fusionStation?.registered ?? false,
                SceneMode = x.fusionStation?.configurations?.FirstOrDefault(c => c.resourceId == "/10001/0/2")?.value ?? "",
                SID = x.station?.SID??"",
                StationName = x.station.StationName ?? "",
                StationType = x.station.StationType ?? "",
                SpaceType = x.station.SpaceType ?? "",
                SystemVersion = x.fusionStation?.version?.systemVersion ?? "",
                UsageDepartCode = x.usageDept?.DeptCode??"",
                UsageDepartName = x.usageDept?.DeptName ?? "",
                ModifyDate = x.station.ModifyDate,
                FenceCodeList = x.fenceCodeList,
                SectorCodeList = x.sectorCodeList
            });

        // 如果param.BuildingCode有值，則過濾出符合的BuildingCode
        if (!string.IsNullOrWhiteSpace(param.BuildingCode))
        {
            stations = stations.Where(e => e.BuildingCode == param.BuildingCode);
        }

        // 分頁
        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // 排序；如果沒有 sort 條件預設使用 ModifyDate 降冪排序
        string sort = string.IsNullOrEmpty(param.sort) ? "ModifyDate:desc" : param.sort;
        string sortByField = sort.Split(":")[0];
        string sortDirection = sort.Split(":")[1];

        // 根據 sortByField 和 sortDirection 進行動態排序
        stations = SortByField(stations, sortByField, sortDirection.ToLower() == "desc").ToList();

        // 進行資料庫分頁
        var recordList = size == 0 
            ? stations.ToList() // 如果 size == 0，表示不分頁，直接取回所有資料
            : stations.Skip(skip).Take(size).ToList(); // 分頁查詢

        return recordList;
    }

    public static IEnumerable<T> SortByField<T>(IEnumerable<T> list, string sortBy, bool isDescending)
    {
        var param = Expression.Parameter(typeof(T), "x");
        var property = Expression.Property(param, sortBy);
        var lambda = Expression.Lambda<Func<T, object>>(Expression.Convert(property, typeof(object)), param);

        return isDescending ? list.AsQueryable().OrderByDescending(lambda) : list.AsQueryable().OrderBy(lambda);
    }
}
