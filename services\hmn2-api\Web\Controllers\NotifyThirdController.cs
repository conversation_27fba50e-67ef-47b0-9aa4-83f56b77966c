using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using Web.Models.Controller.NotifyThird;
using Web.Models.Controller;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;
using Web.Validation;
using Web.Constant;

namespace Web.Controller;

/// <summary>
/// 第三方通知
/// </summary>
[Route("[controller]")]
[Authorize]
public class NotifyThirdController(IDataAccessService dataAccessService,
                                   ICredentialService credentialService,
                                   IRequestContextService requestContextService,
                                   ILogService logService) : BaseController(dataAccessService, credentialService, requestContextService, logService)
{   
    /// <summary>
    /// 新增第三方通知檢核
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    [HttpPost("thirds/validate")]
    [RequestParamListDuplicate("ThirdCode")]
    [RequestParamListNotNullOrEmpty]
    public IActionResult ValidateNotifyThird([FromBody] List<CreateNotifyThird> paramList)
    {
        // 進到 controller 代表驗證通過，回傳空的錯誤列表
        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new List<ReturnError>()
        });
    }

    /// <summary>
    /// 新增第三方通知
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    [HttpPost("thirds")]
    [RequestParamListDuplicate("ThirdCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateNotifyThird([FromBody] List<CreateNotifyThird> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        string appCode = _user.AppCode;
        _logService.Logging("info", logActionName, requestUUID, "NotifyThird Data Validated, start to append.");

        _dataAccessService.BeginTransaction();

        foreach (CreateNotifyThird nt in paramList)
        {
            NotifyThird notifyThird = new NotifyThird
            {
                AppCode = _user.AppCode,
                AreaCode = nt.AreaCode,
                NotifyType = nt.NotifyType,
                ThirdCode = nt.ThirdCode,
                ThirdName = nt.ThirdName,
                URL_MAC = nt.URL_MAC,
                CreateDate = DateTime.Now,
                CreateUserAccount = _user.Account,
                ModifyDate = DateTime.Now
            };

            // 新增 NotifyThird
            await _dataAccessService.CreateAsync<NotifyThird>(notifyThird);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "NotifyThird Data append done.");

        returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status201Created,
            result = true
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    /// <summary>
    /// 更新第三方通知
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    [HttpPatch("thirds")]
    [RequestParamListDuplicate("ThirdCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateNotifyThird([FromBody] List<UpdateNotifyThird> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        _dataAccessService.BeginTransaction();

        foreach (UpdateNotifyThird param in paramList)
        {
            NotifyThird notifyThird = _dataAccessService.Fetch<NotifyThird>(e => e.AppCode == _user.AppCode && e.ThirdCode == param.ThirdCode).AsTracking().First();

            List<string> updateField = new List<string>();

            if (param.ThirdName != null)
            {
                updateField.Add("ThirdName");
                notifyThird.ThirdName = param.ThirdName;
            }

            if (param.URL_MAC != null && notifyThird.NotifyType != "Display") // Display 類型不可修改URL/MAC
            {
                updateField.Add("URL_MAC");
                notifyThird.URL_MAC = param.URL_MAC;
            }

            updateField.Add("ModifyDate");
            updateField.Add("ModifyUserAccount");
            notifyThird.ModifyDate = DateTime.Now;
            notifyThird.ModifyUserAccount = _user.Account;

            await _dataAccessService.UpdateAsync<NotifyThird>(notifyThird, updateField.ToArray());
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "NotifyThird Data update done.");

        return Ok(new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = true
                });
    }

    /// <summary>
    /// 刪除第三方通知
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    [HttpDelete("thirds")]
    [RequestParamListDuplicate("ThirdCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeleteNotifyThird([FromBody] List<DeleteNotifyThird> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 開始刪除第三方通知資料
        _logService.Logging("info", logActionName, requestUUID, "NotifyThird Data Validated, start to delete.");
        _dataAccessService.BeginTransaction();

        foreach (DeleteNotifyThird nt in paramList)
        {
            // 刪除第三方通知
            await _dataAccessService.DeleteAsync<NotifyThird>(e => e.AppCode == _user.AppCode && e.ThirdCode == nt.ThirdCode);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "NotifyThird Data delete done.");

        return Ok(new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = true
                });
    }

    /// <summary>
    /// 查詢第三方通知
    /// </summary>
    /// <param name="queryParam"></param>
    /// <returns></returns>
    [HttpGet("thirds")]
    public async Task<IActionResult> RetrieveNotifyThird([FromQuery] RetrieveNotifyThird queryParam)
    {
        RetrieveNotifyThird param = queryParam;
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // 排序；如果沒有 sort 條件預設使用 ModifyDate 降冪排序
        string sort = string.IsNullOrEmpty(param.sort) ? "ModifyDate:desc" : param.sort;
        string sortByField = sort.Split(":")[0];
        string sortDirection = sort.Split(":")[1];

        var areaList = _dataAccessService.Fetch<Area>(e => e.AppCode == _user.AppCode);
        var notifyThirdList = _dataAccessService.Fetch<NotifyThird>(x => x.AppCode == _user.AppCode);

        var query = (from a in notifyThirdList
                    join b in areaList on a.AreaCode equals b.AreaCode into temp
                    from t in temp.DefaultIfEmpty()
                    select new
                    {
                        a.Id,
                        a.NotifyType,
                        a.ThirdCode,
                        a.ThirdName,
                        a.URL_MAC,
                        AreaName = (t == null) ? "" : t.AreaName,
                        a.AreaCode,
                        a.CreateDate,
                        a.CreateUserAccount,
                        a.ModifyDate,
                        a.ModifyUserAccount
                    })
                   .Where(x => (param.ThirdName == null || x.ThirdName.ToUpper().Contains(param.ThirdName.ToUpper()))
                            && (param.ThirdCode == null || x.ThirdCode.ToUpper().Contains(param.ThirdCode.ToUpper()))
                            && (param.AreaCode == null || x.AreaCode.ToUpper().Contains(param.AreaCode.ToUpper()))
                            && (param.NotifyType == null || x.NotifyType.ToUpper().Contains(param.NotifyType.ToUpper())));

        // 取得總筆數
        int recordTotal = await query.CountAsync();

        // 根據 sortByField 和 sortDirection 進行動態排序
        query = sortDirection.ToLower() == "desc"
            ? query.OrderByDescending(x => EF.Property<object>(x, sortByField))
            : query.OrderBy(x => EF.Property<object>(x, sortByField));

        // 進行資料庫分頁
        var recordList = size == 0 
            ? await query.ToListAsync() // 如果 size == 0，表示不分頁，直接取回所有資料
            : await query.Skip(skip).Take(size).ToListAsync(); // 分頁查詢

        ReturnModel returnModel = new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = new
                    {
                        recordTotal,
                        recordList
                    }
                };

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);
    }
}