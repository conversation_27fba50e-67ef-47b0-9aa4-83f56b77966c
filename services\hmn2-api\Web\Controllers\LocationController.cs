﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Data;
using Web.Constant;
using Web.Models.Controller;
using Web.Models.Controller.Location;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;
using Web.Validation;

namespace Web.Controller;

/// <summary>
/// Location 控制器
/// </summary>
[Route("[controller]")]
[Authorize]
public class LocationController(IDataAccessService dataAccessService,
                            ICredentialService credentialService,
                            IRequestContextService requestContextService,
                            IUtilityService utilityService,
                            ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly IUtilityService _utilityService = utilityService;

    [HttpPost("locations/validate")]
    [RequestParamListDuplicate("AreaCode,LocCode")]
    [RequestParamListNotNullOrEmpty]
    public IActionResult ValidateLocation([FromBody] List<CreateLocation> paramList)
    {
        // 進到 controller 代表驗證通過，回傳空的錯誤列表
        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new List<ReturnError>()
        });
    }

    [HttpPost("locations")]
    [RequestParamListDuplicate("AreaCode,LocCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateLocation([FromBody] List<CreateLocation> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");
        string appCode = _user.AppCode;

        // 開始新增位置資料
        _logService.Logging("info", logActionName, requestUUID, "Location Data Validated, start to append.");
        _dataAccessService.BeginTransaction();

        foreach (CreateLocation param in paramList)
        {
            Location location = new Location
            {
                AppCode = _user.AppCode,
                AreaCode = param.AreaCode,
                LocCode = param.LocCode,
                LocName = param.LocName,
                BuildingCode = param.BuildingCode,
                PlaneCode = param.PlaneCode,
                Enable = param.Enable == "Y",
                CreateUserAccount = _user.Account,
                ModifyUserAccount = _user.Account,
                CreateDate = DateTime.Now,
                ModifyDate = DateTime.Now
            };

            // 新增位置
            await _dataAccessService.CreateAsync<Location>(location);

            // 將 SIDList 的基站新增至建立的位置
            List<string> sidList = param.SIDList;
            if (sidList != null && sidList.Count > 0)
            {
                // 取出 SID 對應的 Station 並更新其 RegionCode
                var stationsToUpdate = _dataAccessService.Fetch<Station>(e => e.AppCode == appCode && sidList.Contains(e.SID.ToUpper())).AsTracking().ToList();

                if (stationsToUpdate.Any())
                {
                    foreach (var station in stationsToUpdate)
                    {
                        station.RegionCode = location.LocCode; // 更新 RegionCode
                        station.ModifyDate = DateTime.Now;         // 更新修改日期
                        station.ModifyUserAccount = _user.Account; // 更新修改者
                    }

                    // 批量更新物件
                    await _dataAccessService.UpdateAsync(stationsToUpdate);
                }
            }
        }

        await _dataAccessService.CommitAsync();
        _logService.Logging("info", logActionName, requestUUID, "Location Data append done.");

        ReturnModel returnModel = new()
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status201Created,
            result = true
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpPatch("locations")]
    [RequestParamListDuplicate("AreaCode,LocCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateLocation([FromBody] List<UpdateLocation> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 開始更新位置資料
        _logService.Logging("info", logActionName, requestUUID, "Location Data Validated, start to update.");
        _dataAccessService.BeginTransaction();

        foreach (UpdateLocation param in paramList)
        {
            Location location = _dataAccessService.Fetch<Location>(e => e.AppCode == _user.AppCode && e.AreaCode == param.AreaCode && e.LocCode == param.LocCode).AsTracking().First();
            string locCode = param.LocCode;

            List<string> updateField = new List<string>();
            if (!string.IsNullOrEmpty(param.LocName))
            {
                updateField.Add("LocName");
                location.LocName = param.LocName;
            }

            if (!string.IsNullOrEmpty(param.BuildingCode))
            {
                updateField.Add("BuildingCode");
                location.BuildingCode = param.BuildingCode;
            }

            if (!string.IsNullOrEmpty(param.PlaneCode))
            {
                updateField.Add("PlaneCode");
                location.PlaneCode = param.PlaneCode;
            }

            if (!string.IsNullOrEmpty(param.Enable))
            {
                updateField.Add("Enable");
                location.Enable = param.Enable == "Y";
            }

            updateField.Add("ModifyDate");
            updateField.Add("ModifyUserAccount");
            location.ModifyDate = DateTime.Now;
            location.ModifyUserAccount = _user.Account;

            // 更新位置
            await _dataAccessService.UpdateAsync<Location>(location, updateField.ToArray());

            // 如果 SIDList 為 null 就不更新基站資料
            if (param.SIDList == null)
                continue;

            // 更新原本屬於該 LocCode 的對象物件
            var stationsToReset = _dataAccessService.Fetch<Station>(e => e.AppCode == _user.AppCode && e.AreaCode == param.AreaCode && e.RegionCode == locCode).AsTracking().ToList();
            if (stationsToReset.Any())
            {
                foreach (var s in stationsToReset)
                {
                    s.RegionCode = null;
                    s.ModifyDate = DateTime.Now;
                    s.ModifyUserAccount = _user.Account;
                }
                await _dataAccessService.UpdateAsync(stationsToReset);
            }

             // 將 SIDList 的基站新增至位置
            List<string> sidList = param.SIDList;
            if (sidList != null && sidList.Count > 0)
            {
                var stationsToUpdate = _dataAccessService.Fetch<Station>(e => e.AppCode == _user.AppCode && sidList.Contains(e.SID.ToUpper())).AsTracking().ToList();
                if (stationsToUpdate.Any())
                {
                    foreach (var s in stationsToUpdate)
                    {
                        s.RegionCode = locCode;
                        s.ModifyDate = DateTime.Now;
                        s.ModifyUserAccount = _user.Account;
                    }
                    await _dataAccessService.UpdateAsync(stationsToUpdate);
                }
            }
        }

        await _dataAccessService.CommitAsync();
        _logService.Logging("info", logActionName, requestUUID, "Location Data update done.");

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true
        });

    }

    [HttpDelete("locations")]
    [RequestParamListDuplicate("AreaCode,LocCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeleteLocation([FromBody] List<DeleteLocation> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 開始刪除位置資料
        _logService.Logging("info", logActionName, requestUUID, "Location Data Validated, start to delete.");
        _dataAccessService.BeginTransaction();

        foreach (DeleteLocation param in paramList)
        {
            // 刪除位置
            await _dataAccessService.DeleteAsync<Location>(e => e.AppCode == _user.AppCode && e.AreaCode == param.AreaCode && e.LocCode == param.LocCode);
        }

        await _dataAccessService.CommitAsync();
        _logService.Logging("info", logActionName, requestUUID, "Location Data delete done.");

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true
        });
    }

    [HttpGet("locations")]
    public async Task<IActionResult> RetrieveLocation([FromQuery] RetrieveLocation queryParam)
    {
        RetrieveLocation param = queryParam;
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // 排序；如果沒有 sort 條件預設使用 ModifyDate 降冪排序
        string sort = string.IsNullOrEmpty(param.sort) ? "ModifyDate:desc" : param.sort;
        string sortByField = sort.Split(":")[0];
        string sortDirection = sort.Split(":")[1];

        // 開始查詢位置資料
        var areaList = _dataAccessService.Fetch<Area>(e => e.AppCode == _user.AppCode);
        var buildingList = _dataAccessService.Fetch<Building>(x => x.AppCode == _user.AppCode);
        var planeList = _dataAccessService.Fetch<Plane>(x => x.AppCode == _user.AppCode);
        var stationList = _dataAccessService.Fetch<Station>(x => x.AppCode == _user.AppCode);
        var locationList = _dataAccessService.Fetch<Location>(x => x.AppCode == _user.AppCode);

        var query = (from a in locationList
                    join b in areaList on a.AreaCode equals b.AreaCode into temp
                    from t in temp.DefaultIfEmpty()
                    join c in planeList on a.PlaneCode equals c.PlaneCode into temp2
                    from tt in temp2.DefaultIfEmpty()
                    join d in buildingList on tt.BuildingCode equals d.BuildingCode into temp3
                    from ttt in temp3.DefaultIfEmpty()
                     select new
                     {
                         a.LocId,
                         a.AreaCode,
                         AreaName = (t == null) ? "" : t.AreaName,
                         Enable = (a.Enable == true) ? "Y" : "N",
                         a.LocCode,
                         a.LocName,
                         ttt.BuildingCode,
                         ttt.BuildingName,
                         a.PlaneCode,
                         tt.PlaneName,
                         stations = (from s in stationList
                            where s.RegionCode == a.LocCode
                            orderby s.SID
                            select new
                            {
                                s.SID,
                                s.StationName,
                                s.StationType,
                                s.SpaceType,
                                s.AppCode,
                                s.AreaCode,
                                s.RegionCode,
                                s.PlaneCode
                            }).ToList(),
                         a.ModifyDate
                     })
            .Where(x => (param.LocCode == null || x.LocCode.ToUpper().Contains(param.LocCode.ToUpper()))
                     && (param.LocName == null || x.LocName.ToUpper().Contains(param.LocName.ToUpper()))
                     && (param.BuildingCode == null || x.BuildingCode.ToUpper().Contains(param.BuildingCode.ToUpper()))
                     && (param.BuildingName == null || x.BuildingName.ToUpper().Contains(param.BuildingName.ToUpper()))
                     && (param.PlaneCode == null || x.PlaneCode.ToUpper().Contains(param.PlaneCode.ToUpper()))
                     && (param.PlaneName == null || x.PlaneName.ToUpper().Contains(param.PlaneName.ToUpper()))
                     && (param.AreaCode == null || x.AreaCode.ToUpper().Contains(param.AreaCode.ToUpper()))
                     && (param.Enable == null || x.Enable == (param.Enable))
                );

        // 取得總筆數
        int recordTotal = await query.CountAsync();

        // 根據 sortByField 和 sortDirection 進行動態排序
        query = sortDirection.ToLower() == "desc"
            ? query.OrderByDescending(x => EF.Property<object>(x, sortByField))
            : query.OrderBy(x => EF.Property<object>(x, sortByField));

        // 進行資料庫分頁
        var recordList = size == 0
            ? await query.ToListAsync() // 如果 size == 0，表示不分頁，直接取回所有資料
            : await query.Skip(skip).Take(size).ToListAsync(); // 分頁查詢

        ReturnModel returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal,
                recordList
            }
        };

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);
    }
}


