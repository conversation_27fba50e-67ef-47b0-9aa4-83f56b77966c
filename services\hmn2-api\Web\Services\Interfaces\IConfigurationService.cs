using Web.Models.Controller;
using Web.Models.Controller.Device;
using Web.Models.Controller.Object;
using Web.Models.Service.Configuration;
using Web.Models.Service.Fusion;
using Web.Repository.Models.Entities;
using static Web.Models.Service.Fusion.DeviceResults;

namespace Web.Services.Interfaces;

public interface IConfigurationService
{
    Task<List<ReturnError>> CreateDevice(string appCode, List<InCreateDevice> paramList, bool needPostFusion = true);
    Task<OutCreateEvents> CreateEvents(string appCode, List<Repository.Models.Entities.ObjectEvent> objectEventList, List<Repository.Models.Entities.ObjectDeviceDetail> objectDeviceDetailList);
    Task<OutCreateObjectDatum> CreateObjectDatum(string appCode,
        //List<Repository.Models.Entities.ObjectDeviceDetail> objectDeviceDetailList, 
        IEnumerable<ObjectDatum> addObjectDataList,
        IEnumerable<Repository.Models.Entities.ObjectDevice> addObjectDeviceList);
    Task<OutCreateObjectDevice> CreateObjectDevice(string appCode,
        IEnumerable<Repository.Models.Entities.ObjectDevice> addObjectDeviceList//, 
                                                                                //List<Repository.Models.Entities.ObjectDeviceDetail> objectDeviceDetailList
        );
    Task<OutCreateObjectDeviceDetail> CreateObjectDeviceDetail(List<Repository.Models.Entities.ObjectDeviceDetail> objectDeviceDetailList);
    Task<List<ReturnError>> DeleteDevice(string appCode, List<InDeleteDevice> paramList);
    Task<OutDeleteEvents> DeleteEvents(List<Repository.Models.Entities.ObjectEvent> deleteObjectEventList);
    Task<List<DeleteAPIResult>> DeleteObjectDatum(List<ObjectDatum> deleteObjectDataList);
    (List<ObjectDatum> objectDataList, List<Repository.Models.Entities.ObjectDevice> objectDeviceList, List<Repository.Models.Entities.ObjectDeviceDetail> objectDeviceDetailList, List<Repository.Models.Entities.ObjectEvent> objectEventList, List<EventFence> eventFenceList) ExtractCreateObjectParam(string appCode, List<InCreateObject> paramList);
    (List<ObjectDatum> objectDataList, List<Repository.Models.Entities.ObjectDevice> objectDeviceList, List<Repository.Models.Entities.ObjectDeviceDetail> objectDeviceDetailList, List<Repository.Models.Entities.ObjectEvent> objectEventList, List<EventFence> eventFenceList) ExtractUpdateObjectParam(string appCode, List<InUpdateObject> paramList);
    Task<List<device>> FetchDeviceOnStation(string stationSidList);
    Task<bool> FetchIsShowOtherDeptDeviceOnDeptInventory(string appCode);
    Task<List<DeviceType>> FetchSupportDeviceTypeList(string appCode, List<string> areaCodeList);
    string GenerateEventCode(InGenerateEventCode param);
    string GenerateEventName(InGenerateEventName param);
    Task<List<DeviceAllInfo>> GetAllDevicesWithoutPermissionCheck(string appCode);
    Task<List<Devices>> GetCoreDeviceList(string pidList);
    List<DeviceAllInfo> GetDeviceAllInfo(string appCode);
    List<DeviceAllInfo> GetDeviceAllInfo(string areaCode, string deptCode, bool deviceBoundObject);
    List<DeviceAllInfo> GetDeviceAllInfo(string areaCode, List<string> deptCodeList, bool deviceBoundObject);
    Task<IEnumerable<Models.Service.Configuration.DevicePositions>> GetDevicePosition();
    Task<List<Models.Service.Configuration.DevicePositions>> GetDevicePosition(string pidList);
    Task<List<DeviceResults.Result>> GetDeviceReport(List<string>? deviceTypeList, List<string>? objectCodeList, List<string>? pidList, List<string> resourceIdList, string startDate, string endDate, string lineType = "LAST", long interval = 1);
    Task<List<DeviceType>> GetDeviceTypeList();
    string GetFenceCodeByEventCode(string eventCode);
    Task<(List<ObjectDatum>, List<Repository.Models.Entities.ObjectDevice>, List<Repository.Models.Entities.ObjectDeviceDetail>, List<Repository.Models.Entities.ObjectEvent>)> GetObjectRelationData(InGetObjectRelationData inGetObjectRelationData);
    Task<(List<ObjectType> objectTypeList, List<Fence> fenceList, List<FenceStation> fenceStationList, List<FenceAlarmGroup> fenceAlarmGroupList)> GetObjectSustainConfData(string appCode);
    Task<List<sddResource>> GetSddResourceList();
    Task<List<serviceCode>> GetServiceList();
    Task<List<TrajectoryOutput>> GetTrajectory(string appCode, string objectCode, string fromDate, string toDate);
    Task<OutDeleteDevices> UboundObjectDevice(List<Repository.Models.Entities.ObjectDevice> deleteObjectDeviceList);
    Task<List<ReturnError>> UpdateDevice(string appCode, List<InUpdateDevice> paramList);
    Task<List<ReturnError>> ValidaionInCreateObjectList(List<InCreateObject> paramList);
    Task<List<ReturnError>> ValidaionInUpdateObjectList(List<InCreateObject> paramList);
    Task<List<ReturnError>> ValidationInCreateDeviceList(List<InCreateDevice> paramList);
    Task<List<ReturnError>> ValidationInRetrieveDevice(InRetrieveDevice param);
    Task<List<ReturnError>> ValidationInUpdateUgColor(InUpdateUgColor param, string callMethod = "UpdateUgColor.ObjectController");
}