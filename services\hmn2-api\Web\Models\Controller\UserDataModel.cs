﻿using System.ComponentModel.DataAnnotations;
using Web.Validation;
using Web.Constant;

namespace Web.Models.Controller.UserData;

public class InRetrieveUserData
{
    public string page { get; set; }
    public string size { get; set; }
    public string AreaCode { get; set; }
    public string UserAccount { get; set; }
    public string Enable { get; set; }
    public string DeptCode { get; set; }
    public string DeptName { get; set; }
    public string UserAccountDisp { get; set; }
    public string RoleCode { get; set; }
    //SearchMode, CompareKind, CompareType 這是供全文檢索用的，怕忘記這個欄位叫CompareMode
    public string CompareMode { get; set; }
}

public class InUpdateUserData
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Exists("AreaCode", "Department", "DeptCode", ErrorMessage = Constants.ErrorCode.NotFound + "DeptCode")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "DeptCode")]
    public string DeptCode { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }

    [StringLength(100, ErrorMessage = Constants.ErrorCode.Length + "LineToken")]
    public string? LineToken { get; set; }

    // 是否為單位主管
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "IsSupervisor")]
    public string IsSupervisor { get; set; }

    // 是否有管理其它單位
    [RegularExpression(@"^[YNA]+$", ErrorMessage = Constants.ErrorCode.Pattern + "IsAdmin")]
    [ListPropertyAllExists("", "UserDeptMonPermList", "UsageDeptCode", "Department", "DeptCode", ErrorMessage = Constants.ErrorCode.NotFound + "UserDeptMonPermList")]
    [CheckIsAdmin("DeptCode", "UserDeptMonPermList", ErrorMessage = Constants.ErrorCode.Invalid + "UserDeptMonPermList")]
    public string IsAdmin { get; set; }

    // 是否為超級管理員
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "IsSuperAdmin")]
    public string IsSuperAdmin { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "NeedChangePwd")]
    public string NeedChangePwd { get; set; }

    [StringLength(20, ErrorMessage = Constants.ErrorCode.Length + "Phone")]
    public string? Phone { get; set; }

    [StringLength(1, ErrorMessage = Constants.ErrorCode.Length + "PwdExpType")]
    [RegularExpression(@"^[0SR]+$", ErrorMessage = Constants.ErrorCode.Pattern + "NeedChangePwd")]
    [CheckPwdExpType("PwdExpStartDate", "PwdExpEndDate", ErrorMessage = Constants.ErrorCode.Invalid + "PwdExpType")]
    public string PwdExpType { get; set; }
    
    public DateTime? PwdExpStartDate{ get; set; }
    
    public DateTime? PwdExpEndDate { get; set; }
    
    [Exists("", "Role", "RoleCode", ErrorMessage = Constants.ErrorCode.NotFound + "RoleCode")]
    public string RoleCode { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "ThirdPartyAuth")]
    public string? ThirdPartyAuth { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "UserAccount")]
    [Exists("", "UserDatum", "UserAccount", false, true, true, ErrorMessage = Constants.ErrorCode.NotFound + "UserAccount")]
    public virtual string UserAccount { get; set; }

    [StringLength(100, ErrorMessage = Constants.ErrorCode.Length + "UserEmail")]
    [RegularExpression(@"^[^@\s]+@[^@\s]+\.[^@\s]+$", ErrorMessage = Constants.ErrorCode.Pattern + "UserEmail")]
    public string? UserEmail { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "UserName")]
    public string UserName { get; set; }

    [EqualsPropertyValue("", "", "", "", "ConfirmPassword", false, ErrorMessage = Constants.ErrorCode.Invalid + "UserPassword")]
    public string? UserPassword { get; set; }

    [EqualsPropertyValue("", "", "", "", "UserPassword", false, ErrorMessage = Constants.ErrorCode.Invalid + "ConfirmPassword")]
    public string? ConfirmPassword { get; set; }  

    public List<InUserDeptMonPerm> UserDeptMonPermList { get; set; }
}

public class InCreateUserData
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "DeptCode")]
    [Exists("AreaCode", "Department", "DeptCode", ErrorMessage = Constants.ErrorCode.NotFound + "DeptCode")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "DeptCode")]
    public string DeptCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Enable")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }
    
    [StringLength(100, ErrorMessage = Constants.ErrorCode.Length + "LineToken")]
    public string? LineToken { get; set; }

    // 是否為單位主管
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "IsSupervisor")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "IsSupervisor")]
    public string IsSupervisor { get; set; }

    // 是否有管理其它單位
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "IsAdmin")]
    [RegularExpression(@"^[YNA]+$", ErrorMessage = Constants.ErrorCode.Pattern + "IsAdmin")]
    [ListPropertyAllExists("", "UserDeptMonPermList", "UsageDeptCode", "Department", "DeptCode", ErrorMessage = Constants.ErrorCode.NotFound + "UserDeptMonPermList")]
    [CheckIsAdmin("DeptCode", "UserDeptMonPermList", ErrorMessage = Constants.ErrorCode.Invalid + "UserDeptMonPermList")]
    public string IsAdmin { get; set; }

    // 是否為超級管理員
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "IsSuperAdmin")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "IsSuperAdmin")]
    public string IsSuperAdmin { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "NeedChangePwd")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "NeedChangePwd")]
    public string NeedChangePwd { get; set; }

    [StringLength(20, ErrorMessage = Constants.ErrorCode.Length + "Phone")]
    public string? Phone { get; set; }

    [StringLength(1, ErrorMessage = Constants.ErrorCode.Length + "PwdExpType")]
    [RegularExpression(@"^[0SR]+$", ErrorMessage = Constants.ErrorCode.Pattern + "NeedChangePwd")]
    [CheckPwdExpType("PwdExpStartDate", "PwdExpEndDate", ErrorMessage = Constants.ErrorCode.Invalid + "PwdExpType")]
    public string PwdExpType { get; set; }

    public DateTime? PwdExpStartDate{ get; set; }

    public DateTime? PwdExpEndDate { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "RoleCode")]
    [Exists("", "Role", "RoleCode", ErrorMessage = Constants.ErrorCode.NotFound + "RoleCode")]
    public string RoleCode { get; set; } 

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "ThirdPartyAuth")]
    public string? ThirdPartyAuth { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "UserAccount")]
    [Unique("", "UserDatum", "UserAccount", false, true, true, ErrorMessage = Constants.ErrorCode.Unique + "UserAccount")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "UserAccount")]
    [RegularExpression(@"^[a-zA-Z0-9@:$_-]*$", ErrorMessage = Constants.ErrorCode.Pattern + "UserAccount")]
    public virtual string UserAccount { get; set; }

    [StringLength(100, ErrorMessage = Constants.ErrorCode.Length + "UserEmail")]
    [RegularExpression(@"^[^@\s]+@[^@\s]+\.[^@\s]+$", ErrorMessage = Constants.ErrorCode.Pattern + "UserEmail")]
    public string? UserEmail { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "UserName")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "UserName")]
    public string UserName { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "UserPassword")]
    [EqualsPropertyValue("", "", "", "", "ConfirmPassword", false, ErrorMessage = Constants.ErrorCode.Invalid + "UserPassword")]
    public string? UserPassword { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ConfirmPassword")]
    [EqualsPropertyValue("", "", "", "", "UserPassword", false, ErrorMessage = Constants.ErrorCode.Invalid + "ConfirmPassword")]
    public string? ConfirmPassword { get; set; }  

    public List<InUserDeptMonPerm> UserDeptMonPermList { get; set; }
}

public class InDeleteUserData
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "UserAccount")]
    [Exists("", "UserDatum", "UserAccount", false, true, true, ErrorMessage = Constants.ErrorCode.NotFound + "UserAccount")]
    public string UserAccount { get; set; }
}

public class ParamUserDeptMonPerm
{
    public string AreaCode { get; set; }
    public string UserAccount { get; set; }
    public string UsageDeptCode { get; set; }
}

public class InUserDeptMonPerm : ParamUserDeptMonPerm;
