﻿using Web.Models.Service;
using Web.Models.Service.Fusion;
using Object = System.Object;

namespace Web.Models.Controller;

public class Authorize
{
    public string Account { get; set; }
    public string AppCode { get; set; }
    public string AreaCode { get; set; }
    public IEnumerable<string>? AreaCodeList { get; set; }
    public string? DeptCode { get; set; }
    public IEnumerable<string>? DeptCodeList { get; set; }
    public string? IsAdmin { get; set; }
    public bool? IsViewAllArea { get; set; }

    public static explicit operator Authorize(UserResult userResult)
    {
        //實作轉換邏輯
        return new Authorize
        {
            Account = userResult.Account,
            AppCode = userResult.AppCode,
            AreaCode = userResult.AreaCode,
            AreaCodeList = userResult.AreaCodeList,
            DeptCode = userResult.DeptCode,
            DeptCodeList = userResult.DeptCodeList,
            IsAdmin = userResult.IsAdmin,
            IsViewAllArea = userResult.IsViewAllArea
        };
    }
}

public class ReturnModel
{
    public ReturnModel() { }
    public ReturnModel(int _httpStatusCode, bool _result, object _data)
        : this()
    {
        this.result = _result;
        this.data = _data;
        this.httpStatus = _httpStatusCode;
    }
    public ReturnModel(object _data, object _model)
        : this()
    {
        this.result = true;
        this.data = _data;
        this.model = _model;

    }
    public string requestUUID { get; set; }
    //記錄使用者資訊
    public Authorize authorize { get; set; }
    //執行WebAPI後，回傳前端的HttpStatusCode
    public int httpStatus { get; set; }
    //執行WebAPI成功與否之結果
    public bool result { get; set; }
    //可放非data之外,前端所需要的相關屬性值
    public object model { get; set; }
    //資料
    public object data { get; set; }
    //回傳前端需要顯示給User看的成功/錯誤訊息
    public string title { get; set; }
    //發生錯誤時,不應該顯示internal_message給user看,但在輸出Log的時候應該將真正的error message記錄下來
    public string innerMsg { get; set; }
    //Token由底層自行控制,開發者請勿自行控制
    public string token { get; set; }
}

public class ReturnError
{
    public int index { get; set; }
    public string code { get; set; }
    public List<ErrorDetail> errors { get; set; }
    public static explicit operator ReturnError(CommonAPIResult apiResult)
    {
        return new ReturnError
        {
            code = apiResult.code,
            errors = apiResult.errors?.Select((e, index) => new ErrorDetail
            {
                index = index,  // 设置 index 为列表中的索引
                error = e.error,
                message = e.description,
                details = new List<ErrorDetail>()  // 如果需要，可以进一步处理嵌套的错误
            }).ToList() ?? new List<ErrorDetail>()
        };
    }
}

public class ErrorDetail
{
    public int index { get; set; }
    public string? code { get; set; }
    public string error { get; set; }
    public string? message { get; set; }
    public string innerMsg { get; set; }
    public List<ErrorDetail> details{ get; set; }
}