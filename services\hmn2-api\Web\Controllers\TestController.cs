﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Mvc;
using MQTTnet;
using MQTTnet.Client;
using MQTTnet.Client.Options;
using System.Security.Claims;
using Web.Models.Controller;
using Web.Models.Service.Monitor;
using Web.Services;
using Web.Services.Interfaces;

namespace Web.Controller;

/// <summary>
/// 測試用控制器
/// </summary>
[Route("[controller]")]
public class TestController(IConfiguration configuration,
    IDataAccessService dataAccessService,
    IMonitorService monitorService,
    IAuthService authService,
    ICredentialService credentialService,
    IRequestContextService requestContextService,
    ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly IConfiguration _configuration = configuration;
    private readonly IMonitorService _monitorService = monitorService;
    private readonly IAuthService _authService = authService;
    //private readonly UserResult _user = configurationService.UserResult;

    [HttpGet("sendmqtt")]
    public async Task<IActionResult> SendMessage(string message)
    {
        // 建立 MQTT 訊息
        var mqttMessage = new MqttApplicationMessageBuilder()
            .WithTopic("task") // 設定主題
            .WithPayload(message) // 設定訊息內容
            .Build();

        var mqttClient = new MqttFactory().CreateMqttClient();

        IMqttClientOptions options = new MqttClientOptionsBuilder()
            .WithTcpServer("localhost")
            .WithClientId(Guid.NewGuid().ToString()[..10])
            .WithCleanSession()
            .Build();

        await mqttClient.ConnectAsync(options);

        await mqttClient.PublishAsync(mqttMessage);

        return Ok();
    }

    [HttpGet("testFollowingTask")]
    public async Task<IActionResult> TestFollowingTask(string userAccount,string areaCode, string appCode)
    {
        await CreateFakeCredential(userAccount, appCode);

        var followTaskList = await _monitorService.GetFollowingTaskList(new InFollowingTaskList
        {
            AreaCode = areaCode
        });

        ReturnModel returnModel = new(StatusCodes.Status200OK, true, followTaskList);

        return Ok(returnModel);
    }

    [HttpGet("signalr")]
    public IActionResult SignalR()
    {
        return View();
    }

    private async Task CreateFakeCredential(string userAccount, string appCode)
    {
        // 將使用者UserAccount、UserName 及 UserClientPara 等等資料，轉換成Claims（HMNAdmin所要指定的屬性值，都在此method）
        List<Claim> claims = await _authService.GenerateClaimList(appCode, userAccount);

        // 產生ClaimsIdentity（注意！！有兩個 out 參數）
        _authService.CreatePersistentUserClaimsIdentity(claims, out ClaimsIdentity claimsIdentity, out AuthenticationProperties authProperties);

        // 使用ClaimsIdentity 產生Cookie
        await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, new ClaimsPrincipal(claimsIdentity), authProperties);
    }
}
