﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class Role
{
    public int Id { get; set; }

    public string? AppCode { get; set; }

    public string RoleCode { get; set; } = null!;

    public bool? Enable { get; set; }

    public string? RoleName { get; set; }

    public string? RoleDesc { get; set; }

    public DateTime? CreateDate { get; set; }

    public DateTime? ModifyDate { get; set; }
}
