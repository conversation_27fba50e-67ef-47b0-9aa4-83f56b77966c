﻿using Microsoft.EntityFrameworkCore.Query.SqlExpressions;
using Web.Repository.Models.Entities;
using System.ComponentModel.DataAnnotations;
namespace Web.Models.Controller.Object;
using Web.Validation;
using Web.Constant;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using Web.Models.Service.Fusion;

public class InCreateObject
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    [SetToList("AreaCode", "ObjectDeviceList")]
    [SetDeviceType("ObjectDeviceList")]
    [HasTempHudDeviceType("UrgColor", "ObjectDeviceList", ErrorMessage = Constants.ErrorCode.Invalid + "ObjectDeviceList")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ObjectCode")]
    [Unique("", "ObjectDatum", "ObjectCode", true, ErrorMessage = Constants.ErrorCode.Unique + "ObjectCode")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "ObjectCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "ObjectCode")]
    [ListDuplicate("ObjectDeviceList", "Pid", ErrorMessage = Constants.ErrorCode.Duplicate + "ObjectDeviceList.Pid")]
    public string ObjectCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Enable")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }
    
    [Exists("AreaCode", "ObjectGroup", "GroupCode", ErrorMessage = Constants.ErrorCode.NotFound + "GroupCode")]
    public string GroupCode { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Name")]
    [StringLength(64, ErrorMessage = Constants.ErrorCode.Length + "Name")]
    public string Name { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ObjectType")]
    [Exists("AreaCode", "ObjectType", "ObjectTypeCode", "Enable:true", ErrorMessage = Constants.ErrorCode.NotFound + "ObjectType")]
    [RegularExpression(@"^[1-6]$", ErrorMessage = Constants.ErrorCode.Pattern + "ObjectType")]
    public string ObjectType { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "UsageDepartCode")]
    [IsUsageDept("AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "UsageDeptCode")]
    [SetToList("UsageDepartCode", "ObjectDeviceList")]
    public string UsageDepartCode { get; set; }
    public string Remark { get; set; }
    public string EquipmentStatus { get;set; }
    
    [IsMonitorAlarmColor("AreaCode", ErrorMessage = Constants.ErrorCode.Invalid + "UrgColor")]
    public string? UrgColor { get; set; }

    public List<InCreateObjectDevice> ObjectDeviceList { get; set; }
}

public class InCreateObjectDevice
{
    [JsonIgnore]
    [SetToList("AreaCode", "ObjectEventList")]
    [SetSupportServiceCodes("DeviceType", "ObjectEventList")]
    public string? AreaCode { get; set; }

    [JsonIgnore]
    public DeviceType? DeviceType { get; set; }

    [JsonIgnore]
    public string UsageDepartCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ObjectDeviceList.Pid")]
    [Exists("", "Device", "Pid", ErrorMessage = Constants.ErrorCode.NotFound + "ObjectDeviceList.Pid")]
    [Unique("", "ObjectDevice", "Pid", true, ErrorMessage = Constants.ErrorCode.Unique + "ObjectDeviceList.Pid")]
    [ExistsDeviceType("DeviceType", ErrorMessage = Constants.ErrorCode.NotFound + "ObjectDeviceList.DeviceType")]
    [EqualsPropertyValue("AreaCode", "Device", "Pid", "UsageDepartCode", "UsageDepartCode", false, ErrorMessage = Constants.ErrorCode.Invalid + "ObjectDeviceList.Pid")]
    public string Pid { get; set; }

    [RequiredWhenEquals("ObjectEventList.ServiceCode", "mmWaveFallDetection,mmWaveLeaveBedmmmWaveStayTimeout", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ObjectDeviceList.ObjectEventList.SddComp")] 
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "MmWaveType")]
    public string MmWaveType { get; set; }
    
    public List<InCreateObjectEvent> ObjectEventList { get; set; }
    public List<ObjectDeviceDetail> ObjectDeviceDetailList { get; set; }
}

public class InUpdateObjectDevice
{
    [JsonIgnore]
    [SetToList("AreaCode", "ObjectEventList")]
    [SetSupportServiceCodes("DeviceType", "ObjectEventList")]
    public string? AreaCode { get; set; }

    [JsonIgnore]
    public DeviceType? DeviceType { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ObjectDeviceList.Pid")]
    [Exists("", "Device", "Pid", ErrorMessage = Constants.ErrorCode.NotFound + "ObjectDeviceList.Pid")]
    [ExistsDeviceType("DeviceType", ErrorMessage = Constants.ErrorCode.NotFound + "ObjectDeviceList.DeviceType")]
    public string Pid { get; set; }

    [RequiredWhenEquals("ObjectEventList.ServiceCode", "mmWaveFallDetection,mmWaveLeaveBedmmmWaveStayTimeout", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ObjectDeviceList.ObjectEventList.SddComp")] 
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "MmWaveType")]
    public string MmWaveType { get; set; }
    
    public List<InUpdateObjectEvent> ObjectEventList { get; set; }
    public List<ObjectDeviceDetail> ObjectDeviceDetailList { get; set; }
}

public class ObjectDeviceDetail
{
    public string SddResource { get; set; }
    public string SddComp { get; set; }
    public double Threshold { get; set; }
    public int? StayOvertime { get; set; }
    public int? Duration { get; set; }
    public int? SilentInterval { get; set; }
}

public class InCreateObjectEvent
{
    [JsonIgnore]
    public string? AreaCode { get; set; }

    [JsonIgnore]
    public List<string> SupportDataEventServiceCodes { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ObjectDeviceList.ObjectEventList.ServiceCode")] 
    [ExistsServiceCode("SupportDataEventServiceCodes", ErrorMessage = Constants.ErrorCode.NotFound + "ObjectDeviceList.ObjectEventList.ServiceCode")]
    public string ServiceCode { get; set; }
    
    [RequiredWhenEquals("ServiceCode", "SensorDataDriven", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ObjectDeviceList.ObjectEventList.SddResource")] 
    public string SddResource { get; set; }

    [RequiredWhenEquals("ServiceCode", "SensorDataDriven", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ObjectDeviceList.ObjectEventList.SddComp")] 
    [RegularExpression(@"^(lt|eq|gt)$", ErrorMessage = Constants.ErrorCode.Pattern + "ObjectDeviceList.ObjectEventList.SddComp")]
    public string SddComp { get; set; }
    
    [RequiredWhenEquals("ServiceCode", "Enter,Leave", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ObjectDeviceList.ObjectEventList.FenceCode")] 
    [Exists("AreaCode", "Fence", "FenceCode", ErrorMessage = Constants.ErrorCode.NotFound + "ObjectDeviceList.ObjectEventList.FenceCode")]
    public string FenceCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ObjectDeviceList.ObjectEventList.EventName")] 
    public string EventName { get; set; }
}

public class InUpdateObjectEvent
{
    [JsonIgnore]
    public string? AreaCode { get; set; }

    [JsonIgnore]
    public List<string> SupportDataEventServiceCodes { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ObjectDeviceList.ObjectEventList.ServiceCode")] 
    [ExistsServiceCode("SupportDataEventServiceCodes", ErrorMessage = Constants.ErrorCode.NotFound + "ObjectDeviceList.ObjectEventList.ServiceCode")]
    public string ServiceCode { get; set; }
    
    [RequiredWhenEquals("ServiceCode", "SensorDataDriven", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ObjectDeviceList.ObjectEventList.SddResource")] 
    public string SddResource { get; set; }

    [RequiredWhenEquals("ServiceCode", "SensorDataDriven", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ObjectDeviceList.ObjectEventList.SddComp")] 
    [RegularExpression(@"^(lt|eq|gt)$", ErrorMessage = Constants.ErrorCode.Pattern + "ObjectDeviceList.ObjectEventList.SddComp")]
    public string SddComp { get; set; }
    
    [RequiredWhenEquals("ServiceCode", "Enter,Leave", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ObjectDeviceList.ObjectEventList.FenceCode")] 
    [Exists("AreaCode", "Fence", "FenceCode", ErrorMessage = Constants.ErrorCode.NotFound + "ObjectDeviceList.ObjectEventList.FenceCode")]
    public string FenceCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ObjectDeviceList.ObjectEventList.EventName")] 
    public string EventName { get; set; }
}

public class InUpdateObject
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    [SetToList("AreaCode", "ObjectDeviceList")]
    [SetDeviceType("ObjectDeviceList")]
    [HasTempHudDeviceType("UrgColor", "ObjectDeviceList", ErrorMessage = Constants.ErrorCode.Invalid + "ObjectDeviceList")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ObjectCode")]
    [Exists("", "ObjectDatum", "ObjectCode", ErrorMessage = Constants.ErrorCode.NotFound + "ObjectCode")]
    [ListDuplicate("ObjectDeviceList", "Pid", ErrorMessage = Constants.ErrorCode.Duplicate + "ObjectDeviceList.Pid")]
    [HasReferenceWhenEquals("Enable", "N", "AreaCode", "ObjectDevice", "ObjectCode", ErrorMessage = Constants.ErrorCode.Reference + "ObjectCode")]
    public string ObjectCode { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }
    
    [Exists("AreaCode", "ObjectGroup", "GroupCode", ErrorMessage = Constants.ErrorCode.NotFound + "GroupCode")]
    public string GroupCode { get; set; }
    
    [StringLength(64, ErrorMessage = Constants.ErrorCode.Length + "Name")]
    public string Name { get; set; }

    [Exists("AreaCode", "ObjectType", "ObjectTypeCode", "Enable:true", ErrorMessage = Constants.ErrorCode.NotFound + "ObjectType")]
    [RegularExpression(@"^[1-6]$", ErrorMessage = Constants.ErrorCode.Pattern + "ObjectType")]
    public string ObjectType { get; set; }
    
    [IsUsageDept("AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "UsageDeptCode")]
    public string UsageDepartCode { get; set; }
    public string Remark { get; set; }
    public string EquipmentStatus { get;set; }
    
    [IsMonitorAlarmColor("AreaCode", ErrorMessage = Constants.ErrorCode.Invalid + "UrgColor")]
    public string? UrgColor { get; set; }

    public List<InUpdateObjectDevice> ObjectDeviceList { get; set; }
}

public class InUpdateUgColor
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ObjectCodes")]
    [ListAllExists("", "ObjectDatum", "ObjectCode", ErrorMessage = Constants.ErrorCode.NotFound + "ObjectCodes")]
    public List<string> ObjectCodes { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "UrgColor")]
    [IsMonitorAlarmColor("AreaCode", ErrorMessage = Constants.ErrorCode.Invalid + "UrgColor")]
    public string UrgColor { get; set; }
}

public class InDeleteObject
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ObjectCode")]
    [Exists("", "ObjectDatum", "ObjectCode", ErrorMessage = Constants.ErrorCode.NotFound + "ObjectCode")]
    [HasReference("AreaCode", "ObjectDevice", "ObjectCode", ErrorMessage = Constants.ErrorCode.Reference + "ObjectCode")]
    public string ObjectCode { get; set; }
}

public class InRetrieveObject
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    public string UsageDepartCodes { get; set; }
    public string DeviceTypes {  get; set; }
    public string ObjectCode { get; set; }
    public string ObjectName { get; set; }
    public string GroupCode { get; set; }
    public string GroupName { get; set; }
    public string ObjectType { get; set; }
    public string Enable { get; set; }
    public string Active { get; set; }
}