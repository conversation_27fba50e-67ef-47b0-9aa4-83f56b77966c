﻿using System.Runtime.CompilerServices;
using Web.Models.Controller;
using Web.Models.Controller.Schedule;

namespace Web.Services.Interfaces
{
    public interface IAdminService
    {
        Task<ReturnModel> CreateSchedule(List<CreateSchedule> paramList);
        Task<ReturnModel> DeleteScheule(List<DeleteSchedule> paramList);
        Task<ReturnModel> UpdateSchedule(List<UpdateSchedule> paramList, [CallerMemberName] string callerName = null);
    }
}