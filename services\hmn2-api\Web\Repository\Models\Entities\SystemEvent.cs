﻿using System;
namespace Web.Repository.Models.Entities;

public partial class SystemEvent
{
    public int Id { get; set; }
    public string AppCode { get; set; } = null!;
    public string ServiceCode { get; set; } = null!;
    public string EventCode { get; set; }
    public string EventName { get; set; }
    public string DeviceType { get; set; }
    public bool Enable { get; set; }
    public double? Threshold { get; set; }
    public string CreateUserAccount { get; set; }
    public DateTime CreateDate { get; set; }
    public string? ModifyUserAccount { get; set; }
    public DateTime? ModifyDate { get; set; }
}
