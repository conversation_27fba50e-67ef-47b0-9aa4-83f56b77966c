﻿using System.Collections.Concurrent;

namespace Web.Services;

public class SignalRGroupService()
{
    // 這裡使用了ConcurrentDictionary，但你可以選擇其他存储方式
    private readonly ConcurrentDictionary<string, bool> _groups = new ConcurrentDictionary<string, bool>();

    public void AddGroup(string groupName) => _groups.TryAdd(groupName, true);

    public void RemoveGroup(string groupName) => _groups.TryRemove(groupName, out _);

    public IEnumerable<string> GetGroups() => _groups.Keys;
}
