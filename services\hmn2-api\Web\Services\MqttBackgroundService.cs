using Microsoft.AspNetCore.SignalR;
using System.Security.Cryptography.X509Certificates;
using Web.Models.AppSettings;
using Web.Repository.Models.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Options;
using System.Text;
using Web.Models.Service.Monitor;
using MQTTnet.Client;
using MQTTnet;
using Web.Hubs;
using System.Text.Json;
using Web.Models.Service.Fusion;
using Web.Services.Interfaces;
using MQTTnet.Client.Options;
using MQTTnet.Client.Receiving;
using MQTTnet.Server;
using MQTTnet.Protocol;
using System.Collections.Generic;
using System.Text.Json.Serialization;
using DocumentFormat.OpenXml.Presentation;

namespace Web.Services;

public class MQTTPostion
{
    public class Plane
    {
        public string PlaneCode { get; set; }
        public string? PlaneMapPath { get; set; }
        public string? PlaneName { get; set; }
        public double? MapWidth { get; set; }
        public double? MapHeight { get; set; }
        public double? PositionX { get; set; }
        public double? PositionY { get; set; }
    }
    public class Sector
    {
        public string SectorCode { get; set; }
        public string PlaneCode { get; set; }
        public string SectorName { get; set; }
        public string? SectorMapPath { get; set; }
        public double? MapWidth { get; set; }
        public double? MapHeight { get; set; }
        public double? PositionX { get; set; }
        public double? PositionY { get; set; }
        public List<string> DeptCodeList { get; set; }
    }
    public class @Object
    {
        public class TaskInfo
        {
            public int? TaskId { get; set; }
            public string ServiceCode { get; set; }
        }
        public string Name { get; set; } = null!;
        public string? GroupCode { get; set; }
        /// <summary>
        /// 1:協助人員; 2:傳送; 3:院內人員;4:設備; 5:病患; 0:空間
        /// </summary>
        public string? ObjectType { get; set; }
        public string Pid { get; set; }
        public List<TaskInfo> TaskList { get; set; }
        public string UsageDepartCode { get; set; }
    }
    public class Station
    {
        public string SID { get; set; }
        public List<Sector> SectorList { get; set; }
        public string? LocCode { get; set; }
        public string? LocName { get; set; }
        public double? DiffPositionX { get; set; }
        public double? DiffPositionY { get; set; }
    }

    public class DepartSector
    {
        public string DeptCode { get; set; }
        public string SectorCode { get; set; }
    }

    public @Object @object { get; set; }
    public List<Sector> sectorList { get; set; }
    public Plane plane { get; set; }
    public Station station { get; set; }
    public string latestPositionTime { get; set; }
}

public class MQTTTask
{
    public long taskId { get; set; }
    public string? action { get; set; }
    public int? deleteReason { get; set; }
    public string? serviceCode { get; set; }
    public string? eventName { get; set; }
    public string? eventCode { get; set; }
    public double? startedTime { get; set; }
    public List<Sponsor>? sponsors { get; set; }
    public extra extra { get; set; }
}

public class Sponsor
{
    public string? objectCode { get; set; }
    public string? objectName { get; set; }
    public string? devicePid { get; set; }
}

public class OutSignalRData
{
    public string? startTime { get; set; }
    public string? serviceCode { get; set; }
    public string? eventCode { get; set; }
    public string? locationName { get; set; } = null;
    public long taskId { get; set; }
    public string? pid { get; set; }
    public string? objectName { get; set; }
    public string? planeMapPath { get; set; } = null;
    public string? mapWidth { get; set; } = null;
    public string? mapHeight { get; set; } = null;
    public string? positionX { get; set; } = null;
    public string? positionY { get; set; } = null;
}

public class position
{
    public class p1
    {
        public string bn { get; set; }
        public long bt { get; set; }
        public string n { get; set; }
        public string vs { get; set; }
    }
    public class p2
    {
        public string n { get; set; }
        public string vs { get; set; }
    }
    public class p3
    {
        public string n { get; set; }
        public float v { get; set; }
    }
    public class p4
    {
        public string n { get; set; }
        public float v { get; set; }
    }

    public p1 p_1 { get; set; }
    public p2 p_2 { get; set; }
    public p3 p_3 { get; set; }
    public p4 p_4 { get; set; }


}

public class DeviceRaw
{
    public long? bt { get; set; }
    public string bn { get; set; }
    public string n { get; set; }
    public double v { get; set; }
    public string vs { get; set; }
    public bool vb { get; set; }
}

public class DeviceRawSignalR
{
    public string Pid { get; set; }
    public string ResourceId { get; set; }
    public double Value { get; set; }
    public string ValueString { get; set; }
    public bool ValueBoolean { get; set; }
    public string UsageDepartCode { get; set; }
    public long? timestampMs { get; set; }
}

public class MqttBackgroundService : BackgroundService
{
    private readonly AppInfo _appInfo;
    private readonly IHubContext<MessageHub> _hubContext;
    private readonly MqttLogService _logService;
    private readonly MqttServerLogService _serverLogService;
    private readonly MQTTParam _mqttParam;
    private readonly MQTTServerParam _mqttServerParam;
    private readonly IServiceProvider _serviceProvider;
    private readonly SignalRGroupService _signalRGroupService;
    private IMqttServer _mqttServer;
    private Dictionary<string, IMqttClient> _mqttClients = new Dictionary<string, IMqttClient>();
    private readonly FusionS3HMNContext _dbContext;
    private readonly Dictionary<string, UserDatum> _mqttServerUserMap;
    private CancellationTokenSource _cancellationTokenSource;
    private Dictionary<string, bool> _isConnectingOrDisconnecting = new Dictionary<string, bool>();
    private Timer _dataSyncTimer;

    public MqttBackgroundService(
        IOptions<AppInfo> appInfo,
        IHubContext<MessageHub> hubContext,
        IOptions<MQTTParam> mqttParam,
        IOptions<MQTTServerParam> mqttServerParam,
        IServiceProvider serviceProvider,
        IDbContextFactory<FusionS3HMNContext> contextFactory,
        SignalRGroupService signalRGroupService)
    {
        _appInfo = appInfo.Value;
        _hubContext = hubContext;
        _mqttParam = mqttParam.Value;
        _mqttServerParam = mqttServerParam.Value;
        _serviceProvider = serviceProvider;
        _dbContext = contextFactory.CreateDbContext();
        _signalRGroupService = signalRGroupService;
        _logService = CreateLogService();
        _serverLogService = CreateServerLogService();
        _cancellationTokenSource = new CancellationTokenSource();
        _mqttServerUserMap = new Dictionary<string, UserDatum>();
    }

    private MqttLogService CreateLogService()
    {
        using (var scope = _serviceProvider.CreateScope())
        {
            return scope.ServiceProvider.GetRequiredService<MqttLogService>();
        }
    }

    private MqttServerLogService CreateServerLogService()
    {
        using (var scope = _serviceProvider.CreateScope())
        {
            return scope.ServiceProvider.GetRequiredService<MqttServerLogService>();
        }
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        string logActionName = $"{GetType().Name}/ExecuteAsync";
        string? requestUUID = Guid.NewGuid().ToString();
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 當_mqttParam.Enable有指定且指定值為N時，不啟動MQTT服務
        if (_mqttParam.Enable != null && _mqttParam.Enable == "N")
        {
            _logService.Logging("info", logActionName, requestUUID, "MQTT service is disabled.");
            _logService.Logging("info", logActionName, requestUUID, "End");
            return;
        }

        // 使用Timer和while循环两种方式实现定时任务的主要差异:
        // 1. 执行方式: Timer在后台线程执行,while循环在当前线程执行
        // 2. 执行频率: Timer可指定首次延迟和间隔时间,while循环使用Task.Delay引入固定延迟
        // 3. 取消支持: Timer需手动处理取消,while循环可通过检查stoppingToken实现取消
        // 4. 异常处理: Timer需在回调方法内处理异常,while循环可在循环内使用try-catch处理异常
        // 选择取决于需求和场景,在后台服务中通常建议使用while循环方式,以便更好地集成服务生命周期和支持优雅取消。
        // 但是,如果定时任务的执行频率较低,并且不需要及时响应取消请求,使用Timer方式也是一个合适的选择。
        _dataSyncTimer = new Timer(SyncFromFusionTask, null, 0, 600000);

        using var scope = _serviceProvider.CreateScope();
        using var context = scope.ServiceProvider.GetRequiredService<FusionS3HMNContext>();

        var orgList = await context.Organizations.ToListAsync();
        var mqttFactory = new MqttFactory();

        // 啟動 MQTT Server
        _mqttServer = mqttFactory.CreateMqttServer();
        await StartMQTTServer();

        foreach (var org in orgList)
        {
            if (org.AppCode == null)
                continue;
            string appCode = org.AppCode;

            _ = Task.Run(async () =>
            {
                string localRequestUUID = Guid.NewGuid().ToString();
                var mqttClient = mqttFactory.CreateMqttClient();

                var mqttClientOptionsBuilder = new MqttClientOptionsBuilder()
                    .WithTcpServer(_mqttParam.HostIp, _mqttParam.HostPort)
                    .WithClientId(Guid.NewGuid().ToString())
                    .WithCleanSession();

                if (_mqttParam.UseTLS == "Y" && !string.IsNullOrEmpty(_mqttParam.SelfCertificateFile))
                {
                    mqttClientOptionsBuilder.WithTls(new MqttClientOptionsBuilderTlsParameters
                    {
                        UseTls = _mqttParam.UseTLS == "Y",
                        Certificates = new List<X509Certificate>
                        {
                            new X509Certificate2(_mqttParam.SelfCertificateFile)
                        },
                        CertificateValidationHandler = context => true
                    });
                }

                IMqttClientOptions mqttClientOptions = mqttClientOptionsBuilder
                    .WithCredentials(org?.LicenseCode ?? "", org?.LicenseKey ?? "")
                    .Build();

                mqttClient.ApplicationMessageReceivedHandler = new MqttApplicationMessageReceivedHandlerDelegate(new Action<MqttApplicationMessageReceivedEventArgs>(MqttMessageReceive));

                // 儲存到全域變數 _mqttClients 字典
                _mqttClients[appCode] = mqttClient;

                // 連接MQTT Broker
                await ConnectToMqttBroker(appCode, mqttClientOptions, stoppingToken);

                // 訂閱MQTT主題
                await SubscribeTopic(localRequestUUID, mqttFactory, appCode, stoppingToken);

                // 依指定時間間隔檢查MQTT連線狀態及是否有被要求停止
                while (!stoppingToken.IsCancellationRequested)
                {
                    try
                    {
                        // Log MQTT connection status
                        _logService.Logging("info", logActionName, localRequestUUID, $"[{appCode}] MQTT connection status: {mqttClient.IsConnected}");

                        // 檢查是否已連線
                        if (!mqttClient.IsConnected)
                        {
                            // 如果MQTT已斷線，則再檢查是否已在連線中，避免重複連線
                            if (!_isConnectingOrDisconnecting.ContainsKey(appCode) || !_isConnectingOrDisconnecting[appCode])
                            {
                                _isConnectingOrDisconnecting[appCode] = true;

                                _logService.Logging("warning", logActionName, localRequestUUID, $"[{appCode}] MQTT connection lost, trying to reconnect...");

                                // 重新連線
                                await ConnectToMqttBroker(appCode, mqttClientOptions, stoppingToken);

                                // 重新訂閱（之前測試過，斷線後重新連線，訂閱會自動接上，但現在不會，所以加上重新訂閱，目前測試重複訂閱正常@20240808）
                                await SubscribeTopic(localRequestUUID, mqttFactory, appCode, stoppingToken);

                                _isConnectingOrDisconnecting[appCode] = false;
                            }
                        }
                        else
                        {
                            // 如果MQTT連線正常，則記錄log
                            _logService.Logging("info", logActionName, localRequestUUID, $"[{appCode}] MQTT connection is normal.");
                        }
                    }
                    catch (Exception ex)
                    {
                        _isConnectingOrDisconnecting[appCode] = false;
                        _logService.Logging("error", logActionName, localRequestUUID, $"[{org.AppCode}] Error checking MQTT connection: {ex.Message}");
                    }

                    await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
                }

                await mqttClient.DisconnectAsync(options: null, _cancellationTokenSource.Token);

            }, stoppingToken);
        }
    }

    public async Task StartMQTTServer()
    {
        string logActionName = $"{GetType().Name}/StartMQTTServer";
        try
        {
            _serverLogService.Logging("info", logActionName, "", "Start");

            // 設定 MQTT server 的運作環境參數
            var optionsBuilder = new MqttServerOptionsBuilder()
                .WithConnectionBacklog(_mqttServerParam.ConnectionBacklog) // 設置要保留的連線數
                .WithDefaultEndpointPort(int.Parse(_mqttServerParam.HostPort)) // 設置host port
                .WithMaxPendingMessagesPerClient(_mqttServerParam.MaxPendingMessagesPerClient) // 設置每個客户端允许最多未决消息
                .WithConnectionValidator(c => // 驗證連線使用者
                {
                    var token = c.ClientId.Split(':')[0];

                    if (string.IsNullOrEmpty(token) || token.Length < 10)
                    {
                        c.ReasonCode = MqttConnectReasonCode.ClientIdentifierNotValid;
                        return;
                    }

                    try
                    {
                        DateTime sysDateTime = DateTime.Now;
                        // 查詢使用者權杖
                        UserToken userTokenData = _dbContext.UserTokens.Where(x => x.Token == token && x.TokenDeleted == false).FirstOrDefault();
                        if (userTokenData == null)
                        {
                            _serverLogService.Logging("warn", logActionName, c.ClientId, String.Format("Client({0}) Connection Valida Failer:Token Cannot Found", c.ClientId));
                            c.ReasonCode = MqttConnectReasonCode.ClientIdentifierNotValid;
                            return;
                        }
                        else if (DateTime.Compare((DateTime)userTokenData.TokenExpiryDate, sysDateTime) < 0)
                        {
                            _serverLogService.Logging("warn", logActionName, c.ClientId, String.Format("Client({0}) Connection Valida Failer:Token Expired", c.ClientId));
                            c.ReasonCode = MqttConnectReasonCode.ClientIdentifierNotValid;
                            return;
                        }
                        else
                        {
                            UserDatum userData = _dbContext.UserData.Where(x => x.Id == userTokenData.UserDataId).FirstOrDefault();
                            if (userData == null)
                            {
                                _serverLogService.Logging("warn", logActionName, c.ClientId, String.Format("Client({0}) Connection Valida Failer:User({1}) Can't Found", c.ClientId, userTokenData.UserDataId));
                                c.ReasonCode = MqttConnectReasonCode.BadUserNameOrPassword;
                                return;
                            }

                            // 連線成功者加入資訊
                            if (_mqttServerUserMap.ContainsKey(c.ClientId))
                            {
                                _mqttServerUserMap.Remove(c.ClientId);
                            }
                            _mqttServerUserMap.Add(c.ClientId, userData);
                        }

                        c.ReasonCode = MqttConnectReasonCode.Success;
                    }
                    catch (Exception e)
                    {
                        _serverLogService.Logging("error", logActionName, c.ClientId, String.Format("Client({0}) Connection Valida Failer:{1}", c.ClientId, e.ToString()));
                        c.ReasonCode = MqttConnectReasonCode.UnspecifiedError;
                        return;
                    }
                });

            // 綁定當訊息送到此伺服器之後的事件，定義該做甚麼事情
            _mqttServer.UseApplicationMessageReceivedHandler(async e =>
            {
                // 對於此訊息的 Payload 部分，將會是經過編碼的，因此需要先解碼
                var payload = Encoding.UTF8.GetString(e.ApplicationMessage.Payload);
                var topic = e.ApplicationMessage.Topic;
                _serverLogService.Logging("info", logActionName, e.ClientId, String.Format("Client({0}) Send Message:Topic({1})/Payload({2})", e.ClientId, topic, payload));
                if (!string.IsNullOrEmpty(e.ClientId))
                {
                    UserDatum userData = _mqttServerUserMap[e.ClientId];
                    switch (topic)
                    {
                        case "GetSubscribeList":
                            // 檢核帳號密碼
                            try
                            {
                                // 預設單位資訊
                                List<string> defaultSubscribeList = new List<string>();
                                defaultSubscribeList.Add(string.Format("Task/{0}/{1}/{2}", userData.AppCode, userData.AreaCode, userData.DeptCode));
                                defaultSubscribeList.Add(string.Format("DevicePosition/{0}/{1}/{2}", userData.AppCode, userData.AreaCode, userData.DeptCode));
                                // 讀取所有管理單位
                                List<string> userDeptList = userData.IsAdmin == "A" ? _dbContext.Departments.Where(x => x.AppCode == userData.AppCode).Select(x => string.Format("{0}/{1}/{2}", x.AppCode, x.AreaCode, x.DeptCode)).Distinct().ToList()
                                     : _dbContext.VwUserDeptMonInfos.Where(x => x.AppCode == userData.AppCode && x.UserAccount.ToUpper() == userData.UserAccount.ToUpper()).Select(x => string.Format("{0}/{1}/{2}", x.AppCode, x.AreaCode, x.DeptCode)).Distinct().ToList();

                                // 發佈驗證成功的訊息
                                if (userDeptList != null && userDeptList.Count > 0)
                                {
                                    List<string> querySubscribeList = new List<string>();
                                    foreach (var userDept in userDeptList)
                                    {
                                        querySubscribeList.Add(string.Format("Task/{0}", userDept));
                                        querySubscribeList.Add(string.Format("DevicePosition/{0}", userDept));
                                    }
                                    await ServerPublishMessage(string.Format("GetSubscribeList/Success/{0}", e.ClientId), Newtonsoft.Json.JsonConvert.SerializeObject(querySubscribeList));
                                }
                                else
                                {
                                    await ServerPublishMessage(string.Format("GetSubscribeList/Success/{0}", e.ClientId), Newtonsoft.Json.JsonConvert.SerializeObject(defaultSubscribeList));
                                }
                                _serverLogService.Logging("info", logActionName, e.ClientId, String.Format("Client({0}) GetSubscribeList Success", e.ClientId));
                            }
                            catch (Exception ex)
                            {
                                _serverLogService.Logging("warn", logActionName, e.ClientId, String.Format("Client({0}) GetSubscribeList Failer:{1},{2}", e.ClientId, ex.ToString(), DateTimeOffset.Now));
                            }
                            break;
                        default:
                            _serverLogService.Logging("warn", logActionName, e.ClientId, String.Format("Client({0}) Send Message:Topic({1}) Undefined", e.ClientId, topic));
                            break;
                    }
                }
            });

            // 一旦有用戶端連線上來之後，將會觸發這個事件
            _mqttServer.UseClientConnectedHandler(e =>
            {
                _serverLogService.Logging("info", logActionName, e.ClientId, String.Format("Client Connected:ClientId({0})", e.ClientId));
            });

            // 啟動這個 MQTT 伺服器
            _mqttServer.StartAsync(optionsBuilder.Build());

            _serverLogService.Logging("info", logActionName, "", String.Format("MQTT Server Start on Port {0}", _mqttServerParam.HostPort));
        }
        catch (Exception ex)
        {
            _serverLogService.Logging("info", logActionName, "", String.Format("MQTT Server Start Failer:{0},{1}", ex.ToString(), DateTimeOffset.Now));
        }
    }

    private async Task ServerPublishMessage(string topic, string message)
    {
        string logActionName = $"{GetType().Name}/ServerPublishMessage";

        // 產生一個 MQTT 訊息
        var mqttMessage = new MqttApplicationMessageBuilder()
                            .WithTopic(topic)
                            .WithPayload(message)
                            .WithAtLeastOnceQoS()
                            .WithRetainFlag(false)
                            .WithDupFlag(false)
                            .Build();

        // 發佈此剛剛建立的訊息
        var result = await _mqttServer.PublishAsync(mqttMessage, CancellationToken.None);

        if (result.ReasonCode == MQTTnet.Client.Publishing.MqttClientPublishReasonCode.Success)
            _serverLogService.Logging("info", logActionName, "", String.Format("Server Message published:[{0}]{1}", topic, message));
    }

    public async Task ServerPublishTaskMessage(string appCode, MQTTTask mqttTask)
    {
        // 廣播新任務至該訂閱者
        if (_mqttServer != null && _mqttServer.IsStarted && mqttTask.taskId > 0)
        {
            var devicePid = mqttTask.sponsors.FirstOrDefault().devicePid;
            var objectCode = mqttTask.sponsors.FirstOrDefault().objectCode;
            List<string> publishTopicList = new List<string>();
            // 裝置單位
            if (!string.IsNullOrEmpty(devicePid))
            {
                var deviceData = await _dbContext.Devices.Where(x => x.AppCode == appCode && x.Pid == devicePid).FirstOrDefaultAsync();
                if (deviceData != null)
                {
                    // 管理單位
                    string deviceManageDepartPublishTopic = string.Format("Task/{0}/{1}/{2}", deviceData.AppCode, deviceData.AreaCode, deviceData.ManageDepartCode);
                    if (!publishTopicList.Contains(deviceManageDepartPublishTopic))
                    {
                        publishTopicList.Add(deviceManageDepartPublishTopic);
                    }
                    // 使用單位
                    string deviceUsageDepartPublishTopic = string.Format("Task/{0}/{1}/{2}", deviceData.AppCode, deviceData.AreaCode, deviceData.UsageDepartCode);
                    if (!publishTopicList.Contains(deviceUsageDepartPublishTopic))
                    {
                        publishTopicList.Add(deviceUsageDepartPublishTopic);
                    }
                }
            }
            // 對象單位
            if (!string.IsNullOrEmpty(objectCode))
            {
                var objectData = await _dbContext.ObjectData.Where(x => x.AppCode == appCode && x.ObjectCode == objectCode).FirstOrDefaultAsync();
                if (objectData != null)
                {
                    // 使用單位
                    string objectUsageDepartPublishTopic = string.Format("Task/{0}/{1}/{2}", objectData.AppCode, objectData.AreaCode, objectData.UsageDepartCode);
                    if (!publishTopicList.Contains(objectUsageDepartPublishTopic))
                    {
                        publishTopicList.Add(objectUsageDepartPublishTopic);
                    }
                }
            }
            // 等待資料寫入
            int waitCount = 5;
            for (int i = 0; i < waitCount; i++)
            {
                TaskDatum taskData = await _dbContext.TaskData.Where(x => x.AppCode == appCode && x.TaskId == mqttTask.taskId).FirstOrDefaultAsync();
                if (taskData != null)
                {
                    // 依序發佈各單位廣播訊息
                    foreach (var item in publishTopicList)
                    {
                        await ServerPublishMessage(item, taskData.Id.ToString());
                    }
                    break;
                }
                Thread.Sleep(500);
            }
        }
    }

    public async Task ServerPublishDevicePositionMessage(string appCode, string devicePid)
    {
        // 廣播新任務至該訂閱者
        if (_mqttServer != null && _mqttServer.IsStarted && !string.IsNullOrEmpty(devicePid))
        {
            List<string> publishTopicList = new List<string>();
            // 裝置單位
            if (!string.IsNullOrEmpty(devicePid))
            {
                var deviceData = await _dbContext.Devices.Where(x => x.AppCode == appCode && x.Pid == devicePid).FirstOrDefaultAsync();
                if (deviceData != null)
                {
                    // 管理單位
                    string deviceManageDepartPublishTopic = string.Format("DevicePosition/{0}/{1}/{2}", deviceData.AppCode, deviceData.AreaCode, deviceData.ManageDepartCode);
                    if (!publishTopicList.Contains(deviceManageDepartPublishTopic))
                    {
                        publishTopicList.Add(deviceManageDepartPublishTopic);
                    }
                    // 使用單位
                    string deviceUsageDepartPublishTopic = string.Format("DevicePosition/{0}/{1}/{2}", deviceData.AppCode, deviceData.AreaCode, deviceData.UsageDepartCode);
                    if (!publishTopicList.Contains(deviceUsageDepartPublishTopic))
                    {
                        publishTopicList.Add(deviceUsageDepartPublishTopic);
                    }
                }
            }
            // 依序發佈各單位廣播訊息
            foreach (var item in publishTopicList)
            {
                await ServerPublishMessage(item, devicePid);
            }
        }
    }

    /// <summary>
    /// 訂閱MQTT主題 event/position/station-registration
    /// </summary>
    /// <param name="requestUUID"></param>
    /// <param name="mqttFactory"></param>
    /// <param name="appCode"></param>
    /// <param name="stoppingToken"></param>
    /// <returns></returns>
    private async Task SubscribeTopic(string requestUUID, MqttFactory mqttFactory, string appCode, CancellationToken stoppingToken)
    {
        string logActionName = $"{GetType().Name}/SubscribeTopic";

        _logService.Logging("info", logActionName, requestUUID, "Start");

        var staticTopicFilters = new List<string>
        {
            $"{appCode.ToLower()}/event/#",
            $"{appCode.ToLower()}/device-position/global/#",
            $"{appCode.ToLower()}/station-registration/#",
            $"{appCode.ToLower()}/device-raw/+/mmwave/#", // 訂閱 MmWave DeviceType topic
        };

        using var scope = _serviceProvider.CreateScope();
        using var context = scope.ServiceProvider.GetRequiredService<FusionS3HMNContext>();

        // 取得TempHudDeviceType
        var globalSysPara = await context.SysParameters.FirstOrDefaultAsync(e => e.ParaCode == "TempHudDeviceType");

        // 取得TempHudDeviceType
        string tempHudDeviceTypeStr = globalSysPara?.ParaValue ?? "[]";

        // 將TempHudDeviceType轉換為List<string>
        var tempHudDeviceTypes = JsonSerializer.Deserialize<List<string>>(tempHudDeviceTypeStr);

        // 將TempHudDeviceType轉換為主題過濾器
        List<string> tempHudDeviceTypeList = tempHudDeviceTypes.Select(t => $"{appCode.ToLower()}/device-raw/global/{t}/#").ToList();

        // 合併固定和動態的主題過濾器
        var allTopicFilters = staticTopicFilters.Concat(tempHudDeviceTypeList).ToList();

        // 取得PhysiologicDeviceType
        globalSysPara = await context.SysParameters.FirstOrDefaultAsync(e => e.ParaCode == "PhysiologicDeviceType");

        // 取得PhysiologicDeviceType
        string physiologicDeviceTypeStr = globalSysPara?.ParaValue ?? "[]";

        // 將PhysiologicDeviceType轉換為List<string>
        var physiologicDeviceTypes = JsonSerializer.Deserialize<List<string>>(physiologicDeviceTypeStr);

        // 將PhysiologicDeviceType轉換為主題過濾器
        List<string> physiologicDeviceTypeList = physiologicDeviceTypes.Select(t => $"{appCode.ToLower()}/device-raw/global/{t}/#").ToList();

        // 合併固定和動態的主題過濾器
        allTopicFilters = allTopicFilters.Concat(physiologicDeviceTypeList).ToList();

        // 使用合併後的主題過濾器創建訂閱選項
        var mqttSubscribeOptions = allTopicFilters.Aggregate(
            mqttFactory.CreateSubscribeOptionsBuilder(),
            (builder, topic) => builder.WithTopicFilter(f => f.WithTopic(topic)),
            builder => builder.Build());

        if (_mqttClients.TryGetValue(appCode, out var mqttClient))
        {
            await mqttClient.SubscribeAsync(mqttSubscribeOptions, stoppingToken);
            _logService.Logging("info", logActionName, requestUUID, $"Subscribing to MQTT topic:{appCode.ToLower()}/event/#");
            _logService.Logging("info", logActionName, requestUUID, $"Subscribing to MQTT topic:{appCode.ToLower()}/device-position/global/#");
            _logService.Logging("info", logActionName, requestUUID, $"Subscribing to MQTT topic:{appCode.ToLower()}/station-registration/#");
        }
    }

    private string GetStationSidByServiceCode(string serviceCode, extra extra)
    {
        string stationSid = string.Empty;
        //進入圍籬是要記錄進到那一個Station
        if (serviceCode == "Enter")
        {
            stationSid = extra.digitalFenceEnter.toSid;
        }
        //離開圍籬是要記錄離開那一個Station
        else if (serviceCode == "Leave")
        {
            stationSid = extra.digitalFenceLeave.fromSid;
        }
        //求救是要記錄最近的Station
        else if (serviceCode == "Help")
        {
            stationSid = extra.help.nearestSid;
        }
        //長按比照求救是要記錄最近的Station
        else if (serviceCode == "LongPress")
        {
            stationSid = extra.longPress.nearestSid;
        }
        else if (serviceCode == "mmWaveFallDetection")
        {
            stationSid = extra.mmWaveFallDetection.sid;
        }
        else if (serviceCode == "mmWaveLeaveBed")
        {
            stationSid = extra.mmWaveLeaveBed.sid;
        }
        else if (serviceCode == "mmWaveStayTimeout")
        {
            stationSid = extra.mmWaveStayTimeout.sid;
        }
        else if (serviceCode == "AbnormalStation")
        {
            stationSid = extra.abnormalStation.sid;
        }
        return stationSid;
    }

    private async void SyncFromFusionTask(object state)
    {
        string logActionName = $"{GetType().Name}/SyncFromFusionTask";
        string? requestUUID = Guid.NewGuid().ToString();
        _logService.Logging("info", logActionName, requestUUID, "Start");

        using var scope = _serviceProvider.CreateScope();
        using var context = scope.ServiceProvider.GetRequiredService<FusionS3HMNContext>();
        ITaskService? taskService = scope.ServiceProvider.GetRequiredService<ITaskService>();

        var orgList = await context.Organizations.ToListAsync();

        foreach (var org in orgList)
        {
            if (org.AppCode == null)
                continue;
            string appCode = org.AppCode;

            string localRequestUUID = Guid.NewGuid().ToString();

            try
            {
                // 取得HMN目前TaskData Table中最大值的TaskId
                int? maxTaskId = await context.TaskData.Where(x => x.AppCode == appCode).MaxAsync(x => x.TaskId);

                maxTaskId = maxTaskId == null ? int.MinValue : maxTaskId;

                _logService.Logging("info", logActionName, localRequestUUID, $"[{appCode}] Trying to sync data from Fusion...now HMN Max TaskId:{maxTaskId} ");

                // 取得大於指定TaskId的Fusion Task
                // 這裡會有種情況會導致資料未完全同步，就是假設SyncFromFusionTask每10分鐘執行一次，但是MQTT在10分鐘斷線次數大於1次，這樣會導致資料未完全同步
                // 例如第一次斷線時，MaxTask=100，但Timer時間未到，有事件發生TaskId=101，MQTT又連線，事件發生TaskId=102會被寫入TaskData
                // 但是Timer時間到，執行SyncFromFusionTask，這時取得的MaxTaskId=102，所以不會取得TaskId=101的事件
                // 與Ann 討論過，但是Ann認為不處理這個問題，因為這個問題發生的機率很低 @20240605
                // 上述情況在廠內因進入/離開圍籬事件發生頻率高，所以會有這個問題，如下列情況
                // （10:00:00 TaskId 1000 Help MQTT沒收到，10:00:03 TaskId 1001 Leave 產生且有收到MQTT，所以1000 就不會被檢測到）
                var taskList = await taskService.GetTaskList(appCode, $"id gt {maxTaskId} and code like '@HMN@'");

                // 取得HMN的Object,Device,Area資料，用來轉換Fusion Task
                var objects = context.ObjectData.Where(x => x.AppCode == appCode);
                var devices = context.Devices.Where(x => x.AppCode == appCode);
                var areas = context.Areas.Where(x => x.AppCode == appCode);

                if (taskList != null && taskList.Count > 0)
                {

                    _logService.Logging("info", logActionName, localRequestUUID, $"[{appCode}] more greater HMN TaskID Fusion TaskList Count:{taskList.Count}");

                    foreach (var task in taskList)
                    {
                        // 將Fusion Task轉換為HMN Task
                        TaskDatum taskDatum = await ProcessFusionTask(appCode, objects, devices, areas, task);

                        // 判斷如果TaskI不存在就新增
                        int TaskId = await CreateTaskWithoutTaskId(localRequestUUID, taskDatum);

                        if (TaskId == 999999999)
                        {
                            _logService.Logging("error", logActionName, localRequestUUID, $"[{appCode}] TaskId:{task.id},TaskId exists in TaskData");
                        }
                        else
                        {
                            _logService.Logging("info", logActionName, localRequestUUID, $"[{appCode}] create Task ,TaskId:{TaskId}");

                            // 發送事件通知
                            if (taskDatum.TaskId.HasValue)
                            {
                                await SendEventNotifyMessages(requestUUID, appCode, taskDatum.TaskId.Value);
                            }
                        }
                    }
                }
                else
                {
                    _logService.Logging("info", logActionName, localRequestUUID, $"[{appCode}] No new Task from Fusion");
                }

                // 檢查 HMN Task 是否有未解除的事件 action = 10 但是 console 已經解除 action = 30，有的畫同步事件狀態到 HMN
                var dataAccessService = scope.ServiceProvider.GetRequiredService<IDataAccessService>();

                // 取得 HMN Task 未解除 action = 10 的事件列表
                var taskDataList = await dataAccessService.Fetch<TaskDatum>(e => e.AppCode == appCode && e.Action == 10).AsTracking().ToListAsync();
                if (taskDataList.Count() > 0)
                {
                    // 呼叫 console 取得這些事件狀態為已解除 action = 30 的事件列表
                    string taskIdString = string.Join(",", taskDataList.Select(x => x.TaskId).Where(id => id.HasValue).Select(id => id.Value));
                    taskList = await taskService.GetTaskList(appCode, $"active eq true and id in {taskIdString} and action eq 30");

                    var clearedTaskDataList = taskDataList.Where(x => taskList.Any(t => t.id == x.TaskId)).ToList();

                    if (clearedTaskDataList.Count() > 0)
                    {
                        _logService.Logging("info", logActionName, localRequestUUID, $"[{appCode}] Found {taskDataList.Count()} unresolved task datum but already resolved in Fusion, start to resolve");

                        dataAccessService.BeginTransaction();

                        // 解除 HMN 事件
                        foreach (var taskData in clearedTaskDataList)
                        {
                            List<string> updateField = new List<string>();

                            updateField.Add("Action");
                            taskData.Action = 30;

                            updateField.Add("FinishesAt");
                            taskData.FinishesAt = DateTime.Now;

                            updateField.Add("ModifiesAt");
                            taskData.ModifiesAt = DateTime.Now;

                            await dataAccessService.UpdateAsync<TaskDatum>(taskData, updateField.ToArray());
                        }

                        await dataAccessService.CommitAsync();

                        // 解除事件發送 SignalR 通知
                        foreach (var taskData in clearedTaskDataList)
                        {
                            _logService.Logging("info", logActionName, localRequestUUID, $"[{appCode}] TaskClearSendSignalR--{DateTimeOffset.Now}--TaskId:{taskData.TaskId}");
                            await SendClearTaskWithSignalR(requestUUID, appCode, taskData.TaskId.Value, taskData.Action.Value.ToString(), 2); // 2 自動解除
                        }

                        _logService.Logging("info", logActionName, localRequestUUID, $"[{appCode}] {taskDataList.Count()} unresolved task datum was resolved by SyncFromFusionTask.");
                    }
                }

                _logService.Logging("info", logActionName, localRequestUUID, $"End");
            }
            catch (Exception ex)
            {
                _logService.Logging("error", $"{GetType().Name}/SyncDataAsync", localRequestUUID, $"[{appCode}] Data synchronization encountered an error: {ex.Message}");
            }
        }
    }

    private async Task<TaskDatum> ProcessFusionTask(string appCode, IQueryable<ObjectDatum> objects, IQueryable<Device> devices, IQueryable<Area> areas, FusionTask task)
    {
        var pid = task.sponsorObjects?.FirstOrDefault()?.devices?.FirstOrDefault()?.pid ?? "";
        var objectCode = task.sponsorObjects?.FirstOrDefault()?.code ?? "";

        using var scope = _serviceProvider.CreateScope();
        using var context = scope.ServiceProvider.GetRequiredService<FusionS3HMNContext>();

        var device = devices.FirstOrDefault(x => x.Pid == pid) ?? new Device();
        var objectData = objects.FirstOrDefault(x => x.ObjectCode == objectCode) ?? new ObjectDatum();
        var dept = await context.Departments.FirstOrDefaultAsync(x => x.DeptCode == device.UsageDepartCode) ?? new Department();

        var stationSid = GetStationSidByServiceCode(task.serviceCode, task.extra);

        var station = await context.Stations.FirstOrDefaultAsync(x => x.SID == stationSid) ?? new Station();

        var sectorStation = await context.SectorStations.FirstOrDefaultAsync(x => x.Stationsid == stationSid) ?? new SectorStation();
        var location = await context.Locations.FirstOrDefaultAsync(x => x.LocCode == station.RegionCode) ?? new Location();
        var sector = await context.Sectors.FirstOrDefaultAsync(x => x.SectorCode == sectorStation.SectorCode) ?? new Sector();
        var plane = await context.Planes.FirstOrDefaultAsync(x => x.PlaneCode == station.PlaneCode) ?? new Plane();
        var building = await context.Buildings.FirstOrDefaultAsync(x => x.BuildingCode == plane.BuildingCode) ?? new Building();

        var areaCode = !string.IsNullOrEmpty(station.AreaCode) ? station.AreaCode : device.AreaCode;
        var taskDatum = new TaskDatum
        {
            TaskId = task.id,
            AppCode = appCode,

            SponsorObjectCode = objectData.ObjectCode,
            SponsorObjectName = objectData.Name,
            SponsorObjectType = objectData.ObjectType,

            SponsorDevicePid = device.Pid,
            SponsorDeviceName = device.Name,
            SponsorDeviceType = device.DeviceType,

            EventCode = task.eventCode,
            EventName = task.eventName,

            AreaCode = areaCode,
            DeptCode = device.UsageDepartCode,
            DeptName = dept.DeptName,

            AreaName = (await areas.Where(a => a.AreaCode == areaCode).FirstOrDefaultAsync())?.AreaName,

            BuildingCode = building.BuildingCode,
            BuildingName = building.BuildingName,
            PlaneCode = plane.PlaneCode,
            PlaneName = plane.PlaneName,
            PlaneMapPath = plane.PlaneMapPath,
            MapWidth = plane.MapWidth,
            MapHeight = plane.MapHeight,
            PositionX = plane.PositionX,
            PositionY = plane.PositionY,
            Action = task.action,
            Active = task.active,
            ServiceCode = task.serviceCode,
            StartsAt = task.startsAt,
            FinishesAt = task.finishesAt == DateTime.MinValue ? null : task.finishesAt,
            ModifiesAt = task.modifiesAt == DateTime.MinValue ? null : task.modifiesAt,
            SponsorStation = stationSid,
            DiffPositionX = station.DiffPositionX,
            DiffPositionY = station.DiffPositionY,
            SponsorLocationCode = location.LocCode,
            SponsorLocationName = location.LocName,
        };

        return taskDatum;
    }

    public async Task<int> CreateTaskWithoutTaskId(string requestUUID, TaskDatum newTask)
    {
        string logActionName = $"{GetType().Name}/CreateTaskWithoutTaskId";
        _logService.Logging("info", logActionName, requestUUID, "Start");

        int taskId = 0;

        try
        {
            using var scope = _serviceProvider.CreateScope();
            using var context = scope.ServiceProvider.GetRequiredService<FusionS3HMNContext>();

            var taskData = await context.TaskData.FirstOrDefaultAsync(x => x.TaskId == newTask.TaskId);

            //如果資料已存在就不新增
            if (taskData != null)
            {
                _logService.Logging("error", logActionName, requestUUID, $"Task id exists in TaskData,TaskId:{newTask.TaskId}");
                return 999999999;
            }

            context.Add(newTask);
            taskId = await context.SaveChangesAsync();
        }
        catch (Exception e)
        {
            //// 如果不是因為TaskId重複的錯誤，就拋出例外 "23505: duplicate key value violates unique constraint \"task_id_unique\"
            //if (e.InnerException?.Message.Contains("23505") != true &&
            //    e.InnerException?.Message.Contains("task_id_unique") != true)
            //{
            //    throw;
            //}

            //// 如果TaskId重複，就將TaskId設為9999(有可能是同時啟動多台IIS)
            //_logService.Logging("Warning", logActionName, requestUUID, $"TaskId exists in TaskData,TaskId:{mqtt.taskId}");

            _logService.Logging("error", logActionName, requestUUID, $"TaskId:{newTask.TaskId}, Exception:{e}");

            taskId = 999999999;
        }

        _logService.Logging("info", logActionName, requestUUID, "End");

        return taskId;
    }

    private async Task ConnectToMqttBroker(string appCode, IMqttClientOptions mqttClientOptions, CancellationToken stoppingToken)
    {
        string logActionName = $"{GetType().Name}/ConnectToMqttBroker";
        string? requestUUID = Guid.NewGuid().ToString();

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                if (_mqttClients.TryGetValue(appCode, out var mqttClient))
                {
                    await mqttClient.ConnectAsync(mqttClientOptions, _cancellationTokenSource.Token);
                    _logService.Logging("info", logActionName, requestUUID, "Connected to MQTT broker.");
                    break;
                }
            }
            catch (Exception ex)
            {
                _logService.Logging("error", logActionName, requestUUID, $"Error occurred while connecting to MQTT broker: {ex.Message}");
                await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
            }
        }
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        _cancellationTokenSource.Cancel();
        await base.StopAsync(cancellationToken);
    }

    private async void MqttMessageReceive(MqttApplicationMessageReceivedEventArgs e)
    {
        string logActionName = $"{GetType().Name}/MqttMessageReceive";
        string? requestUUID = Guid.NewGuid().ToString();
        _logService.Logging("info", logActionName, requestUUID, "Start");

        try
        {
            var payload = Encoding.UTF8.GetString(e.ApplicationMessage.Payload);
            string topic = e.ApplicationMessage.Topic;

            if (string.IsNullOrEmpty(payload) || string.IsNullOrEmpty(topic))
            {
                _logService.Logging("error", logActionName, requestUUID, "Payload or Topic is null.");
                return;
            }

            if (topic.ToLower().Contains("event"))
            {
                _logService.Logging("info", logActionName, requestUUID, $"Receive Event--top:{topic},payload:{payload},{DateTimeOffset.Now}");
                await ProcessTask(requestUUID, payload, topic);
            }
            else if (topic.ToLower().Contains("position"))
            {
                _logService.Logging("info", logActionName, requestUUID, $"Receive Position--top:{topic},payload:{payload},{DateTimeOffset.Now}");
                await ProcessPosition(requestUUID, payload, topic);
            }
            else if (topic.ToLower().Contains("station-registration/registered"))
            {
                _logService.Logging("info", logActionName, requestUUID, $"Receive station-registration/registered--top:{topic},payload:{payload},{DateTimeOffset.Now}");
                await ProcessStationRegistration(requestUUID, topic);
            }
            else if (topic.ToLower().Contains("device-raw"))
            {
                _logService.Logging("info", logActionName, requestUUID, $"Receive device-raw--top:{topic},payload:{payload},{DateTimeOffset.Now}");
                await ProcessDeviceRaw(requestUUID, payload, topic);
            }
        }
        catch (Exception ex)
        {
            _logService.Logging("error", logActionName, requestUUID, "Error occurred while processing MQTT message. Exception:" + ex.ToString());
        }

        _logService.Logging("info", logActionName, requestUUID, "End");
    }

    private async Task ProcessDeviceRaw(string requestUUID, string payload, string topic)
    {
        string logActionName = $"{GetType().Name}/ProcessDeviceRaw";
        _logService.Logging("info", logActionName, requestUUID, "Start");

        string appCode = topic.Split("/")[0];
        string pid = topic.Split("/")[4];
        string deviceType = topic.Split("/")[3];

        if (string.IsNullOrEmpty(pid))
        {
            _logService.Logging("error", logActionName, requestUUID, $"PID is null,Topic:{topic},PayLoad:{payload},{DateTimeOffset.Now}");
            return;
        }

        var result = JsonSerializer.Deserialize<List<DeviceRaw>>(payload);

        if (result == null || result.Count == 0)
        {
            _logService.Logging("error", logActionName, requestUUID, $"List<DeviceRaw> is null,Topic:{topic},PayLoad:{payload},{DateTimeOffset.Now}");
            return;
        }
        using var scope = _serviceProvider.CreateScope();
        using var context = scope.ServiceProvider.GetRequiredService<FusionS3HMNContext>();

        var device = await context.Devices.FirstOrDefaultAsync(x => x.AppCode == appCode && x.Pid == pid);

        // 如果Device不存在HMN或非此AppCode的裝置就不處理
        if (device != null)
        {
            string bn = result[0].bn;
            long bt = result[0].bt ?? -1;
            var signalResult = new List<DeviceRawSignalR>();
            foreach (var e in result)
            {
                var deviceRawSignal = new DeviceRawSignalR
                {
                    timestampMs = e.bt ?? bt,
                    Pid = pid,
                    ResourceId = $"{e.bn ?? bn}{e.n}",
                    Value = e.v,
                    ValueString = e.vs,
                    ValueBoolean = e.vb,
                    UsageDepartCode = device?.UsageDepartCode ?? ""
                };

                signalResult.Add(deviceRawSignal);

                // 更新 bn 和 bt
                bn = e.bn ?? bn;
                bt = e.bt ?? bt;
            }

            await SendDeviceRawWithSignalR(requestUUID, appCode, signalResult);
        }
    }

    private async Task SendDeviceRawWithSignalR(string requestUUID, string appCode, IEnumerable<DeviceRawSignalR> signalResult)
    {
        string logActionName = $"{GetType().Name}/SendDeviceRawWithSignalR";
        _logService.Logging("info", logActionName, requestUUID, "Start");

        IEnumerable<string> groupNames = _signalRGroupService.GetGroups();

        _logService.Logging("info", logActionName, requestUUID, $"GroupNames--{DateTimeOffset.Now}--{JsonSerializer.Serialize(groupNames)}");

        // 讓程式同時以多執行緒執行多筆資料的處理
        // 使用 Task.WhenAll 来等待所有任务完成
        await Task.WhenAll(groupNames.Select(async groupName =>
        {
            if (!groupName.Contains(appCode))
                return;

            await _hubContext.Clients.Group(groupName).SendAsync("deviceRaw", signalResult);
        }).ToArray());

        _logService.Logging("info", logActionName, requestUUID, "End");
    }

    private async Task ProcessStationRegistration(string requestUUID, string topic)
    {
        string logActionName = $"{GetType().Name}/ProcessStationRegistration";
        _logService.Logging("info", logActionName, requestUUID, "Start");

        _logService.Logging("info", logActionName, requestUUID, $"topic:{topic}");

        string appCode = topic.Split("/")[0];
        string stationSid = topic.Split("/")[4];
        string stationType = topic.Split("/")[3];

        using var scope = _serviceProvider.CreateScope();
        using var context = scope.ServiceProvider.GetRequiredService<FusionS3HMNContext>();

        if (await context.Stations.AnyAsync(x => x.AppCode == appCode && x.SID == stationSid))
        {
            _logService.Logging("warning", logActionName, requestUUID, $"Station is already exist,Topic:{topic},{DateTimeOffset.Now}");
            return;
        }

        // 因應 FSN-6323 需求，如果是fih-guard2的基站就不處理
        if (stationType != null && stationType == "fih-guard2")
        {
            _logService.Logging("warning", logActionName, requestUUID, $"pass fih-guard2 mqtt,Topic:{topic},{DateTimeOffset.Now}");
            return;
        }

        // 預設取得第一筆AreaCode at 20240702 By Ann Mail(2024/7/2 (週二) 下午 12:05，RE: 自註冊基站所屬院區問題) FSN-6144
        // 因無基站未做UT
        string areaCode = (await context.Areas.FirstOrDefaultAsync(x => x.AppCode == appCode))?.AreaCode ?? "";

        var station = new Web.Repository.Models.Entities.Station
        {
            AppCode = appCode,
            AreaCode = areaCode,
            SID = stationSid,
            Enable = true,
            StationName = stationSid,
            StationType = stationType,
            AxisX = 0,
            AxisY = 0,
            DiffPositionX = 0,
            DiffPositionY = 0,
            SpaceType = "room",
            CreateDate = DateTime.Now,
            CreateUserAccount = "system"
        };

        await context.AddAsync(station);

        int stationId = await context.SaveChangesAsync();
        string createResult = stationId == 0 ? "failed" : "success";
        string logType = stationId == 0 ? "error" : "info";

        _logService.Logging(logType, logActionName, requestUUID, $"Station create:{createResult},StationId:{stationId},Topic:{topic},{DateTimeOffset.Now}");

        _logService.Logging("info", logActionName, requestUUID, "End");
    }

    private async Task ProcessPosition(string requestUUID, string payload, string topic)
    {
        string logActionName = $"{GetType().Name}/ProcessPosition";
        _logService.Logging("info", logActionName, requestUUID, "Start");

        var result = JsonSerializer.Deserialize<List<dynamic>>(payload);

        var p1str = JsonSerializer.Serialize(result?[0] ?? "");
        var p2str = JsonSerializer.Serialize(result?[1] ?? "");

        var obj1 = JsonSerializer.Deserialize<position.p1>(p1str);
        var obj2 = JsonSerializer.Deserialize<position.p2>(p2str);

        string sid = obj1.vs;
        string planeCode = obj2.vs;
        string appCode = topic.Split("/")[0];
        string deviceType = topic.Split("/")[3];
        string pid = topic.Split("/")[4];
        DateTime positionTimeObj = DateTime.Now;
        string positionTime = positionTimeObj.ToString("yyyy-MM-ddTHH:mm:ss");

        if (string.IsNullOrEmpty(pid))
        {
            _logService.Logging("error", logActionName, requestUUID, $"PID is null,Topic:{topic},PayLoad:{payload},{DateTimeOffset.Now}");
            return;
        }

        if (string.IsNullOrEmpty(sid))
        {
            _logService.Logging("error", logActionName, requestUUID, $"SID is null,Topic:{topic},PayLoad:{payload},{DateTimeOffset.Now}");
            return;
        }

        if (string.IsNullOrEmpty(planeCode))
        {
            _logService.Logging("error", logActionName, requestUUID, $"PlaneCode is null,Topic:{topic},PayLoad:{payload},{DateTimeOffset.Now}");
            return;
        }

        using var scope = _serviceProvider.CreateScope();
        using var context = scope.ServiceProvider.GetRequiredService<FusionS3HMNContext>();

        var objectDevice = await context.ObjectDevices.FirstOrDefaultAsync(x => x.AppCode == appCode && x.Pid == pid);
        var objectCode = objectDevice?.ObjectCode ?? "";
        var objectData = await context.ObjectData.FirstOrDefaultAsync(x => x.AppCode == appCode && x.ObjectCode == objectCode);

        if (objectData == null)
        {
            _logService.Logging("error", logActionName, requestUUID, $"ObjectData is null,Topic:{topic},PayLoad:{payload},{DateTimeOffset.Now}");
            return;
        }

        var station = await context.Stations.FirstOrDefaultAsync(x => x.AppCode == appCode && x.SID == sid);
        var locCode = station?.RegionCode ?? "";

        var location = await context.Locations.FirstOrDefaultAsync(x => x.AppCode == appCode && x.LocCode == locCode);
        var sectorStationList = await context.SectorStations.Where(x => x.AppCode == appCode && x.Stationsid == sid).Select(x => x.SectorCode).ToListAsync();
        var sectorList = await context.Sectors.Where(x => x.AppCode == appCode && sectorStationList.Contains(x.SectorCode)).ToListAsync();
        var plane = await context.Planes.Where(x => x.AppCode == appCode && x.PlaneCode == planeCode).FirstOrDefaultAsync();
        var taskList = await context.TaskData.Where(x => x.AppCode == appCode && x.SponsorDevicePid == pid && x.Action == 10).Select(t => new MQTTPostion.Object.TaskInfo { ServiceCode = t.ServiceCode, TaskId = t.TaskId }).ToListAsync();
        var departSectorList = await context.DepartSectors.Where(x => x.AppCode == appCode && sectorStationList.Contains(x.SectorCode)).ToListAsync();

        var positionInfo = new MQTTPostion();

        positionInfo.plane = new MQTTPostion.Plane
        {
            PlaneCode = plane.PlaneCode,
            PlaneMapPath = plane.PlaneMapPath,
            PlaneName = plane.PlaneName,
            MapWidth = plane.MapWidth,
            MapHeight = plane.MapHeight,
            PositionX = plane.PositionX,
            PositionY = plane.PositionY
        };

        positionInfo.sectorList = sectorList.Select(x => new MQTTPostion.Sector
        {
            SectorCode = x.SectorCode,
            PlaneCode = x.PlaneCode,
            SectorName = x.SectorName,
            SectorMapPath = x.SectorMapPath,
            MapWidth = x.MapWidth,
            MapHeight = x.MapHeight,
            PositionX = x.PositionX,
            PositionY = x.PositionY,
            DeptCodeList = departSectorList.Where(ds => ds.SectorCode == x.SectorCode).Select(s => s.DeptCode).ToList()
        }).ToList();

        positionInfo.@object = new MQTTPostion.Object
        {
            Name = objectData.Name,
            GroupCode = objectData.GroupCode,
            ObjectType = objectData.ObjectType,
            Pid = objectDevice.Pid,
            UsageDepartCode = objectData.UsageDepartCode,
            TaskList = taskList
        };

        positionInfo.station = new MQTTPostion.Station
        {
            SID = sid,
            LocCode = locCode,
            LocName = location?.LocName ?? "",
            SectorList = sectorStationList.Select(sectorCode => new MQTTPostion.Sector
            {
                SectorCode = sectorCode ?? "",
                DeptCodeList = departSectorList.Where(ds => ds.SectorCode == (sectorCode ?? "")).Select(s => s.DeptCode).ToList()
            }).ToList(),
            DiffPositionX = station.DiffPositionX,
            DiffPositionY = station.DiffPositionY
        };

        positionInfo.latestPositionTime = positionTime;

        await SendPositionWithSignalR(requestUUID, appCode, positionInfo);

        await ServerPublishDevicePositionMessage(appCode, pid);

        // 發送位置異動通知
        if (<EMAIL>())
        {
            foreach (var <NAME_EMAIL>)
            {
                if (task.TaskId.HasValue)
                {
                    await SendPositionNotifyMessages(requestUUID, appCode, task.TaskId.Value, positionTimeObj, sid);
                }
            }
        }

        _logService.Logging("info", logActionName, requestUUID, "End");
    }

    private async Task ProcessTask(string requestUUID, string payload, string topic)
    {
        string logActionName = $"{GetType().Name}/ProcessTask";
        _logService.Logging("info", logActionName, requestUUID, "Start");

        var mqttTask = JsonSerializer.Deserialize<MQTTTask>(payload);

        OutSignalRData result = new();

        // 判斷是否有deleteReason屬性，有的話表示是解除事件
        mqttTask.action = mqttTask.deleteReason == null ? "taskOccurred" : "taskCleared";

        // 從topic取得appCode, serviceCode 及 eventCode
        string appCode = topic.Split("/")[0];
        mqttTask.eventCode = topic.Split("/")[3];
        mqttTask.serviceCode = topic.Split("/")[2];

        result.taskId = mqttTask.taskId;

        if (!mqttTask.eventCode.Contains("@HMN@"))
        {
            return;
        }
        // 如果是收到新事件
        if (mqttTask.action == "taskOccurred")
        {
            _logService.Logging("info", logActionName, requestUUID, $"TaskOccurred--{DateTimeOffset.Now}--{JsonSerializer.Serialize(mqttTask)}");
            int taskId = await SaveCreateEventToDb(requestUUID, appCode, mqttTask);

            _logService.Logging("info", logActionName, requestUUID, $"TaskCreateToDb--{DateTimeOffset.Now}--TaskId:{mqttTask.taskId}");
            await SendNewTaskWithSignalR(requestUUID, appCode, mqttTask);

            // 伺服器發佈任務訊息
            _logService.Logging("info", logActionName, requestUUID, $"TaskPublishMessageToClient--{DateTimeOffset.Now}--TaskId:{mqttTask.taskId}");
            await ServerPublishTaskMessage(appCode, mqttTask);

            // 發送事件通知
            await SendEventNotifyMessages(requestUUID, appCode, (int)mqttTask.taskId);
        }
        else
        {
            _logService.Logging("info", logActionName, requestUUID, $"taskCleared--{DateTimeOffset.Now}--{JsonSerializer.Serialize(mqttTask)}");

            // 因非同步DB寫入原因及MQTT寫入30，造成資料複寫，改統一由TaskController的ClearTask處理 @20240606
            //_logService.Logging("info", logActionName, requestUUID, $"TaskCleared--{DateTimeOffset.Now}--{JsonSerializer.Serialize(mqttTask)}");
            //await SaveClearEventToDb(requestUUID, mqttTask);

            // 因MQTT 自解仍要更新TaskData，所以判斷是否為自解，如果是自解就才寫入DB，參考FSN-6118 @20240621 by GM
            // 1表示手動解除, 2表示自動解無. 3設定系統定時解除, 4 事件刪除 只有當 delteReason 是 1, 2, 3 的時候才更新 HMN 資料庫，參考 FSN-6633 by brucesmwang
            if (mqttTask.deleteReason == 1 || mqttTask.deleteReason == 2 || mqttTask.deleteReason == 3)
            {
                await SaveClearEventToDb(requestUUID, appCode, mqttTask);
            }

            _logService.Logging("info", logActionName, requestUUID, $"TaskClearSendSignalR--{DateTimeOffset.Now}--TaskId:{mqttTask.taskId}");
            await SendClearTaskWithSignalR(requestUUID, appCode, mqttTask.taskId, mqttTask.action, mqttTask.deleteReason ?? 0);
        }

        _logService.Logging("info", logActionName, requestUUID, "End");
    }

    private async Task SendPositionNotifyMessages(string requestUUID, string appCode, int taskId, DateTime positionTime, string toSid)
    {
        string logActionName = $"{GetType().Name}/SendPositionNotifyMessages";
        _logService.Logging("info", logActionName, requestUUID, "Start");

        using var scope = _serviceProvider.CreateScope();
        using var context = scope.ServiceProvider.GetRequiredService<FusionS3HMNContext>();

        var noticeService = scope.ServiceProvider.GetRequiredService<INoticeService>();
        var dataAccessService = scope.ServiceProvider.GetRequiredService<IDataAccessService>();

        var taskData = await context.TaskData.FirstOrDefaultAsync(x => x.AppCode == appCode && x.TaskId == taskId);

        if (taskData == null)
        {
            _logService.Logging("error", logActionName, requestUUID, $"TaskData is null,TaskId:{taskId}");
            return;
        }

        _logService.Logging("info", logActionName, requestUUID, $"TaskData--{DateTimeOffset.Now}--{JsonSerializer.Serialize(taskData)}");

        // 取得事件通知設定 EventNotifySettings
        var eventNotifySettings = await findEventNotifySettingsByTaskData(context, taskData);

        if (!eventNotifySettings.Any())
        {
            _logService.Logging("info", logActionName, requestUUID, $"No EventNotifySettings found,TaskId:{taskId}");
            return;
        }

        _logService.Logging("info", logActionName, requestUUID, $"EventNotifySettings--{DateTimeOffset.Now}--{JsonSerializer.Serialize(eventNotifySettings)}");

        // 依序處理每筆事件通知設定
        foreach (var eventNotifySetting in eventNotifySettings)
        {
            _logService.Logging("info", logActionName, requestUUID, $"EventNotifySetting--{DateTimeOffset.Now}--{JsonSerializer.Serialize(eventNotifySetting)}");


            // 如果事件通知設定的 PositionNotify 不是 true，就跳過此次通知設定
            if (eventNotifySetting.PositionNotify != true)
            {
                _logService.Logging("info", logActionName, requestUUID, $"EventNotifySetting PositionNotify is false, skip,TaskId:{taskId}");
                continue;
            }

            int traceTime = eventNotifySetting.TraceTime ?? 0;

            // 如果 PositionTime 與 StartsAt 的時間差大於 TraceTime，就跳過此次通知設定
            if (taskData.StartsAt.HasValue)
            {
                TimeSpan timeDiff = positionTime - taskData.StartsAt.Value;
                if (timeDiff.TotalMinutes > traceTime)
                {
                    _logService.Logging("info", logActionName, requestUUID, $"PositionTime is out of TraceTime, skip,TaskId:{taskId}");
                    continue;
                }
            }

            // 使用元組列表存儲聯繫人名稱和值
            List<(string ContactName, string ContactValue)> receivers = new List<(string, string)>();

            // 建立 LineToken 集合
            List<(string ContactName, string ContactValue)> lineTokens = new List<(string, string)>();

            // 建立 ENS Urls 集合
            List<(string ContactName, string ContactValue)> ensUrls = new List<(string, string)>();

            // 建立 Display device MAC 集合
            List<(string ContactName, string ContactValue)> displayDeviceMACs = new List<(string, string)>();

            // 取得事件通知設定的通知標頭 EventNotifyHeaders
            var eventNotifyHeaders = await context.EventNotifyHeaders.Where(x => x.AppCode == eventNotifySetting.AppCode && x.EventNotifySettingId == eventNotifySetting.Id).ToListAsync();
            if (eventNotifyHeaders.Any())
            {
                _logService.Logging("info", logActionName, requestUUID, $"EventNotifyHeaders--{DateTimeOffset.Now}--{JsonSerializer.Serialize(eventNotifyHeaders)}");

                foreach (var eventNotifyHeader in eventNotifyHeaders)
                {
                    // 取得通知標頭的通知明細 NotifyDetails
                    var notifyDetails = await context.NotifyDetails.Where(x => x.AppCode == eventNotifyHeader.AppCode && x.NotifyCode == eventNotifyHeader.NotifyCode).ToListAsync();
                    if (notifyDetails.Any())
                    {
                        _logService.Logging("info", logActionName, requestUUID, $"NotifyDetails--{DateTimeOffset.Now}--{JsonSerializer.Serialize(notifyDetails)}");

                        foreach (var notifyDetail in notifyDetails)
                        {
                            // 取得事件通知明細的通知名單列表
                            var contactList = await context.ContactData.Where(x => x.AppCode == notifyDetail.AppCode && x.ContactCode == notifyDetail.ContactCode).ToListAsync();
                            if (contactList.Any())
                            {
                                _logService.Logging("info", logActionName, requestUUID, $"ContactList--{DateTimeOffset.Now}--{JsonSerializer.Serialize(contactList)}");

                                foreach (var contact in contactList)
                                {
                                    // 取得通知名單的通知明細列表
                                    var contactDetails = await context.ContactDetails.Where(x => x.AppCode == contact.AppCode && x.ContactCode == contact.ContactCode).ToListAsync();
                                    if (contactDetails.Any())
                                    {
                                        _logService.Logging("info", logActionName, requestUUID, $"ContactDetails--{DateTimeOffset.Now}--{JsonSerializer.Serialize(contactDetails)}");

                                        foreach (var contactDetail in contactDetails)
                                        {
                                            if (contactDetail.ContactType == notifyDetail.NotifyType)
                                            {
                                                if (contactDetail.ContactType == "Email")
                                                {
                                                    receivers.Add((contact.ContactName, contactDetail.ContactValue));
                                                }
                                                else if (contactDetail.ContactType == "Line")
                                                {
                                                    lineTokens.Add((contact.ContactName, contactDetail.ContactValue));
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 取得事件通知設定的通知名單 EventNotifyContacts
            var eventNotifyContacts = await context.EventNotifyContacts.Where(x => x.AppCode == eventNotifySetting.AppCode && x.EventNotifySettingId == eventNotifySetting.Id).ToListAsync();
            if (eventNotifyContacts.Any())
            {
                _logService.Logging("info", logActionName, requestUUID, $"EventNotifyContacts--{DateTimeOffset.Now}--{JsonSerializer.Serialize(eventNotifyContacts)}");

                foreach (var eventNotifyContact in eventNotifyContacts)
                {
                    // 取得通知名單的通知名單列表
                    var contactList = await context.ContactData.Where(x => x.AppCode == eventNotifyContact.AppCode && x.ContactCode == eventNotifyContact.ContactCode).ToListAsync();
                    if (contactList.Any())
                    {
                        _logService.Logging("info", logActionName, requestUUID, $"ContactList--{DateTimeOffset.Now}--{JsonSerializer.Serialize(contactList)}");

                        foreach (var contact in contactList)
                        {
                            // 取得通知名單的通知明細列表
                            var contactDetails = await context.ContactDetails.Where(x => x.AppCode == contact.AppCode && x.ContactCode == contact.ContactCode).ToListAsync();
                            if (contactDetails.Any())
                            {
                                _logService.Logging("info", logActionName, requestUUID, $"ContactDetails--{DateTimeOffset.Now}--{JsonSerializer.Serialize(contactDetails)}");

                                foreach (var contactDetail in contactDetails)
                                {
                                    if (contactDetail.ContactType == eventNotifyContact.NotifyType)
                                    {
                                        if (contactDetail.ContactType == "Email")
                                        {
                                            receivers.Add((contact.ContactName, contactDetail.ContactValue));
                                        }
                                        else if (contactDetail.ContactType == "Line")
                                        {
                                            lineTokens.Add((contact.ContactName, contactDetail.ContactValue));
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 取得事件通知設定的第三方通知設定 EventNotifyThirds
            var eventNotifyThirds = await context.EventNotifyThirds.Where(x => x.AppCode == eventNotifySetting.AppCode && x.EventNotifySettingId == eventNotifySetting.Id).ToListAsync();
            if (eventNotifyThirds.Any())
            {
                _logService.Logging("info", logActionName, requestUUID, $"EventNotifyThirds--{DateTimeOffset.Now}--{JsonSerializer.Serialize(eventNotifyThirds)}");

                foreach (var eventNotifyThird in eventNotifyThirds)
                {
                    // 取得事件第三方通知設定的第三方通知設定列表 NotifyThird
                    var notifyThirds = await context.NotifyThirds.Where(x => x.AppCode == eventNotifyThird.AppCode && x.ThirdCode == eventNotifyThird.ThirdCode).ToListAsync();
                    if (notifyThirds.Any())
                    {
                        _logService.Logging("info", logActionName, requestUUID, $"NotifyThirds--{DateTimeOffset.Now}--{JsonSerializer.Serialize(notifyThirds)}");

                        foreach (var notifyThird in notifyThirds)
                        {
                            // 如果 notifyThird.NotifyType 是 ENS 的話將 URL_MAC 加入 ensUrls
                            if (notifyThird.NotifyType == "ENS")
                            {
                                ensUrls.Add((notifyThird.ThirdName, notifyThird.URL_MAC));
                            }
                            // 如果 notifyThird.NotifyType 是 Display 的話將 URL_MAC 轉成 int 加入 displayChannelIds
                            else if (notifyThird.NotifyType == "Display")
                            {
                                displayDeviceMACs.Add((notifyThird.ThirdName, notifyThird.URL_MAC));
                            }
                        }
                    }
                }
            }

            // 如果收件者不為空，發送郵件通知
            if (receivers.Any())
            {
                _logService.Logging("info", logActionName, requestUUID, $"SendMail--{DateTimeOffset.Now}--{JsonSerializer.Serialize(receivers)}");

                string message = prepareMessage(eventNotifySetting.EmailMessage, taskData);
                string subject = prepareMessage(eventNotifySetting.EmailSubject, taskData);
                string to = string.Join(",", receivers.Select(x => x.ContactValue));
                var result = await noticeService.SendMail(eventNotifySetting.AppCode, subject, to, message);

                // 寫入事件通知紀錄 TaskNotifyDetail
                receivers.ForEach(async x =>
                {
                    string appCode = eventNotifySetting.AppCode;
                    string areaCode = eventNotifySetting.AreaCode;
                    int taskId = taskData.TaskId ?? -1;
                    string notifyType = "Email";
                    string contactName = x.ContactName;
                    bool notifyStatus = result;
                    string notifyContent = x.ContactValue;
                    string notifyMessage1 = message;
                    string notifyMessage2 = null;

                    await writeTaskNotifyDetail(dataAccessService, appCode, areaCode, taskId, notifyType, contactName, notifyStatus, notifyContent, notifyMessage1, notifyMessage2, subject);
                });
            }

            // 如果 LineToken 不為空，發送 Line 通知
            if (lineTokens.Any())
            {
                _logService.Logging("info", logActionName, requestUUID, $"SendLineNotify--{DateTimeOffset.Now}--{JsonSerializer.Serialize(lineTokens)}");

                string message = prepareMessage(eventNotifySetting.NotifyMessage, taskData);
                string to = string.Join(",", lineTokens.Select(x => x.ContactValue));
                var result = await noticeService.SendLineNotify(eventNotifySetting.AppCode, message, to);

                // 寫入事件通知紀錄 TaskNotifyDetail
                lineTokens.ForEach(async x =>
                {
                    string appCode = eventNotifySetting.AppCode;
                    string areaCode = eventNotifySetting.AreaCode;
                    int taskId = taskData.TaskId ?? -1;
                    string notifyType = "Line";
                    string contactName = x.ContactName;
                    bool notifyStatus = result;
                    string notifyContent = x.ContactValue;
                    string notifyMessage1 = message;

                    await writeTaskNotifyDetail(dataAccessService, appCode, areaCode, taskId, notifyType, contactName, notifyStatus, notifyContent, notifyMessage1);
                });
            }

            // 如果 ensUrls 不為空，發送 ENS 通知
            if (ensUrls.Any())
            {
                foreach (var (thirdName, ensUrl) in ensUrls)
                {
                    _logService.Logging("info", logActionName, requestUUID, $"SendENSMessage--{DateTimeOffset.Now}--{ensUrl}");

                    string message = prepareMessage(eventNotifySetting.ENSMessage, taskData);
                    var result = await noticeService.SendENSMessage(eventNotifySetting.AppCode, taskData.SponsorObjectCode, taskData.TaskId ?? -1, taskData.ServiceCode, ensUrl, message, taskData.Extra, toSid);

                    // 寫入事件通知紀錄 TaskNotifyDetail
                    await writeTaskNotifyDetail(dataAccessService, eventNotifySetting.AppCode, eventNotifySetting.AreaCode, taskData.TaskId ?? -1, "ENS", thirdName, result, ensUrl, message);
                }
            }

            // 如果 displayDeviceMACs 不為空，發送 Display 通知
            if (displayDeviceMACs.Any())
            {
                _logService.Logging("info", logActionName, requestUUID, $"SendDisplayMessage--{DateTimeOffset.Now}--{JsonSerializer.Serialize(displayDeviceMACs)}");

                foreach (var (thirdName, displayDeviceMAC) in displayDeviceMACs)
                {
                    string message = prepareMessage(eventNotifySetting.DisplayMessage1, taskData);
                    string message2 = prepareMessage(eventNotifySetting.DisplayMessage2, taskData);
                    var result = await noticeService.SendDisplayMessage(eventNotifySetting.AppCode, taskData.SponsorObjectCode, taskData.TaskId ?? -1, taskData.ServiceCode, displayDeviceMAC, message, message2, taskData.Extra);

                    // 寫入事件通知紀錄 TaskNotify
                    await writeTaskNotifyDetail(dataAccessService, eventNotifySetting.AppCode, eventNotifySetting.AreaCode, taskData.TaskId ?? -1, "Display", thirdName, result, displayDeviceMAC, message, message2);
                }
            }

        }
    }

    private async Task SendEventNotifyMessages(string requestUUID, string appCode, int taskId)
    {
        string logActionName = $"{GetType().Name}/SendEventNotifyMessages";
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 使用 taskId 取得 TaskData
        using var scope = _serviceProvider.CreateScope();
        using var context = scope.ServiceProvider.GetRequiredService<FusionS3HMNContext>();

        var noticeService = scope.ServiceProvider.GetRequiredService<INoticeService>();
        var dataAccessService = scope.ServiceProvider.GetRequiredService<IDataAccessService>();

        var taskData = await context.TaskData.FirstOrDefaultAsync(x => x.AppCode == appCode && x.TaskId == taskId);

        if (taskData == null)
        {
            _logService.Logging("error", logActionName, requestUUID, $"TaskData is null,TaskId:{taskId}");
            return;
        }

        _logService.Logging("info", logActionName, requestUUID, $"TaskData--{DateTimeOffset.Now}--{JsonSerializer.Serialize(taskData)}");

        // 取得事件通知設定 EventNotifySettings
        var eventNotifySettings = await findEventNotifySettingsByTaskData(context, taskData);
        if (!eventNotifySettings.Any())
        {
            _logService.Logging("info", logActionName, requestUUID, $"No EventNotifySettings found,TaskId:{taskId}");
            return;
        }

        _logService.Logging("info", logActionName, requestUUID, $"EventNotifySettings--{DateTimeOffset.Now}--{JsonSerializer.Serialize(eventNotifySettings)}");

        // 依序處理每筆事件通知設定
        foreach (var eventNotifySetting in eventNotifySettings)
        {
            _logService.Logging("info", logActionName, requestUUID, $"EventNotifySetting--{DateTimeOffset.Now}--{JsonSerializer.Serialize(eventNotifySetting)}");

            if (eventNotifySetting.EnableSchedule == true)
            {
                _logService.Logging("info", logActionName, requestUUID, $"EventNotifySetting EnableSchedule--{DateTimeOffset.Now}--{JsonSerializer.Serialize(eventNotifySetting)}");

                // 取得事件通知設定的排程 EventNotifySchedules
                var eventNotifySchedules = await context.EventNotifySchedules.Where(x => x.AppCode == eventNotifySetting.AppCode && x.EventNotifySettingId == eventNotifySetting.Id).ToListAsync();
                if (eventNotifySchedules.Any())
                {
                    _logService.Logging("info", logActionName, requestUUID, $"EventNotifySchedules--{DateTimeOffset.Now}--{JsonSerializer.Serialize(eventNotifySchedules)}");

                    // 依序判斷每筆 EventNotifySchedule 設定，taskData.startsAt 是否在時間範圍內
                    bool isWithinScheduleTime = false;
                    foreach (var eventNotifySchedule in eventNotifySchedules)
                    {
                        // 判斷 taskData.StartsAt 是否在 eventNotifySchedule 的 weekly 設定內，weekly 是個 "0,1,2,3,4,5,6" 字串代表星期幾
                        if (!string.IsNullOrEmpty(eventNotifySchedule.Weekly))
                        {
                            string[] weeklyArray = eventNotifySchedule.Weekly.Split(',');

                            if (taskData.StartsAt.HasValue && !weeklyArray.Contains(((int)taskData.StartsAt.Value.DayOfWeek).ToString()))
                            {
                                continue;
                            }
                        }

                        if (taskData.StartsAt >= eventNotifySchedule.StartTime && taskData.StartsAt <= eventNotifySchedule.EndTime)
                        {
                            isWithinScheduleTime = true;
                            break;
                        }
                    }

                    // 如果不在時間範圍內，就跳過此次通知設定
                    if (!isWithinScheduleTime)
                    {
                        _logService.Logging("info", logActionName, requestUUID, $"TaskData StartsAt is not within EventNotifySchedule time,TaskId:{taskId}");
                        continue;
                    }
                }
            }

            // 使用元組列表存儲聯繫人名稱和值
            List<(string ContactName, string ContactValue)> receivers = new List<(string, string)>();

            // 建立 LineToken 集合
            List<(string ContactName, string ContactValue)> lineTokens = new List<(string, string)>();

            // 建立 ENS Urls 集合
            List<(string ContactName, string ContactValue)> ensUrls = new List<(string, string)>();

            // 建立 Display device MAC 集合
            List<(string ContactName, string ContactValue)> displayDeviceMACs = new List<(string, string)>();

            // 取得事件通知設定的通知標頭 EventNotifyHeaders
            var eventNotifyHeaders = await context.EventNotifyHeaders.Where(x => x.AppCode == eventNotifySetting.AppCode && x.EventNotifySettingId == eventNotifySetting.Id).ToListAsync();
            if (eventNotifyHeaders.Any())
            {
                _logService.Logging("info", logActionName, requestUUID, $"EventNotifyHeaders--{DateTimeOffset.Now}--{JsonSerializer.Serialize(eventNotifyHeaders)}");

                foreach (var eventNotifyHeader in eventNotifyHeaders)
                {
                    // 取得通知標頭的通知明細 NotifyDetails
                    var notifyDetails = await context.NotifyDetails.Where(x => x.AppCode == eventNotifyHeader.AppCode && x.NotifyCode == eventNotifyHeader.NotifyCode).ToListAsync();
                    if (notifyDetails.Any())
                    {
                        _logService.Logging("info", logActionName, requestUUID, $"NotifyDetails--{DateTimeOffset.Now}--{JsonSerializer.Serialize(notifyDetails)}");

                        foreach (var notifyDetail in notifyDetails)
                        {
                            // 取得事件通知明細的通知名單列表
                            var contactList = await context.ContactData.Where(x => x.AppCode == notifyDetail.AppCode && x.ContactCode == notifyDetail.ContactCode).ToListAsync();
                            if (contactList.Any())
                            {
                                _logService.Logging("info", logActionName, requestUUID, $"ContactList--{DateTimeOffset.Now}--{JsonSerializer.Serialize(contactList)}");

                                foreach (var contact in contactList)
                                {
                                    // 取得通知名單的通知明細列表
                                    var contactDetails = await context.ContactDetails.Where(x => x.AppCode == contact.AppCode && x.ContactCode == contact.ContactCode).ToListAsync();
                                    if (contactDetails.Any())
                                    {
                                        _logService.Logging("info", logActionName, requestUUID, $"ContactDetails--{DateTimeOffset.Now}--{JsonSerializer.Serialize(contactDetails)}");

                                        foreach (var contactDetail in contactDetails)
                                        {
                                            if (contactDetail.ContactType == notifyDetail.NotifyType)
                                            {
                                                if (contactDetail.ContactType == "Email")
                                                {
                                                    receivers.Add((contact.ContactName, contactDetail.ContactValue));
                                                }
                                                else if (contactDetail.ContactType == "Line")
                                                {
                                                    lineTokens.Add((contact.ContactName, contactDetail.ContactValue));
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 取得事件通知設定的通知名單 EventNotifyContacts
            var eventNotifyContacts = await context.EventNotifyContacts.Where(x => x.AppCode == eventNotifySetting.AppCode && x.EventNotifySettingId == eventNotifySetting.Id).ToListAsync();
            if (eventNotifyContacts.Any())
            {
                _logService.Logging("info", logActionName, requestUUID, $"EventNotifyContacts--{DateTimeOffset.Now}--{JsonSerializer.Serialize(eventNotifyContacts)}");

                foreach (var eventNotifyContact in eventNotifyContacts)
                {
                    // 取得通知名單的通知名單列表
                    var contactList = await context.ContactData.Where(x => x.AppCode == eventNotifyContact.AppCode && x.ContactCode == eventNotifyContact.ContactCode).ToListAsync();
                    if (contactList.Any())
                    {
                        _logService.Logging("info", logActionName, requestUUID, $"ContactList--{DateTimeOffset.Now}--{JsonSerializer.Serialize(contactList)}");

                        foreach (var contact in contactList)
                        {
                            // 取得通知名單的通知明細列表
                            var contactDetails = await context.ContactDetails.Where(x => x.AppCode == contact.AppCode && x.ContactCode == contact.ContactCode).ToListAsync();
                            if (contactDetails.Any())
                            {
                                _logService.Logging("info", logActionName, requestUUID, $"ContactDetails--{DateTimeOffset.Now}--{JsonSerializer.Serialize(contactDetails)}");

                                foreach (var contactDetail in contactDetails)
                                {
                                    if (contactDetail.ContactType == eventNotifyContact.NotifyType)
                                    {
                                        if (contactDetail.ContactType == "Email")
                                        {
                                            receivers.Add((contact.ContactName, contactDetail.ContactValue));
                                        }
                                        else if (contactDetail.ContactType == "Line")
                                        {
                                            lineTokens.Add((contact.ContactName, contactDetail.ContactValue));
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 取得事件通知設定的第三方通知設定 EventNotifyThirds
            var eventNotifyThirds = await context.EventNotifyThirds.Where(x => x.AppCode == eventNotifySetting.AppCode && x.EventNotifySettingId == eventNotifySetting.Id).ToListAsync();
            if (eventNotifyThirds.Any())
            {
                _logService.Logging("info", logActionName, requestUUID, $"EventNotifyThirds--{DateTimeOffset.Now}--{JsonSerializer.Serialize(eventNotifyThirds)}");

                foreach (var eventNotifyThird in eventNotifyThirds)
                {
                    // 取得事件第三方通知設定的第三方通知設定列表 NotifyThird
                    var notifyThirds = await context.NotifyThirds.Where(x => x.AppCode == eventNotifyThird.AppCode && x.ThirdCode == eventNotifyThird.ThirdCode).ToListAsync();
                    if (notifyThirds.Any())
                    {
                        _logService.Logging("info", logActionName, requestUUID, $"NotifyThirds--{DateTimeOffset.Now}--{JsonSerializer.Serialize(notifyThirds)}");

                        foreach (var notifyThird in notifyThirds)
                        {
                            // 如果 notifyThird.NotifyType 是 ENS 的話將 URL_MAC 加入 ensUrls
                            if (notifyThird.NotifyType == "ENS")
                            {
                                ensUrls.Add((notifyThird.ThirdName, notifyThird.URL_MAC));
                            }
                            // 如果 notifyThird.NotifyType 是 Display 的話將 URL_MAC 轉成 int 加入 displayChannelIds
                            else if (notifyThird.NotifyType == "Display")
                            {
                                displayDeviceMACs.Add((notifyThird.ThirdName, notifyThird.URL_MAC));
                            }
                        }
                    }
                }
            }

            // 如果收件者不為空，發送郵件通知
            if (receivers.Any())
            {
                _logService.Logging("info", logActionName, requestUUID, $"SendMail--{DateTimeOffset.Now}--{JsonSerializer.Serialize(receivers)}");

                string message = prepareMessage(eventNotifySetting.EmailMessage, taskData);
                string subject = prepareMessage(eventNotifySetting.EmailSubject, taskData);
                string to = string.Join(",", receivers.Select(x => x.ContactValue));
                var result = await noticeService.SendMail(eventNotifySetting.AppCode, subject, to, message);

                // 寫入事件通知紀錄 TaskNotifyDetail
                receivers.ForEach(async x =>
                {
                    string appCode = eventNotifySetting.AppCode;
                    string areaCode = eventNotifySetting.AreaCode;
                    int taskId = taskData.TaskId ?? -1;
                    string notifyType = "Email";
                    string contactName = x.ContactName;
                    bool notifyStatus = result;
                    string notifyContent = x.ContactValue;
                    string notifyMessage1 = message;
                    string notifyMessage2 = null;

                    await writeTaskNotifyDetail(dataAccessService, appCode, areaCode, taskId, notifyType, contactName, notifyStatus, notifyContent, notifyMessage1, notifyMessage2, subject);
                });
            }

            // 如果 LineToken 不為空，發送 Line 通知
            if (lineTokens.Any())
            {
                _logService.Logging("info", logActionName, requestUUID, $"SendLineNotify--{DateTimeOffset.Now}--{JsonSerializer.Serialize(lineTokens)}");

                string message = prepareMessage(eventNotifySetting.NotifyMessage, taskData);
                string to = string.Join(",", lineTokens.Select(x => x.ContactValue));
                var result = await noticeService.SendLineNotify(eventNotifySetting.AppCode, message, to);

                // 寫入事件通知紀錄 TaskNotifyDetail
                lineTokens.ForEach(async x =>
                {
                    string appCode = eventNotifySetting.AppCode;
                    string areaCode = eventNotifySetting.AreaCode;
                    int taskId = taskData.TaskId ?? -1;
                    string notifyType = "Line";
                    string contactName = x.ContactName;
                    bool notifyStatus = result;
                    string notifyContent = x.ContactValue;
                    string notifyMessage1 = message;

                    await writeTaskNotifyDetail(dataAccessService, appCode, areaCode, taskId, notifyType, contactName, notifyStatus, notifyContent, notifyMessage1);
                });
            }

            // 如果 ensUrls 不為空，發送 ENS 通知
            if (ensUrls.Any())
            {
                foreach (var (thirdName, ensUrl) in ensUrls)
                {
                    _logService.Logging("info", logActionName, requestUUID, $"SendENSMessage--{DateTimeOffset.Now}--{ensUrl}");

                    string message = prepareMessage(eventNotifySetting.ENSMessage, taskData);
                    var result = await noticeService.SendENSMessage(eventNotifySetting.AppCode, taskData.SponsorObjectCode, taskData.TaskId ?? -1, taskData.ServiceCode, ensUrl, message, taskData.Extra);

                    // 寫入事件通知紀錄 TaskNotifyDetail
                    await writeTaskNotifyDetail(dataAccessService, eventNotifySetting.AppCode, eventNotifySetting.AreaCode, taskData.TaskId ?? -1, "ENS", thirdName, result, ensUrl, message);
                }
            }

            // 如果 displayDeviceMACs 不為空，發送 Display 通知
            if (displayDeviceMACs.Any())
            {
                _logService.Logging("info", logActionName, requestUUID, $"SendDisplayMessage--{DateTimeOffset.Now}--{JsonSerializer.Serialize(displayDeviceMACs)}");

                foreach (var (thirdName, displayDeviceMAC) in displayDeviceMACs)
                {
                    string message = prepareMessage(eventNotifySetting.DisplayMessage1, taskData);
                    string message2 = prepareMessage(eventNotifySetting.DisplayMessage2, taskData);
                    var result = await noticeService.SendDisplayMessage(eventNotifySetting.AppCode, taskData.SponsorObjectCode, taskData.TaskId ?? -1, taskData.ServiceCode, displayDeviceMAC, message, message2, taskData.Extra);

                    // 寫入事件通知紀錄 TaskNotify
                    await writeTaskNotifyDetail(dataAccessService, eventNotifySetting.AppCode, eventNotifySetting.AreaCode, taskData.TaskId ?? -1, "Display", thirdName, result, displayDeviceMAC, message, message2);
                }
            }

        }
    }

    private async Task writeTaskNotifyDetail(IDataAccessService dataAccessService, string appCode, string areaCode, int taskId, string notifyType, string contactName, bool notifyStatus, string notifyContent, string notifyMessage1, string notifyMessage2 = null, string subject = null)
    {
        TaskNotifyDetail taskNotifyDetail = new TaskNotifyDetail
        {
            AppCode = appCode,
            AreaCode = areaCode,
            TaskId = taskId,
            NotifyType = notifyType,
            ContactName = contactName,
            NotifyContent = notifyContent,
            NotifyMessage1 = notifyMessage1,
            NotifyMessage2 = notifyMessage2,
            Subject = subject,
            NotifyStatus = notifyStatus,
            CreateUserAccount = "MQTTBackgroundService",
            CreateDate = DateTime.Now
        };
        await dataAccessService.CreateAsync(taskNotifyDetail);
    }

    private string prepareMessage(string message, TaskDatum taskData)
    {
        // 如果訊息不為空或 Null，開始替換訊息內容
        if (!string.IsNullOrWhiteSpace(message))
        {
            message = message.Replace("{Building}", taskData.BuildingName);
            message = message.Replace("{Device}", taskData.SponsorDeviceName);
            message = message.Replace("{Floor}", taskData.PlaneName);
            message = message.Replace("{Location}", taskData.SponsorLocationName);
            message = message.Replace("{Station}", taskData.SponsorStation);
            message = message.Replace("{Object}", taskData.SponsorObjectName);
            message = message.Replace("{Date}", taskData.StartsAt.Value.ToString("yyyy-MM-dd"));
            message = message.Replace("{Time}", taskData.StartsAt.Value.ToString("HH:mm:ss"));
            message = message.Replace("{Datetime}", taskData.StartsAt.Value.ToString("yyyy-MM-dd HH:mm:ss"));
            message = message.Replace("{Depart}", taskData.DeptName);
            message = message.Replace("{Event}", taskData.EventName);
        }
        return message;
    }

    private async Task<List<EventNotifySetting>> findEventNotifySettingsByTaskData(FusionS3HMNContext context, TaskDatum taskData)
    {
        // 取得符合基本條件 AppCode, ServiceCode, AreaCode, Enable = true 的通知設定列表
        var eventNotifySettings = await context.EventNotifySettings.Where(x => x.AppCode == taskData.AppCode && x.ServiceCode == taskData.ServiceCode && x.AreaCode == taskData.AreaCode && x.Enable == true).ToListAsync();

        if (eventNotifySettings.Any())
        {
            // 取得 taskData.SponsorObjectCode 的 objectData 的 GroupCode
            var objectData = await context.ObjectData.FirstOrDefaultAsync(x => x.AppCode == taskData.AppCode && x.ObjectCode == taskData.SponsorObjectCode);
            string groupCode = objectData?.GroupCode ?? "";

            // 如果 taskData.ServiceCode 是 SensorDataDriven 時過濾條件增加 taskDta.Extra 是否包含 SddResource
            if (taskData.ServiceCode == "SensorDataDriven")
            {
                // 過濾條件有 BuildingCode, PlaneCode, ObjectType, GroupCode, DeptCode, SddResource 如果存在六個條件都符合的通知設定列表就返回
                var matchedEventNotifySettings = eventNotifySettings.Where(x => x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode && (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))).ToList();
                if (matchedEventNotifySettings.Any())
                {
                    return matchedEventNotifySettings;
                }

                // 過濾條件有 BuildingCode, PlaneCode, ObjectType, GroupCode, DeptCode, SddResource 如果存在五個條件都符合的通知設定列表就返回
                matchedEventNotifySettings = eventNotifySettings.Where(x =>
                // 五個條件組合 1: BuildingCode, PlaneCode, ObjectType, GroupCode, DeptCode
                (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode) ||

                // 五個條件組合 2: BuildingCode, PlaneCode, ObjectType, GroupCode, SddResource
                (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode &&
                    (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                // 五個條件組合 3: BuildingCode, PlaneCode, ObjectType, DeptCode, SddResource
                (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType && x.DeptCode == taskData.DeptCode &&
                    (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                // 五個條件組合 4: BuildingCode, PlaneCode, GroupCode, DeptCode, SddResource
                (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode &&
                    (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                // 五個條件組合 5: BuildingCode, ObjectType, GroupCode, DeptCode, SddResource
                (x.BuildingCode == taskData.BuildingCode && x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode &&
                    (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                // 五個條件組合 6: PlaneCode, ObjectType, GroupCode, DeptCode, SddResource
                (x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode &&
                    (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource))))
                ).ToList();
                if (matchedEventNotifySettings.Any())
                {
                    return matchedEventNotifySettings;
                }

                // 過濾條件有 BuildingCode, PlaneCode, ObjectType, GroupCode, DeptCode, SddResource 如果存在四個條件都符合的通知設定列表就返回
                matchedEventNotifySettings = eventNotifySettings.Where(x =>
                    // 四個條件組合 1: BuildingCode, PlaneCode, ObjectType, GroupCode
                    (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode) ||

                    // 四個條件組合 2: BuildingCode, PlaneCode, ObjectType, DeptCode
                    (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType && x.DeptCode == taskData.DeptCode) ||

                    // 四個條件組合 3: BuildingCode, PlaneCode, ObjectType, SddResource
                    (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 四個條件組合 4: BuildingCode, PlaneCode, GroupCode, DeptCode
                    (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode) ||

                    // 四個條件組合 5: BuildingCode, PlaneCode, GroupCode, SddResource
                    (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode && x.GroupCode == groupCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 四個條件組合 6: BuildingCode, PlaneCode, DeptCode, SddResource
                    (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode && x.DeptCode == taskData.DeptCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 四個條件組合 7: BuildingCode, ObjectType, GroupCode, DeptCode
                    (x.BuildingCode == taskData.BuildingCode && x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode) ||

                    // 四個條件組合 8: BuildingCode, ObjectType, GroupCode, SddResource
                    (x.BuildingCode == taskData.BuildingCode && x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 四個條件組合 9: BuildingCode, ObjectType, DeptCode, SddResource
                    (x.BuildingCode == taskData.BuildingCode && x.ObjectType == taskData.SponsorObjectType && x.DeptCode == taskData.DeptCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 四個條件組合 10: BuildingCode, GroupCode, DeptCode, SddResource
                    (x.BuildingCode == taskData.BuildingCode && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 四個條件組合 11: PlaneCode, ObjectType, GroupCode, DeptCode
                    (x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode) ||

                    // 四個條件組合 12: PlaneCode, ObjectType, GroupCode, SddResource
                    (x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 四個條件組合 13: PlaneCode, ObjectType, DeptCode, SddResource
                    (x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType && x.DeptCode == taskData.DeptCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 四個條件組合 14: PlaneCode, GroupCode, DeptCode, SddResource
                    (x.PlaneCode == taskData.PlaneCode && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 四個條件組合 15: ObjectType, GroupCode, DeptCode, SddResource
                    (x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource))))
                ).ToList();
                if (matchedEventNotifySettings.Any())
                {
                    return matchedEventNotifySettings;
                }

                // 過濾條件有 BuildingCode, PlaneCode, ObjectType, GroupCode, DeptCode, SddResource 如果存在三個條件都符合的通知設定列表就返回
                matchedEventNotifySettings = eventNotifySettings.Where(x =>
                    // 三個條件組合 1: BuildingCode, PlaneCode, ObjectType
                    (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType) ||

                    // 三個條件組合 2: BuildingCode, PlaneCode, GroupCode
                    (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode && x.GroupCode == groupCode) ||

                    // 三個條件組合 3: BuildingCode, PlaneCode, DeptCode
                    (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode && x.DeptCode == taskData.DeptCode) ||

                    // 三個條件組合 4: BuildingCode, PlaneCode, SddResource
                    (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 三個條件組合 5: BuildingCode, ObjectType, GroupCode
                    (x.BuildingCode == taskData.BuildingCode && x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode) ||

                    // 三個條件組合 6: BuildingCode, ObjectType, DeptCode
                    (x.BuildingCode == taskData.BuildingCode && x.ObjectType == taskData.SponsorObjectType && x.DeptCode == taskData.DeptCode) ||

                    // 三個條件組合 7: BuildingCode, ObjectType, SddResource
                    (x.BuildingCode == taskData.BuildingCode && x.ObjectType == taskData.SponsorObjectType &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 三個條件組合 8: BuildingCode, GroupCode, DeptCode
                    (x.BuildingCode == taskData.BuildingCode && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode) ||

                    // 三個條件組合 9: BuildingCode, GroupCode, SddResource
                    (x.BuildingCode == taskData.BuildingCode && x.GroupCode == groupCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 三個條件組合 10: BuildingCode, DeptCode, SddResource
                    (x.BuildingCode == taskData.BuildingCode && x.DeptCode == taskData.DeptCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 三個條件組合 11: PlaneCode, ObjectType, GroupCode
                    (x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode) ||

                    // 三個條件組合 12: PlaneCode, ObjectType, DeptCode
                    (x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType && x.DeptCode == taskData.DeptCode) ||

                    // 三個條件組合 13: PlaneCode, ObjectType, SddResource
                    (x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 三個條件組合 14: PlaneCode, GroupCode, DeptCode
                    (x.PlaneCode == taskData.PlaneCode && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode) ||

                    // 三個條件組合 15: PlaneCode, GroupCode, SddResource
                    (x.PlaneCode == taskData.PlaneCode && x.GroupCode == groupCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 三個條件組合 16: PlaneCode, DeptCode, SddResource
                    (x.PlaneCode == taskData.PlaneCode && x.DeptCode == taskData.DeptCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 三個條件組合 17: ObjectType, GroupCode, DeptCode
                    (x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode) ||

                    // 三個條件組合 18: ObjectType, GroupCode, SddResource
                    (x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 三個條件組合 19: ObjectType, DeptCode, SddResource
                    (x.ObjectType == taskData.SponsorObjectType && x.DeptCode == taskData.DeptCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 三個條件組合 20: GroupCode, DeptCode, SddResource
                    (x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource))))
                ).ToList();
                if (matchedEventNotifySettings.Any())
                {
                    return matchedEventNotifySettings;
                }

                // 過濾條件有 BuildingCode, PlaneCode, ObjectType, GroupCode, DeptCode, SddResource 如果存在兩個條件都符合的通知設定列表就返回
                matchedEventNotifySettings = eventNotifySettings.Where(x =>
                    // 兩個條件組合 1: BuildingCode, PlaneCode
                    (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode) ||

                    // 兩個條件組合 2: BuildingCode, ObjectType
                    (x.BuildingCode == taskData.BuildingCode && x.ObjectType == taskData.SponsorObjectType) ||

                    // 兩個條件組合 3: BuildingCode, GroupCode
                    (x.BuildingCode == taskData.BuildingCode && x.GroupCode == groupCode) ||

                    // 兩個條件組合 4: BuildingCode, DeptCode
                    (x.BuildingCode == taskData.BuildingCode && x.DeptCode == taskData.DeptCode) ||

                    // 兩個條件組合 5: BuildingCode, SddResource
                    (x.BuildingCode == taskData.BuildingCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 兩個條件組合 6: PlaneCode, ObjectType
                    (x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType) ||

                    // 兩個條件組合 7: PlaneCode, GroupCode
                    (x.PlaneCode == taskData.PlaneCode && x.GroupCode == groupCode) ||

                    // 兩個條件組合 8: PlaneCode, DeptCode
                    (x.PlaneCode == taskData.PlaneCode && x.DeptCode == taskData.DeptCode) ||

                    // 兩個條件組合 9: PlaneCode, SddResource
                    (x.PlaneCode == taskData.PlaneCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 兩個條件組合 10: ObjectType, GroupCode
                    (x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode) ||

                    // 兩個條件組合 11: ObjectType, DeptCode
                    (x.ObjectType == taskData.SponsorObjectType && x.DeptCode == taskData.DeptCode) ||

                    // 兩個條件組合 12: ObjectType, SddResource
                    (x.ObjectType == taskData.SponsorObjectType &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 兩個條件組合 13: GroupCode, DeptCode
                    (x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode) ||

                    // 兩個條件組合 14: GroupCode, SddResource
                    (x.GroupCode == groupCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))) ||

                    // 兩個條件組合 15: DeptCode, SddResource
                    (x.DeptCode == taskData.DeptCode &&
                        (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource))))
                ).ToList();
                if (matchedEventNotifySettings.Any())
                {
                    return matchedEventNotifySettings;
                }

                // 返回過濾條件有 BuildingCode, PlaneCode, ObjectType, GroupCode, DeptCode SddResource 其中一個個條件的通知設定列表
                matchedEventNotifySettings = eventNotifySettings.Where(x => x.BuildingCode == taskData.BuildingCode ||
                    x.PlaneCode == taskData.PlaneCode ||
                    x.ObjectType == taskData.SponsorObjectType ||
                    x.GroupCode == groupCode ||
                    x.DeptCode == taskData.DeptCode ||
                    (string.IsNullOrEmpty(x.SddResource) || (!string.IsNullOrEmpty(taskData.Extra) && taskData.Extra.Contains(x.SddResource)))
                ).ToList();
                if (matchedEventNotifySettings.Any())
                {
                    return matchedEventNotifySettings;
                }

                return eventNotifySettings;
            }
            else
            {
                // 過濾條件有 BuildingCode, PlaneCode, ObjectType, GroupCode, DeptCode 如果存在四個條件都符合的通知設定列表就返回
                var matchedEventNotifySettings = eventNotifySettings.Where(x => x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode).ToList();
                if (matchedEventNotifySettings.Any())
                {
                    return matchedEventNotifySettings;
                }

                // 過濾條件符合 BuildingCode, PlaneCode, ObjectType, GroupCode, DeptCode 其中四個條件的通知設定列表，如果存在就返回
                matchedEventNotifySettings = eventNotifySettings.Where(x => (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType && x.DeptCode == taskData.DeptCode) ||
                    (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode) ||
                    (x.BuildingCode == taskData.BuildingCode && x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode) ||
                    (x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode)
                ).ToList();
                if (matchedEventNotifySettings.Any())
                {
                    return matchedEventNotifySettings;
                }

                // 過濾條件符合 BuildingCode, PlaneCode, ObjectType, GroupCode, DeptCode 其中三個條件的通知設定列表，如果存在就返回
                matchedEventNotifySettings = eventNotifySettings.Where(x => (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType) ||
                    (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode && x.GroupCode == groupCode) ||
                    (x.BuildingCode == taskData.BuildingCode && x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode) ||
                    (x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode) ||
                    (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode && x.DeptCode == taskData.DeptCode) ||
                    (x.BuildingCode == taskData.BuildingCode && x.ObjectType == taskData.SponsorObjectType && x.DeptCode == taskData.DeptCode) ||
                    (x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType && x.DeptCode == taskData.DeptCode) ||
                    (x.BuildingCode == taskData.BuildingCode && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode) ||
                    (x.PlaneCode == taskData.PlaneCode && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode) ||
                    (x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode)
                ).ToList();
                if (matchedEventNotifySettings.Any())
                {
                    return matchedEventNotifySettings;
                }

                // 過濾條件符合 BuildingCode, PlaneCode, ObjectType, GroupCode, DeptCode 其中兩個條件的通知設定列表，如果存在就返回
                matchedEventNotifySettings = eventNotifySettings.Where(x => (x.BuildingCode == taskData.BuildingCode && x.PlaneCode == taskData.PlaneCode) ||
                    (x.BuildingCode == taskData.BuildingCode && x.ObjectType == taskData.SponsorObjectType) ||
                    (x.BuildingCode == taskData.BuildingCode && x.GroupCode == groupCode) ||
                    (x.BuildingCode == taskData.BuildingCode && x.DeptCode == taskData.DeptCode) ||
                    (x.PlaneCode == taskData.PlaneCode && x.ObjectType == taskData.SponsorObjectType) ||
                    (x.PlaneCode == taskData.PlaneCode && x.GroupCode == groupCode) ||
                    (x.PlaneCode == taskData.PlaneCode && x.DeptCode == taskData.DeptCode) ||
                    (x.ObjectType == taskData.SponsorObjectType && x.GroupCode == groupCode) ||
                    (x.ObjectType == taskData.SponsorObjectType && x.DeptCode == taskData.DeptCode) ||
                    (x.GroupCode == groupCode && x.DeptCode == taskData.DeptCode)
                ).ToList();
                if (matchedEventNotifySettings.Any())
                {
                    return matchedEventNotifySettings;
                }

                // 返回過濾條件符合 BuildingCode, PlaneCode, ObjectType, GroupCode, DeptCode 其中一個條件的通知設定列表
                matchedEventNotifySettings = eventNotifySettings.Where(x => x.BuildingCode == taskData.BuildingCode ||
                    x.PlaneCode == taskData.PlaneCode ||
                    x.ObjectType == taskData.SponsorObjectType ||
                    x.GroupCode == groupCode ||
                    x.DeptCode == taskData.DeptCode
                ).ToList();
                if (matchedEventNotifySettings.Any())
                {
                    return matchedEventNotifySettings;
                }
                return eventNotifySettings;
            }
        }

        return eventNotifySettings;
    }

    private async Task SendPositionWithSignalR(string requestUUID, string appCode, MQTTPostion positionInfo)
    {
        string logActionName = $"{GetType().Name}/SendPositionWithSignalR";
        _logService.Logging("info", logActionName, requestUUID, "Start");

        IEnumerable<string> groupNames = _signalRGroupService.GetGroups();

        _logService.Logging("info", logActionName, requestUUID, $"GroupNames--{DateTimeOffset.Now}--{JsonSerializer.Serialize(groupNames)}");

        // 讓程式同時以多執行緒執行多筆資料的處理
        // 使用 Task.WhenAll 来等待所有任务完成
        await Task.WhenAll(groupNames.Select(async groupName =>
        {
            if (!groupName.Contains(appCode))
                return;
            await _hubContext.Clients.Group(groupName).SendAsync("position", positionInfo);
        }).ToArray());

        _logService.Logging("info", logActionName, requestUUID, "End");
    }

    private async Task SendClearTaskWithSignalR(string requestUUID, string appCode, long taskId, string action, int deleteReason)
    {
        string logActionName = $"{GetType().Name}/SendClearTaskWithSignalR";
        _logService.Logging("info", logActionName, requestUUID, "Start");

        using var scope = _serviceProvider.CreateScope();
        using var context = scope.ServiceProvider.GetRequiredService<FusionS3HMNContext>();

        var taskData = await context.TaskData.FirstOrDefaultAsync(x => x.TaskId == taskId);

        var result = new
        {
            taskId,
            action,
            deleteReason,
            pid = taskData?.SponsorDevicePid,
            sid = taskData?.SponsorStation,
        };

        IEnumerable<string> groupNames = _signalRGroupService.GetGroups();
        foreach (var groupName in groupNames)
        {
            if (!groupName.Contains(appCode))
                continue;

            await _hubContext.Clients.Group(groupName).SendAsync(action, result);
        }

        _logService.Logging("info", logActionName, requestUUID, "End");
    }

    public async Task NotifyUserAccountDisable(string requestUUID, UserDatum accountData, string action)
    {
        await SendUserAccountDisableMessageWithSignalR(requestUUID, accountData, action);
    }

    private async Task SendUserAccountDisableMessageWithSignalR(string requestUUID, UserDatum accountData, string action)
    {
        string logActionName = $"{GetType().Name}/SendNewMessageWithSignalR";
        _logService.Logging("info", logActionName, requestUUID, "Start");

        IEnumerable<string> groupNames = _signalRGroupService.GetGroups();

        _logService.Logging("info", logActionName, requestUUID, $"GroupNames--{DateTimeOffset.Now}--{JsonSerializer.Serialize(groupNames)}");

        foreach (var groupName in groupNames)
        {
            try
            {
                _logService.Logging("info", logActionName, requestUUID, $"Sending userAccount disable:{accountData.UserAccount} to Group:{groupName}");
                await _hubContext.Clients.Group(groupName).SendAsync(action + "Disable", accountData.UserAccount);
                _logService.Logging("info", logActionName, requestUUID, $"Sent userAccount disable:{accountData.UserAccount} to Group:{groupName} at {DateTimeOffset.Now}");
            }
            catch (Exception e)
            {
                _logService.Logging("error", logActionName, requestUUID, $"Failed to send userAccount disable:{accountData.UserAccount} to Group:{groupName}. Exception: {e.Message}");
            }
        }

        _logService.Logging("info", logActionName, requestUUID, "End");
    }

    private async Task SendNewTaskWithSignalR(string requestUUID, string appCode, MQTTTask? mqttTask)
    {
        string logActionName = $"{GetType().Name}/SendNewTaskWithSignalR";
        _logService.Logging("info", logActionName, requestUUID, "Start");

        using var scope = _serviceProvider.CreateScope();
        IMonitorService _monitorService = scope.ServiceProvider.GetRequiredService<IMonitorService>();

        IEnumerable<string> groupNames = _signalRGroupService.GetGroups();

        _logService.Logging("info", logActionName, requestUUID, $"GroupNames--{DateTimeOffset.Now}--{JsonSerializer.Serialize(groupNames)}");

        //// 讓程式同時以多執行緒執行多筆資料的處理
        //// 使用 Task.WhenAll 来等待所有任务完成
        //// 使用Task.When 會讓EF產生一個已有相同查詢正在進行的Exception，所以改用foreach來處理
        //// Task.WhenAll Code 可以參考2024/4/22 的commit

        // 讓程式依序處理每一組資料
        foreach (var groupName in groupNames)
        {
            if (!groupName.Contains(appCode))
                continue;

            var parts = groupName.Split("/");
            // string appCode = parts[0];
            string areaCode = parts[1];
            string deptCodeList = parts[2];

            _logService.Logging("info", logActionName, requestUUID, $"TaskId:{mqttTask.taskId}, AppCode:{appCode}, AreaCode:{areaCode}, DeptCodeList:{deptCodeList}");

            try
            {
                // 同步方式等待異步獲取後續任務列表，因為在 foreach 中，不再需要 Task.WhenAll 同時處理所有任務
                var followTaskList = await _monitorService.GetFollowingTaskList(new InFollowingTaskList
                {
                    AppCode = appCode,
                    AreaCode = areaCode,
                    DeptCodeList = deptCodeList.Split(",")
                });

                // 依據MQTT來的 TaskId 過濾出對應的任務 @2024/5/16
                followTaskList = followTaskList.Where(x => x.TaskId == mqttTask.taskId).ToList();

                _logService.Logging("info", logActionName, requestUUID, $"FollowTaskList--{DateTimeOffset.Now}--{JsonSerializer.Serialize(followTaskList)}");

                // 如果列表不为空，则发送给对应的组
                if (followTaskList != null && followTaskList.Any())
                {
                    _logService.Logging("info", logActionName, requestUUID, $"SendAsync--{DateTimeOffset.Now}--Group:{groupName}, {JsonSerializer.Serialize(followTaskList)}");
                    await _hubContext.Clients.Group(groupName).SendAsync(mqttTask.action, followTaskList);
                }
            }
            catch (Exception e)
            {
                // 錯誤處理
                throw;
            }
        }

        _logService.Logging("info", logActionName, requestUUID, "End");
    }

    private async Task<bool> SaveClearEventToDb(string requestUUID, string appCode, MQTTTask mqtt)
    {
        string logActionName = $"{GetType().Name}/SaveClearEventToDb";
        _logService.Logging("info", logActionName, requestUUID, "Start");

        /**
         * 20240507@GM 增加x.Action != 30，避免重複解除
         * 原因是現在resolveTask API允許多次解除，每次解除console都會送出一次MQTT，而Service收到MQTT時，也會再更新taskData ，
         * 結果執行順序會變如下：
         * 1。API read task->2。API call console resolve->3。mqtt read task->4。api save task->5。mqtt save task....
         * 3,4 是不同執行緒（非同步執行），如果4比3快就沒問題，如果3比4快，就會產生API更新的資料被覆蓋的情況了
         * 
         */
        /**
         * 20240904@GM 改成使用context = scope.ServiceProvider.GetRequiredService<FusionS3HMNContext>()
         * 
         * 原因是MQTT會被同時多次觸發，所以當使用_dataAccessService.Fetch<TaskDatum>時，會因DBContext共用而有
         * System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed.
         * This is usually caused by different threads concurrently using the same instance of DbContext.
         * For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
         * 的錯誤
         * 
         * 如果改成使用_dataAccessService.FetchWithNewContext，則會因退出FetchWithNewContext時，context.Dispose()而導致後續的SaveChangesAsync失敗
         */
        using var scope = _serviceProvider.CreateScope();
        using var context = scope.ServiceProvider.GetRequiredService<FusionS3HMNContext>();

        var taskData = await context.TaskData.FirstOrDefaultAsync(x => x.AppCode == appCode && x.TaskId == mqtt.taskId && x.Action != 30);

        //如果資料不存在就不刪除
        if (taskData == null)
        {
            _logService.Logging("error", logActionName, requestUUID, $"TaskData is null,TaskId:{mqtt.taskId}");
            return false;
        }

        _logService.Logging("info", logActionName, requestUUID, $"TaskId:{mqtt.taskId},TaskData:{JsonSerializer.Serialize(taskData)}");

        int result = 0;
        if (taskData != null)
        {
            // 參考FSN-6118
            taskData.Active = mqtt.deleteReason != 4;
            taskData.Action = 30;
            taskData.FinishesAt = DateTime.Now;
            taskData.ModifiesAt = DateTime.Now;

            // 保存更改
            result = await context.SaveChangesAsync();
        }

        _logService.Logging("info", logActionName, requestUUID, $"Task Cleared, DeleteReasion:{mqtt.deleteReason}, TaskId:{mqtt.taskId}, Update Result:{result}");

        _logService.Logging("info", logActionName, requestUUID, "End");

        return true;
    }

    private async Task<int> SaveCreateEventToDb(string requestUUID, string appCode, MQTTTask mqtt)
    {
        string logActionName = $"{GetType().Name}/SaveCreateEventToDb";
        _logService.Logging("info", logActionName, requestUUID, "Start");

        TaskDatum task = new();

        string stationSid = GetStationSidByServiceCode(mqtt.serviceCode, mqtt.extra);

        //將 mqtt 資料轉換成 task 資料
        task.TaskId = (int)mqtt.taskId;
        task.AppCode = appCode;

        task.SponsorObjectCode = mqtt.sponsors?.FirstOrDefault()?.objectCode ?? "";
        task.SponsorDevicePid = mqtt.sponsors?.FirstOrDefault()?.devicePid ?? "";
        task.SponsorObjectName = mqtt.sponsors?.FirstOrDefault()?.objectName ?? "";
        task.EventCode = mqtt.eventCode;

        using var scope = _serviceProvider.CreateScope();
        using var context = scope.ServiceProvider.GetRequiredService<FusionS3HMNContext>();


        // 依據 SponsorObjectCode 取得 ObjectData
        var objectData = await context.ObjectData.FirstOrDefaultAsync(x => x.AppCode == appCode && x.ObjectCode == task.SponsorObjectCode);
        objectData ??= new ObjectDatum();

        // 依據 SponsorDevicePid 取得 Device
        var device = await context.Devices.FirstOrDefaultAsync(x => x.Pid == task.SponsorDevicePid);
        device ??= new Device();

        task.DeptCode = device.UsageDepartCode;
        task.SponsorDeviceName = device.Name;
        task.SponsorDeviceType = device.DeviceType;


        var station = await context.Stations.FirstOrDefaultAsync(x => x.SID == stationSid);
        station ??= new Station();
        task.AreaCode = !string.IsNullOrEmpty(station.AreaCode) ? station.AreaCode : device.AreaCode;

        var area = await context.Areas.FirstOrDefaultAsync(x => x.AreaCode == task.AreaCode);
        task.AreaName = area?.AreaName ?? "";

        var sectorStation = await context.SectorStations.FirstOrDefaultAsync(x => x.Stationsid == stationSid);
        sectorStation ??= new SectorStation();

        var location = await context.Locations.FirstOrDefaultAsync(x => x.LocCode == station.RegionCode);
        location ??= new Location();

        var sector = await context.Sectors.FirstOrDefaultAsync(x => x.SectorCode == sectorStation.SectorCode);
        sector ??= new Sector();

        var plane = await context.Planes.FirstOrDefaultAsync(x => x.PlaneCode == station.PlaneCode);
        plane ??= new Plane();

        var building = await context.Buildings.FirstOrDefaultAsync(x => x.BuildingCode == plane.BuildingCode);
        building ??= new Building();

        var dept = await context.Departments.FirstOrDefaultAsync(x => x.DeptCode == task.DeptCode);
        dept ??= new Department();

        task.BuildingCode = building.BuildingCode;
        task.BuildingName = building.BuildingName;
        task.PlaneCode = plane.PlaneCode;
        task.PlaneName = plane.PlaneName;
        task.DeptCode = dept.DeptCode;
        task.DeptName = dept.DeptName;
        task.PlaneMapPath = plane.PlaneMapPath;
        task.MapWidth = plane.MapWidth;
        task.MapHeight = plane.MapHeight;
        task.PositionX = plane.PositionX;
        task.PositionY = plane.PositionY;
        task.Action = 10;
        task.Active = true;
        task.EventCode = mqtt.eventCode;
        task.EventName = mqtt.eventName;
        task.ServiceCode = mqtt.serviceCode;
        task.StartsAt = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc).AddSeconds((mqtt.startedTime ?? 1000) / 1000).ToLocalTime();
        task.SponsorStation = stationSid;
        task.DiffPositionX = station.DiffPositionX;
        task.DiffPositionY = station.DiffPositionY;
        task.SponsorLocationCode = location.LocCode;
        task.SponsorLocationName = location.LocName;
        task.SponsorObjectCode = task.SponsorObjectCode;
        task.SponsorObjectName = task.SponsorObjectName;
        task.SponsorObjectType = objectData.ObjectType;
        task.SponsorDevicePid = task.SponsorDevicePid;
        task.SponsorDeviceName = task.SponsorDeviceName;
        task.SponsorDeviceType = task.SponsorDeviceType;

        // 增加寫入 extra 欄位
        task.Extra = JsonSerializer.Serialize(mqtt.extra, new JsonSerializerOptions { DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull });

        int taskId = await CreateTaskWithoutTaskId(requestUUID, task);

        _logService.Logging("info", logActionName, requestUUID, $"Task Created, TaskId:{taskId}");

        _logService.Logging("info", logActionName, requestUUID, "End");

        return taskId;
    }
}
