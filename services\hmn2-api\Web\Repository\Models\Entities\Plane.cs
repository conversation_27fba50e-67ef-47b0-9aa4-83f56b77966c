﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class Plane
{
    public int PlaneId { get; set; }

    public string? AppCode { get; set; }

    public string? BuildingCode { get; set; }

    public string PlaneCode { get; set; } = null!;

    public string? CustomPlaneCode { get; set; }

    public bool? Enable { get; set; }

    public string? PlaneNo { get; set; }

    public string? PlaneName { get; set; }

    public string? PlaneMapPath { get; set; }

    public double? MapWidth { get; set; }

    public double? MapHeight { get; set; }

    public double? PositionX { get; set; }

    public double? PositionY { get; set; }

    public string? CreateUserAccount { get; set; }

    public DateTime? CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }
}
