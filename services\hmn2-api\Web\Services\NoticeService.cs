﻿using System.ComponentModel.Design;
using System.Text.RegularExpressions;
using Web.Models.Controller.Department;
using Web.Models.Controller;
using Web.Models.Service;
using Web.Models.Service.Business;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;
using Web.Models.Service.Fusion;

namespace Web.Services;

public class NoticeService(INotificationsService notificationsService) : INoticeService
{
    private readonly INotificationsService _notificationsService = notificationsService;

    public async Task<bool> SendMail(string appCode, string subject, string mailto, string mailContent)
    {
        var sendMessageResult = await _notificationsService.SendMail(appCode, subject, mailContent, mailto);

        return sendMessageResult.success == 1;
    }

    public async Task<bool> SendLineNotify(string appCode, string message, string to, string accessToken = null, string url = null)
    {
        var sendMessageResult = await _notificationsService.SendLineNotify(appCode, message, to, accessToken, url);

        return sendMessageResult.success == 1;
    }

    public async Task<bool> SendENSMessage(string appCode, string objectCode, int taskId, string taskType, string url, string message, string taskExtra, string toSid = null)
    {
        var sendMessageResult = await _notificationsService.SendENSMessage(appCode, objectCode, taskId, taskType, url, message, taskExtra, toSid);

        return sendMessageResult.success == 1;
    }

    public async Task<bool> SendDisplayMessage(string appCode, string objectCode, int taskId, string taskType, string receiverCode, string message, string message2, string taskExtra)
    {
        var sendMessageResult = await _notificationsService.SendDisplayMessage(appCode, objectCode, taskId, taskType, receiverCode, message, message2, taskExtra);

        return sendMessageResult.success == 1;
    }
}
