﻿using Web.Repository.Models.Entities;
using System.ComponentModel.DataAnnotations;
using Web.Validation;
using Web.Constant;

namespace Web.Models.Controller.Schedule;

public class RetrieveSchedule
{
    public string AreaCode { get; set; }
}

public class UpdateSchedule
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ScheduleJobId")]
    [Exists("", "ScheduleJob", "ScheduleJobId", ErrorMessage = Constants.ErrorCode.NotFound + "ScheduleJobId")]
    public int ScheduleJobId { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; } = null!;

    /// <summary>
    /// LowBatter:低電量,PwdCheck:密碼檢查
    /// </summary>
    [StringLength(30, ErrorMessage = Constants.ErrorCode.Length + "JobType")]
    public string JobType { get; set; } = null!;

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }

    [StringLength(64, ErrorMessage = Constants.ErrorCode.Length + "JobDesc")]
    public string JobDesc { get; set; } = null!;

    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "Threahold")]
    [RegularExpression(@"^(100|[1-9]?[0-9])$", ErrorMessage = Constants.ErrorCode.Pattern + "Threahold")]
    public string Threahold { get; set; }

    /// <summary>
    /// M:月,W:週,D:日
    /// </summary>
    [RegularExpression(@"^[DWM]+$", ErrorMessage = Constants.ErrorCode.Pattern + "ScheduleFreq")]
    public string ScheduleFreq { get; set; } = null!;

    /// <summary>
    /// 月:01~31,週:0~6,日:1，可多組用半形逗號隔開
    /// </summary>
    public string ScheduleDay { get; set; } = null!;

    /// <summary>
    /// 執行時間（24小時制）
    /// </summary>
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "ScheduleTime")]
    [RegularExpression(@"^(?:[0-9]|1[0-9]|2[0-3])$", ErrorMessage = Constants.ErrorCode.Pattern + "ScheduleTime")]
    public string ScheduleTime { get; set; } = null!;

    [StringLength(64, ErrorMessage = Constants.ErrorCode.Length + "Subject")]
    public string Subject { get; set; }

    [ListAllExists("AreaCode", "Department", "DeptCode", "ALL", ErrorMessage = Constants.ErrorCode.NotFound + "ScheduleDeparts")]
    [CheckScheduleNotifies(false, ErrorMessage = Constants.ErrorCode.NotFound + "ScheduleNotifies")]
    public List<string> ScheduleDeparts { get; set; }

    public List<UpdateScheduleNotify> ScheduleNotifies { get; set; }
}

public class UpdateScheduleNotify
{
    public string NotifyType { get; set; }
    public string Source { get; set; }
    public string ContactCode { get; set; }
    public string Email { get; set; }
}

public class DeleteSchedule
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ScheduleJobId")]
    [Exists("", "ScheduleJob", "ScheduleJobId", ErrorMessage = Constants.ErrorCode.NotFound + "ScheduleJobId")]
    public int ScheduleJobId { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; } = null!;
}

public class CreateSchedule
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "DeptList")]
    [ListAllExists("AreaCode", "Department", "DeptCode", "ALL", ErrorMessage = Constants.ErrorCode.NotFound + "ScheduleDeparts")]
    [CheckScheduleNotifies(true, ErrorMessage = Constants.ErrorCode.NotFound + "ScheduleNotifyList")]
    public List<string> DeptList { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "JobType")]
    [StringLength(30, ErrorMessage = Constants.ErrorCode.Length + "JobType")]
    public string JobType { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "JobDesc")]
    [StringLength(64, ErrorMessage = Constants.ErrorCode.Length + "JobDesc")]
    public string JobDesc { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Enable")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Threahold")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "Threahold")]
    [RegularExpression(@"^(100|[1-9]?[0-9])$", ErrorMessage = Constants.ErrorCode.Pattern + "Threahold")]
    public string Threahold { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ScheduleFreq")]
    [RegularExpression(@"^[DWM]+$", ErrorMessage = Constants.ErrorCode.Pattern + "ScheduleFreq")]
    public string ScheduleFreq { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ScheduleDay")]
    public string ScheduleDay { get; set; }


    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ScheduleTime")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "ScheduleTime")]
    [RegularExpression(@"^(?:[0-9]|1[0-9]|2[0-3])$", ErrorMessage = Constants.ErrorCode.Pattern + "ScheduleTime")]
    public string ScheduleTime { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Subject")]
    [StringLength(64, ErrorMessage = Constants.ErrorCode.Length + "Subject")]
    public string Subject { get; set; }
    
    public List<ScheduleNotify> ScheduleNotifyList { get; set; }
}

public class ScheduleNotify
{
    public string NotifyType { get; set; }
    public string Source { get; set; }
    public string ContactCode { get; set; }
    public string Email { get; set; }
}

public class RetrieveScheduleDto
{
    public int ScheduleJobId { get; set; }

    public string AppCode { get; set; } = null!;

    public string AreaCode { get; set; } = null!;

    /// <summary>
    /// LowBatter:低電量,PwdCheck:密碼檢查
    /// </summary>
    public string JobType { get; set; } = null!;

    public string Enable { get; set; }

    public string JobDesc { get; set; } = null!;

    public string? Threahold { get; set; }

    /// <summary>
    /// M:月,W:週,D:日
    /// </summary>
    public string ScheduleFreq { get; set; } = null!;

    /// <summary>
    /// 月:01~31,週:0~6,日:1，可多組用半形逗號隔開
    /// </summary>
    public string ScheduleDay { get; set; } = null!;

    /// <summary>
    /// 執行時間（24小時制）
    /// </summary>
    public string ScheduleTime { get; set; } = null!;

    public string? Subject { get; set; }

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }

    public List<ScheduleDepartDto> ScheduleDeparts { get; set; }

    public List<ScheduleNotifyDto> ScheduleNotifies { get; set; }
}

public class ScheduleDepartDto
{
    public int ScheduleDepartId { get; set; }

    public string AppCode { get; set; } = null!;

    public string AreaCode { get; set; } = null!;

    public int ScheduleJobId { get; set; }

    /// <summary>
    /// A表示全部單位（不過濾單位）
    /// </summary>
    public string UsageDepartCode { get; set; } = null!;

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }

}

public class ScheduleNotifyDto
{
    public int ScheduleNotifyId { get; set; }

    public string AppCode { get; set; } = null!;

    public string AreaCode { get; set; } = null!;

    public int ScheduleJobId { get; set; }

    /// <summary>
    /// Line,SMS,EMail 對應ContactData欄位
    /// </summary>
    public string NotifyType { get; set; } = null!;

    /// <summary>
    /// 如果資料由ContactData來，記與0,1,2與ContactData一致，如果直接輸入Email則記3
    /// </summary>
    public string? Source { get; set; }

    public string? ContactCode { get; set; }

    /// <summary>
    /// 當Source為3，則此欄必填
    /// </summary>
    public string? Email { get; set; }

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }

}