﻿using System.ComponentModel.DataAnnotations;
using Web.Models.Controller.Log;
using Web.Models.Interface;
using Web.Validation;

namespace Web.Models.Controller.Area;

public class InCreateArea : ILogRequest
{
    [Required(ErrorMessage = "err.null.param.AreaCode")]
    [Unique("AreaCode", "Area", "AreaCode", ErrorMessage = "err.uniqe.param.AreaCode")]
    [StringLength(50, ErrorMessage = "err.length.param.AreaCode")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = "err.pattern.param.AreaCode")]
    public string? AreaCode { get; set; }
    [Required(ErrorMessage = "err.null.param.AreaName")]
    [StringLength(50, ErrorMessage = "err.length.param.AreaName")]
    public string? AreaName { get; set; }
    [StringLength(50, ErrorMessage = "err.length.param.CustomAreaCode")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = "err.pattern.param.CustomAreaCode")]
    public string? CustomAreaCode { get; set; }
}

public class InRetrieveArea : ILogRequest
{
    public int? AreaId { get; set; }
    public string? page { get; set; }
    public string? size { get; set; }
    public List<string>? AreaCode { get; set; }
    public string sort { get; set; } = "AreaCode:desc";

}
public class InUpdateArea : ILogRequest
{
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = "err.notfound.param.AreaCode")]
    public string? AreaCode { get; set; }
    [Required(ErrorMessage = "err.null.param.AreaName")]
    [StringLength(50, ErrorMessage = "err.length.param.AreaName")]
    public string? AreaName { get; set; }
    [StringLength(50, ErrorMessage = "err.length.param.CustomAreaCode")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = "err.pattern.param.CustomAreaCode")]
    public string? CustomAreaCode { get; set; }
}