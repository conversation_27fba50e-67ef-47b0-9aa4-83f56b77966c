﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class ScheduleDepart
{
    public int ScheduleDepartId { get; set; }

    public string AppCode { get; set; } = null!;

    public string AreaCode { get; set; } = null!;

    public int ScheduleJobId { get; set; }

    /// <summary>
    /// A表示全部單位（不過濾單位）
    /// </summary>
    public string UsageDepartCode { get; set; } = null!;

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }

    public virtual ScheduleJob ScheduleJob { get; set; } = null!;
}
