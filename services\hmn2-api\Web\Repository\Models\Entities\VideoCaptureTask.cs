﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class VideoCaptureTask
{
    public int Id { get; set; }
    public bool Active { get; set; }
    public string AppCode { get; set; } = null!;
    public string DeptCode { get; set; } = null!;
    public string ServiceCode { get; set; } = null!;
    public int TaskId { get; set; }
    public string? EventCode { get; set; }
    public string? EventName { get; set; }
    public string? ObjectName { get; set; }
    public string? DeviceName { get; set; }
    public string? DeviceType { get; set; }
    public string TaskStationSid { get; set; }
    public DateTime TaskStartsAt { get; set; }
    public string BackupDirectory { get; set; } = null!;
    public string? Account { get; set; }
    public string? Password { get; set; }
    public int TaskStartedFragment { get; set; }
    public int FragmentCount { get; set; }
    public int TotalVideoCount { get; set; }
    public int TaskResult { get; set; }
    public DateTime CreateDate { get; set; }
    public DateTime? ModifyDate { get; set; }
}
