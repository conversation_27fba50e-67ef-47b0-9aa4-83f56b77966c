﻿using System.ComponentModel.Design;
using System.Text.RegularExpressions;
using Web.Models.Controller.Department;
using Web.Models.Controller;
using Web.Models.Service;
using Web.Models.Service.Business;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;

namespace Web.Services;

public class ScheduleService(IPreferenceService preferenceService,
    IDataAccessService dataAccessService) : IScheduleService
{
    private readonly IDataAccessService _dataAccessService = dataAccessService;
    private readonly IPreferenceService _preferenceService = preferenceService;

    public List<ScheduleDepart> FetchScheduleDepartList(string appCode)
    {
        return _dataAccessService.Fetch<ScheduleDepart>(e => e.AppCode == appCode).ToList<ScheduleDepart>();
    }

    public List<ScheduleDepart> FetchScheduleDepartList(string appCode, string areaCode)
    {
        return _dataAccessService.Fetch<ScheduleDepart>(e => e.AppCode == appCode && e.AreaCode == areaCode).ToList<ScheduleDepart>();
    }

    public List<ScheduleDepart> FetchScheduleDepartList(int scheduleJobId)
    {
        return _dataAccessService.Fetch<ScheduleDepart>(e => e.ScheduleJobId == scheduleJobId).ToList<ScheduleDepart>();
    }

    public List<ScheduleJob> FetchScheduleJobList(string appCode)
    {
        return _dataAccessService.Fetch<ScheduleJob>(e => e.AppCode == appCode).ToList<ScheduleJob>();
    }

    public List<ScheduleJob> FetchScheduleJobList(string appCode, List<string> areaCodeList)
    {
        return _dataAccessService.Fetch<ScheduleJob>(e => e.AppCode == appCode && areaCodeList.Contains(e.AreaCode)).OrderByDescending(e => e.ModifyDate).ToList<ScheduleJob>();
    }

    public List<ScheduleJob> FetchScheduleJobListByDayOfMonth(string appCode, string day, int hour)
    {
        return _dataAccessService.Fetch<ScheduleJob>(x => x.AppCode == appCode 
                                           && x.Enable == true
                                           && x.ScheduleFreq=="M"
                                           && x.ScheduleDay.Contains(day)
                                           && x.ScheduleTime == hour.ToString()).ToList();
    }

    public List<ScheduleJob> FetchScheduleJobListByDayOfWeek(string appCode, int day, int hour)
    {
        return _dataAccessService.Fetch<ScheduleJob>(x => x.AppCode == appCode
                                           && x.Enable == true
                                           && x.ScheduleFreq == "W"
                                           && x.ScheduleDay.Contains(day.ToString())
                                           && x.ScheduleTime == hour.ToString()).ToList();
    }

    public List<ScheduleJob> FetchScheduleJobListByEveryDay(string appCode, int hour)
    {
        return _dataAccessService.Fetch<ScheduleJob>(x => x.AppCode == appCode
                                           && x.Enable == true
                                           && x.ScheduleFreq == "D"
                                           && x.ScheduleTime == hour.ToString()).ToList();
    }

    public ScheduleJob FetchScheduleJob(int id)
    {
        return _dataAccessService.Fetch<ScheduleJob>(x => x.ScheduleJobId == id).FirstOrDefault();
    }

    public List<ScheduleNotify> FetchScheduleNotifyList(string appCode)
    {
        return _dataAccessService.Fetch<ScheduleNotify>(x => x.AppCode == appCode).ToList();
    }

    public List<ScheduleNotify> FetchScheduleNotifyList(string appCode, string areaCode)
    {
        return _dataAccessService.Fetch<ScheduleNotify>(x => x.AppCode == appCode && x.AreaCode == areaCode).ToList();
    }

    public List<ScheduleNotify> FetchScheduleNotifyList(int scheduleJobId)
    {
        return _dataAccessService.Fetch<ScheduleNotify>(x => x.ScheduleJobId == scheduleJobId).ToList();
    }

    public async Task<bool> CreateScheduleJobNNotify(string appCode, string userAccount, string areaCode, List<string> deptList, string jobType
            , string jobDesc, bool enable, string threahold, string scheduleFreq
            , string scheduleDay, string scheduleTime, string subject
            , dynamic scheduleNotifyList, bool endOfSave)
    {
        ScheduleJob scheduleJob = new ScheduleJob();

        scheduleJob.AppCode = appCode;
        scheduleJob.AreaCode = areaCode;
        scheduleJob.JobType = jobType;
        scheduleJob.Enable = enable;
        scheduleJob.JobDesc = jobDesc;
        scheduleJob.Threahold = threahold;
        scheduleJob.ScheduleFreq = scheduleFreq;
        scheduleJob.ScheduleDay = scheduleDay;
        scheduleJob.ScheduleTime = scheduleTime;
        scheduleJob.Subject = subject;
        scheduleJob.CreateUserAccount = userAccount;
        scheduleJob.CreateDate = DateTime.Now;
        scheduleJob.ModifyDate = DateTime.Now;
        
        if (scheduleNotifyList != null)
        {
            List<ScheduleNotify> scheduleNotifies = new List<ScheduleNotify>();
            
            foreach (var item in scheduleNotifyList)
            {
                ScheduleNotify scheduleNotify = new ScheduleNotify();

                scheduleNotify.AppCode = appCode;
                scheduleNotify.AreaCode = areaCode;
                scheduleNotify.ScheduleJobId = scheduleJob.ScheduleJobId;
                scheduleNotify.NotifyType = item.NotifyType ?? "EMAIL";
                scheduleNotify.Source = item.Source;
                scheduleNotify.ContactCode = item.ContactCode;
                scheduleNotify.Email = item.Email;
                scheduleNotify.CreateUserAccount = userAccount;
                scheduleNotify.CreateDate = DateTime.Now;
                scheduleNotify.ModifyDate = DateTime.Now;

                scheduleNotifies.Add(scheduleNotify);
            }

            await _dataAccessService.CreateRangeAsync<ScheduleNotify>(scheduleNotifies);
        }

        if (deptList != null)
        {
            List<ScheduleDepart> scheduleDeparts = new List<ScheduleDepart>();
            
            foreach (var item in deptList)
            {
                ScheduleDepart scheduleDepart = new ScheduleDepart();

                scheduleDepart.AppCode = appCode;
                scheduleDepart.AreaCode = areaCode;
                scheduleDepart.ScheduleJobId = scheduleJob.ScheduleJobId;
                scheduleDepart.UsageDepartCode = item;
                scheduleDepart.CreateUserAccount = userAccount;
                scheduleDepart.CreateDate = DateTime.Now;
                scheduleDepart.ModifyDate = DateTime.Now;

                scheduleDeparts.Add(scheduleDepart);
            }

            await _dataAccessService.CreateRangeAsync<ScheduleDepart>(scheduleDeparts);
        }

        int? res = await _dataAccessService.CreateAsync<ScheduleJob>(scheduleJob);

        bool result = res.HasValue && res.Value > 0;

        return result;
    }

    public async Task<bool> UpdateScheduleJobNNotify(
            int scheduleJobId, string appCode, string userAccount
            , string areaCode, List<string> deptList, string jobType
            , string jobDesc, bool enable, string threahold, string scheduleFreq
            , string scheduleDay, string scheduleTime, string subject
            , dynamic scheduleNotifyList)
    {
        //先刪除所有資料
        bool result = await DeleteScheduleJobNNotify(scheduleJobId, false);

        //重新新增
        result |= await CreateScheduleJobNNotify(appCode, userAccount, areaCode, deptList, jobType, jobDesc, enable, threahold, scheduleFreq
            , scheduleDay, scheduleTime, subject, scheduleNotifyList, false);

        return result;
    }

    public async Task<bool> DeleteScheduleJobNNotify(int scheduleJobId, bool endOfSave)
    {
        ScheduleJob job = FetchScheduleJob(scheduleJobId);

        await _dataAccessService.DeleteAsync<ScheduleNotify>(x => x.ScheduleJobId == scheduleJobId);
        await _dataAccessService.DeleteAsync<ScheduleDepart>(x => x.ScheduleJobId == scheduleJobId);
        int? res = await _dataAccessService.DeleteAsync<ScheduleJob>(job);

        bool result = res.HasValue && res.Value > 0;

        return result;
    }

    public async Task<bool> SaveScheduleJobEnable(string userAccount, List<int> jobIdList, bool enable)
    {
        bool result = true;

        foreach (int jobId in jobIdList)
        {
            ScheduleJob job = FetchScheduleJob(jobId);
            
            List<string> updateField = new List<string>();
            updateField.Add("Enable");
            job.Enable = enable;

            updateField.Add("ModifyDate");
            updateField.Add("ModifyUserAccount");
            job.ModifyDate = DateTime.Now;
            job.ModifyUserAccount = userAccount;

            int res = await _dataAccessService.UpdateAsync<ScheduleJob>(job, updateField.ToArray());
            result |= res >= 0;
        }

        return result;
    }
}
