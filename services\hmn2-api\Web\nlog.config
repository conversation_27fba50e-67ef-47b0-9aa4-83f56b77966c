<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      throwConfigExceptions="true"
      internalLogLevel="info"
      internalLogFile="${basedir}/nlog-internal.log">

    <!-- enable asp.net core layout renderers -->
    <extensions>
        <add assembly="NLog.Web.AspNetCore"/>
    </extensions>

    <!-- the targets to write to -->
    <targets>
        <!-- write logs to file -->
        <target xsi:type="File" name="allfile" fileName="${basedir}/logs/${shortdate}.log"
                layout="${longdate}|${event-properties:item=EventId_Id}|${logger}|${uppercase:${level}}|${message} ${exception}" />

		<!-- write logs to file -->
		<target xsi:type="File" name="dataaccess" fileName="${basedir}/logs/${shortdate}-DataAccess.log"
                layout="${longdate}|${event-properties:item=EventId_Id}|${logger}|${uppercase:${level}}|${message} ${exception}" />

		<!-- write logs to file -->
		<target xsi:type="File" name="MQTT" fileName="${basedir}/logs/${shortdate}-MQTT.log"
                layout="${longdate}|${event-properties:item=EventId_Id}|${logger}|${uppercase:${level}}|${message} ${exception}" />

		<!-- write logs to file -->
		<target xsi:type="File" name="MQTTServer" fileName="${basedir}/logs/${shortdate}-MQTTServer.log"
                layout="${longdate}|${event-properties:item=EventId_Id}|${logger}|${uppercase:${level}}|${message} ${exception}" />
	
        <!-- another example of a target -->
        <target xsi:type="Console" name="logconsole"
                layout="${longdate}|${event-properties:item=EventId_Id}|${logger}|${uppercase:${level}}|${message} ${exception}" />
    </targets>

    <!-- rules to map from logger name to target -->
    <rules>
		<logger name="Microsoft.EntityFrameworkCore.Database.Command" minlevel="Info" writeTo="" final="true">
			<filters>
			  <when condition="equals('${message}', 'Executed DbCommand')" action="Log" />
			</filters>
		</logger>
		<logger name="Microsoft.EntityFrameworkCore.*" minlevel="Trace" final="true" />
		<logger name="Microsoft.AspNetCore.*" minlevel="Trace" final="true" />
		<logger name="Microsoft.Extensions.Http.*" minlevel="Trace" final="true" />
		<logger name="System.Net.Http.HttpClient.FusionCoreApi.LogicalHandler" minlevel="Info" writeTo="logconsole,allfile" final="true" />
		<logger name="System.Net.Http.HttpClient.*" minlevel="Trace" final="true" />
		<logger name="dataaccess" minlevel="Trace" writeTo="dataaccess" final="true" />
		<logger name="MQTT" minlevel="Trace" writeTo="MQTT" final="true" />
		<logger name="MQTTServer" minlevel="Trace" writeTo="MQTTServer" final="true" />
        <logger name="*" minlevel="Trace" writeTo="logconsole,allfile" />
    </rules>
</nlog>