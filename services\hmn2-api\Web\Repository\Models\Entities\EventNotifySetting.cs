﻿using System;
using System.Collections.Generic;

#nullable disable

namespace Web.Repository.Models.Entities;

public partial class EventNotifySetting
{
    public int Id { get; set; }
    public string AppCode { get; set; }
    public string AreaCode { get; set; }
    public string ServiceCode { get; set; }
    public string SddResource { get; set; }
    public string Description { get; set; }
    public string BuildingCode { get; set; }
    public string PlaneCode { get; set; }
    public string DeptCode { get; set; }
    public string ObjectType { get; set; }
    public string GroupCode { get; set; }
    public bool? Enable { get; set; }
    public bool? EnableSchedule { get; set; }
    public bool? PositionNotify { get; set; }
    public int? TraceTime { get; set; }
    public string ENSMessage { get; set; }
    public string DisplayMessage1 { get; set; }
    public string DisplayMessage2 { get; set; }
    public string EmailSubject { get; set; }
    public string EmailMessage { get; set; }
    public string NotifyMessage { get; set; }
    public string SMSMessage { get; set; }
    public string CreateUserAccount { get; set; }
    public DateTime CreateDate { get; set; }
    public string ModifyUserAccount { get; set; }
    public DateTime? ModifyDate { get; set; }
}