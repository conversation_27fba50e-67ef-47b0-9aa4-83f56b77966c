﻿using System.ComponentModel.DataAnnotations;
using Web.Constant;
using Web.Validation;

namespace Web.Models.Controller.Building;

public class RetrieveBuilding
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    public string AreaCode { get; set; }
    public string BuildingCode { get; set; }
    public string CustomBuildingCode { get; set; }
    public string BuildingName { get; set; }
    public string Enable { get; set; }

}

public class CreateBuilding
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "BuildingCode")]
    [Unique("", "Building", "BuildingCode", ErrorMessage = Constants.ErrorCode.Unique + "BuildingCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "BuildingCode")]
    public string BuildingCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "CustomBuildingCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "CustomBuildingCode")]
    public string CustomBuildingCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "BuildingName")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "BuildingName")]
    public string BuildingName { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Enable")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }

}

public class UpdateBuilding
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "BuildingCode")]
    [Exists("", "Building", "BuildingCode", ErrorMessage = Constants.ErrorCode.NotFound + "BuildingCode")]
    [HasReferenceWhenEquals("Enable", "N", "", "Plane", "BuildingCode", ErrorMessage = Constants.ErrorCode.Reference + "BuildingCode")]
    public string BuildingCode { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "CustomBuildingCode")]
    public string CustomBuildingCode { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "BuildingName")]
    public string BuildingName { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }

}

public class DeleteBuilding
{   
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "BuildingCode")]
    [Exists("", "Building", "BuildingCode", ErrorMessage = Constants.ErrorCode.NotFound + "BuildingCode")]
    [HasReference("", "Plane", "BuildingCode", ErrorMessage = Constants.ErrorCode.Reference + "BuildingCode")]
    public string BuildingCode { get; set; }

}
