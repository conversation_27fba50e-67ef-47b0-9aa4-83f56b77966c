﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class Device
{
    public int DeviceId { get; set; }

    public string AppCode { get; set; } = null!;

    public string? AreaCode { get; set; }

    public string Pid { get; set; } = null!;

    public bool Active { get; set; }

    public bool Enable { get; set; }

    public string Name { get; set; } = null!;

    public string? DeviceType { get; set; }

    public string? ManageDepartCode { get; set; }

    public string? UsageDepartCode { get; set; }

    public string? StationSid { get; set; }

    public string? mmWaveType { get; set; }

    public string? CreateUserAccount { get; set; }

    public DateTime? CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }
}
