﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Data;
using Web.Constant;
using Web.Models.Controller;
using Web.Models.Controller.Building;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;
using Web.Validation;

namespace Web.Controller;

/// <summary>
/// Building控制器
/// </summary>
[Route("[controller]")]
[Authorize]
public class BuildingController(IDataAccessService dataAccessService,
                            ICredentialService credentialService,
                            IRequestContextService requestContextService,
                            IUtilityService utilityService,
                            ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly IUtilityService _utilityService = utilityService;

    [HttpPost("buildings")]
    [RequestParamListDuplicate("BuildingCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateBuilding([FromBody] List<CreateBuilding> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 開始新增棟別資料
        _logService.Logging("info", logActionName, requestUUID, "Building Data Validated, start to append.");
        _dataAccessService.BeginTransaction();

        foreach (CreateBuilding param in paramList)
        {
            Building building = new Building
            {
                AppCode = _user.AppCode,
                AreaCode = param.AreaCode,
                BuildingCode = param.BuildingCode,
                CustomBuildingCode = param.CustomBuildingCode,
                BuildingName = param.BuildingName,
                Enable = param.Enable == "Y",
                CreateUserAccount = _user.Account,
                ModifyUserAccount = _user.Account,
                CreateDate = DateTime.Now,
                ModifyDate = DateTime.Now
            };

            // 新增棟別
            await _dataAccessService.CreateAsync<Building>(building);
        }

        await _dataAccessService.CommitAsync();
        _logService.Logging("info", logActionName, requestUUID, "Building Data append done.");

        ReturnModel returnModel = new()
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status201Created,
            result = true
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpPatch("buildings")]
    [RequestParamListDuplicate("BuildingCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateBuilding([FromBody] List<UpdateBuilding> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 開始更新棟別資料
        _logService.Logging("info", logActionName, requestUUID, "Building Data Validated, start to update.");
        _dataAccessService.BeginTransaction();

        foreach (UpdateBuilding param in paramList)
        {
            Building building = _dataAccessService.Fetch<Building>(e => e.AppCode == _user.AppCode && e.BuildingCode == param.BuildingCode).AsTracking().First();

            List<string> updateField = new List<string>();
            if (param.CustomBuildingCode != null)
            {
                updateField.Add("CustomBuildingCode");
                building.CustomBuildingCode = param.CustomBuildingCode;
            }

            if (param.BuildingName != null)
            {
                updateField.Add("BuildingName");
                building.BuildingName = param.BuildingName;
            }

            if (param.Enable != null)
            {
                updateField.Add("Enable");
                building.Enable = param.Enable == "Y";
            }

            updateField.Add("ModifyDate");
            updateField.Add("ModifyUserAccount");
            building.ModifyDate = DateTime.Now;
            building.ModifyUserAccount = _user.Account;

            // 更新棟別
            await _dataAccessService.UpdateAsync<Building>(building, updateField.ToArray());
        }

        await _dataAccessService.CommitAsync();
        _logService.Logging("info", logActionName, requestUUID, "Building Data update done.");

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true
        });

    }

    [HttpDelete("buildings")]
    [RequestParamListDuplicate("BuildingCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeleteBuilding([FromBody] List<DeleteBuilding> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 開始刪除棟別資料
        _logService.Logging("info", logActionName, requestUUID, "Building Data Validated, start to delete.");
        _dataAccessService.BeginTransaction();

        foreach (DeleteBuilding param in paramList)
        {
            // 刪除棟別
            await _dataAccessService.DeleteAsync<Building>(e => e.AppCode == _user.AppCode && e.BuildingCode == param.BuildingCode);
        }

        await _dataAccessService.CommitAsync();
        _logService.Logging("info", logActionName, requestUUID, "Building Data delete done.");

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true
        });
    }

    [HttpGet("buildings")]
    public async Task<IActionResult> RetrieveBuilding([FromQuery] RetrieveBuilding queryParam)
    {
        RetrieveBuilding param = queryParam;
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // 排序；如果沒有 sort 條件預設使用 ModifyDate 降冪排序
        string sort = string.IsNullOrEmpty(param.sort) ? "ModifyDate:desc" : param.sort;
        string sortByField = sort.Split(":")[0];
        string sortDirection = sort.Split(":")[1];

        // 開始查詢棟別資料
        var areaList = _dataAccessService.Fetch<Area>(e => e.AppCode == _user.AppCode);
        var buildingList = _dataAccessService.Fetch<Building>(x => x.AppCode == _user.AppCode);

        var query = (from a in buildingList
                     join b in areaList on a.AreaCode equals b.AreaCode into temp
                     from t in temp.DefaultIfEmpty()
                     select new
                     {
                         a.BuildingId,
                         Enable = (a.Enable == true) ? "Y" : "N",
                         a.BuildingCode,
                         a.CustomBuildingCode,
                         a.BuildingName,
                         a.AreaCode,
                         AreaName = (t == null) ? "" : t.AreaName,
                         a.ModifyDate
                     })
            .Where(x => (param.BuildingCode == null || x.BuildingCode.ToUpper().Contains(param.BuildingCode.ToUpper()))
                     && (param.CustomBuildingCode == null || x.CustomBuildingCode.ToUpper().Contains(param.CustomBuildingCode.ToUpper()))
                     && (param.BuildingName == null || x.BuildingName.ToUpper().Contains(param.BuildingName.ToUpper()))
                     && (param.AreaCode == null || x.AreaCode.ToUpper().Contains(param.AreaCode.ToUpper()))
                     && (param.Enable == null || x.Enable == param.Enable)
                );

        // 取得總筆數
        int recordTotal = await query.CountAsync();

        // 根據 sortByField 和 sortDirection 進行動態排序
        query = sortDirection.ToLower() == "desc"
            ? query.OrderByDescending(x => EF.Property<object>(x, sortByField))
            : query.OrderBy(x => EF.Property<object>(x, sortByField));

        // 進行資料庫分頁
        var recordList = size == 0
            ? await query.ToListAsync() // 如果 size == 0，表示不分頁，直接取回所有資料
            : await query.Skip(skip).Take(size).ToListAsync(); // 分頁查詢

        ReturnModel returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal,
                recordList
            }
        };

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);
    }
}


