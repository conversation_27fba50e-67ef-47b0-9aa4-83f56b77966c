﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class UserLogonLog
{
    public int Id { get; set; }

    public string UserAccount { get; set; } = null!;

    public string? ClientIp { get; set; }

    public string? IsAdmin { get; set; }

    public bool? IsSuperAdmin { get; set; }

    public string? RoleCode { get; set; }

    public bool LogonResult { get; set; }

    public DateTime CreateDate { get; set; }
}
