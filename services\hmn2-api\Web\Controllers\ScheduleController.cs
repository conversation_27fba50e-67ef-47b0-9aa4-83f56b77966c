﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Data;
using System.Text.Json;
using Web.Constant;
using Web.Models.Controller;
using Web.Models.Controller.Schedule;
using Web.Models.Service;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;
using Web.Validation;

namespace Web.Controller;

/// <summary>
/// 排程控制器
/// </summary>
[Authorize]
[Route("[controller]")]
public class ScheduleController(IDataAccessService dataAccessService,
                            IAdminService adminService,
                            IRequestContextService requestContextService,
                            ICredentialService credentialService,
                            ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly IAdminService _adminService = adminService;

    [HttpGet("schedules")]
    public async Task<IActionResult> RetrieveSchedule(RetrieveSchedule param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        string appCode = _user.AppCode;
        string areaCode = param.AreaCode;

        // 檢查參數是否為空
        if (param == null)
        {
            _logService.Logging("error", logActionName, requestUUID, "err.null.schedules.Schedule.param");
            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, "err.null.schedules.Schedule.param"));
        }

        // 檢查院區代碼是否為空
        if (string.IsNullOrEmpty(param.AreaCode))
        {
            _logService.Logging("error", logActionName, requestUUID, "err.null.schedules.Schedule.AreaCode");
            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, "err.null.schedules.Schedule.AreaCode"));
        }

        var scheduleJobQuery = _dataAccessService
            .Fetch<ScheduleJob>(s => s.AppCode == _user.AppCode && s.AreaCode == param.AreaCode)
            .OrderByDescending(s => s.ModifyDate).ThenByDescending(s => s.CreateDate)
            .Select(s => new RetrieveScheduleDto
            {
                ScheduleJobId = s.ScheduleJobId,
                AppCode = s.AppCode,
                AreaCode = s.AreaCode,
                JobType = s.JobType,
                JobDesc = s.JobDesc,
                Threahold = s.Threahold,
                ScheduleFreq = s.ScheduleFreq,
                ScheduleDay = s.ScheduleDay,
                ScheduleTime = s.ScheduleTime,
                Subject = s.Subject,
                Enable = s.Enable == true ? "Y" : "N",
                CreateUserAccount = s.CreateUserAccount,
                CreateDate = s.CreateDate,
                ModifyUserAccount = s.ModifyUserAccount,
                ModifyDate = s.ModifyDate,
                ScheduleDeparts = s.ScheduleDeparts.Select(scheduleDepart => new ScheduleDepartDto
                {
                    ScheduleDepartId = scheduleDepart.ScheduleDepartId,
                    AppCode = scheduleDepart.AppCode,
                    AreaCode = scheduleDepart.AreaCode,
                    ScheduleJobId = scheduleDepart.ScheduleJobId,
                    UsageDepartCode = scheduleDepart.UsageDepartCode,
                    CreateUserAccount = scheduleDepart.CreateUserAccount,
                    CreateDate = scheduleDepart.CreateDate,
                    ModifyUserAccount = scheduleDepart.ModifyUserAccount,
                    ModifyDate = scheduleDepart.ModifyDate
                }).ToList(),
                ScheduleNotifies = s.ScheduleNotifies.Select(scheduleNotify => new ScheduleNotifyDto
                {
                    ScheduleNotifyId = scheduleNotify.ScheduleNotifyId,
                    AppCode = scheduleNotify.AppCode,
                    AreaCode = scheduleNotify.AreaCode,
                    ScheduleJobId = scheduleNotify.ScheduleJobId,
                    NotifyType = scheduleNotify.NotifyType,
                    Source = scheduleNotify.Source,
                    ContactCode = scheduleNotify.ContactCode,
                    Email = scheduleNotify.Email,
                    CreateUserAccount = scheduleNotify.CreateUserAccount,
                    CreateDate = scheduleNotify.CreateDate,
                    ModifyUserAccount = scheduleNotify.ModifyUserAccount,
                    ModifyDate = scheduleNotify.ModifyDate
                }).ToList()
            });

        // 實際執行查詢並獲取結果
        var scheduleJobList = await scheduleJobQuery.ToListAsync();

        var result = new ReturnModel(StatusCodes.Status200OK, true, scheduleJobList);

        return StatusCode(result.httpStatus, result);
    }

    [HttpPatch("schedules")]
    [RequestParamListDuplicate("ScheduleJobId")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateSchedule([FromBody] List<UpdateSchedule> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        var result = await _adminService.UpdateSchedule(paramList);

        if (result.result)
        {
            var deletedScheduleIds = string.Join(",", paramList.Select(p => p.ScheduleJobId).ToList());

            _logService.Logging("info", logActionName, requestUUID, "Deleted Schedule Id:" + deletedScheduleIds);
        }
        else
        {
            _logService.Logging("error", logActionName, requestUUID, JsonSerializer.Serialize(result));
        }

        _logService.Logging("info", logActionName, requestUUID, $"End");

        return StatusCode(result.httpStatus, result);
    }

    [HttpDelete("schedules")]
    [RequestParamListDuplicate("ScheduleJobId")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeleteSchedule([FromBody]List<DeleteSchedule> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        var result = await _adminService.DeleteScheule(paramList);

        if (result.result)
        {
            foreach (var param in paramList)
            {
                // 刪除 ScheduleDepart 的關聯
                await _dataAccessService.DeleteAsync<ScheduleDepart>(e => e.AppCode == _user.AppCode && e.AreaCode == param.AreaCode && e.ScheduleJobId == param.ScheduleJobId);

                // 刪除 ScheduleNotify 的關聯
                await _dataAccessService.DeleteAsync<Web.Repository.Models.Entities.ScheduleNotify>(e => e.AppCode == _user.AppCode && e.AreaCode == param.AreaCode && e.ScheduleJobId == param.ScheduleJobId);
            }

            var deletedScheduleIds = string.Join(",", paramList.Select(p => p.ScheduleJobId).ToList());

            _logService.Logging("info", logActionName, requestUUID, "Deleted Schedule Id:" + deletedScheduleIds);
        }
        else
        {
            _logService.Logging("error", logActionName, requestUUID, JsonSerializer.Serialize(result));
        }

        _logService.Logging("info", logActionName, requestUUID, $"End");

        return StatusCode(result.httpStatus, result);
    }

    [HttpPost("schedules")]
    [RequestParamListDuplicate("JobType")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateSchedule([FromBody]List<CreateSchedule> param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        string appCode = _user.AppCode;

        var result = await _adminService.CreateSchedule(param);

        var scheduleJobId = result.data;

        _logService.Logging("info", logActionName, requestUUID, $"Schedule Created ID:{scheduleJobId}");

        _logService.Logging("info", logActionName, requestUUID, "End");

        return StatusCode(result.httpStatus, result);
    }
}
