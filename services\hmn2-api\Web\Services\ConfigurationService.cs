﻿using Web.Repository.Models.Entities;
using Web.Models.Service.Configuration;
using Microsoft.EntityFrameworkCore;
using Web.Models.Service;
using System.Text.Json;
using Web.Models.Service.Fusion;
using Microsoft.EntityFrameworkCore.ValueGeneration.Internal;
using Web.Services.Interfaces;
using Web.Models.Controller.Object;
using Web.Models.Controller;
using Web.Models;
using ObjectDevice = Web.Repository.Models.Entities.ObjectDevice;
using Web.Exceptions;
using System.Text.RegularExpressions;
using System.Text;
using DocumentFormat.OpenXml.Office2016.Drawing.ChartDrawing;
using System.ComponentModel.Design;
using System.Reflection.Emit;
using System.ComponentModel.DataAnnotations;
using Microsoft.AspNetCore.Mvc;
using static Web.Models.Service.Fusion.DeviceResults;
using Microsoft.AspNetCore.Http;
using Web.Models.Controller.Device;
using Web.Validation;
using System.Collections.Generic;
using Web.Constant;
using System.Collections.Immutable;


namespace Web.Services;

public class ConfigurationService(IDataAccessService dataAccessService,
    ICredentialService credentialService,
    IPreferenceService preferenceService,
    IObjectService objectService,
    IEventService eventService,
    IDeviceService deviceService,
    IStationService stationService,
    ILangService langService) : IConfigurationService
{
    private readonly IDataAccessService _dataAccessService = dataAccessService;
    private readonly IPreferenceService _preferenceService = preferenceService;
    private readonly IObjectService _objectService = objectService;
    private readonly IEventService _eventService = eventService;
    private readonly IDeviceService _deviceService = deviceService;
    private readonly IStationService _stationService = stationService;
    private readonly ILangService _langService = langService;

    private readonly UserResult _user = credentialService.UserResult;

    public string GenerateEventCode(InGenerateEventCode param)
    {
        string eventCode;

        if (new[] { "Enter", "Leave" }.Contains(param.ServiceCode))
        {
            eventCode = $"@{param.AppCode}@{param.ServiceCode}@{param.FenceCode}@{param.ObjectCode}@{param.Pid}";
        }
        else if (param.ServiceCode == "SensorDataDriven")
        {
            eventCode = $"@{param.AppCode}{param.SddResource.Replace('/', '@')}@{param.SddComp.Substring(0, 1).ToUpper()}@{param.ObjectCode}@{param.Pid}";
        }
        else
        {   //Help,LongPress,mmWaveFallDetection,mmWaveLeaveBed,mmWaveStayTimeout
            eventCode = $"@{param.AppCode}@{param.ServiceCode}@{param.ObjectCode}@{param.Pid}";
        }

        return eventCode;
    }

    public string GenerateEventName(InGenerateEventName param)
    {
        string eventName;

        if (new[] { "Enter", "Leave" }.Contains(param.ServiceCode))
        {
            eventName = param.FenceName;
        }
        else
        {
            eventName = param.EventName ?? param.EventCode;
        }

        return eventName;
    }

    public async Task<OutCreateObjectDatum> CreateObjectDatum(string appCode,
        IEnumerable<ObjectDatum> addObjectDataList,
        IEnumerable<Repository.Models.Entities.ObjectDevice> addObjectDeviceList)
    {
        var objectTypeList = await _dataAccessService.Fetch<ObjectType>(e => e.AppCode == appCode && e.Enable && e.Active).ToListAsync();

        // 要新增到Console的Object資料
        List<PostFusionObjectInput> fusionObjectInputs = [];

        // 要回傳的結果
        OutCreateObjectDatum outCreateObjectDatum = new();

        // 將新增ObjectData資料轉換成Fusion Object資料
        foreach (var e in addObjectDataList)
        {
            fusionObjectInputs.Add(new()
            {
                code = e.ObjectCode,
                name = e.Name,
                type = objectTypeList.FirstOrDefault(o => o.ObjectTypeCode == e.ObjectType)?.FusionCoreValue ?? "",
                devicePids = addObjectDeviceList.Where(o => o.ObjectCode == e.ObjectCode).Select(o => o.Pid).ToArray()
            });
        }

        // 執行新增Fusion Object
        outCreateObjectDatum.APIResultList = await _objectService.AddObject(fusionObjectInputs);

        outCreateObjectDatum.InsertObjectDataResultList = [];
        outCreateObjectDatum.InsertObjectDeviceResultList = [];
        outCreateObjectDatum.InsertObjectDeviceDetailResultList = [];

        // 將新增Fusion Object成功的資料寫入DB
        foreach (var result in outCreateObjectDatum.APIResultList)
        {
            var obj = addObjectDataList.First(o => o.ObjectCode == result.code);

            if (result.errors == null || result.errors.Count == 0)
            {
                ObjectDatum objectData = new()
                {
                    AppCode = appCode,
                    AreaCode = obj.AreaCode,
                    ObjectType = obj.ObjectType,
                    ObjectCode = obj.ObjectCode,
                    Active = true,
                    Enable = true,
                    Name = obj.Name,
                    GroupCode = obj.GroupCode,
                    UsageDepartCode = obj.UsageDepartCode,
                    CreateUserAccount = _user.Account,
                    CreateDate = DateTime.Now,
                    ModifyDate = DateTime.Now,
                    Remark = obj.Remark,
                    EquipmentStatus = obj.EquipmentStatus,
                    UrgColor = obj.UrgColor
                };

                List<Repository.Models.Entities.ObjectDevice> objectDevices = addObjectDeviceList.Where(od => od.ObjectCode == obj.ObjectCode).Select(od => new Repository.Models.Entities.ObjectDevice
                {
                    AppCode = appCode,
                    AreaCode = obj.AreaCode,
                    ObjectCode = obj.ObjectCode,
                    Pid = od.Pid,
                    CreateUserAccount = _user.Account,
                    CreateDate = DateTime.Now,
                    ModifyDate = DateTime.Now
                }).ToList();

                int? createObjectDataResult = await _dataAccessService.CreateAsync(objectData);
                int? createObjectDeviceResult = await _dataAccessService.CreateRangeAsync(objectDevices);

                outCreateObjectDatum.InsertObjectDataResultList.Add(new DBResult { code = obj.ObjectCode, result = (createObjectDataResult != null && createObjectDataResult == 1) });
                outCreateObjectDatum.InsertObjectDeviceResultList.Add(new DBResult { code = obj.ObjectCode, result = (createObjectDeviceResult != null && createObjectDeviceResult == objectDevices.Count) });
            }
        }

        return outCreateObjectDatum;
    }

    public async Task<OutCreateObjectDevice> CreateObjectDevice(string appCode,
        IEnumerable<Repository.Models.Entities.ObjectDevice> addObjectDeviceList
    )
    {
        // 要新增到Console的Object資料
        List<AddObjectDeviceInput> fusionObjectDeviceInputs = [];

        // 要回傳的結果
        OutCreateObjectDevice outCreateObjectDevice = new();

        if (addObjectDeviceList == null || addObjectDeviceList.Count() == 0)
        {
            return outCreateObjectDevice;
        }

        outCreateObjectDevice.InsertObjectDeviceResultList = [];
        outCreateObjectDevice.InsertObjectDeviceDetailResultList = [];

        var objectCodeList = addObjectDeviceList.Select(e => e.ObjectCode).Distinct();

        // 將新增ObjectDevice資料轉換成Fusion Object資料
        foreach (string objectCode in objectCodeList)
        {
            fusionObjectDeviceInputs.Add(new()
            {
                code = objectCode,
                devicePids = addObjectDeviceList.Where(e => e.ObjectCode == objectCode).Select(e => e.Pid).ToArray()
            });
        }

        // 執行新增Fusion Object Device
        outCreateObjectDevice.APIResultList = await _objectService.AddDevice(fusionObjectDeviceInputs);

        // 將新增Fusion Object Device成功的資料寫入DB
        foreach (var result in outCreateObjectDevice.APIResultList)
        {
            var objectDevices = addObjectDeviceList.Where(e => e.ObjectCode == result.code);

            var addObjectDevice = objectDevices.Select(e => new ObjectDevice
            {
                AppCode = appCode,
                AreaCode = e.AreaCode,
                ObjectCode = result.code,
                Pid = e.Pid,
                MmWaveType = e.MmWaveType,
                CreateUserAccount = _user.Account,
                CreateDate = DateTime.Now,
                ModifyDate = DateTime.Now
            });

            int? createResult = await _dataAccessService.CreateRangeAsync(addObjectDevice);
            outCreateObjectDevice.InsertObjectDeviceResultList.Add(new DBResult { code = result.code, result = createResult != null && createResult == addObjectDevice.Count() });
        }

        return outCreateObjectDevice;
    }

    public async Task<OutCreateEvents> CreateEvents(string appCode,
        List<Repository.Models.Entities.ObjectEvent> objectEventList,
        List<Repository.Models.Entities.ObjectDeviceDetail> objectDeviceDetailList
        )
    {
        var fenceList = await _dataAccessService.Fetch<Fence>(e => e.AppCode == appCode).ToListAsync();
        var fenceStationList = await _dataAccessService.Fetch<FenceStation>(e => e.AppCode == appCode).ToListAsync();
        var fenceAlarmGroupList = await _dataAccessService.Fetch<FenceAlarmGroup>().ToListAsync();

        // 要新增到Console的ObjectEvent資料
        List<PostFusionEventInput> postFusionEventInputList = [];

        // 要更新到Console的ObjectEvent資料
        List<PatchFusionEventInput> patchFusionEventInputList = [];

        // 要回傳的結果
        OutCreateEvents outCreateEvents = new();

        // 將ObjectEvent資料轉換成Fusion Event資料
        foreach (var objectEvent in objectEventList)
        {
            PostFusionEventInput postFusionEventInput = new()
            {
                enable = true,
                code = objectEvent.EventCode,
                name = GenerateEventName(new Models.Service.Configuration.InGenerateEventName
                {
                    ServiceCode = objectEvent.ServiceCode,
                    FenceName = fenceList.FirstOrDefault(f => f.FenceCode == GetFenceCodeByEventCode(objectEvent.EventCode))?.FenceName ?? "",
                    EventCode = objectEvent.EventCode,
                    EventName = objectEvent.EventName
                }),
                serviceCode = objectEvent.ServiceCode,
                sponsorDevice = new PostFusionEventInput.SponsorDevice
                {
                    devicePids = [objectEvent.Pid]
                }
            };

            PatchFusionEventInput patchFusionEventInput = new()
            {
                enable = true,
                code = objectEvent.EventCode,
                name = GenerateEventName(new Models.Service.Configuration.InGenerateEventName
                {
                    ServiceCode = objectEvent.ServiceCode,
                    FenceName = fenceList.FirstOrDefault(f => f.FenceCode == GetFenceCodeByEventCode(objectEvent.EventCode))?.FenceName ?? "",
                    EventCode = objectEvent.EventCode,
                    EventName = objectEvent.EventName
                }),
                serviceCode = objectEvent.ServiceCode,
                sponsorDevice = new PatchFusionEventInput.SponsorDevice
                {
                    devicePids = [objectEvent.Pid]
                }
            };

            if (objectEvent.ServiceCode == "SensorDataDriven")
            {
                var objectDeviceDetail = objectDeviceDetailList.First(
                    e => e.AppCode == appCode &&
                    e.AreaCode == objectEvent.AreaCode &&
                    e.ObjectCode == objectEvent.ObjectCode &&
                    e.Pid == objectEvent.Pid &&
                    e.SddResource == objectEvent.SddResource &&
                    e.SddComp == objectEvent.SddComp);

                postFusionEventInput.arguments =
                [
                    new (){key = "sddComp", value = objectEvent.SddComp},
                    new (){key = "sddValue", value = objectDeviceDetail.Threshold.ToString()},
                    new (){key = "autoTreated", value = new[] { "/1043/0/23", "/1043/0/28", "/1043/0/29" }.Contains(objectEvent.SddResource) ? "false" : "true"}, // 如果是起身提醒 @SNS@GU "/1043/0/28", 無體動 @SNS@MV "/1043/0/23", 離床預警 @SNS@LW "/1043/0/29" 的話 autoTreated 為 false
                    new (){key = "sddResource", value = objectEvent.SddResource},
                    new (){key = "sddDuration", value = objectDeviceDetail.Duration.ToString()},
                    new (){key = "alarmInterval", value = objectDeviceDetail.SilentInterval.ToString()},
                    new (){key = "eventRecreatable", value = new[] { "/1043/0/23", "/1043/0/28", "/1043/0/29" }.Contains(objectEvent.SddResource) ? "1" : "0"}, // 如果是起身提醒 @SNS@GU "/1043/0/28", 無體動 @SNS@MV "/1043/0/23", 離床預警 @SNS@LW "/1043/0/29" 的話增加 eventRecreatable 為 1 否則為 0
                ];
                patchFusionEventInput.arguments = postFusionEventInput.arguments.Select(arg => new PatchFusionEventInput.Argument
                {
                    key = arg.key,
                    value = arg.value
                }).ToList();

            }
            else if (new[] { "Enter", "Leave" }.Contains(objectEvent.ServiceCode))
            {
                string fenceCode = this.GetFenceCodeByEventCode(objectEvent.EventCode);

                var fence = fenceList.First(e => e.AppCode == appCode && e.AreaCode == objectEvent.AreaCode && e.FenceCode == fenceCode);

                postFusionEventInput.stationLayers = fenceStationList.Where(e => e.AreaCode == objectEvent.AreaCode && e.FenceCode == fenceCode)
                    .Select(f => new PostFusionEventInput.StationLayer
                    {
                        sid = f.Stationsid,
                        layer = int.Parse(f.Layer)
                    }).ToList();
                patchFusionEventInput.stationLayers = postFusionEventInput.stationLayers
                    .Select(sl => new PatchFusionEventInput.StationLayer
                    {
                        sid = sl.sid,
                        layer = sl.layer
                    }).ToList();

                postFusionEventInput.stationAlertGroups = new PostFusionEventInput.StationAlertGroups
                {
                    stationSids = fenceAlarmGroupList.Where(e => e.FenceCode == fenceCode).Select(e => e.Stationsid).ToList(),
                };
                patchFusionEventInput.stationAlertGroups = new PatchFusionEventInput.StationAlertGroups
                {
                    stationSids = fenceAlarmGroupList.Where(e => e.FenceCode == fenceCode).Select(e => e.Stationsid).ToList(),
                };

                postFusionEventInput.arguments =
                [
                    new (){key = "autoTreated", value = "true"},
                    new (){key = "noSignalAsLeave", value = "true"},
                    new (){key = "rssiDelta1", value = fence.RSSIDelta1.ToString()},
                    new (){key = "rssiDelta2", value = fence.RSSIDelta2.ToString()},
                ];
                patchFusionEventInput.arguments = postFusionEventInput.arguments.Select(arg => new PatchFusionEventInput.Argument
                {
                    key = arg.key,
                    value = arg.value
                }).ToList();
            }
            else if (new[] { "mmWaveFallDetection", "mmWaveLeaveBed", "mmWaveStayTimeout" }.Contains(objectEvent.ServiceCode))
            {
                var objectDeviceDetail = objectDeviceDetailList.FirstOrDefault(
                    e => e.AppCode == appCode &&
                    e.AreaCode == objectEvent.AreaCode &&
                    e.ObjectCode == objectEvent.ObjectCode &&
                    e.Pid == objectEvent.Pid &&
                    e.SddResource == objectEvent.SddResource);

                // mmWaveFallDetection 只需要 autoTreated 的參數：如果是 mmWaveFallDetection 事件時 autoTreated 為 false；其餘為 true
                postFusionEventInput.arguments =
                [
                    new (){key = "autoTreated", value = objectEvent.ServiceCode == "mmWaveFallDetection" ? "false" : "true"},
                ];
                // 如果是 mmWaveLeaveBed 事件不需要增加 sddDuration
                // 如果是 mmWaveStayTimeout, mmWaveLeaveBed 事件增加 threshold
                if (objectEvent.ServiceCode == "mmWaveStayTimeout" || objectEvent.ServiceCode == "mmWaveLeaveBed")
                {
                    postFusionEventInput.arguments.Add(new() { key = "threshold", value = objectDeviceDetail?.Threshold?.ToString() ?? "" });
                }

                patchFusionEventInput.arguments = postFusionEventInput.arguments.Select(arg => new PatchFusionEventInput.Argument
                {
                    key = arg.key,
                    value = arg.value
                }).ToList();
            }

            postFusionEventInputList.Add(postFusionEventInput);
            patchFusionEventInputList.Add(patchFusionEventInput);
        }

        outCreateEvents.APIResultList = [];
        outCreateEvents.InsertTableResultList = [];

        // 執行新增 Fusion Even: 先執行更新啟用，如果 event 已經存在則啟用成功，否則執行新增
        var enablePatchFusionEventInputList = patchFusionEventInputList.ToList();
        if (enablePatchFusionEventInputList.Any())
        {
            var patchResults = await _eventService.PatchActiveEvents(enablePatchFusionEventInputList);

            // 把 PatchAPIResult 列表更新成功的轉成 PostAPIResult 列表
            var postResults = patchResults.Where(patchResult => patchResult.errors == null || patchResult.errors.Count == 0)
            .Select(patchResult => new PostAPIResult
            {
                code = patchResult.code,
                pid = patchResult.pid,
                sid = patchResult.sid,
                id = patchResult.id,
                index = patchResult.index,
                errors = patchResult.errors?.Select(error => new FusionCoreAPIError
                {
                    error = error.error,
                    description = error.description,
                    descriptions = error.descriptions
                }).ToList()
            })
            .ToList();

            outCreateEvents.APIResultList.AddRange(postResults);

            // 把 PatchAPIResult 列表更新失敗的執行新增
            postFusionEventInputList = postFusionEventInputList.Where(postFusionEventInput => patchResults.Any(p => p.code == postFusionEventInput.code && (p.errors != null && p.errors.Count > 0))).ToList();
        }

        var addPostFusionEventInputList = postFusionEventInputList.ToList();
        if (addPostFusionEventInputList.Any())
        {
            var postResults = await _eventService.AddActiveEvent(addPostFusionEventInputList);
            outCreateEvents.APIResultList.AddRange(postResults);
        }

        // 將新增Fusion Event成功的資料寫入DB
        foreach (var result in outCreateEvents.APIResultList)
        {
            if (result.errors == null || result.errors.Count == 0 || result.errors[0].error == "eventCodeExists")
            {
                var oe = objectEventList.First(e => e.EventCode == result.code);

                // 檢查 ObjectEvent 是否已經存在，已存在就編輯 Active, Enable 為 true
                var existsObjectEvent = _dataAccessService.Fetch<Repository.Models.Entities.ObjectEvent>(e => e.EventCode == oe.EventCode && e.AreaCode == oe.AreaCode).AsTracking().FirstOrDefault();
                if (existsObjectEvent != null)
                {
                    // 啟用 ObjectEvent 修改 Active, Enable 為 true
                    List<string> updateField = new List<string>();
                    updateField.Add("Active");
                    existsObjectEvent.Active = true;

                    updateField.Add("Enable");
                    existsObjectEvent.Enable = true;

                    updateField.Add("ModifyDate");
                    updateField.Add("ModifyUserAccount");
                    existsObjectEvent.ModifyDate = DateTime.Now;
                    existsObjectEvent.ModifyUserAccount = _user.Account;

                    int? updateResult = await _dataAccessService.UpdateAsync<ObjectEvent>(existsObjectEvent, updateField.ToArray());
                    outCreateEvents.InsertTableResultList.Add(new DBResult { code = oe.EventCode, result = (updateResult != null && updateResult == 1) });
                }
                else
                {
                    // 新增 ObjectEvent
                    Repository.Models.Entities.ObjectEvent objectEvent = new()
                    {
                        AppCode = appCode,
                        AreaCode = oe.AreaCode,
                        ObjectCode = oe.ObjectCode,
                        EventCode = oe.EventCode,
                        Pid = oe.Pid,
                        ServiceCode = oe.ServiceCode,
                        SddResource = oe.SddResource,
                        SddComp = oe.SddComp,
                        CreateUserAccount = _user.Account,
                        Active = true,
                        Enable = true,
                        CreateDate = DateTime.Now,
                        ModifyDate = DateTime.Now
                    };

                    int? createResult = await _dataAccessService.CreateAsync(objectEvent);
                    outCreateEvents.InsertTableResultList.Add(new DBResult { code = oe.EventCode, result = (createResult != null && createResult == 1) });
                }

                // 如果是 Enter 或 Leave 事件要新增 EventFence
                if (new[] { "Enter", "Leave" }.Contains(oe.ServiceCode))
                {
                    // 檢查 EventFence 是否已經存在，已存在就編輯 Active, Enable 為 true
                    var existsEventFence = _dataAccessService.Fetch<Repository.Models.Entities.EventFence>(e => e.EventCode == oe.EventCode && e.AreaCode == oe.AreaCode).AsTracking().FirstOrDefault();
                    if (existsEventFence != null)
                    {
                        // 啟用 EventFence 修改 Active, Enable 為 true
                        var fenceCode = existsEventFence.FenceCode;
                        List<string> updateField = new List<string>();
                        updateField.Add("Active");
                        existsEventFence.Active = true;

                        updateField.Add("Enable");
                        existsEventFence.Enable = true;

                        updateField.Add("ModifyDate");
                        updateField.Add("ModifyUserAccount");
                        existsEventFence.ModifyDate = DateTime.Now;
                        existsEventFence.ModifyUserAccount = _user.Account;

                        int? updateResult = await _dataAccessService.UpdateAsync<EventFence>(existsEventFence, updateField.ToArray());
                        outCreateEvents.InsertTableResultList.Add(new DBResult { code = fenceCode, result = (updateResult != null && updateResult == 1) });
                    }
                    else
                    {
                        // 新增 EventFence
                        var fenceCode = GetFenceCodeByEventCode(oe.EventCode);
                        Repository.Models.Entities.EventFence eventFence = new()
                        {
                            AppCode = appCode,
                            AreaCode = oe.AreaCode,
                            EventCode = oe.EventCode,
                            FenceCode = fenceCode,
                            CreateUserAccount = _user.Account,
                            Active = true,
                            Enable = true,
                            CreateDate = DateTime.Now,
                            ModifyDate = DateTime.Now
                        };

                        int? createResult = await _dataAccessService.CreateAsync(eventFence);
                        outCreateEvents.InsertTableResultList.Add(new DBResult { code = fenceCode, result = (createResult != null && createResult == 1) });
                    }
                }
            }
        }

        return outCreateEvents;
    }

    public async Task<OutDeleteEvents> DeleteEvents(List<Repository.Models.Entities.ObjectEvent> deleteObjectEventList)
    {
        // 要回傳的結果
        OutDeleteEvents outDeleteEvents = new();
        outDeleteEvents.APIResultDict = new Dictionary<string, PatchAPIResult>();
        outDeleteEvents.DeleteTableResultDict = new Dictionary<string, List<DBResult>>();

        // 刪除ObjectEvent 資料
        foreach (var e in deleteObjectEventList)
        {
            // 執行刪除 Fusion Event，更改為 disable Event
            // var resultList = await _eventService.DeleteActiveEvent(e.EventCode);
            var patchEventData = new List<PatchFusionEventInput> { new PatchFusionEventInput { code = e.EventCode, enable = false } };
            var resultList = await _eventService.PatchActiveEvents(patchEventData);

            if (resultList != null && resultList[0] != null)
            {
                var result = resultList[0];
                outDeleteEvents.APIResultDict[e.EventCode] = result;
                outDeleteEvents.DeleteTableResultDict[e.EventCode] = [];

                // 如果刪除成功或是Console 已無此事件，則刪除DB中的資料
                if (result.errors == null || result.errors.Count == 0 || result.errors[0].error == "eventNotFound")
                {
                    var objectEvent = await _dataAccessService.Fetch<Repository.Models.Entities.ObjectEvent>(e => e.EventCode == result.code).AsTracking().FirstAsync();

                    // 刪除 ObjectEvent 修改 Active, Enable 為 false
                    List<string> updateField = new List<string>();
                    updateField.Add("Active");
                    objectEvent.Active = false;

                    updateField.Add("Enable");
                    objectEvent.Enable = false;

                    updateField.Add("ModifyDate");
                    updateField.Add("ModifyUserAccount");
                    objectEvent.ModifyDate = DateTime.Now;
                    objectEvent.ModifyUserAccount = _user.Account;

                    int? deleteResult = await _dataAccessService.UpdateAsync<ObjectEvent>(objectEvent, updateField.ToArray());
                    outDeleteEvents.DeleteTableResultDict[e.EventCode].Add(new DBResult { code = e.EventCode, result = (deleteResult != null && deleteResult > 0) });
                    // await _dataAccessService.DeleteAsync(objectEvent);

                    // 刪除 ObectEvent 對應的 EventFence，修改 Active, Enable 為 false
                    var eventFence = await _dataAccessService.Fetch<Repository.Models.Entities.EventFence>(e => e.EventCode == result.code).AsTracking().FirstOrDefaultAsync();
                    if (eventFence != null)
                    {
                        eventFence.Active = false;
                        eventFence.Enable = false;
                        eventFence.ModifyDate = DateTime.Now;
                        eventFence.ModifyUserAccount = _user.Account;

                        deleteResult = await _dataAccessService.UpdateAsync<EventFence>(eventFence, updateField.ToArray());
                        outDeleteEvents.DeleteTableResultDict[e.EventCode].Add(new DBResult { code = eventFence.FenceCode, result = (deleteResult != null && deleteResult > 0) });
                    }
                }
            }
        }
        return outDeleteEvents;
    }

    public async Task<List<DeleteAPIResult>> DeleteObjectDatum(List<ObjectDatum> deleteObjectDataList)
    {
        var resultList = new List<DeleteAPIResult>();
        foreach (var e in deleteObjectDataList)
        {
            var deleteAPIResult = await _objectService.DeleteObject(e.ObjectCode);
            if (deleteAPIResult != null && deleteAPIResult[0] != null)
            {
                var result = deleteAPIResult[0];
                resultList.Add(result);

                if (result.errors == null || result.errors.Count == 0)
                {
                    var objectData = _dataAccessService.Fetch<Repository.Models.Entities.ObjectDatum>(e => e.ObjectCode == result.code);
                    await _dataAccessService.DeleteAsync(objectData);
                }
            }
        }
        return resultList;
    }
    public async Task<OutDeleteDevices> UboundObjectDevice(List<Repository.Models.Entities.ObjectDevice> deleteObjectDeviceList)
    {
        // 要回傳的結果
        OutDeleteDevices outDeleteDevices = new();
        outDeleteDevices.APIResultDict = new Dictionary<string, DeleteAPIResult>();
        outDeleteDevices.DeleteTableResultDict = new Dictionary<string, List<DBResult>>();

        foreach (var e in deleteObjectDeviceList)
        {
            var resultList = await _deviceService.UnbondObject(e.Pid);

            if (resultList != null && resultList[0] != null)
            {
                var result = resultList[0];

                outDeleteDevices.APIResultDict[e.Pid] = result;
                outDeleteDevices.DeleteTableResultDict[e.Pid] = [];

                if (result.errors == null || result.errors.Count == 0)
                {
                    var objectDevice = _dataAccessService.Fetch<Repository.Models.Entities.ObjectDevice>(e => e.Pid == result.pid);
                    int? deleteResult = await _dataAccessService.DeleteAsync(objectDevice);
                    outDeleteDevices.DeleteTableResultDict[e.Pid].Add(new DBResult { code = e.Pid, result = (deleteResult != null && deleteResult > 0) });

                    var objectDeviceDetail = _dataAccessService.Fetch<Repository.Models.Entities.ObjectDeviceDetail>(e => e.Pid == result.pid);
                    deleteResult = await _dataAccessService.DeleteAsync(objectDeviceDetail);
                    outDeleteDevices.DeleteTableResultDict[e.Pid].Add(new DBResult { code = e.Pid, result = (deleteResult != null && deleteResult > 0) });
                }
            }
        }

        return outDeleteDevices;
    }

    public async Task<(List<ObjectType> objectTypeList, List<Fence> fenceList, List<FenceStation> fenceStationList, List<FenceAlarmGroup> fenceAlarmGroupList)> GetObjectSustainConfData(string appCode)
    {
        var objectTypeList = await _dataAccessService.Fetch<ObjectType>(e => e.AppCode == appCode && e.Enable && e.Active).ToListAsync();
        var fenceList = await _dataAccessService.Fetch<Fence>(e => e.AppCode == appCode).ToListAsync();
        var fenceStationList = await _dataAccessService.Fetch<FenceStation>(e => e.AppCode == appCode).ToListAsync();
        var fenceAlarmGroupList = await _dataAccessService.Fetch<FenceAlarmGroup>().ToListAsync();

        return (objectTypeList, fenceList, fenceStationList, fenceAlarmGroupList);
    }

    public async Task<(List<ObjectDatum>, List<ObjectDevice>, List<Repository.Models.Entities.ObjectDeviceDetail>, List<Repository.Models.Entities.ObjectEvent>)> GetObjectRelationData(InGetObjectRelationData inGetObjectRelationData)
    {
        /**
         * 以下拆成兩段，是因為寫成一次查詢會出現
         * var objectDataList = await _dataAccessService.Fetch<ObjectDatum>(e=>e.AppCode == inGetObjectRelationData.AppCode && inGetObjectRelationData.ObjectList.Any(o=>o.AreaCode == e.AreaCode && o.ObjectCode == e.ObjectCode)).ToListAsync();
         * 
         * System.InvalidOperationException: The LINQ expression '__inGetObjectRelationData_ObjectList_1.Any(e => e.AreaCode == StructuralTypeShaperExpression:
         * Web.Repository.Models.Entities.ObjectDatum
         * ValueBufferExpression:
         * ProjectionBindingExpression: EmptyProjectionMember
         * IsNullable: False
         * .AreaCode && e.ObjectCode == StructuralTypeShaperExpression:
         * Web.Repository.Models.Entities.ObjectDatum
         * ValueBufferExpression:
         * ProjectionBindingExpression: EmptyProjectionMember
         * IsNullable: False
         * .ObjectCode)' could not be translated. Either rewrite the query in a form that can be translated, or switch to client evaluation explicitly by inserting a call to 'AsEnumerable', 'AsAsyncEnumerable', 'ToList', or 'ToListAsync'. See https://go.microsoft.com/fwlink/?linkid=2101038 for more information.
         */
        var objectDataByAppCode = await _dataAccessService.Fetch<ObjectDatum>(e => e.AppCode == inGetObjectRelationData.AppCode).ToListAsync();
        var objectDataList = objectDataByAppCode
            .Where(e => inGetObjectRelationData.ObjectList.Any(o => o.ObjectCode == e.ObjectCode)).ToList();

        var objectDeviceByAppCode = await _dataAccessService.Fetch<ObjectDevice>(e => e.AppCode == inGetObjectRelationData.AppCode).ToListAsync();
        var objectDeviceList = objectDeviceByAppCode
            .Where(e => inGetObjectRelationData.ObjectList.Any(o => o.ObjectCode == e.ObjectCode)).ToList();

        var objectDeviceDetailByAppCode = await _dataAccessService.Fetch<Repository.Models.Entities.ObjectDeviceDetail>(e => e.AppCode == inGetObjectRelationData.AppCode).ToListAsync();
        var objectDeviceDetailList = objectDeviceDetailByAppCode
            .Where(e => inGetObjectRelationData.ObjectList.Any(o => o.ObjectCode == e.ObjectCode)).ToList();

        var objectEventListByAppCode = await _dataAccessService.Fetch<Repository.Models.Entities.ObjectEvent>(e => e.AppCode == inGetObjectRelationData.AppCode).ToListAsync();
        var objectEventList = objectEventListByAppCode
            .Where(e => inGetObjectRelationData.ObjectList.Any(o => o.ObjectCode == e.ObjectCode)).ToList();

        return (objectDataList, objectDeviceList, objectDeviceDetailList, objectEventList);
    }

    public (List<ObjectDatum> objectDataList,
        List<Repository.Models.Entities.ObjectDevice> objectDeviceList,
        List<Repository.Models.Entities.ObjectDeviceDetail> objectDeviceDetailList,
        List<Repository.Models.Entities.ObjectEvent> objectEventList,
        List<EventFence> eventFenceList
        ) ExtractCreateObjectParam(string appCode, List<InCreateObject> paramList)
    {
        List<ObjectDatum> objectDataList = [];
        List<Repository.Models.Entities.ObjectDevice> objectDeviceList = [];
        List<Repository.Models.Entities.ObjectDeviceDetail> objectDeviceDetailList = [];
        List<Repository.Models.Entities.ObjectEvent> objectEventList = [];
        List<EventFence> eventFenceList = [];

        objectDataList = paramList.Select(e => new ObjectDatum
        {
            AppCode = appCode,
            AreaCode = e.AreaCode,
            ObjectType = e.ObjectType,
            ObjectCode = e.ObjectCode,
            Active = true,
            Enable = true,
            Name = e.Name,
            GroupCode = e.GroupCode,
            UsageDepartCode = e.UsageDepartCode,
            Remark = e.Remark,
            EquipmentStatus = e.EquipmentStatus,
            UrgColor = e.UrgColor,
            CreateUserAccount = _user.Account,
            CreateDate = DateTime.Now,
            ModifyDate = DateTime.Now
        }).ToList();

        objectDeviceList = paramList.SelectMany(e =>
            (e.ObjectDeviceList?.Select(od => new Repository.Models.Entities.ObjectDevice
            {
                AppCode = appCode,
                AreaCode = e.AreaCode,
                ObjectCode = e.ObjectCode,
                Pid = od.Pid,
                MmWaveType = string.IsNullOrWhiteSpace(od.MmWaveType) ? null : od.MmWaveType == "Y" ? true : false,
                CreateUserAccount = _user.Account,
                CreateDate = DateTime.Now,
                ModifyDate = DateTime.Now
            }) ?? Enumerable.Empty<Repository.Models.Entities.ObjectDevice>())
        ).ToList();

        objectEventList = paramList.SelectMany(e =>
            (e.ObjectDeviceList?.SelectMany(od =>
                (od.ObjectEventList?.Select(oe => new Repository.Models.Entities.ObjectEvent
                {
                    AppCode = appCode,
                    AreaCode = e.AreaCode,
                    ObjectCode = e.ObjectCode,
                    Pid = od.Pid,
                    ServiceCode = oe.ServiceCode,
                    SddResource = oe.SddResource,
                    EventCode = this.GenerateEventCode(new Models.Service.Configuration.InGenerateEventCode
                    {
                        AppCode = appCode,
                        ServiceCode = oe.ServiceCode,
                        ObjectCode = e.ObjectCode,
                        Pid = od.Pid,
                        FenceCode = oe.FenceCode,
                        SddResource = oe.SddResource,
                        SddComp = oe.SddComp
                    }),
                    EventName = oe.EventName,
                    SddComp = oe.SddComp,
                    Active = true,
                    Enable = true,
                    CreateUserAccount = _user.Account,
                    CreateDate = DateTime.Now,
                    ModifyDate = DateTime.Now
                }) ?? Enumerable.Empty<Repository.Models.Entities.ObjectEvent>())
            ) ?? Enumerable.Empty<Repository.Models.Entities.ObjectEvent>())
        ).ToList();


        objectDeviceDetailList = paramList.SelectMany(e =>
            (e.ObjectDeviceList?.SelectMany(od =>
                (od.ObjectDeviceDetailList?.Select(odd => new Repository.Models.Entities.ObjectDeviceDetail
                {
                    AppCode = appCode,
                    AreaCode = e.AreaCode,
                    ObjectCode = e.ObjectCode,
                    Pid = od.Pid,
                    SddResource = odd.SddResource,
                    SddComp = odd.SddComp,
                    Threshold = odd.Threshold,
                    StayOvertime = odd.StayOvertime,
                    Duration = odd.Duration,
                    SilentInterval = odd.SilentInterval,
                    CreateUserAccount = _user.Account,
                    CreateDate = DateTime.Now,
                    ModifyDate = DateTime.Now
                }) ?? Enumerable.Empty<Repository.Models.Entities.ObjectDeviceDetail>())
            ) ?? Enumerable.Empty<Repository.Models.Entities.ObjectDeviceDetail>())
        ).ToList();

        eventFenceList = paramList.SelectMany(e =>
            (e.ObjectDeviceList?.SelectMany(od =>
                (od.ObjectEventList?.Where(oe => new[] { "Enter", "Leave" }.Contains(oe.ServiceCode)).Select(oe => new Repository.Models.Entities.EventFence
                {
                    AppCode = appCode,
                    AreaCode = e.AreaCode,
                    FenceCode = oe.FenceCode,
                    EventCode = this.GenerateEventCode(new Models.Service.Configuration.InGenerateEventCode
                    {
                        AppCode = appCode,
                        ServiceCode = oe.ServiceCode,
                        ObjectCode = e.ObjectCode,
                        Pid = od.Pid,
                        FenceCode = oe.FenceCode,
                        SddResource = oe.SddResource,
                        SddComp = oe.SddComp
                    }),
                    Active = true,
                    Enable = true,
                    CreateUserAccount = _user.Account,
                    CreateDate = DateTime.Now,
                    ModifyDate = DateTime.Now
                }) ?? Enumerable.Empty<Repository.Models.Entities.EventFence>())
            ) ?? Enumerable.Empty<Repository.Models.Entities.EventFence>())
        ).ToList();


        return (objectDataList, objectDeviceList, objectDeviceDetailList, objectEventList, eventFenceList);
    }

    public (List<ObjectDatum> objectDataList,
        List<Repository.Models.Entities.ObjectDevice> objectDeviceList,
        List<Repository.Models.Entities.ObjectDeviceDetail> objectDeviceDetailList,
        List<Repository.Models.Entities.ObjectEvent> objectEventList,
        List<EventFence> eventFenceList
        ) ExtractUpdateObjectParam(string appCode, List<InUpdateObject> paramList)
    {
        List<ObjectDatum> objectDataList = [];
        List<Repository.Models.Entities.ObjectDevice> objectDeviceList = [];
        List<Repository.Models.Entities.ObjectDeviceDetail> objectDeviceDetailList = [];
        List<Repository.Models.Entities.ObjectEvent> objectEventList = [];
        List<EventFence> eventFenceList = [];

        objectDataList = paramList.Select(e => new ObjectDatum
        {
            AppCode = appCode,
            AreaCode = e.AreaCode,
            ObjectType = e.ObjectType,
            ObjectCode = e.ObjectCode,
            Active = true,
            Enable = true,
            Name = e.Name,
            GroupCode = e.GroupCode,
            UsageDepartCode = e.UsageDepartCode,
            Remark = e.Remark,
            EquipmentStatus = e.EquipmentStatus,
            UrgColor = e.UrgColor,
            CreateUserAccount = _user.Account,
            CreateDate = DateTime.Now,
            ModifyDate = DateTime.Now
        }).ToList();

        objectDeviceList = paramList.SelectMany(e =>
            (e.ObjectDeviceList?.Select(od => new Repository.Models.Entities.ObjectDevice
            {
                AppCode = appCode,
                AreaCode = e.AreaCode,
                ObjectCode = e.ObjectCode,
                Pid = od.Pid,
                MmWaveType = string.IsNullOrWhiteSpace(od.MmWaveType) ? null : od.MmWaveType == "Y" ? true : false,
                CreateUserAccount = _user.Account,
                CreateDate = DateTime.Now,
                ModifyDate = DateTime.Now
            }) ?? Enumerable.Empty<Repository.Models.Entities.ObjectDevice>())
        ).ToList();

        objectEventList = paramList.SelectMany(e =>
            (e.ObjectDeviceList?.SelectMany(od =>
                (od.ObjectEventList?.Select(oe => new Repository.Models.Entities.ObjectEvent
                {
                    AppCode = appCode,
                    AreaCode = e.AreaCode,
                    ObjectCode = e.ObjectCode,
                    Pid = od.Pid,
                    ServiceCode = oe.ServiceCode,
                    SddResource = oe.SddResource,
                    EventCode = this.GenerateEventCode(new Models.Service.Configuration.InGenerateEventCode
                    {
                        AppCode = appCode,
                        ServiceCode = oe.ServiceCode,
                        ObjectCode = e.ObjectCode,
                        Pid = od.Pid,
                        FenceCode = oe.FenceCode,
                        SddResource = oe.SddResource,
                        SddComp = oe.SddComp
                    }),
                    SddComp = oe.SddComp,
                    EventName = oe.EventName,
                    Active = true,
                    Enable = true,
                    CreateUserAccount = _user.Account,
                    CreateDate = DateTime.Now,
                    ModifyDate = DateTime.Now
                }) ?? Enumerable.Empty<Repository.Models.Entities.ObjectEvent>())
            ) ?? Enumerable.Empty<Repository.Models.Entities.ObjectEvent>())
        ).ToList();


        objectDeviceDetailList = paramList.SelectMany(e =>
            (e.ObjectDeviceList?.SelectMany(od =>
                (od.ObjectDeviceDetailList?.Select(odd => new Repository.Models.Entities.ObjectDeviceDetail
                {
                    AppCode = appCode,
                    AreaCode = e.AreaCode,
                    ObjectCode = e.ObjectCode,
                    Pid = od.Pid,
                    SddResource = odd.SddResource,
                    SddComp = odd.SddComp,
                    Threshold = odd.Threshold,
                    StayOvertime = odd.StayOvertime,
                    Duration = odd.Duration,
                    SilentInterval = odd.SilentInterval,
                    CreateUserAccount = _user.Account,
                    CreateDate = DateTime.Now,
                    ModifyDate = DateTime.Now
                }) ?? Enumerable.Empty<Repository.Models.Entities.ObjectDeviceDetail>())
            ) ?? Enumerable.Empty<Repository.Models.Entities.ObjectDeviceDetail>())
        ).ToList();

        eventFenceList = paramList.SelectMany(e =>
            (e.ObjectDeviceList?.SelectMany(od =>
                (od.ObjectEventList?.Where(oe => new[] { "Enter", "Leave" }.Contains(oe.ServiceCode)).Select(oe => new Repository.Models.Entities.EventFence
                {
                    AppCode = appCode,
                    AreaCode = e.AreaCode,
                    FenceCode = oe.FenceCode,
                    EventCode = this.GenerateEventCode(new Models.Service.Configuration.InGenerateEventCode
                    {
                        AppCode = appCode,
                        ServiceCode = oe.ServiceCode,
                        ObjectCode = e.ObjectCode,
                        Pid = od.Pid,
                        FenceCode = oe.FenceCode,
                        SddResource = oe.SddResource,
                        SddComp = oe.SddComp
                    }),
                    Active = true,
                    Enable = true,
                    CreateUserAccount = _user.Account,
                    CreateDate = DateTime.Now,
                    ModifyDate = DateTime.Now
                }) ?? Enumerable.Empty<Repository.Models.Entities.EventFence>())
            ) ?? Enumerable.Empty<Repository.Models.Entities.EventFence>())
        ).ToList();


        return (objectDataList, objectDeviceList, objectDeviceDetailList, objectEventList, eventFenceList);
    }

    //public async Task<List<ReturnError>> ValidateInCreateObjectList(List<InCreateObject> paramList)
    //{
    //    // 檢查傳入的參數是否為空
    //    if (paramList == null || paramList.Count == 0)
    //    {
    //        return [new ReturnError { index = 1, code = "", errors = [new ErrorDetail { index = 1, error = "err.null.objects.Object.param" }] }];
    //    }

    //    string appCode = _user.AppCode;

    //    // 同步跟DB取得資料
    //    var results = await _dataAccessService.ExecuteParallelQueriesAsync(
    //        ("A", async context => await context.Areas.Where(e => e.AppCode == appCode).ToListAsync()),
    //        ("OT", async context => await context.ObjectTypes.Where(e => e.AppCode == appCode).ToListAsync()),
    //        ("O", async context => await context.ObjectData.Where(e => e.AppCode == appCode && e.Active && e.Enable).ToListAsync()),
    //        ("OD", async context => await context.ObjectDevices.Where(e => e.AppCode == appCode).ToListAsync()),
    //        ("OE", async context => await context.ObjectEvents.Where(e => e.AppCode == appCode).ToListAsync()),
    //        ("ODetail", async context => await context.ObjectDeviceDetails.Where(e => e.AppCode == appCode).ToListAsync()),
    //        ("D", async context => await context.Devices.Where(e => e.AppCode == appCode).ToListAsync()),
    //        ("EF", async context => await context.EventFences.Where(e => e.AppCode == appCode).ToListAsync()),
    //        ("DEP", async context => await context.Departments.Where(e => e.AppCode == appCode).ToListAsync())
    //    );

    //    // 解包結果
    //    var areas = (List<Area>)results["A"];
    //    var objects = (List<ObjectDatum>)results["O"];
    //    var objectTypes = (List<ObjectType>)results["OT"];
    //    var objectDevices = (List<Repository.Models.Entities.ObjectDevice>)results["OD"];
    //    var objectEvents = (List<Repository.Models.Entities.ObjectEvent>)results["OE"];
    //    var objectDeviceDetails = (List<Repository.Models.Entities.ObjectDeviceDetail>)results["ODetail"];
    //    var eventFences = (List<EventFence>)results["EF"];
    //    var devices = (List<Device>)results["D"];
    //    var deviceTypes = await GetDeviceTypeList();
    //    var departments = (List<Department>)results["DEP"];

    //    var tempHudDeviceTypeStr = await _preferenceService.FetchGlobalParameter(_user.AppCode, "TempHudDeviceType");
    //    var tempHudDeviceTypeList = JsonSerializer.Deserialize<List<string>>(tempHudDeviceTypeStr);

    //    var monitorAlarmColorStr = await _preferenceService.FetchSysParameter(appCode, _user.AreaCode, "MonitorAlarmColorList");
    //    var monitorAlarmColorList = JsonSerializer.Deserialize<List<string>>(monitorAlarmColorStr);

    //    var errors = new List<ReturnError>();

    //    // 驗證輸入的資料
    //    for (int i = 0; i < paramList.Count; i++)
    //    {
    //        var param = paramList[i];
    //        var errorDetail = new List<ErrorDetail>();

    //        errorDetail.AddRange(ValidateArea(param, areas));
    //        errorDetail.AddRange(ValidateObjectCode(param, objects, paramList, mustExists: false));
    //        errorDetail.AddRange(ValidateObjectName(param));
    //        errorDetail.AddRange(ValidateObjectType(param, objectTypes));
    //        errorDetail.AddRange(ValidateUsageDepartment(param, departments));
    //        errorDetail.AddRange(ValidateUrgColor(param, monitorAlarmColorList));

    //        bool hasBindingTempHudDeviceType = false;// await ValidateObjectDevices(param, objectDevices, devices, deviceTypes, tempHudDeviceTypeList, eventFences, errorDetail);

    //        if (param.ObjectDeviceList != null && param.ObjectDeviceList.Count > 0)
    //        {
    //            foreach (var objectDevice in param.ObjectDeviceList)
    //            {
    //                var objectDeviceErrorList = new List<ErrorDetail>();

    //                objectDeviceErrorList.AddRange(ValidateObjectDevicePid(objectDevice, objectDevices, devices, param.ObjectDeviceList));

    //                if (objectDeviceErrorList.Count == 0)
    //                {
    //                    var device = devices.First(e => e.Pid == objectDevice.Pid);
    //                    var deviceType = deviceTypes.First(e => e.type == device.DeviceType);

    //                    if (tempHudDeviceTypeList.Contains(device.DeviceType))
    //                    {
    //                        hasBindingTempHudDeviceType = true;
    //                    }

    //                    var serviceCodes = deviceType.supportDataEvent.Select(e => e.serviceCode).ToList();
    //                    await ValidateObjectEvents(objectDevice, serviceCodes, eventFences, objectDeviceErrorList);
    //                }

    //                if (objectDeviceErrorList.Count > 0)
    //                {
    //                    errorDetail.Add(new ErrorDetail { Index = errorDetail.Count + 1, Code = objectDevice.Pid, Error = "err.invalid.objects.Object.param.ObjectDevice", Details = objectDeviceErrorList });
    //                }
    //            }
    //        }

    //        // 檢查hasBindingTempHudDeviceType是否為true，如果是，檢查UrgColor是否為空
    //        if (hasBindingTempHudDeviceType && string.IsNullOrWhiteSpace(param.UrgColor))
    //        {
    //            errorDetail.Add(new ErrorDetail { Index = errorDetail.Count + 1, Error = "err.null.objects.Object.param.UrgColor" });
    //        }

    //        if (errorDetail.Count > 0)
    //        {
    //            errors.Add(new ReturnError { Index = i + 1, Code = param.ObjectCode, Errors = errorDetail });
    //        }
    //    }

    //    return errors ;
    //}

    //private List<ErrorDetail> ValidateArea(InCreateObject param, List<Area> areas, bool mustExists=true)
    //{
    //    List<ErrorDetail> errorDetail = [];

    //    if (string.IsNullOrWhiteSpace(param.AreaCode))
    //    {
    //        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.AreaCode, error = "err.null.objects.Object.param.AreaCode" });
    //    }
    //    else if (mustExists && !areas.Any(e => e.AreaCode == param.AreaCode))
    //    {
    //        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.AreaCode, error = "err.notfound.objects.Object.param.AreaCode" });
    //    }

    //    return errorDetail;
    //}

    //private List<ErrorDetail> ValidateObjectCode(InCreateObject param, List<ObjectDatum> objects, List<InCreateObject> paramList, bool mustExists = true)
    //{
    //    List<ErrorDetail> errorDetail = [];

    //    if (string.IsNullOrWhiteSpace(param.ObjectCode))
    //    {
    //        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.objects.Object.param.ObjectCode" });
    //    }
    //    else
    //    {
    //        if (mustExists && objects.Any(e => e.ObjectCode == param.ObjectCode))
    //        {
    //            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.exists.objects.Object.param.ObjectCode" });
    //        }

    //        if (paramList.Count(e => e.ObjectCode == param.ObjectCode) > 1)
    //        {
    //            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.duplicate.objects.Object.param.ObjectCode" });
    //        }

    //        if (param.ObjectCode.Length > 50)
    //        {
    //            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.length.object.Object.param.ObjectCode", code = param.ObjectCode });
    //        }

    //        if (!Regex.IsMatch(param.ObjectCode, @"^[a-zA-Z0-9@:$_-]*$"))
    //        {
    //            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.invalid.objects.Object.param.ObjectCode", code = param.ObjectCode });
    //        }
    //    }

    //    if (string.IsNullOrWhiteSpace(param.Name))
    //    {
    //        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.objects.Object.param.Name" });
    //    }
    //    else if (Encoding.UTF8.GetByteCount(param.Name) > 64)
    //    {
    //        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.length.object.Object.param.Name", code = param.Name });
    //    }

    //    return errorDetail;
    //}

    //private List<ErrorDetail> ValidateObjectName(InCreateObject param)
    //{
    //    List<ErrorDetail> errorDetail = [];

    //    if (string.IsNullOrWhiteSpace(param.Name))
    //    {
    //        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.objects.Object.param.Name" });
    //    }
    //    else if (Encoding.UTF8.GetByteCount(param.Name) > 64)
    //    {
    //        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.length.object.Object.param.Name", code = param.Name });
    //    }

    //    return errorDetail;
    //}

    //private List<ErrorDetail> ValidateObjectType(InCreateObject param, List<ObjectType> objectTypes, bool mustExists = true)
    //{
    //    List<ErrorDetail> errorDetail = [];

    //    if (string.IsNullOrWhiteSpace(param.ObjectType))
    //    {
    //        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.objects.Object.param.ObjectType" });
    //    }
    //    else
    //    {
    //        var objectType = objectTypes.FirstOrDefault(e => e.ObjectTypeCode == param.ObjectType);
    //        if (mustExists) {
    //            if(objectType == null)
    //            {
    //                errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.ObjectType, error = "err.notfound.objects.Object.param.ObjectType" });
    //            }
    //            else if (!objectType.Enable)
    //            {
    //                errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.ObjectType, error = "err.invalid.objects.Object.param.ObjectType" });
    //            }
    //        }
    //    }

    //    return errorDetail;
    //}

    //private List<ErrorDetail> ValidateUsageDepartment(InCreateObject param, List<Department> departments, bool mustExists = true)
    //{
    //    List<ErrorDetail> errorDetail = [];

    //    if (string.IsNullOrWhiteSpace(param.UsageDepartCode))
    //    {
    //        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.objects.Object.param.UsageDepartCode" });
    //    }
    //    else if (mustExists && !departments.Any(e => e.AreaCode == param.AreaCode && e.DeptCode == param.UsageDepartCode))
    //    {
    //        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.UsageDepartCode, error = "err.notfound.objects.Object.param.UsageDepartCode" });
    //    }

    //    return errorDetail;
    //}

    //private List<ErrorDetail> ValidateUrgColor(InCreateObject param, List<string> monitorAlarmColorList)
    //{
    //    List<ErrorDetail> errorDetail = [];

    //    if (!string.IsNullOrWhiteSpace(param.UrgColor) && !monitorAlarmColorList.Contains(param.UrgColor))
    //    {
    //        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.notfound.object.Object.param.UrgColor", code = param.UrgColor });
    //    }

    //    return errorDetail;
    //}
    //private List<ErrorDetail> ValidateObjectDevicePid(Web.Models.Controller.Object.ObjectDevice objectDevice, List<Repository.Models.Entities.ObjectDevice> objectDevices, List<Device> devices, List<Web.Models.Controller.Object.ObjectDevice> paramObjectDevices)
    //{
    //    List<ErrorDetail> errorDetail = [];

    //    if (string.IsNullOrWhiteSpace(objectDevice.Pid))
    //    {
    //        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = objectDevice.Pid, error = "err.null.objects.Object.param.ObjectDevice.Pid" });
    //    }
    //    else
    //    {
    //        if (objectDevices.Any(e => e.Pid == objectDevice.Pid))
    //        {
    //            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = objectDevice.Pid, error = "err.exists.objects.Object.param.ObjectDevice.Pid" });
    //        }

    //        if (paramObjectDevices.Count(e => e.Pid == objectDevice.Pid) > 1)
    //        {
    //            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = objectDevice.Pid, error = "err.duplicate.objects.Object.param.ObjectDevice.Pid" });
    //        }

    //        if (!devices.Any(e => e.Pid == objectDevice.Pid))
    //        {
    //            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = objectDevice.Pid, error = "err.notfound.objects.Object.param.ObjectDevice.Pid" });
    //        }
    //    }

    //    return errorDetail;
    //}
    public async Task<List<ReturnError>> ValidationInRetrieveDevice(InRetrieveDevice param)
    {
        List<ReturnError> returnError = [];
        string appCode = _user.AppCode;

        // 檢查傳入的參數是否為空
        if (param == null)
        {
            returnError.Add(new ReturnError { index = 1, code = "", errors = [new ErrorDetail { index = 1, error = "err.null.devices.Device.param" }] });
        }
        else
        {
            // 同步跟DB取得資料
            var results = await _dataAccessService.ExecuteParallelQueriesAsync(
                ("A", async context => await context.Areas.Where(e => e.AppCode == appCode).ToListAsync()),
                ("DEP", async context => await context.Departments.Where(e => e.AppCode == appCode).ToListAsync())
            );

            // 解包結果
            var areas = (List<Area>)results["A"];
            var depts = (List<Department>)results["DEP"];

            // 檢查AreaCode是否為空
            if (string.IsNullOrWhiteSpace(param.AreaCode))
            {
                returnError.Add(new ReturnError { index = 1, code = "", errors = [new ErrorDetail { index = 1, error = "err.null.devices.Device.param.AreaCode" }] });
            }
            else
            {
                // 檢查AreaCode是否存在
                var area = areas.FirstOrDefault(e => e.AreaCode == param.AreaCode);
                if (area == null)
                {
                    returnError.Add(new ReturnError { index = 1, code = "", errors = [new ErrorDetail { index = 1, error = "err.notfound.devices.Device.param.AreaCode" }] });
                }
            }

            // 如果有UsageDepartCode，檢查UsageDepartCode是否存在
            if (!string.IsNullOrEmpty(param.UsageDepartCode))
            {
                var dept = depts.FirstOrDefault(e => e.AreaCode == param.AreaCode && e.DeptCode == param.UsageDepartCode);

                if (dept == null)
                {
                    returnError.Add(new ReturnError { index = 1, code = "", errors = [new ErrorDetail { index = 1, error = "err.notfound.devices.Device.param.UsageDepartCode" }] });
                }
            }
        }

        return returnError;
    }
    //public async Task<List<NewErrorDetail>> ValidationInCreateDeviceList2(List<InCreateDevice> paramList)
    //{
    //    string appCode = _user.AppCode;

    //    List<NewErrorDetail> errorDetail = [];

    //    // 檢查傳入的參數是否為空
    //    errorDetail.AddRange(_validationRules.CheckParamListEmpty(paramList) ? [] : [new NewErrorDetail { error = "err.null.devices.Device.param" }]);

    //    for (int i = 0; i < paramList.Count; i++)
    //    {
    //        var param = paramList[i];

    //        // 檢查AreaCode是否為空
    //        errorDetail.AddRange(_validationRules.CheckAreaCodeEmpty(param.AreaCode, false) ? [] : [new NewErrorDetail { nestedObjectIndexPath = $"paramList[{i}]", code = param.Pid, error = "err.null.devices.Device.param.AreaCode" }]);
    //        // 檢查AreaCode是否存在
    //        errorDetail.AddRange(await _validationRules.CheckAreaCodeExists(appCode, param.AreaCode, false) ? [] : [new NewErrorDetail { nestedObjectIndexPath = $"paramList[{i}]", code = param.Pid, error = "err.notfound.devices.Device.param.AreaCode" }]);
    //        // 檢查PID是否為空
    //        errorDetail.AddRange(_validationRules.CheckPidEmpty(param.Pid, false) ? [] : [new NewErrorDetail { nestedObjectIndexPath = $"paramList[{i}]", code = param.Pid, error = "err.null.devices.Device.param.Pid" }]);
    //        // 檢查PID是否存在
    //        errorDetail.AddRange(await _validationRules.CheckPidExists(appCode, param.Pid, false) ? [] : [new NewErrorDetail { nestedObjectIndexPath = $"paramList[{i}]", code = param.Pid, error = "err.exists.devices.Device.param.Pid" }]);
    //        // 檢查PID是否重複
    //        errorDetail.AddRange(_validationRules.CheckPidDuplicate(paramList.Select(e => e.Pid)) ?[] : [new NewErrorDetail { nestedObjectIndexPath = $"paramList[{i}]", code = param.Pid, error = "err.duplicate.devices.Device.param.Pid" }]);
    //        // 檢查PID是否為20碼
    //        errorDetail.AddRange(_validationRules.CheckPidLengthIs20(param.Pid) ?[] : [new NewErrorDetail { nestedObjectIndexPath = $"paramList[{i}]", code = param.Pid, error = "err.invalid.devices.Device.param.Pid" }]);
    //    }

    //    return errorDetail;
    //}
    public async Task<List<ReturnError>> ValidationInCreateDeviceList(List<InCreateDevice> paramList)
    {
        // 檢查傳入的參數是否為空
        if (paramList == null || paramList.Count == 0)
        {
            return [new ReturnError { index = 1, code = "", errors = [new ErrorDetail { index = 1, error = "err.null.devices.Device.param" }] }];
        }

        List<ReturnError> errors = [];
        string appCode = _user.AppCode;

        // 同步跟DB取得資料
        var results = await _dataAccessService.ExecuteParallelQueriesAsync(
            ("A", async context => await context.Areas.Where(e => e.AppCode == appCode).ToListAsync()),
            ("D", async context => await context.Devices.Where(e => e.AppCode == appCode).ToListAsync()),
            ("DEP", async context => await context.Departments.Where(e => e.AppCode == appCode).ToListAsync())
        );

        // 解包結果
        var areas = (List<Area>)results["A"];
        var devices = (List<Web.Repository.Models.Entities.Device>)results["D"];
        var deviceTypes = await GetDeviceTypeList();
        var departments = (List<Department>)results["DEP"];

        // 驗證輸入的資料
        for (int i = 0; i < paramList.Count; i++)
        {
            var param = paramList[i];

            List<ErrorDetail> errorDetail = [];

            // 檢查AreaCode是否為空
            if (string.IsNullOrWhiteSpace(param.AreaCode))
            {
                errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.AreaCode, error = "err.null.devices.Device.param.AreaCode" });
            }
            else
            {
                // 檢查AreaCode是否存在
                var area = areas.FirstOrDefault(e => e.AreaCode == param.AreaCode);
                if (area == null)
                {
                    errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.AreaCode, error = "err.notfound.devices.Device.param.AreaCode" });
                }
                else
                {
                    // 檢查PID是否為空
                    if (string.IsNullOrWhiteSpace(param.Pid))
                    {
                        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.devices.Device.param.Pid" });
                    }
                    else
                    {
                        // 檢查PID是否存在
                        var device = devices.FirstOrDefault(e => e.Pid == param.Pid);
                        if (device != null)
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.exists.devices.Device.param.Pid" });
                        }

                        // 檢查PID是否重複
                        var pidCount = paramList.Select(e => e.Pid).Count(e => e == param.Pid);
                        if (pidCount > 1)
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.duplicate.devices.Device.param.Pid" });
                        }

                        // 檢查PID是否為20碼
                        if (param.Pid.Length > 20)
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.length.devices.Device.param.Pid", code = param.Pid });
                        }

                        // 檢查PID是否符合規則 英文數字及 @ : $ _ -
                        if (!Regex.IsMatch(param.Pid, @"^[a-zA-Z0-9@:$_-]*$"))
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.invalid.devices.Device.param.Pid", code = param.Pid });
                        }
                    }

                    // 檢查Enable是否為空
                    if (param.Enable == null)
                    {
                        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.devices.Device.param.Enable" });
                    }
                    else
                    {
                        // 檢查Enable是否為"Y"或"N"
                        if (param.Enable != "Y" && param.Enable != "N")
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.invalid.devices.Device.param.Enable", code = param.Enable });
                        }
                    }

                    // 檢查Name是否為空
                    if (string.IsNullOrWhiteSpace(param.Name))
                    {
                        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.devices.Device.param.Name" });
                    }
                    else
                    {
                        // 檢查Name是否為20碼
                        if (param.Name.Length > 20)
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.length.devices.Device.param.Name", code = param.Name });
                        }

                        // 檢查Name是否符合規則 英文數字及 @ : $ _ -
                        if (!Regex.IsMatch(param.Name, @"^[a-zA-Z0-9@:$_-]*$"))
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.invalid.devices.Device.param.Name", code = param.Name });
                        }
                    }

                    // 檢查DeviceType是否為空
                    if (string.IsNullOrWhiteSpace(param.DeviceType))
                    {
                        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.devices.Device.param.DeviceType" });
                    }
                    else
                    {
                        // 檢查DeviceType是否存在
                        var deviceType = deviceTypes.FirstOrDefault(e => e.type == param.DeviceType);

                        if (deviceType == null)
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.DeviceType, error = "err.notfound.devices.Device.param.DeviceType" });
                        }
                    }

                    // 檢查ManageDepartCode是否為空
                    if (string.IsNullOrWhiteSpace(param.ManageDepartCode))
                    {
                        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.devices.Device.param.ManageDepartCode" });
                    }
                    else
                    {
                        // 檢查ManageDepartCode是否存在
                        var dept = departments.FirstOrDefault(e => e.AreaCode == param.AreaCode && e.DeptCode == param.ManageDepartCode);

                        if (dept == null)
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.ManageDepartCode, error = "err.notfound.devices.Device.param.ManageDepartCode" });
                        }
                    }

                    // 檢查UsageDepartCode是否為空
                    if (string.IsNullOrWhiteSpace(param.UsageDepartCode))
                    {
                        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.devices.Device.param.UsageDepartCode" });
                    }
                    else
                    {
                        // 檢查UsageDepartCode是否存在
                        var dept = departments.FirstOrDefault(e => e.AreaCode == param.AreaCode && e.DeptCode == param.UsageDepartCode);

                        if (dept == null)
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.UsageDepartCode, error = "err.notfound.devices.Device.param.UsageDepartCode" });
                        }
                    }
                }
            }

            if (errorDetail != null && errorDetail.Count > 0)
            {
                errors.Add(new ReturnError { index = i + 1, code = param.Pid, errors = errorDetail });
            }
        }

        return errors;
    }

    public async Task<List<ReturnError>> ValidaionInCreateObjectList(List<InCreateObject> paramList)
    {
        // 檢查傳入的參數是否為空
        if (paramList == null || paramList.Count == 0)
        {
            return [new ReturnError { index = 1, code = "", errors = [new ErrorDetail { index = 1, error = "err.null.objects.Object.param" }] }];
        }

        List<ReturnError> errors = [];
        string appCode = _user.AppCode;

        // 同步跟DB取得資料
        var results = await _dataAccessService.ExecuteParallelQueriesAsync(
            ("A", async context => await context.Areas.Where(e => e.AppCode == appCode).ToListAsync()),
            ("OT", async context => await context.ObjectTypes.Where(e => e.AppCode == appCode).ToListAsync()),
            ("O", async context => await context.ObjectData.Where(e => e.AppCode == appCode && e.Active && e.Enable).ToListAsync()),
            ("OD", async context => await context.ObjectDevices.Where(e => e.AppCode == appCode).ToListAsync()),
            ("OE", async context => await context.ObjectEvents.Where(e => e.AppCode == appCode).ToListAsync()),
            ("ODetail", async context => await context.ObjectDeviceDetails.Where(e => e.AppCode == appCode).ToListAsync()),
            ("D", async context => await context.Devices.Where(e => e.AppCode == appCode).ToListAsync()),
            ("EF", async context => await context.EventFences.Where(e => e.AppCode == appCode).ToListAsync()),
            ("DEP", async context => await context.Departments.Where(e => e.AppCode == appCode).ToListAsync())
        );

        // 解包結果
        var areas = (List<Area>)results["A"];
        var objects = (List<ObjectDatum>)results["O"];
        var objectTypes = (List<ObjectType>)results["OT"];
        var objectDevices = (List<Repository.Models.Entities.ObjectDevice>)results["OD"];
        var objectEvents = (List<Repository.Models.Entities.ObjectEvent>)results["OE"];
        var objectDeviceDetails = (List<Repository.Models.Entities.ObjectDeviceDetail>)results["ODetail"];
        var eventFences = (List<EventFence>)results["EF"];
        var devices = (List<Web.Repository.Models.Entities.Device>)results["D"];
        var deviceTypes = await GetDeviceTypeList();
        var departments = (List<Department>)results["DEP"];

        var tempHudDeviceTypeStr = await _preferenceService.FetchGlobalParameter(_user.AppCode, "TempHudDeviceType");

        // 將TempHudDeviceType的Json 字串轉成List<string>
        var tempHudDeviceTypeList = JsonSerializer.Deserialize<List<string>>(tempHudDeviceTypeStr);

        // 取得監控卡片告警顏色選項
        var monitorAlarmColorStr = await _preferenceService.FetchSysParameter(appCode, _user.AreaCode, "MonitorAlarmColorList");

        // 將監控卡片告警顏色選項的Json 字串轉成List<string>
        var monitorAlarmColorList = JsonSerializer.Deserialize<List<string>>(monitorAlarmColorStr);

        // 驗證輸入的資料
        for (int i = 0; i < paramList.Count; i++)
        {
            var param = paramList[i];

            List<ErrorDetail> errorDetail = [];

            // 檢查AreaCode是否為空
            if (string.IsNullOrWhiteSpace(param.AreaCode))
            {
                errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.AreaCode, error = "err.null.objects.Object.param.AreaCode" });
            }
            else
            {
                // 檢查AreaCode是否存在
                var area = areas.FirstOrDefault(e => e.AreaCode == param.AreaCode);
                if (area == null)
                {
                    errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.AreaCode, error = "err.notfound.objects.Object.param.AreaCode" });
                }
                else
                {
                    // 檢查ObjectCode是否為空
                    if (string.IsNullOrWhiteSpace(param.ObjectCode))
                    {
                        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.objects.Object.param.ObjectCode" });
                    }
                    else
                    {
                        // 檢查ObjectCode是否存在
                        var obj = objects.FirstOrDefault(e => e.ObjectCode == param.ObjectCode);
                        if (obj != null)
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.exists.objects.Object.param.ObjectCode" });
                        }

                        // 檢查ObjectCode是否重複
                        var objectCodeCount = paramList.Select(e => e.ObjectCode).Count(e => e == param.ObjectCode);
                        if (objectCodeCount > 1)
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.duplicate.objects.Object.param.ObjectCode" });
                        }

                        // 檢查ObjectCode是否為50碼
                        if (param.ObjectCode.Length > 50)
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.length.object.Object.param.ObjectCode", code = param.ObjectCode });
                        }

                        // 檢查ObjectCode是否符合規則 英文數字及 @ : $ _ -
                        if (!Regex.IsMatch(param.ObjectCode, @"^[a-zA-Z0-9@:$_-]*$"))
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.invalid.objects.Object.param.ObjectCode", code = param.ObjectCode });
                        }

                    }

                    // 檢查ObjectName是否為空
                    if (string.IsNullOrWhiteSpace(param.Name))
                    {
                        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.objects.Object.param.Name" });
                    }
                    else
                    {
                        // 檢查ObjectName是否為64碼
                        if (Encoding.UTF8.GetByteCount(param.Name) > 64)
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.length.object.Object.param.Name", code = param.Name });
                        }
                    }

                    // 檢查ObjectType 是否為空
                    if (string.IsNullOrWhiteSpace(param.ObjectType))
                    {
                        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.objects.Object.param.ObjectType" });
                    }
                    else
                    {
                        // 檢查ObjectType是否存在
                        var objectType = objectTypes.FirstOrDefault(e => e.ObjectTypeCode == param.ObjectType);
                        if (objectType == null)
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.ObjectType, error = "err.notfound.objects.Object.param.ObjectType" });
                        }
                        else
                        {
                            // 檢查ObjectType是否啟用
                            if (objectType.Enable == false)
                            {
                                errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.ObjectType, error = "err.invalid.objects.Object.param.ObjectType" });
                            }
                        }
                    }

                    // 檢查UsageDepartCode是否為空
                    if (string.IsNullOrWhiteSpace(param.UsageDepartCode))
                    {
                        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.objects.Object.param.UsageDepartCode" });
                    }
                    else
                    {
                        // 檢查UsageDepartCode是否存在
                        var dept = departments.FirstOrDefault(e => e.AreaCode == param.AreaCode && e.DeptCode == param.UsageDepartCode);

                        if (dept == null)
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.UsageDepartCode, error = "err.notfound.objects.Object.param.UsageDepartCode" });
                        }
                    }

                    // 如果有UrgColor，檢查UrgColor是否存在於監控卡片告警顏色選項
                    if (!string.IsNullOrWhiteSpace(param.UrgColor))
                    {
                        // 檢查UrgColor是否存在於監控卡片告警顏色選項
                        if (!monitorAlarmColorList.Any(e => e == param.UrgColor))
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.notfound.object.Object.param.UrgColor", code = param.UrgColor });
                        }
                    }

                    // 此對象是否有綁定TempHudDeviceType，如果有要檢查UrgColor不為空
                    bool hasBindingTempHudDeviceType = false;

                    // ObjectDeviceList不為空時，檢查ObjectDeviceList
                    if (param.ObjectDeviceList != null && param.ObjectDeviceList.Count > 0)
                    {
                        List<ErrorDetail> objectDeviceErrorList = [];

                        foreach (var objectDevice in param.ObjectDeviceList)
                        {
                            // 檢查Pid是否為空
                            if (string.IsNullOrWhiteSpace(objectDevice.Pid))
                            {
                                objectDeviceErrorList.Add(new ErrorDetail { index = objectDeviceErrorList.Count + 1, code = objectDevice.Pid, error = "err.null.objects.Object.param.ObjectDevice.Pid" });
                            }
                            else
                            {
                                // 檢查Pid是否已綁定
                                var objDevice = objectDevices.FirstOrDefault(e => e.Pid == objectDevice.Pid);
                                if (objDevice != null)
                                {
                                    objectDeviceErrorList.Add(new ErrorDetail
                                    {
                                        index = objectDeviceErrorList.Count + 1,
                                        code = objectDevice.Pid,
                                        error = "err.exists.objects.Object.param.ObjectDevice.Pid"
                                    });
                                }

                                // 檢查Pid是否重複
                                var dupPidCount = paramList.SelectMany(e => e.ObjectDeviceList).Select(e => e.Pid).Count(e => e == objectDevice.Pid);
                                if (dupPidCount > 1)
                                {
                                    objectDeviceErrorList.Add(new ErrorDetail
                                    {
                                        index = objectDeviceErrorList.Count + 1,
                                        code = objectDevice.Pid,
                                        error = "err.duplicate.objects.Object.param.ObjectDevice.Pid"
                                    });
                                }

                                // 檢查Pid是否存在
                                var device = devices.FirstOrDefault(e => e.Pid == objectDevice.Pid);
                                if (device == null)
                                {
                                    objectDeviceErrorList.Add(new ErrorDetail
                                    {
                                        index = objectDeviceErrorList.Count + 1,
                                        code = objectDevice.Pid,
                                        error = "err.notfound.objects.Object.param.ObjectDevice.Pid"
                                    });
                                }
                                else
                                {
                                    // 取得DeviceType
                                    var deviceType = deviceTypes.FirstOrDefault(e => e.type == device.DeviceType);

                                    // 檢查DeviceType是否存在TempHudDeviceTypeList
                                    if (tempHudDeviceTypeList.Any(e => e == deviceType.type))
                                    {
                                        hasBindingTempHudDeviceType = true;
                                    }

                                    // 取得此DeviceType的SupportDataEvent的ServiceCode
                                    var serviceCodes = deviceType.supportDataEvent.Select(e => e.serviceCode).ToList();

                                    // 檢查ObjectEventList是否為空
                                    if (objectDevice.ObjectEventList != null && objectDevice.ObjectEventList.Count > 0)
                                    {
                                        List<ErrorDetail> objectEventErrorList = [];

                                        foreach (var objectEvent in objectDevice.ObjectEventList)
                                        {
                                            // 檢查ServiceCode是否為空
                                            if (string.IsNullOrWhiteSpace(objectEvent.ServiceCode))
                                            {
                                                objectEventErrorList.Add(new ErrorDetail
                                                {
                                                    index = objectEventErrorList.Count + 1,
                                                    code = objectEvent.ServiceCode,
                                                    error = "err.null.objects.Object.param.ObjectDevice.ObjectEvent.ServiceCode"
                                                });
                                            }
                                            else
                                            {
                                                // 檢查ServiceCode是否存在
                                                if (!serviceCodes.Contains(objectEvent.ServiceCode))
                                                {
                                                    objectEventErrorList.Add(new ErrorDetail
                                                    {
                                                        index = objectEventErrorList.Count + 1,
                                                        code = objectEvent.ServiceCode,
                                                        error = "err.notfound.objects.Object.param.ObjectDevice.ObjectEvent.ServiceCode"
                                                    });
                                                }

                                                if (new[] { "Enter", "Leave" }.Contains(objectEvent.ServiceCode))
                                                {
                                                    if (string.IsNullOrWhiteSpace(objectEvent.FenceCode))
                                                    {
                                                        objectEventErrorList.Add(new ErrorDetail
                                                        {
                                                            index = objectEventErrorList.Count + 1,
                                                            code = objectEvent.ServiceCode,
                                                            error = "err.null.objects.Object.param.ObjectDevice.ObjectEvent.FenceCode"
                                                        });
                                                    }
                                                    else
                                                    {
                                                        // 檢查FenceCode是否存在
                                                        var eventFence = eventFences.FirstOrDefault(e => e.FenceCode == objectEvent.FenceCode);

                                                        if (eventFence == null)
                                                        {
                                                            objectEventErrorList.Add(new ErrorDetail
                                                            {
                                                                index = objectEventErrorList.Count + 1,
                                                                code = objectEvent.FenceCode,
                                                                error = "err.notfound.objects.Object.param.ObjectDevice.ObjectEvent.FenceCode"
                                                            });
                                                        }
                                                    }
                                                }
                                                else if (objectEvent.ServiceCode == "SensorDataDriven")
                                                {
                                                    if (string.IsNullOrWhiteSpace(objectEvent.SddResource))
                                                    {
                                                        objectEventErrorList.Add(new ErrorDetail
                                                        {
                                                            index = objectEventErrorList.Count + 1,
                                                            code = objectEvent.ServiceCode,
                                                            error = "err.null.objects.Object.param.ObjectDevice.ObjectEvent.SddResource"
                                                        });
                                                    }
                                                    else
                                                    {
                                                        if (string.IsNullOrWhiteSpace(objectEvent.SddComp))
                                                        {
                                                            objectEventErrorList.Add(new ErrorDetail
                                                            {
                                                                index = objectEventErrorList.Count + 1,
                                                                code = objectEvent.SddResource,
                                                                error = "err.null.objects.Object.param.ObjectDevice.ObjectEvent.SddComp"
                                                            });
                                                        }
                                                        else
                                                        {
                                                            // 檢查SddComp是否為"gt"或"lt"
                                                            if (!(new[] { "lt", "gt" }.Contains(objectEvent.SddComp)))
                                                            {
                                                                objectEventErrorList.Add(new ErrorDetail
                                                                {
                                                                    index = objectEventErrorList.Count + 1,
                                                                    code = objectEvent.SddResource,
                                                                    error = "err.invalid.objects.Object.param.ObjectDevice.ObjectEvent.SddComp"
                                                                });
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        if (objectEventErrorList.Count > 0)
                                        {
                                            objectDeviceErrorList.Add(new ErrorDetail { index = objectDeviceErrorList.Count + 1, error = "err.invalid.objects.Object.param.ObjectDevice.ObjectEvent", details = objectEventErrorList });
                                        }
                                    }
                                }
                            }

                            if (objectDeviceErrorList.Count > 0)
                            {
                                errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = objectDevice.Pid, error = "err.invalid.objects.Object.param.ObjectDevice", details = objectDeviceErrorList });
                            }
                        }
                    }

                    // 檢查hasBindingTempHudDeviceType是否為true，如果是，檢查UrgColor是否為空
                    if (hasBindingTempHudDeviceType && string.IsNullOrWhiteSpace(param.UrgColor))
                    {
                        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.objects.Object.param.UrgColor" });
                    }
                }
            }

            if (errorDetail != null && errorDetail.Count > 0)
            {
                errors.Add(new ReturnError { index = i + 1, code = param.ObjectCode, errors = errorDetail });
            }
        }

        return errors;
    }

    public async Task<List<ReturnError>> ValidationInUpdateUgColor(InUpdateUgColor param, string callMethod = "UpdateUgColor.ObjectController")
    {
        // 檢查傳入的參數是否為空
        if (param == null)
        {
            return [new() { index = 1, code = "", errors = [new() { index = 1, error = $"err.null.{callMethod}.param" }] }];
        }

        List<ReturnError> errors = [];
        string appCode = _user.AppCode;

        var results = await _dataAccessService.ExecuteParallelQueriesAsync(
            ("A", async context => await context.Areas.Where(e => e.AppCode == appCode).ToListAsync()),
            ("O", async context => await context.ObjectData.Where(e => e.AppCode == appCode && e.Active && e.Enable).ToListAsync())
        );

        // 解包結果
        var areas = (List<Area>)results["A"];
        var objects = (List<ObjectDatum>)results["O"];

        // 取得監控卡片告警顏色選項
        var monitorAlarmColorStr = await _preferenceService.FetchSysParameter(appCode, _user.AreaCode, "MonitorAlarmColorList");
        // 將監控卡片告警顏色選項的Json 字串轉成List<string>
        var monitorAlarmColorList = JsonSerializer.Deserialize<List<string>>(monitorAlarmColorStr);

        // 驗證輸入的資料
        List<ErrorDetail> errorDetail = [];

        // 檢查AreaCode是否為空
        if (string.IsNullOrWhiteSpace(param.AreaCode))
        {
            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.AreaCode, error = $"err.null.{callMethod}.param.AreaCode" });
        }
        else
        {
            // 檢查AreaCode是否存在
            var area = areas.FirstOrDefault(e => e.AreaCode == param.AreaCode);
            if (area == null)
            {
                errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.AreaCode, error = $"err.notfound.{callMethod}.param.AreaCode" });
            }
            else
            {
                // 檢查UrgColor是否為空
                if (string.IsNullOrWhiteSpace(param.UrgColor))
                {
                    errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = $"err.null.{callMethod}.param.UrgColor" });
                }
                else
                {
                    // 檢查UrgColor是否存在於監控卡片告警顏色選項
                    if (!monitorAlarmColorList.Any(e => e == param.UrgColor))
                    {
                        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = $"err.notfound.{callMethod}.param.UrgColor", code = param.UrgColor });
                    }
                }

                // 檢查param.ObjectCode 是否有值
                if (param.ObjectCodes == null || param.ObjectCodes.Count == 0)
                {
                    errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = $"err.null.{callMethod}.param.ObjectCodes" });
                }
            }
        }

        if (errorDetail != null && errorDetail.Count > 0)
        {
            errors.Add(new ReturnError { index = 1, code = param.AreaCode, errors = errorDetail });
        }

        // 檢查param.ObjectCodes 是否存在
        if (!string.IsNullOrWhiteSpace(param.AreaCode) && param.ObjectCodes != null && param.ObjectCodes.Count > 0)
        {
            var objectCodes = param.ObjectCodes;
            var areaCode = param.AreaCode;

            for (int i = 0; i < objectCodes.Count; i++)
            {
                var objectCode = objectCodes[i];

                if (!string.IsNullOrWhiteSpace(objectCode))
                {
                    var obj = objects.FirstOrDefault(e => e.AreaCode == areaCode && e.ObjectCode == objectCode);
                    if (obj == null)
                    {
                        errors.Add(new ReturnError { index = i + 1, code = objectCode, errors = [new ErrorDetail { index = 1, error = $"err.notfound.{callMethod}.param.ObjectCode" }] });
                    }
                }
                else
                {
                    errors.Add(new ReturnError { index = i + 1, code = objectCode, errors = [new ErrorDetail { index = 1, error = $"err.null.{callMethod}.param.ObjectCode" }] });
                }
            }
        }

        return errors;

    }
    public async Task<List<ReturnError>> ValidaionInUpdateObjectList(List<InCreateObject> paramList)
    {
        // 檢查傳入的參數是否為空
        if (paramList == null || paramList.Count == 0)
        {
            return [new() { index = 1, code = "", errors = [new() { index = 1, error = "err.null.objects.Object.param" }] }];
        }

        List<ReturnError> errors = [];
        string appCode = _user.AppCode;

        // 同步跟DB取得資料
        var results = await _dataAccessService.ExecuteParallelQueriesAsync(
            ("A", async context => await context.Areas.Where(e => e.AppCode == appCode).ToListAsync()),
            ("OT", async context => await context.ObjectTypes.Where(e => e.AppCode == appCode).ToListAsync()),
            ("O", async context => await context.ObjectData.Where(e => e.AppCode == appCode && e.Active && e.Enable).ToListAsync()),
            ("OD", async context => await context.ObjectDevices.Where(e => e.AppCode == appCode).ToListAsync()),
            ("OE", async context => await context.ObjectEvents.Where(e => e.AppCode == appCode).ToListAsync()),
            ("ODetail", async context => await context.ObjectDeviceDetails.Where(e => e.AppCode == appCode).ToListAsync()),
            ("D", async context => await context.Devices.Where(e => e.AppCode == appCode).ToListAsync()),
            ("EF", async context => await context.EventFences.Where(e => e.AppCode == appCode).ToListAsync()),
            ("DEP", async context => await context.Departments.Where(e => e.AppCode == appCode).ToListAsync())
        );

        // 解包結果
        var areas = (List<Area>)results["A"];
        var objects = (List<ObjectDatum>)results["O"];
        var objectTypes = (List<ObjectType>)results["OT"];
        var objectDevices = (List<Repository.Models.Entities.ObjectDevice>)results["OD"];
        var objectEvents = (List<Repository.Models.Entities.ObjectEvent>)results["OE"];
        var objectDeviceDetails = (List<Repository.Models.Entities.ObjectDeviceDetail>)results["ODetail"];
        var eventFences = (List<EventFence>)results["EF"];
        var devices = (List<Web.Repository.Models.Entities.Device>)results["D"];
        var deviceTypes = await GetDeviceTypeList();
        var departments = (List<Department>)results["DEP"];

        var tempHudDeviceTypeStr = await _preferenceService.FetchGlobalParameter(_user.AppCode, "TempHudDeviceType");

        // 將TempHudDeviceType的Json 字串轉成List<string>
        var tempHudDeviceTypeList = JsonSerializer.Deserialize<List<string>>(tempHudDeviceTypeStr);

        // 取得監控卡片告警顏色選項
        var monitorAlarmColorStr = await _preferenceService.FetchSysParameter(appCode, _user.AreaCode, "MonitorAlarmColorList");

        // 將監控卡片告警顏色選項的Json 字串轉成List<string>
        var monitorAlarmColorList = JsonSerializer.Deserialize<List<string>>(monitorAlarmColorStr);

        // 驗證輸入的資料
        for (int i = 0; i < paramList.Count; i++)
        {
            var param = paramList[i];

            List<ErrorDetail> errorDetail = [];

            // 檢查AreaCode是否為空
            if (string.IsNullOrWhiteSpace(param.AreaCode))
            {
                errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.AreaCode, error = "err.null.objects.Object.param.AreaCode" });
            }
            else
            {
                // 檢查AreaCode是否存在
                var area = areas.FirstOrDefault(e => e.AreaCode == param.AreaCode);
                if (area == null)
                {
                    errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.AreaCode, error = "err.notfound.objects.Object.param.AreaCode" });
                }
                else
                {
                    // 檢查ObjectCode是否為空
                    if (string.IsNullOrWhiteSpace(param.ObjectCode))
                    {
                        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.objects.Object.param.ObjectCode" });
                    }
                    else
                    {
                        // 檢查ObjectCode是否存在
                        var obj = objects.FirstOrDefault(e => e.ObjectCode == param.ObjectCode);
                        if (obj == null)
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.notfound.objects.Object.param.ObjectCode" });
                        }

                        // 檢查ObjectCode是否重複
                        var objectCodeCount = paramList.Select(e => e.ObjectCode).Count(e => e == param.ObjectCode);
                        if (objectCodeCount > 1)
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.duplicate.objects.Object.param.ObjectCode" });
                        }
                    }

                    // 檢查ObjectName是否為空
                    if (string.IsNullOrWhiteSpace(param.Name))
                    {
                        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.objects.Object.param.Name" });
                    }
                    else
                    {
                        // 檢查ObjectName是否為64碼
                        if (Encoding.UTF8.GetByteCount(param.Name) > 64)
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.length.object.Object.param.Name", code = param.Name });
                        }
                    }

                    // 檢查ObjectType 是否為空
                    if (string.IsNullOrWhiteSpace(param.ObjectType))
                    {
                        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.objects.Object.param.ObjectType" });
                    }
                    else
                    {
                        // 檢查ObjectType是否存在
                        var objectType = objectTypes.FirstOrDefault(e => e.ObjectTypeCode == param.ObjectType);
                        if (objectType == null)
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.ObjectType, error = "err.notfound.objects.Object.param.ObjectType" });
                        }
                        else
                        {
                            // 檢查ObjectType是否啟用
                            if (objectType.Enable == false)
                            {
                                errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.ObjectType, error = "err.invalid.objects.Object.param.ObjectType" });
                            }
                        }
                    }

                    // 檢查UsageDepartCode是否為空
                    if (string.IsNullOrWhiteSpace(param.UsageDepartCode))
                    {
                        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.objects.Object.param.UsageDepartCode" });
                    }
                    else
                    {
                        // 檢查UsageDepartCode是否存在
                        var dept = departments.FirstOrDefault(e => e.AreaCode == param.AreaCode && e.DeptCode == param.UsageDepartCode);

                        if (dept == null)
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = param.UsageDepartCode, error = "err.notfound.objects.Object.param.UsageDepartCode" });
                        }
                    }

                    // 如果有UrgColor，檢查UrgColor是否存在於監控卡片告警顏色選項
                    if (!string.IsNullOrWhiteSpace(param.UrgColor))
                    {
                        // 檢查UrgColor是否存在於監控卡片告警顏色選項
                        if (!monitorAlarmColorList.Any(e => e == param.UrgColor))
                        {
                            errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.notfound.object.Object.param.UrgColor", code = param.UrgColor });
                        }
                    }

                    // 此對象是否有綁定TempHudDeviceType，如果有要檢查UrgColor不為空
                    bool hasBindingTempHudDeviceType = false;

                    // ObjectDeviceList不為空時，檢查ObjectDeviceList
                    if (param.ObjectDeviceList != null && param.ObjectDeviceList.Count > 0)
                    {
                        List<ErrorDetail> objectDeviceErrorList = [];

                        foreach (var objectDevice in param.ObjectDeviceList)
                        {
                            // 檢查Pid是否為空
                            if (string.IsNullOrWhiteSpace(objectDevice.Pid))
                            {
                                objectDeviceErrorList.Add(new ErrorDetail { index = objectDeviceErrorList.Count + 1, code = objectDevice.Pid, error = "err.null.objects.Object.param.ObjectDevice.Pid" });
                            }
                            else
                            {
                                // 檢查Pid是否已被其它對象綁定
                                var objDevice = objectDevices.FirstOrDefault(e => e.Pid == objectDevice.Pid && e.ObjectCode != param.ObjectCode);
                                if (objDevice != null)
                                {
                                    objectDeviceErrorList.Add(new ErrorDetail
                                    {
                                        index = objectDeviceErrorList.Count + 1,
                                        code = objectDevice.Pid,
                                        error = "err.exists.objects.Object.param.ObjectDevice.Pid"
                                    });
                                }

                                // 檢查Pid是否重複
                                var dupPidCount = paramList.SelectMany(e => e.ObjectDeviceList).Select(e => e.Pid).Count(e => e == objectDevice.Pid);
                                if (dupPidCount > 1)
                                {
                                    objectDeviceErrorList.Add(new ErrorDetail
                                    {
                                        index = objectDeviceErrorList.Count + 1,
                                        code = objectDevice.Pid,
                                        error = "err.duplicate.objects.Object.param.ObjectDevice.Pid"
                                    });
                                }

                                // 檢查Pid是否存在
                                var device = devices.FirstOrDefault(e => e.Pid == objectDevice.Pid);
                                if (device == null)
                                {
                                    objectDeviceErrorList.Add(new ErrorDetail
                                    {
                                        index = objectDeviceErrorList.Count + 1,
                                        code = objectDevice.Pid,
                                        error = "err.notfound.objects.Object.param.ObjectDevice.Pid"
                                    });
                                }
                                else
                                {
                                    // 取得DeviceType
                                    var deviceType = deviceTypes.FirstOrDefault(e => e.type == device.DeviceType);

                                    // 檢查DeviceType是否存在TempHudDeviceTypeList
                                    if (tempHudDeviceTypeList.Any(e => e == deviceType.type))
                                    {
                                        hasBindingTempHudDeviceType = true;
                                    }

                                    // 取得此DeviceType的SupportDataEvent的ServiceCode
                                    var serviceCodes = deviceType.supportDataEvent.Select(e => e.serviceCode).ToList();

                                    // 檢查ObjectEventList是否為空
                                    if (objectDevice.ObjectEventList != null && objectDevice.ObjectEventList.Count > 0)
                                    {
                                        List<ErrorDetail> objectEventErrorList = [];

                                        foreach (var objectEvent in objectDevice.ObjectEventList)
                                        {
                                            // 檢查ServiceCode是否為空
                                            if (string.IsNullOrWhiteSpace(objectEvent.ServiceCode))
                                            {
                                                objectEventErrorList.Add(new ErrorDetail
                                                {
                                                    index = objectEventErrorList.Count + 1,
                                                    code = objectEvent.ServiceCode,
                                                    error = "err.null.objects.Object.param.ObjectDevice.ObjectEvent.ServiceCode"
                                                });
                                            }
                                            else
                                            {
                                                // 檢查ServiceCode是否存在
                                                if (!serviceCodes.Contains(objectEvent.ServiceCode))
                                                {
                                                    objectEventErrorList.Add(new ErrorDetail
                                                    {
                                                        index = objectEventErrorList.Count + 1,
                                                        code = objectEvent.ServiceCode,
                                                        error = "err.notfound.objects.Object.param.ObjectDevice.ObjectEvent.ServiceCode"
                                                    });
                                                }

                                                if (new[] { "Enter", "Leave" }.Contains(objectEvent.ServiceCode))
                                                {
                                                    if (string.IsNullOrWhiteSpace(objectEvent.FenceCode))
                                                    {
                                                        objectEventErrorList.Add(new ErrorDetail
                                                        {
                                                            index = objectEventErrorList.Count + 1,
                                                            code = objectEvent.ServiceCode,
                                                            error = "err.null.objects.Object.param.ObjectDevice.ObjectEvent.FenceCode"
                                                        });
                                                    }
                                                    else
                                                    {
                                                        // 檢查FenceCode是否存在
                                                        var eventFence = eventFences.FirstOrDefault(e => e.FenceCode == objectEvent.FenceCode);

                                                        if (eventFence == null)
                                                        {
                                                            objectEventErrorList.Add(new ErrorDetail
                                                            {
                                                                index = objectEventErrorList.Count + 1,
                                                                code = objectEvent.FenceCode,
                                                                error = "err.notfound.objects.Object.param.ObjectDevice.ObjectEvent.FenceCode"
                                                            });
                                                        }
                                                    }
                                                }
                                                else if (objectEvent.ServiceCode == "SensorDataDriven")
                                                {
                                                    if (string.IsNullOrWhiteSpace(objectEvent.SddResource))
                                                    {
                                                        objectEventErrorList.Add(new ErrorDetail
                                                        {
                                                            index = objectEventErrorList.Count + 1,
                                                            code = objectEvent.ServiceCode,
                                                            error = "err.null.objects.Object.param.ObjectDevice.ObjectEvent.SddResource"
                                                        });
                                                    }
                                                    else
                                                    {
                                                        if (string.IsNullOrWhiteSpace(objectEvent.SddComp))
                                                        {
                                                            objectEventErrorList.Add(new ErrorDetail
                                                            {
                                                                index = objectEventErrorList.Count + 1,
                                                                code = objectEvent.SddResource,
                                                                error = "err.null.objects.Object.param.ObjectDevice.ObjectEvent.SddComp"
                                                            });
                                                        }
                                                        else
                                                        {
                                                            // 檢查SddComp是否為"gt"或"lt"
                                                            if (!(new[] { "lt", "gt" }.Contains(objectEvent.SddComp)))
                                                            {
                                                                objectEventErrorList.Add(new ErrorDetail
                                                                {
                                                                    index = objectEventErrorList.Count + 1,
                                                                    code = objectEvent.SddResource,
                                                                    error = "err.invalid.objects.Object.param.ObjectDevice.ObjectEvent.SddComp"
                                                                });
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }

                                        if (objectEventErrorList.Count > 0)
                                        {
                                            objectDeviceErrorList.Add(new ErrorDetail { index = objectDeviceErrorList.Count + 1, error = "err.invalid.objects.Object.param.ObjectDevice.ObjectEvent", details = objectEventErrorList });
                                        }
                                    }
                                }
                            }

                            if (objectDeviceErrorList.Count > 0)
                            {
                                errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, code = objectDevice.Pid, error = "err.invalid.objects.Object.param.ObjectDevice", details = objectDeviceErrorList });
                            }
                        }
                    }

                    // 檢查hasBindingTempHudDeviceType是否為true，如果是，檢查UrgColor是否為空
                    if (hasBindingTempHudDeviceType && string.IsNullOrWhiteSpace(param.UrgColor))
                    {
                        errorDetail.Add(new ErrorDetail { index = errorDetail.Count + 1, error = "err.null.objects.Object.param.UrgColor" });
                    }
                }
            }

            if (errorDetail != null && errorDetail.Count > 0)
            {
                errors.Add(new ReturnError { index = i + 1, code = param.ObjectCode, errors = errorDetail });
            }
        }

        return errors;
    }

    public async Task<List<DeviceType>> FetchSupportDeviceTypeList(string appCode, List<string> areaCodeList)
    {
        // 取得 Core 所有有支援的 DeviceType 和服務語言列表
        var devTypeList = await _deviceService.GetDeviceTypeList().ConfigureAwait(false);
        var serviceLangList = await this.GetServiceList().ConfigureAwait(false);

        // 查詢 SysParameters 作為基礎數據（假設一定存在）
        var sysParameter = await _dataAccessService.Fetch<SysParameters>(y => y.ParaCode.ToLower() == "supportdevicetype")
            .FirstAsync(); // 使用 FirstAsync，因為一定找得到

        // 查詢 GlobalSysPara 用於覆蓋 ParaValue（可能為 null）
        var globalSysPara = await _dataAccessService.Fetch<GlobalSysPara>(y => y.AppCode == appCode
                                                                           && y.ParaCode.ToLower() == "supportdevicetype")
            .FirstOrDefaultAsync();

        // 決定最終的 ParaValue
        string finalParaValue = globalSysPara?.ParaValue ?? sysParameter.ParaValue;

        // 決定設備類型列表
        List<string> deviceTypeCodeList = string.IsNullOrEmpty(finalParaValue)
            ? devTypeList.Select(y => y.type).ToList() // 如果 ParaValue 為空，使用所有 devTypeList
            : JsonSerializer.Deserialize<List<string>>(finalParaValue); // 否則使用 ParaValue 中的值

        // 生成結果
        var result = devTypeList
            .Where(x => deviceTypeCodeList.Contains(x.type))
            .Select(x => new DeviceType
            {
                type = x.type,
                name = $"${x.langs?.nameId}",
                isOtaSupported = x.isOtaSupported,
                isPositioningSupported = x.isPositioningSupported,
                isMsgSupported = x.isMsgSupported,
                supportDataEvent = x.supportDataEvent?.Select(y => new supportDataEvent
                {
                    serviceCode = y.serviceCode,
                    name = $"${serviceLangList.FirstOrDefault(z => z.code == y.serviceCode)?.langs?.nameId}",
                    autoTreated = y.autoTreated,
                    sddResource = y.sddResource?.Select(z => new sddResource
                    {
                        id = z.id,
                        langsId = z.langsId,
                        sddComp = z.sddComp,
                        threshold = z.threshold
                    }).ToList()
                })?.ToList() ?? []
            }).ToList();
        return result;
    }

    public async Task<List<serviceCode>> GetServiceList()
    {
        //由WebAPI取得所有服務列表（用於多國語言的轉換）
        var serviceList = await _langService.GetServiceCodes().ConfigureAwait(false);

        return serviceList;
    }

    #region public FetchIsShowOtherDeptDeviceOnDeptInventory 取得 GlobalSysPara 中 ShowOtherDepartDevice 設定值
    public async Task<bool> FetchIsShowOtherDeptDeviceOnDeptInventory(string appCode)
    {

        // Query SysParameters table
        var sysParameter = await _dataAccessService.Fetch<SysParameters>(e => e.ParaCode == "ShowOtherDepartDevice")
            .FirstOrDefaultAsync();

        // Query GlobalSysPara table
        var globalSysPara = await _dataAccessService.Fetch<GlobalSysPara>(e => e.AppCode == appCode && e.ParaCode == "ShowOtherDepartDevice")
            .FirstOrDefaultAsync();

        // Determine which ParaValue to use
        string paraValue;
        if (globalSysPara != null)
        {
            paraValue = globalSysPara.ParaValue;
        }
        else if (sysParameter != null)
        {
            paraValue = sysParameter.ParaValue;
        }
        else
        {
            return false;
        }

        return bool.Parse(paraValue);
    }
    #endregion

    public async Task<List<DeviceAllInfo>> GetAllDevicesWithoutPermissionCheck(string appCode)
    {
        IEnumerable<string> areaCodeList = _user.AreaCodeList;

        var allDeviceList = _dataAccessService.Fetch<Web.Repository.Models.Entities.Device>(x => x.AppCode == appCode).ToList();
        var allAreaList = _dataAccessService.Fetch<Area>(e => e.AppCode == _user.AppCode && _user.AreaCodeList.Contains(e.AreaCode)).ToList();
        var allObjectDeviceList = _dataAccessService.Fetch<ObjectDevice>(e => e.AppCode == _user.AppCode && _user.AreaCodeList.Contains(e.AreaCode)).ToList();
        var fusionDeviceList = await _deviceService.GetDeviceList().ConfigureAwait(false);

        //取得所有對象資料
        var objectList = _dataAccessService.Fetch<ObjectDatum>(x => x.AppCode == appCode).ToList();

        //所有單位清單(僅供查找非所屬單位之裝置所屬次平面用)
        var allDeptList = _dataAccessService.Fetch<Department>(x => x.AppCode == appCode && x.Enable == true).ToList();

        var query = _dataAccessService.ExecuteLINQ
        (
            from d in allDeviceList
            join dept1 in allDeptList on new { d.AppCode, d.AreaCode, d.ManageDepartCode } equals new { dept1.AppCode, dept1.AreaCode, ManageDepartCode = dept1.DeptCode } into dept1Group
            from dept1 in dept1Group.DefaultIfEmpty()
            join dept2 in allDeptList on new { d.AppCode, d.AreaCode, d.UsageDepartCode } equals new { dept2.AppCode, dept2.AreaCode, UsageDepartCode = dept2.DeptCode } into dept2Group
            from dept2 in dept2Group.DefaultIfEmpty()
            join a in allAreaList on new { d.AppCode, d.AreaCode } equals new { a.AppCode, a.AreaCode }
            join od in allObjectDeviceList on new { d.AppCode, d.AreaCode, Pid = d.Pid } equals new { od.AppCode, od.AreaCode, Pid = od.Pid } into odGroup
            from od in odGroup.DefaultIfEmpty()
            join o in objectList on od != null ? new { od.AppCode, od.AreaCode, od.ObjectCode } : new { AppCode = (string)null, AreaCode = (string)null, ObjectCode = (string)null } equals new { o.AppCode, o.AreaCode, o.ObjectCode } into oGroup
            from o in oGroup.DefaultIfEmpty()
            join fusion in fusionDeviceList on d.Pid equals fusion.pid into fusionGroup
            from fusion in fusionGroup.DefaultIfEmpty()
            select new DeviceAllInfo
            {
                DeviceId = d.DeviceId,
                AppCode = d.AppCode,
                AreaCode = d.AreaCode,
                Pid = d.Pid,
                Active = d.Active,
                Enable = d.Enable,
                Name = d.Name,
                DeviceType = d.DeviceType,
                ManageDepartCode = d.ManageDepartCode,
                UsageDepartCode = d.UsageDepartCode,
                StationSid = d.StationSid,
                MmWaveType = od?.MmWaveType == true ? "Y" : (od?.MmWaveType == false ? "N" : ""),
                CreateUserAccount = d.CreateUserAccount,
                CreateDate = d.CreateDate,
                ModifyUserAccount = d.ModifyUserAccount,
                ModifyDate = d.ModifyDate,
                ManageDepartName = dept1?.DeptName,
                UsageDepartName = dept2?.DeptName,
                AreaName = a.AreaName,
                ObjectCode = o?.ObjectCode, // 使用安全导航操作符
                ObjectName = o?.Name,
                ObjectType = o?.ObjectType,
                GroupCode = o?.GroupCode,
                EquipmentStatus = o?.EquipmentStatus,
                Battery = fusion?.configurations?.FirstOrDefault(e => e.resourceId == Constants.ResourceId.Battery)?.value ?? "",
                Version = fusion?.version?.systemVersion ?? ""
            }
        );

        return [.. query];
    }

    public List<DeviceAllInfo> GetDeviceAllInfo(string appCode)
    {
        IEnumerable<string> areaCodeList = _user.AreaCodeList;

        var allDeviceList = _dataAccessService.Fetch<Web.Repository.Models.Entities.Device>(x => x.AppCode == appCode).ToList();
        var allAreaList = _dataAccessService.Fetch<Area>(e => e.AppCode == _user.AppCode && _user.AreaCodeList.Contains(e.AreaCode)).ToList();
        var allObjectDeviceList = _dataAccessService.Fetch<ObjectDevice>(e => e.AppCode == _user.AppCode && _user.AreaCodeList.Contains(e.AreaCode)).ToList();

        //取得所有對象資料
        var objectList = _dataAccessService.Fetch<ObjectDatum>(x => x.AppCode == appCode).ToList();

        //所有單位清單(僅供查找非所屬單位之裝置所屬次平面用)
        var allDeptList = _dataAccessService.Fetch<Department>(x => x.AppCode == appCode && x.Enable == true).ToList();

        var query = _dataAccessService.ExecuteLINQ
        (
            from d in allDeviceList
            join dept1 in allDeptList on new { d.AppCode, d.AreaCode, d.ManageDepartCode } equals new { dept1.AppCode, dept1.AreaCode, ManageDepartCode = dept1.DeptCode } into dept1Group
            from dept1 in dept1Group.DefaultIfEmpty()
            join dept2 in allDeptList on new { d.AppCode, d.AreaCode, d.UsageDepartCode } equals new { dept2.AppCode, dept2.AreaCode, UsageDepartCode = dept2.DeptCode } into dept2Group
            from dept2 in dept2Group.DefaultIfEmpty()
            join a in allAreaList on new { d.AppCode, d.AreaCode } equals new { a.AppCode, a.AreaCode }
            join od in allObjectDeviceList on new { d.AppCode, d.AreaCode, Pid = d.Pid } equals new { od.AppCode, od.AreaCode, Pid = od.Pid } into odGroup
            from od in odGroup.DefaultIfEmpty()
            join o in objectList on od != null ? new { od.AppCode, od.AreaCode, od.ObjectCode } : new { AppCode = (string)null, AreaCode = (string)null, ObjectCode = (string)null } equals new { o.AppCode, o.AreaCode, o.ObjectCode } into oGroup
            from o in oGroup.DefaultIfEmpty()
            select new DeviceAllInfo
            {
                DeviceId = d.DeviceId,
                AppCode = d.AppCode,
                AreaCode = d.AreaCode,
                Pid = d.Pid,
                Active = d.Active,
                Enable = d.Enable,
                Name = d.Name,
                DeviceType = d.DeviceType,
                ManageDepartCode = d.ManageDepartCode,
                UsageDepartCode = d.UsageDepartCode,
                StationSid = d.StationSid,
                MmWaveType = od?.MmWaveType == true ? "Y" : (od?.MmWaveType == false ? "N" : ""),
                CreateUserAccount = d.CreateUserAccount,
                CreateDate = d.CreateDate,
                ModifyUserAccount = d.ModifyUserAccount,
                ModifyDate = d.ModifyDate,
                ManageDepartName = dept1?.DeptName,
                UsageDepartName = dept2?.DeptName,
                AreaName = a.AreaName,
                ObjectCode = o?.ObjectCode, // 使用安全导航操作符
                ObjectName = o?.Name,
                ObjectType = o?.ObjectType,
                GroupCode = o?.GroupCode,
                EquipmentStatus = o?.EquipmentStatus
            }
        );

        return [.. query];
    }

    public List<DeviceAllInfo> GetDeviceAllInfo(string areaCode, string deptCode, bool deviceBoundObject)
    {
        string appCode = _user.AppCode;

        List<DeviceAllInfo> allDeviceList = GetDeviceAllInfo(appCode);

        var query = allDeviceList.Where(x => x.AreaCode == areaCode && (string.IsNullOrEmpty(deptCode) || x.UsageDepartCode == deptCode));

        if (deviceBoundObject)
        {
            query = query.Where(x => !string.IsNullOrEmpty(x.ObjectCode));
        }

        return [.. query];
    }

    public List<DeviceAllInfo> GetDeviceAllInfo(string areaCode, List<string> deptCodeList, bool deviceBoundObject)
    {
        string appCode = _user.AppCode;

        List<DeviceAllInfo> allDeviceList = GetDeviceAllInfo(appCode);

        var query = allDeviceList.Where(x => x.AreaCode == areaCode && (deptCodeList.Count() == 0 || deptCodeList.Contains(x.UsageDepartCode)));

        if (deviceBoundObject)
        {
            query = query.Where(x => !string.IsNullOrEmpty(x.ObjectCode));
        }

        return [.. query];
    }

    public async Task<List<TrajectoryOutput>> GetTrajectory(string appCode, string objectCode, string fromDate, string toDate)
    {
        var param = new TrajectoryInput
        {
            search = $"positionTime between '{fromDate},{toDate}'" + (string.IsNullOrEmpty(objectCode) && string.IsNullOrWhiteSpace(objectCode) ? "" : $" and object.code in {objectCode}"),
            inlinecount = true,
            size = 1,
            sort = "positionTime,desc"
        };

        param.size = await _deviceService.GetTrajectoryCount(param);

        var allTrajectoryList = param.size == 0 ? [] : await _deviceService.GetTrajectoryList(param);

        var allTimeList = allTrajectoryList.Select(e => e.positionTime).ToList();

        // Step 1: Join with ObjectDatum
        var step1 = allTrajectoryList
            .Join(_dataAccessService.Fetch<ObjectDatum>(x => x.AppCode == appCode),
                  t => <EMAIL>,
                  obj => obj.ObjectCode,
                  (t, obj) => new
                  {
                      Trajectory = t,
                      ObjectDatum = obj
                  })
            .ToList();

        // Step 2: Join with Device
        var step2 = step1
            .Join(_dataAccessService.Fetch<Web.Repository.Models.Entities.Device>(x => x.AppCode == appCode),
                  temp => temp.Trajectory.device.pid,
                  dev => dev.Pid,
                  (temp, dev) => new
                  {
                      temp.Trajectory,
                      temp.ObjectDatum,
                      Device = dev
                  })
            .ToList();

        // Step 3: Join with ToStation
        var step3 = step2
            .Join(_dataAccessService.Fetch<Station>(x => x.AppCode == appCode),
                  temp => temp.Trajectory.toStation.sid,
                  station => station.SID,
                  (temp, station) => new
                  {
                      temp.Trajectory,
                      temp.ObjectDatum,
                      temp.Device,
                      ToStation = station
                  })
            .ToList();

        // Step 5: Join with Plane
        var step5 = step3
            .Join(_dataAccessService.Fetch<Plane>(x => x.AppCode == appCode),
                  temp => temp.Trajectory.plane.code,
                  plane => plane.PlaneCode,
                  (temp, plane) => new
                  {
                      temp.Trajectory,
                      temp.ObjectDatum,
                      temp.Device,
                      temp.ToStation,
                      Plane = plane
                  })
            .ToList();

        var step6 = step5
            .Join(_dataAccessService.Fetch<Location>(x => x.AppCode == appCode),
                  temp => temp.ToStation.RegionCode,
                  location => location.LocCode,
                  (temp, location) => new
                  {
                      temp.Trajectory,
                      temp.ObjectDatum,
                      temp.Device,
                      temp.ToStation,
                      temp.Plane,
                      Location = location
                  })
            .ToList();

        // Step 6: Filter and project final result
        var step7 = step6
            .Where(x => x.ObjectDatum != null && x.Device != null && x.ToStation != null && x.Plane != null);

        var query = step7.Select((temp, index) => new TrajectoryOutput
        {
            id = index + 1,
            device = new()
            {
                pid = temp.Device.Pid,
                name = temp.Device.Name,
                type = temp.Device.DeviceType ?? ""
            },
            @object = new()
            {
                code = temp.ObjectDatum.ObjectCode,
                name = temp.ObjectDatum.Name,
                type = temp.ObjectDatum.ObjectType
            },
            plane = new()
            {
                code = temp.Trajectory.plane.code,
                name = temp.Plane.PlaneName ?? "", //PlaneName 可能为null
                mapHeight = temp.Plane.MapHeight ?? 0, //MapHeight 可能为null
                mapWidth = temp.Plane.MapWidth ?? 0, //MapWidth 可能为null
                positionX = temp.Plane.PositionX ?? 0, //PositionX 可能为null
                positionY = temp.Plane.PositionY ?? 0, //PositionY 可能为null
                planeMapPath = temp.Plane.PlaneMapPath ?? "", //PlaneMapPath 可能为null
            },
            location = new()
            {
                code = temp.ToStation.RegionCode ?? "",
                name = temp.Location.LocName ?? ""
            },
            toStation = new()
            {
                id = temp.ToStation.Id,
                name = temp.ToStation.StationName ?? "",
                sid = temp.ToStation.SID ?? "",
                type = temp.ToStation.StationType ?? "",
                enable = temp.ToStation.Enable ?? false,
            },
            positionX = temp.Trajectory.positionX,
            positionY = temp.Trajectory.positionY,
            positionTime = temp.Trajectory.positionTime
        });

        return [.. query];
    }

    #region public GetDevicePosition 從WebAPI取得指定裝置（by PID）目前的所在位置
    /// <summary>
    /// 從WebAPI取得指定裝置（by PID）目前的所在位置
    /// </summary>
    /// <param name="pidList"></param>
    /// <returns></returns>
    public async Task<List<Models.Service.Configuration.DevicePositions>> GetDevicePosition(string pidList)
    {
        var coreDevicePositionData = await GetDevicePosition();

        var positionResult = coreDevicePositionData.Where(x => pidList.Contains(x.pid)).ToList();

        return positionResult;
    }
    #endregion

    public async Task<List<DeviceResults.Result>> GetDeviceReport(List<string>? deviceTypeList, List<string>? objectCodeList, List<string>? pidList, List<string> resourceIdList, string startDate, string endDate, string lineType = "LAST", long interval = 1)
    {
        if (string.IsNullOrWhiteSpace(startDate) || string.IsNullOrWhiteSpace(endDate))
        {
            return [];
        }

        DateTime startDateTime = DateTime.Parse(startDate);
        startDateTime = startDateTime.ToUniversalTime();

        DateTime endDateTime = DateTime.Parse(endDate);
        endDateTime = endDateTime.ToUniversalTime();

        // 如果沒有指定DeviceType，則從ObjectCode或PID取得DeviceType
        if (deviceTypeList == null || deviceTypeList.Count == 0)
        {
            if (objectCodeList != null && objectCodeList.Count > 0 && (pidList == null || pidList.Count == 0))
            {
                pidList = _dataAccessService.Fetch<ObjectDevice>(e => objectCodeList.Contains(e.ObjectCode)).Select(e => e.Pid).ToList();
            }

            if (pidList != null && pidList.Count > 0)
            {
                deviceTypeList = _dataAccessService.Fetch<Web.Repository.Models.Entities.Device>(d => pidList.Contains(d.Pid)).Select(d => d.DeviceType).Distinct().ToList();
            }
        }

        if (deviceTypeList == null || deviceTypeList.Count == 0)
        {
            return [];
        }

        List<DeviceResults.Result> results = [];

        foreach (string deviceType in deviceTypeList)
        {
            var inDeviceReport = new InGetDeviceReport
            {
                // 以DeviceType 為主進行&& 條件查詢 @20240909 by Ann決議
                DeviceType = deviceType,
                ObjectCodes = string.Join(",", objectCodeList ?? []),
                Pids = string.Join(",", pidList ?? []),
                // 因/reports/api/{base._apiVersion}/devices/records此API的ResourceIds是不帶斜線的，但Fusion無法修正，所以這邊要去掉斜線 @20240902 by Ann決議
                ResourceIds = string.Join(",", resourceIdList == null ? [] : resourceIdList.Select(x => x.TrimStart('/'))),
                StartedTime = (long)(startDateTime - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)).TotalMilliseconds,
                EndedTime = (long)(endDateTime - new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)).TotalMilliseconds,
                LineType = lineType,
                Interval = interval
            };
            var result = await _deviceService.GetDeviceReport(inDeviceReport);

            results.AddRange(result);
        }

        return results;
    }

    #region public GetDevicePosition 從WebAPI取得所有裝置目前的所在位置
    /// <summary>
    /// 從WebAPI取得所有裝置目前的所在位置
    /// </summary>
    /// <returns></returns>
    public async Task<IEnumerable<Models.Service.Configuration.DevicePositions>> GetDevicePosition()
    {
        // 取得登入者所使用的 AppCode
        var appCode = _user.AppCode;

        // 取得所有裝置的目前位置資料
        var coreDevicePositionData = await _deviceService.GetDevicePositionsList();

        // 移除沒有綁定對象或是沒有定位資訊的裝置
        coreDevicePositionData = coreDevicePositionData.Where(x => x.@object != null && x.position != null && x.position.station != null).ToList();

        // 取得有定位資訊的對象代碼
        var positionObjectCodeList = coreDevicePositionData.Where(x => x.@object != null).Select(x => <EMAIL>).ToList();

        // 取得所有啟用的對象資料
        var objectList = await _dataAccessService.Fetch<ObjectDatum>(o => o.AppCode == appCode && positionObjectCodeList.Contains(o.ObjectCode) && o.Enable == true && o.Active == true).ToListAsync();

        // 移除沒有啟用的對象的裝置
        coreDevicePositionData = coreDevicePositionData.Where(x => objectList.Any(o => o.ObjectCode == <EMAIL>)).ToList();

        // 取得所有裝置的基站編號
        var positionSidList = coreDevicePositionData.Where(x => x.position != null && x.position.station != null && x.position.station.sid != null).Select(x => x.position?.station?.sid).ToList();

        // 取得所有基站所在的次平面代碼
        var sectorStationList = await _dataAccessService.Fetch<SectorStation>(s => s.AppCode == appCode && positionSidList.Contains(s.Stationsid)).ToListAsync();

        var sectorCodes = sectorStationList.Select(e => e.SectorCode).ToArray();

        // 取得所有基站的次平面的資料
        var sectorList = await _dataAccessService.Fetch<Sector>(s => s.AppCode == appCode && sectorCodes.Contains(s.SectorCode)).ToListAsync();

        var planeCodes = sectorList.Select(e => e.PlaneCode).ToArray();

        // 取得所有基站的樓層資料
        var planeList = await _dataAccessService.Fetch<Plane>(p => p.AppCode == appCode && planeCodes.Contains(p.PlaneCode)).ToListAsync();

        // 取得所有基站的資料
        var stationList = await _dataAccessService.Fetch<Station>(s => s.AppCode == appCode && positionSidList.Contains(s.SID)).ToListAsync();

        var regionCodes = stationList.Select(e => e.RegionCode).ToArray();

        // 取得所有基站的位置資料
        var locationList = await _dataAccessService.Fetch<Repository.Models.Entities.Location>(l => l.AppCode == appCode && regionCodes.Contains(l.LocCode)).ToListAsync();

        // 取得所有次平面的部門對應資料
        var departSectorList = await _dataAccessService.Fetch<DepartSector>(d => d.AppCode == appCode && sectorCodes.Contains(d.SectorCode)).ToListAsync();

        // 取得所有對象的使用單位代碼
        var objectUsageDeptCodes = objectList.Select(o => o.UsageDepartCode).ToArray();

        // 取得所有次平面的單位代碼
        var sectorDeptCodes = departSectorList.Select(ds => ds.DeptCode).ToArray();

        // 取得所有對象及次平面的單位資料
        var deptList = await _dataAccessService.Fetch<Department>(d => d.AppCode == appCode && d.Enable == true && (objectUsageDeptCodes.Contains(d.DeptCode) || sectorDeptCodes.Contains(d.DeptCode))).ToListAsync();

        // 取得發生中的事件
        var taskList = await _dataAccessService.Fetch<TaskDatum>(t => t.AppCode == appCode && t.Action == 10).ToListAsync();

        var objectTypeCodes = objectList.Select(e => e.ObjectType).ToArray();

        // 取得所有對象的類型資料
        var objectTypeList = await _dataAccessService.Fetch<ObjectType>(ot => ot.AppCode == appCode && objectTypeCodes.Contains(ot.ObjectTypeCode)).ToListAsync();

        var query = from d in coreDevicePositionData
                    let o = objectList.FirstOrDefault(o => o.ObjectCode == <EMAIL>)
                    let ot = objectTypeList.FirstOrDefault(ot => ot.ObjectTypeCode == o.ObjectType)
                    let tasks = taskList.Where(t => t.SponsorDevicePid == d.pid)
                    join s in stationList on d.position.station.sid equals s.SID into stationGroup
                    from station in stationGroup.DefaultIfEmpty()
                    let sectors = sectorList.Where(s => sectorStationList.Any(ss => ss.Stationsid == (station != null ? station.SID : "") && ss.SectorCode == s.SectorCode))
                    let plane = d.position.plane == null ? null : planeList.FirstOrDefault(p => p.PlaneCode == d.position.plane.code)
                    let location = locationList.FirstOrDefault(l => l.LocCode == (station != null ? station.RegionCode : ""))
                    select new Models.Service.Configuration.DevicePositions
                    {
                        id = d.id,
                        pid = d.pid,
                        name = d.name,
                        @object = new Models.Service.Configuration.Object
                        {
                            code = o?.ObjectCode ?? "err.notFound.GetDevicePosition.ConfiguationService.objectCode",
                            name = o?.Name ?? "err.notFound.GetDevicePosition.ConfiguationService.objectCode",
                            deptCode = o?.UsageDepartCode ?? "err.notFound.GetDevicePosition.ConfiguationService.objectCode",
                            type = o?.ObjectType ?? "err.notFound.GetDevicePosition.ConfiguationService.objectCode",
                            typeName = ot?.Name ?? "err.notFound.GetDevicePosition.ConfiguationService.objectType",
                            groupCode = o == null ? "err.notFound.GetDevicePosition.ConfiguationService.objectCode" : o.GroupCode
                        }, // Check for null @object before creating new Object
                        task =
                        [
                            .. tasks.Select(t => new Models.Service.Configuration.DevicePositions.TaskInfo
                                                    {
                                                        ServiceCode = t.ServiceCode,
                                                        TaskId = t.TaskId
                                                    }),
                        ],
                        position = new Models.Service.Configuration.position
                        {
                            positionX = d.position?.positionX ?? 0,
                            positionY = d.position?.positionY ?? 0,
                            station = new Models.Service.Configuration.station
                            {
                                sid = d.position?.station?.sid ?? "err.notFound.GetDevicePosition.ConfiguationService.StationSid",
                                name = d.position?.station?.name ?? "err.notFound.GetDevicePosition.ConfiguationService.StationSid", // Check for null station
                                enable = d.position?.station?.enable ?? false, // Assuming a default value for enable
                                type = d.position?.station?.type ?? "", // type 有可能是 null
                                regionCode = station?.RegionCode ?? "err.notFound.GetDevicePosition.ConfiguationService.RegionCode"
                            },
                            sector =
                            [
                                .. sectors.Select(s=>new sector
                                                            {
                                                                code = s.SectorCode,
                                                                name = s.SectorName,
                                                                mapHeight = s.MapHeight.ToString(),
                                                                mapWidth = s.MapWidth.ToString(),
                                                                positionX = s.PositionX.ToString(),
                                                                positionY = s.PositionY.ToString(),
                                                                deptCode = string.Join(",", departSectorList.Where(ds => ds.SectorCode == s.SectorCode).Select(ds => ds.DeptCode))
                                                            }),
                            ],
                            plane = new Models.Service.Configuration.plane
                            {
                                code = d.position?.plane?.code ?? "err.notFound.GetDevicePosition.ConfiguationService.PlaneCode",
                                name = d.position?.plane?.name ?? "err.notFound.GetDevicePosition.ConfiguationService.PlaneName",
                                mapHeight = plane?.MapHeight == null ? "err.notFound.GetDevicePosition.ConfiguationService.PlaneCode" : plane.MapHeight.ToString(),
                                mapWidth = plane?.MapWidth == null ? "err.notFound.GetDevicePosition.ConfiguationService.PlaneCode" : plane.MapWidth.ToString(),
                                positionX = plane?.PositionX == null ? "err.notFound.GetDevicePosition.ConfiguationService.PlaneCode" : plane.PositionX.ToString(),
                                positionY = plane?.PositionY == null ? "err.notFound.GetDevicePosition.ConfiguationService.PlaneCode" : plane.PositionY.ToString(),
                            },
                            location = location == null ? null : new Models.Service.Configuration.location
                            {
                                code = location?.LocCode ?? "err.notFound.GetDevicePosition.ConfiguationService.LocationCode",
                                name = location?.LocName ?? "err.notFound.GetDevicePosition.ConfiguationService.LocationName"
                            },
                            latestPositionTime = d.position?.latestPositionTime
                        },
                        modifiesAt = d.modifiesAt
                    };

        return query;
    }
    #endregion

    #region public GetCoreDeviceList 從WebAPI取得指定裝置（by PID）目前資訊
    /// <summary>
    /// 從WebAPI取得指定裝置（by PID）目前資訊
    /// </summary>
    /// <param name="pidList"></param>
    /// <returns></returns>
    public async Task<List<Devices>> GetCoreDeviceList(string pidList)
    {
        var coreDeviceList = await _deviceService.GetDeviceList().ConfigureAwait(false);

        coreDeviceList = coreDeviceList.Where(x => pidList.Contains(x.pid)).ToList();

        return coreDeviceList;
    }
    #endregion

    #region public GetFenceCodeByEventCode 依EventCode 取得FenceCode
    /// <summary>
    /// 依EventCode 取得FenceCode
    /// </summary>
    /// <param name="eventCode"></param>
    /// <returns></returns>
    public string GetFenceCodeByEventCode(string eventCode)
    {
        string fenceCode;

        if (string.IsNullOrWhiteSpace(eventCode))
        {
            return string.Empty;
        }

        string[] splitEventCode = eventCode.Split('@');

        fenceCode = new[] { "Enter", "Leave" }.Contains(splitEventCode[2]) ? splitEventCode[3] : string.Empty;

        return fenceCode;
    }
    #endregion

    #region public GethDeviceTypeList 由WebAPI取得裝置類型及各類型所支援的服務
    /// <summary>
    /// 由WebAPI取得裝置類型及各類型所支援的服務
    /// </summary>
    /// <returns></returns>
    public async Task<List<DeviceType>> GetDeviceTypeList()
    {
        var devTypeList = await _deviceService.GetDeviceTypeList().ConfigureAwait(false);

        return devTypeList;
    }
    #endregion

    #region public GetDeviceTypeList 由WebAPI取得裝置類型及各類型所支援的服務
    /// <summary>
    /// 由WebAPI取得所有裝置類型所支援的SddResource及對應的顯示名稱
    /// </summary>
    /// <returns></returns>
    public async Task<List<sddResource>> GetSddResourceList()
    {
        var deviceTypeList = await GetDeviceTypeList();

        var sddResourceList = deviceTypeList
            .Where(device => device.supportDataEvent != null)
            .SelectMany(device => device.supportDataEvent)
            .Where(eventData => eventData.sddResource != null)
            .SelectMany(eventData => eventData.sddResource)
            .GroupBy(resource => resource.id)
            .Select(group => group.First())
            .ToList();

        return sddResourceList;
    }
    #endregion

    #region public FetchDeviceOnStation 依基站編號取得定位在該基站的裝置列表
    /// <summary>
    /// 依基站編號取得定位在該基站的裝置列表
    /// </summary>
    /// <param name="stationSidList">基站編碼，可以逗號隔開</param>
    /// <returns></returns>
    public async Task<List<device>> FetchDeviceOnStation(string stationSidList)
    {
        var stationList = await _stationService.GetStationListWithDevices(stationSidList);
        var deviceList = stationList.Where(s => s.devices != null).SelectMany(s => s.devices).ToList();

        return deviceList;
    }
    #endregion

    public async Task<List<ReturnError>> CreateDevice(string appCode, List<InCreateDevice> paramList, bool needPostFusion = true)
    {
        var addFusionDeviceInputs = paramList.Select(param => new AddDeviceInput
        {
            pid = param.Pid,
            name = param.Name,
            type = param.DeviceType,
            stationSid = param.StationSid
        }).ToList();

        var addHmnDeviceInputs = paramList.Select(param => new Repository.Models.Entities.Device
        {
            AppCode = appCode,
            AreaCode = param.AreaCode,
            Pid = param.Pid,
            Active = true,
            Enable = param.Enable == "Y",
            Name = param.Name,
            DeviceType = param.DeviceType,
            StationSid = param.StationSid?.ToString(),
            UsageDepartCode = param.UsageDepartCode,
            ManageDepartCode = param.ManageDepartCode,
            CreateUserAccount = _user.Account,
            CreateDate = DateTime.Now,
            ModifyDate = DateTime.Now
        }).ToList();

        List<PostAPIResult> addDeviceResults = [];
        // 如果需要新增Fusion裝置
        if (needPostFusion)
        {
            // 新增Fusion裝置
            addDeviceResults = await _deviceService.AddDevice(addFusionDeviceInputs);
        }

        // 取出Fusion新增成功的Device
        List<Repository.Models.Entities.Device> successDeviceList = needPostFusion == false ? addHmnDeviceInputs : addHmnDeviceInputs.Where(e => addDeviceResults.Any(a => a.pid == e.Pid && (a.errors == null || a.errors.Count == 0))).ToList();

        // 將新增成功的Device，寫入資料庫
        await _dataAccessService.CreateRangeAsync(successDeviceList);

        // 判斷每一筆的PostAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> addDeviceErrors = needPostFusion == false ? [] : _deviceService.GetFusionError(addDeviceResults.Select(e => new CommonAPIResult { code = e.code, pid = e.pid, errors = e.errors, id = e.id }).ToList());
        return addDeviceErrors;
    }
    public async Task<List<ReturnError>> UpdateDevice(string appCode, List<InUpdateDevice> paramList)
    {
        List<UpdateDeviceInput> fusionInputs = [];

        var willUpdateHmnDevices = await _dataAccessService.Fetch<Repository.Models.Entities.Device>(e => e.AppCode == appCode).AsTracking().ToListAsync();

        willUpdateHmnDevices = willUpdateHmnDevices.Where(e => paramList.Any(p => p.Pid == e.Pid)).ToList();

        // 使用 ToDictionary 將 willUpdateHmnDevices 轉換為字典，以便快速查找。
        var deviceDict = willUpdateHmnDevices.ToDictionary(d => d.Pid);

        var updatedPids = new HashSet<string>();

        foreach (var param in paramList)
        {
            // 使用 TryGetValue 來安全地從字典中獲取 hmnDevice，避免 KeyNotFoundException。
            if (!deviceDict.TryGetValue(param.Pid, out var hmnDevice))
            {
                continue;
            }

            var fusionInput = new UpdateDeviceInput { pid = param.Pid };
            var needsUpdateFusion = false;
            var needsUpdateHmn = false;

            // 如果有傳入Name，且不為空白，且不等於原本的Name，則更新
            if (!string.IsNullOrWhiteSpace(param.Name) && param.Name != hmnDevice.Name)
            {
                fusionInput.name = param.Name;
                hmnDevice.Name = param.Name;
                needsUpdateFusion = true;
                needsUpdateHmn = true;
            }

            // 如果有傳入Enable，且不為空白，且不等於原本的Enable，則更新
            if (!string.IsNullOrWhiteSpace(param.Enable) && hmnDevice.Enable != (param.Enable == "Y"))
            {
                fusionInput.enable = param.Enable == "Y" ? "true" : "false";
                hmnDevice.Enable = param.Enable == "Y";
                needsUpdateFusion = true;
                needsUpdateHmn = true;
            }

            // 如果有傳入UsageDepartCode，且不為空白，且不等於原本的UsageDepartCode，則更新
            if (!string.IsNullOrWhiteSpace(param.UsageDepartCode) && param.UsageDepartCode != hmnDevice.UsageDepartCode)
            {
                hmnDevice.UsageDepartCode = param.UsageDepartCode;
                needsUpdateHmn = true;
            }

            // 如果有傳入ManageDepartCode，且不為空白，且不等於原本的ManageDepartCode，則更新
            if (!string.IsNullOrWhiteSpace(param.ManageDepartCode) && param.ManageDepartCode != hmnDevice.ManageDepartCode)
            {
                hmnDevice.ManageDepartCode = param.ManageDepartCode;
                needsUpdateHmn = true;
            }

            // 如果需要更新，則加入更新清單
            if (needsUpdateFusion)
            {
                fusionInputs.Add(fusionInput);
            }

            // 如果需要更新HMN Device，則加入更新清單
            if (needsUpdateHmn)
            {
                updatedPids.Add(param.Pid);
            }
        }

        // 更新willUpdateHmnDevices，只包含需要更新的設備
        willUpdateHmnDevices = willUpdateHmnDevices.Where(d => updatedPids.Contains(d.Pid)).ToList();

        // 如果有需要更新Fusion裝置
        List<PatchAPIResult> patchDeviceResults = null;
        if (fusionInputs.Count > 0)
        {
            // 更新Fusion裝置
            patchDeviceResults = await _deviceService.PatchDevice(fusionInputs);
        }

        // 取出Fusion更新成功的HMN Device
        var hmnDevices = patchDeviceResults == null ? willUpdateHmnDevices : willUpdateHmnDevices
            .Where(e => patchDeviceResults.Any(u => u.pid == e.Pid && (u.errors == null || u.errors.Count == 0)));

        // 如果有需要更新HMN Device
        if (hmnDevices.Any())
        {
            await _dataAccessService.UpdateRangeAsync(
                    entities: hmnDevices,
                    updateProperties:
                    [
                        e => e.Enable,
                        e => e.Name,
                        e => e.UsageDepartCode,
                        e => e.ManageDepartCode,
                        e => e.ModifyDate,
                        e => e.ModifyUserAccount
                    ]
                );
        }

        // 判斷每一筆的PostAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> patchDeviceErrors = patchDeviceResults == null ? [] : _deviceService.GetFusionError(patchDeviceResults.Select(e => new CommonAPIResult { code = e.code, pid = e.pid, errors = e.errors, id = e.id }).ToList());
        return patchDeviceErrors;
    }
    public async Task<List<ReturnError>> DeleteDevice(string appCode, List<InDeleteDevice> paramList)
    {
        string pids = string.Join(",", paramList.Select(e => e.Pid));

        List<DeleteAPIResult> deleteFusionDeviceResults = await _deviceService.DeleteDevice(pids);

        // 取出Fusion刪除成功的Device
        var successDeviceList = paramList.Where(e => deleteFusionDeviceResults.Any(a => a.pid == e.Pid && (a.errors == null || a.errors.Count == 0))).ToList();

        // 將Fusion刪除成功的Device，從HMN Device刪除
        foreach (var device in successDeviceList)
        {
            await _dataAccessService.DeleteAsync<Repository.Models.Entities.Device>(e => e.AppCode == appCode && e.Pid == device.Pid);
        }

        // 判斷每一筆的PostAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> deleteFusionDeviceErrors = _deviceService.GetFusionError(deleteFusionDeviceResults.Select(e => new CommonAPIResult { code = e.code, pid = e.pid, errors = e.errors, id = e.id }).ToList());

        // 判斷是否有錯誤
        //deleteResult = deleteFusionDeviceErrors.Count == 0 && (deleteHmnDeviceCount == paramList.Count);

        return deleteFusionDeviceErrors;
    }

    public async Task<OutCreateObjectDeviceDetail> CreateObjectDeviceDetail(List<Repository.Models.Entities.ObjectDeviceDetail> objectDeviceDetailList)
    {
        if (objectDeviceDetailList == null || objectDeviceDetailList.Count == 0)
        {
            return new() { InsertObjectDeviceDetailResultList = [new DBResult { code = "", result = true }] };
        }

        string appCode = objectDeviceDetailList.First().AppCode;
        var objectCodes = objectDeviceDetailList.Select(e => e.ObjectCode).Distinct().ToList();

        var oldObjectDeviceDetailList = await _dataAccessService.Fetch<Repository.Models.Entities.ObjectDeviceDetail>(e => e.AppCode == appCode && objectCodes.Contains(e.ObjectCode)).ToListAsync();

        foreach (var old in oldObjectDeviceDetailList)
        {
            if (objectDeviceDetailList.Any(e => e.ObjectCode == old.ObjectCode && e.AreaCode == old.AreaCode && e.SddResource == old.SddResource && e.SddComp == old.SddComp))
            {
                await _dataAccessService.DeleteAsync(old);
            }
        }

        await _dataAccessService.CreateRangeAsync(objectDeviceDetailList);

        var dataCount = await _dataAccessService.Fetch<Repository.Models.Entities.ObjectDeviceDetail>(e => e.AppCode == appCode && objectCodes.Contains(e.ObjectCode)).CountAsync();

        bool result = dataCount == objectDeviceDetailList.Count();

        return new() { InsertObjectDeviceDetailResultList = [new DBResult { code = "", result = result }] };
    }

}
