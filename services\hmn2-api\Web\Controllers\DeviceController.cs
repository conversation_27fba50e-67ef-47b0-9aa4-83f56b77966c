﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using Web.Models.Controller;
using Web.Models.Controller.Device;
using Web.Models.Service;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Models.Service.Fusion;
using Web.Services.Interfaces;
using Web.Models.Controller.Object;
using MQTTnet;
using System.Reflection.Emit;
using Web.Validation;
using System.Linq.Expressions;
using Web.Models.Controller.Station;
using static Web.Models.Service.Fusion.DeviceResults;
using Web.Constant;
using Web.Models.Service.Configuration;

namespace Web.Controller;

/// <summary>
/// 
/// </summary>
[Route("[controller]")]
[Route("api/v1/[controller]")]
[Authorize]
public class DeviceController(IConfiguration configuration,
    IDataAccessService dataAccessService,
    IConfigurationService configurationService,
    ICredentialService credentialService,
    IPreferenceService preferenceService,
    IDeviceService deviceService,
    IRequestContextService requestContextService,
    ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly IConfiguration _configuration = configuration;
    private readonly IConfigurationService _configurationService = configurationService;
    private readonly IPreferenceService _preferenceService = preferenceService;
    private readonly IDeviceService _deviceService = deviceService;

    /// <summary>
    /// 依指定的基站列表取得定位在這些基站的裝置，但排除指定使用單位的裝置
    /// </summary>
    /// <param name="stationList">指定的基站</param>
    /// <param name="exUsageDeptCode">要排除的裝置使用單位</param>
    /// <returns></returns>
    [HttpGet("DeviceByStationListExUsageDept")]
    public async Task<IActionResult> GetDeviceByStationListExUsageDept(GetDeviceByStationListExUsageDept param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        List<string> stationList;

        // 如果是指定基站列表
        if (param.StationList != null)
        {
            stationList = param.StationList;
        }
        // 如果是指定基站所屬的區域代碼
        else if (!string.IsNullOrEmpty(param.SectorCode))
        {
            stationList = await _dataAccessService.Fetch<SectorStation>(e => e.AppCode == _user.AppCode && e.SectorCode == param.SectorCode).Select(x => x.Stationsid).ToListAsync();
        }
        // 如果是指定基站所屬的單位代碼
        else if (!string.IsNullOrEmpty(param.StationOwnerDeptCode))
        {
            var sectorCode = _dataAccessService.Fetch<Department>(e => e.AppCode == _user.AppCode && e.DeptCode == param.StationOwnerDeptCode).Select(x => x.SectorCode).FirstOrDefault();

            stationList = await _dataAccessService.Fetch<SectorStation>(e => e.AppCode == _user.AppCode && e.SectorCode == sectorCode).Select(x => x.Stationsid).ToListAsync();
        }
        else
        {
            return Ok(new ReturnModel(StatusCodes.Status400BadRequest, false, "err.null.GetDeviceByStationListExUsageDept.Station.StationListOrSectorCodeOrStationOwnerDeptCode"));
        }

        //取得裝置類型
        List<DeviceType> deviceTypeList = await _configurationService.GetDeviceTypeList();

        //依基站編號取得被定位在這些基站下的裝置
        var deviceList = await _configurationService.FetchDeviceOnStation(String.Join(",", stationList));
        List<string> pidList = deviceList.Select(d => d.pid).ToList();

        var filteredList = new List<DeviceAllInfo>();

        // 取得是否顯示非本單位的裝置
        bool showOtherDepartDevice = bool.Parse(await _preferenceService.FetchGlobalParameter(_user.AppCode, "ShowOtherDepartDevice"));
        
        if (showOtherDepartDevice == true)
        {
            var filtered = _configurationService.GetDeviceAllInfo(_user.AppCode);   // 取得所有裝置資訊
            filteredList = filtered.Where(x => pidList.Contains(x.Pid)          // 只取得指定基站下的裝置
                                                && x.UsageDepartCode != param.ExDeviceUsageDeptCode // 排除指定使用單位的裝置
                                                && x.ObjectType == "4").ToList(); //將裝置資訊列表排除使用單位為exUsageDeptCode且對像類型為Device(4)的裝置
        }

        // 依照查詢參數 isPositioningSupported 過濾 DeviceType 的 isPositioningSupported 裝置
        if (param.isPositioningSupported != null)
        {

            filteredList = filteredList.Where(d => deviceTypeList.Any(dt => dt.type == d.DeviceType && dt.isPositioningSupported == param.isPositioningSupported)).ToList();
        }

        pidList = filteredList.Select(d => d.Pid).ToList();

        //TODO:@GM這裡後面要來想一下怎麼改直接用deviceList裡的Station資訊，不用再取得Call API
        //取得filteredList 所有裝置所在的位置資訊
        var devicePositionList = await _configurationService.GetDevicePosition(String.Join(",", pidList));

        //雖然GetCoreDeviceList 回傳中有Position，但不一定是即時資料，Beau 的建議取得Position還是透過 api/v3/devices/positions @20230815
        List<Devices> deviceCoreList = await _configurationService.GetCoreDeviceList(string.Join(",", pidList));

        var returnResult = new ReturnModel(StatusCodes.Status200OK, true, new
        {
            nonSelfDeptDeviceList = filteredList,
            nonSelfDeptDevicePositionList = devicePositionList,
            nonSelfDeptDeviceCoreList = deviceCoreList
        });

        _logService.Logging("info", logActionName, requestUUID, "End");

        return Ok(returnResult);

    }

    [HttpGet("trajectories")]
    public async Task<IActionResult> RetrieveTrajectories(RetrieveTrajectory param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        // 檢查參數是否為空
        if (param == null)
        {
            _logService.Logging("error", logActionName, requestUUID, "err.null.param");
            returnModel = new ReturnModel
            {
                authorize = (Authorize)_user,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = "err.null.param"
            };
            return StatusCode(returnModel.httpStatus, returnModel);
        }

        var errorDetailList = new List<ErrorDetail>();

        // 檢查是否有指定單位代碼或指定對象代碼
        if (//(string.IsNullOrEmpty(param.DeptCode) && string.IsNullOrWhiteSpace(param.DeptCode)) &&
            (string.IsNullOrEmpty(param.ObjectCode) && string.IsNullOrWhiteSpace(param.ObjectCode)))
        {
            errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.null.param.ObjectCode" });
        }

        // 檢查是否有指定開始日期
        if (param.StartDate == null || (string.IsNullOrEmpty(param.StartDate) && string.IsNullOrWhiteSpace(param.StartDate)))
        {
            errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.null.param.StartDate" });
        }

        // 檢查是否有指定結束日期
        if (param.EndDate == null || (string.IsNullOrEmpty(param.EndDate) && string.IsNullOrWhiteSpace(param.EndDate)))
        {
            errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.null.param.EndDate" });
        }

        // 如果有錯誤，則回傳錯誤訊息
        if (errorDetailList.Count > 0)
        {
            returnModel = new ReturnModel
            {
                authorize = (Authorize)_user,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = errorDetailList
            };
            return StatusCode(returnModel.httpStatus, returnModel);
        }

        // 檢查開始日期格式是否正確(yyyy-MM-dd hh24:mi:ss)
        if (DateTime.TryParse(param.StartDate, out DateTime startDate) == false)
        {
            errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.invalid.param.StartDate" });
        }

        // 檢查結束日期格式是否正確(yyyy-MM-dd hh24:mi:ss)
        if (DateTime.TryParse(param.EndDate, out DateTime endDate) == false)
        {
            errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.invalid.param.EndDate" });
        }

        // 如果有錯誤，則回傳錯誤訊息
        if (errorDetailList.Count > 0)
        {
            returnModel = new ReturnModel
            {
                authorize = (Authorize)_user,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = errorDetailList
            };
            return StatusCode(returnModel.httpStatus, returnModel);
        }

        // 檢查開始日期是否小於結束日期
        if (startDate > endDate)
        {
            errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.invalid.param.StartDateLessEndDate" });
        }

        // 如果有錯誤，則回傳錯誤訊息
        if (errorDetailList.Count > 0)
        {
            returnModel = new ReturnModel { authorize = (Authorize)_user, httpStatus = StatusCodes.Status400BadRequest, result = false, data = errorDetailList };
            return StatusCode(returnModel.httpStatus, returnModel);
        }

        var trajectory = await _configurationService.GetTrajectory(_user.AppCode, param.ObjectCode, param.StartDate, param.EndDate);

        returnModel = new ReturnModel { authorize = (Authorize)_user, httpStatus = StatusCodes.Status200OK, result = true, data = trajectory };

        _logService.Logging("info", logActionName, requestUUID, "End");

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    private async Task<IActionResult> RetrievePosition(RetrievePosition param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 檢查參數是否為空
        if (param == null)
        {
            _logService.Logging("error", logActionName, requestUUID, "err.null.positions.Device.param");
            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, "err.null.positions.Device.param"));
        }
        else
        {
            _logService.Logging("info", logActionName, requestUUID, $"param:{JsonSerializer.Serialize(param)}");
        }

        var errorDetailList = new List<ErrorDetail>();

        // 檢查是否有指定單位代碼或Pid列表或樓層代碼
        if (string.IsNullOrEmpty(param.DeptCodes) && (param.PidList == null || param.PidList.Count == 0) && string.IsNullOrWhiteSpace(param.PlaneCode))
        {
            // 如果沒有指定單位代碼，也沒有指定樓層代碼，也沒有Pid列表，則回傳錯誤
            _logService.Logging("error", logActionName, requestUUID, "err.null.positions.Device.param");
            errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.null.positions.Device.param" });
        }

        if (errorDetailList.Count > 0)
        {
            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, errorDetailList));
        }

        // 取得所有裝置的位置資訊
        var devicePositionList = await _configurationService.GetDevicePosition();

        // 取得有指定pid 的裝置
        devicePositionList = devicePositionList.Where(x =>
            (param.PidList == null || param.PidList.Count == 0 || param.PidList.Contains(x.pid))).ToList();

        // 如果有指定單位代碼
        if (!string.IsNullOrEmpty(param.DeptCodes))
        {
            // 處理 param.DeptCode 參數為 deptCodeList
            List<string> deptCodeList = param.DeptCodes?.Split(',').ToList() ?? new List<string>();

            // 不顯示非本單位的裝置或是ShowOtherDepartDevice 為空，則只取得指定單位的裝置
            if (string.IsNullOrWhiteSpace(param.ShowOtherDepartDevice) || param.ShowOtherDepartDevice == "N")
            {
                devicePositionList = devicePositionList.Where(x => deptCodeList.Any(y => <EMAIL>().Contains(y.ToUpper()))).ToList();
            }
            //// 如果要顯示非本單位的裝置，則取得指定單位及非指定單位的裝置
            //else if (param.ShowOtherDepartDevice == "Y")
            //{
            //    devicePositionList = devicePositionList;
            //}
        }

        // 如果有指定樓層代碼，則只取得定位在該樓層的裝置
        if (string.IsNullOrWhiteSpace(param.PlaneCode) == false)
        {
            devicePositionList = devicePositionList.Where(x => x.position.plane.code == param.PlaneCode);
        }

        // 如果有指定次平面代碼，則只取得定位在該次平面的裝置
        if (string.IsNullOrWhiteSpace(param.SectorCode) == false)
        {
            devicePositionList = devicePositionList.Where(x => x.position.sector.Any(s => s.code == param.SectorCode));
        }

        // 取得次平面及樓層的地圖Url位置
        var sectorMapUrl = _configuration.GetSection("MapInfo")["SectorUrl"] ?? "";
        var planeMapUrl = _configuration.GetSection("MapInfo")["PlaneUrl"] ?? "";

        // 取得所有次平面及樓層的地圖路徑
        var sectorMapList = await _dataAccessService.Fetch<Sector>(e => e.AppCode == _user.AppCode).Select(x => new { x.SectorCode, SectorMapPath = $"{x.SectorMapPath}" }).ToListAsync();
        var planeMapList = await _dataAccessService.Fetch<Plane>(e => e.AppCode == _user.AppCode).Select(x => new { x.PlaneCode, PlaneMapPath = $"{x.PlaneMapPath}" }).ToListAsync();
        var departSectorList = await _dataAccessService.Fetch<DepartSector>(e => e.AppCode == _user.AppCode).Select(x => new { x.DeptCode, x.SectorCode }).ToListAsync();

        //// 使用修改後的 FetchAsync
        //var sectorMapList = await _dataAccessService
        //    .FetchAsync<Sector>(e => e.AppCode == _user.AppCode)
        //    .ContinueWith(t => t.Result
        //        .Select(x => new { x.SectorCode, SectorMapPath = $"{sectorMapUrl}{x.SectorMapPath}" })
        //        .ToList());

        //var planeMapList = await _dataAccessService
        //    .FetchAsync<Plane>(e => e.AppCode == _user.AppCode)
        //    .ContinueWith(t => t.Result
        //        .Select(x => new { x.PlaneCode, PlaneMapPath = $"{planeMapUrl}{x.PlaneMapPath}" })
        //        .ToList());

        //var departSectorList = await _dataAccessService
        //    .FetchAsync<DepartSector>(e => e.AppCode == _user.AppCode)
        //    .ContinueWith(t => t.Result
        //        .Select(x => new { x.DeptCode, x.SectorCode })
        //        .ToList());

        // 取得所有裝置所在的次平面資訊
        var sectorList = devicePositionList.SelectMany(d => d.position.sector).Select(s => new
        {
            SectorCode = s.code,
            SectorName = s.name,
            MapPath = sectorMapList.FirstOrDefault(x => x.SectorCode == s.code)?.SectorMapPath,
            DeptCodeList = departSectorList.Where(ds => ds.SectorCode == s.code).Select(s => s.DeptCode)
        }).DistinctBy(s => s.SectorCode).ToList();

        // 取得所有裝置所在的樓層資訊
        var planeList = devicePositionList.Where(x => !x.position.plane.code.StartsWith("err.notFound"))
                                          .Select(x => new
                                          {
                                              PlaneCode = x.position.plane.code,
                                              PlaneName = x.position.plane.name,
                                              MapPath = planeMapList.FirstOrDefault(y => y.PlaneCode == x.position.plane.code)?.PlaneMapPath
                                          }).DistinctBy(p => p.PlaneCode).ToList();

        var returnResult = new ReturnModel(StatusCodes.Status200OK, true, new
        {
            devicePositionList = devicePositionList.ToList(),
            sectorList,
            planeList
        });

        _logService.Logging("info", logActionName, requestUUID, $"returnResult:{JsonSerializer.Serialize(returnResult)}");

        _logService.Logging("info", logActionName, requestUUID, "End");

        return Ok(returnResult);
    }

    [HttpGet("positions")]
    public async Task<IActionResult> RetrievePositionWithGet(RetrievePosition param)
    {
        return await RetrievePosition(param);
    }

    [HttpPost("positions")]
    public async Task<IActionResult> RetrievePositionWithPost([FromBody] RetrievePosition param)
    {
        return await RetrievePosition(param);
    }

    /// <summary>
    /// 查詢Fusion裝置列表
    /// </summary>
    /// <param name="param"></param>
    /// <spec>
    /// Jira: 
    /// 列表畫面欄位及需求 
    /// 查詢視窗欄位及需求 
    /// </spec>
    /// <returns></returns>
    [HttpGet("fusion/devices")]
    public async Task<IActionResult> RetrieveFusionDevice(InRetrieveFusionDevice param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        // 檢查參數是否為空
        if (param == null)
        {
            _logService.Logging("error", logActionName, requestUUID, "err.null.param");

            returnModel = new ReturnModel
            {
                requestUUID = requestUUID,
                authorize = (Authorize)_user,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = "err.null.param"
            };
            return StatusCode(returnModel.httpStatus, returnModel);
        }
        else
        {
            // 檢查PidList是否為空
            if (param.PidList == null || param.PidList.Count == 0)
            {
                _logService.Logging("error", logActionName, requestUUID, "err.null.param.PidList");

                returnModel = new ReturnModel
                {
                    requestUUID = requestUUID,
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status400BadRequest,
                    result = false,
                    data = "err.null.param.PidList"
                };
                return StatusCode(returnModel.httpStatus, returnModel);
            }
        }

        _logService.Logging("info", logActionName, requestUUID, $"param:{JsonSerializer.Serialize(param)}");

        string pids = string.Join(",", param.PidList);

        var result = await _configurationService.GetCoreDeviceList(pids);

        returnModel = new ReturnModel
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal = result.Count(),
                recordList = result.ToList()
            }
        };

        _logService.Logging("info", logActionName, requestUUID, "End");

        return Ok(returnModel);
    }

    [HttpGet("devicesReport")]
    public async Task<IActionResult> RetrieveDeviceReport(InRetrieveDeviceReport param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;
        string appCode = _user.AppCode;

        // 檢查參數是否為空
        if (param == null)
        {
            _logService.Logging("error", logActionName, requestUUID, "err.null.param");

            returnModel = new ReturnModel
            {
                requestUUID = requestUUID,
                authorize = (Authorize)_user,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = "err.null.param"
            };
            return StatusCode(returnModel.httpStatus, returnModel);
        }

        var errorDetailList = new List<ErrorDetail>();

        var areas = _dataAccessService.Fetch<Area>(e => e.AppCode == appCode && _user.AreaCodeList.Contains(e.AreaCode));
        var objects = await _dataAccessService.FetchWithNewContext<ObjectDatum>(e => e.AppCode == appCode && _user.AreaCodeList.Contains(e.AreaCode) && e.Enable && e.Active);
        var groups = await _dataAccessService.FetchWithNewContext<ObjectGroup>(e => e.AppCode == appCode && _user.AreaCodeList.Contains(e.AreaCode) && e.Enable && e.Active);
        var deviceTypes = await _configurationService.FetchSupportDeviceTypeList(appCode, _user.AreaCodeList.ToList());
        var devices = await _dataAccessService.FetchWithNewContext<Repository.Models.Entities.Device>(e => e.AppCode == appCode && _user.AreaCodeList.Contains(e.AreaCode) && e.Enable && e.Active);

        // 檢查是否有指定院區代碼
        if (string.IsNullOrWhiteSpace(param.AreaCode))
        {
            errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.null.param.AreaCode" });
        }

        // 檢查是否有指定開始日期
        if (param.StartDate == null || (string.IsNullOrEmpty(param.StartDate) && string.IsNullOrWhiteSpace(param.StartDate)))
        {
            errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.null.param.StartDate" });
        }

        // 檢查是否有指定結束日期
        if (param.EndDate == null || (string.IsNullOrEmpty(param.EndDate) && string.IsNullOrWhiteSpace(param.EndDate)))
        {
            errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.null.param.EndDate" });
        }

        // 如果有錯誤，則回傳錯誤訊息
        if (errorDetailList.Count > 0)
        {
            returnModel = new ReturnModel
            {
                authorize = (Authorize)_user,
                httpStatus = StatusCodes.Status400BadRequest,
                result = false,
                data = errorDetailList
            };
            return StatusCode(returnModel.httpStatus, returnModel);
        }

        // 如果沒有指定間隔，則預設為1分鐘
        bool intervalTryParse = int.TryParse(param.Interval, out int interval);

        if (!intervalTryParse)
        {
            interval = 1;
        }

        // 取得裝置DeviceRawData call /devices/records
        var fusionResult = await _configurationService.GetDeviceReport(param.DeviceTypes, param.ObjectCodes, param.Pids, param.ResourceIds, param.StartDate, param.EndDate, param.LineType, interval);

        var objectDevices = await _dataAccessService.FetchWithNewContext<Web.Repository.Models.Entities.ObjectDevice>(e => e.AppCode == appCode && e.AreaCode == param.AreaCode);
        var departments = await _dataAccessService.FetchWithNewContext<Department>(e => e.AppCode == appCode && e.AreaCode == param.AreaCode);
        var sddResourceList = await _configurationService.GetSddResourceList();

        var result = fusionResult.Where(e => devices.Any(d => d.AreaCode == param.AreaCode && d.Pid == e.Pid) &&
                                             objectDevices.Any(o => o.Pid == e.Pid))
            .Select((e, index) =>
        {
            string objectCode = e.Object?.Code ?? "";

            var obj = objects.FirstOrDefault(d => d.AppCode == appCode && d.AreaCode == param.AreaCode && d.ObjectCode == objectCode);
            var group = groups.FirstOrDefault(e => e.AppCode == appCode && e.AreaCode == param.AreaCode && e.GroupCode == obj?.GroupCode);
            var device = devices.FirstOrDefault(d => d.Pid == e.Pid);
            var department = departments.FirstOrDefault(d => d.AppCode == appCode && d.AreaCode == param.AreaCode && d.DeptCode == device.UsageDepartCode);

            return new OutRetrieveDeviceReport
            {
                Id = index + 1,
                DeptCode = device?.UsageDepartCode ?? "",
                DeptName = department?.DeptName ?? "",
                GroupCode = obj?.GroupCode ?? "",
                GroupName = group?.GroupName ?? "",
                ObjectCode = objectCode,
                ObjectName = obj?.Name ?? "",
                DeviceName = device?.Name ?? "",
                Pid = e?.Pid ?? "",
                ReportTime = e.Record.ReportTime,
                Value = e.Record.Value,
                // 因/reports/api/{base._apiVersion}/devices/records此API的ResourceIds是不帶斜線的，但Fusion無法修正，所以這邊要加上斜線給前端才能串到資料 @20240902 by Ann決議
                ResourceId = e.Record.ResourceId.StartsWith('/') ? e.Record.ResourceId : $"/{e.Record.ResourceId}",
                ResourceName = sddResourceList.FirstOrDefault(s => s.id == (e.Record.ResourceId.StartsWith('/') ? e.Record.ResourceId : $"/{e.Record.ResourceId}"))?.langsId is string langsId && !string.IsNullOrEmpty(langsId) ? $"${langsId}" : (e.Record.ResourceId.StartsWith('/') ? e.Record.ResourceId : $"/{e.Record.ResourceId}")
            };
        });

        // 如果有指定單位名稱或群組名稱，則進行Like查詢
        result = result.Where(x => (param.DeptName == null || x.DeptName.ToUpper().Contains(param.DeptName.ToUpper()))
                                && (param.GroupName == null || x.GroupName.ToUpper().Contains(param.GroupName.ToUpper()))).ToList();

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // 下方使用CountAsync()會有問題，所以先ToList()再Count()，待解決
        var queryList = result.ToList();

        var recordTotal = queryList.Count();

        var recordList = size == 0 ? result.ToList() : queryList.Skip(skip).Take(size).ToList();

        returnModel = new ReturnModel
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal,
                recordList
            }
        };

        _logService.Logging("info", logActionName, requestUUID, "End");

        return Ok(returnModel);
    }

    [HttpPost("devices")]
    [RequestParamListDuplicate("Pid")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateDevice([FromBody] List<InCreateDevice> paramList)
    {
        string appCode = _user.AppCode;
        string? requestUUID = _requestContextService.GetRequestUID();

        // 因為CreateAT3 也會用到 CreateDevice，所以將CreateDevice封裝成一個Service，與其它Create Controller Action 寫法不太一樣
        List<ReturnError> createDeviceErrors = await _configurationService.CreateDevice(appCode, paramList);

        // 判斷是否有錯誤
        bool addResult = createDeviceErrors.Count == 0;

        ReturnModel returnModel = new()
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = addResult ? StatusCodes.Status201Created : StatusCodes.Status400BadRequest,
            data = createDeviceErrors,
            result = addResult,
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpPost("validation")]
    public async Task<IActionResult> ValidateCreateDevice([FromBody] List<InCreateDevice> paramList)
    {
        string appCode = _user.AppCode;
        string? requestUUID = _requestContextService.GetRequestUID();

        var addFusionDeviceInputs = paramList.Select(param => new AddDeviceInput
        {
            pid = param.Pid,
            name = param.Name,
            type = param.DeviceType,
            stationSid = param.StationSid
        }).ToList();

        // 驗證新增Fusion裝置
        List<PostAPIResult> addDeviceResults = await _deviceService.ValidateDevice(addFusionDeviceInputs);

        // 取得錯誤訊息
        List<ReturnError> addDeviceErrors = _deviceService.GetFusionError(addDeviceResults.Select(e => new CommonAPIResult { code = e.code, pid = e.pid, errors = e.errors, id = e.id }).ToList());

        // 判斷是否有錯誤
        bool validateResult = addDeviceErrors.Count == 0;

        ReturnModel returnModel = new()
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = validateResult ? StatusCodes.Status200OK : StatusCodes.Status400BadRequest,
            data = addDeviceErrors,
            result = validateResult,
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpDelete("devices")]
    [RequestParamListDuplicate("Pid")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeleteDevice([FromBody] List<InDeleteDevice> paramList)
    {
        string appCode = _user.AppCode;
        string? requestUUID = _requestContextService.GetRequestUID();

        // 因為DeleteAT3 也會用到 DeleteDevice，所以將DeleteDevice封裝成一個Service，與其它Delete Controller Action 寫法不太一樣
        List<ReturnError> deleteDeviceErrors = await _configurationService.DeleteDevice(appCode, paramList);

        // 判斷是否有錯誤
        bool deleteResult = deleteDeviceErrors.Count == 0;

        ReturnModel returnModel = new()
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = deleteResult ? StatusCodes.Status200OK : StatusCodes.Status400BadRequest,
            data = deleteDeviceErrors,
            result = deleteResult,
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    /// <summary>
    /// 查詢裝置列表
    /// </summary>
    /// <param name="param"></param>
    /// <spec>
    /// Jira: https://tpe-jira2.fihtdc.com/browse/FSN-6213
    /// 列表畫面欄位及需求 https://tpe-jira2.fihtdc.com/secure/thumbnail/2152389/_thumb_2152389.png
    /// 查詢視窗欄位及需求 https://tpe-jira2.fihtdc.com/secure/thumbnail/2152394/_thumb_2152394.png
    /// </spec>
    /// <returns></returns>
    [HttpGet("devices")]
    public async Task<IActionResult> RetrieveDevice(InRetrieveDevice param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        ReturnModel returnModel;

        string appCode = _user.AppCode;

        // 取得登入者有權限的單位資訊
        var deptList = await _dataAccessService.Fetch<Department>(e => e.AppCode == appCode && _user.AreaCodeList.Contains(e.AreaCode) && _user.DeptCodeList.Contains(e.DeptCode)).ToListAsync();

        // 取得所有裝置資訊
        var deviceList = await _configurationService.GetAllDevicesWithoutPermissionCheck(_user.AppCode);

        // 1.3 取得空白的使用單位裝置
        var emptyUsageDeptDeviceList = deviceList.Where(e => string.IsNullOrEmpty(e.UsageDepartCode));

        // 1.1 ~ 1.2 deviceList依deptList過濾 ManageDepartCode 及 UsageDepartCode
        var result = deviceList.Where(e => deptList.Any(d => d.AreaCode == e.AreaCode && d.DeptCode == e.ManageDepartCode) ||
                                           deptList.Any(d => d.AreaCode == e.AreaCode && d.DeptCode == e.UsageDepartCode));

        // 1.3 登入帳號所屬單位的單位類型是資材單位時同時可以找出資材單位空白的裝置
        var userDept = await _dataAccessService.Fetch<Department>(e => e.AppCode == _user.AppCode && e.AreaCode == _user.AreaCode && e.DeptCode == _user.DeptCode).FirstOrDefaultAsync();
        if (userDept?.IsManagedDept == true)
        {
            result = result.Union(emptyUsageDeptDeviceList);
        }

        result = from r in result
                 where (r.AreaCode == param.AreaCode) //院區代碼(必填)
                    && (string.IsNullOrEmpty(param.ManageDepartCode) || r.ManageDepartCode == param.ManageDepartCode)   //1.1 資材單位
                    && (string.IsNullOrEmpty(param.UsageDepartCode) || r.UsageDepartCode == param.UsageDepartCode)  //1.2 使用單位
                    && (param.DeviceTypes == null || param.DeviceTypes.Contains(r.DeviceType))                 //1.3 裝置類型
                    && (string.IsNullOrEmpty(param.Enable) || r.Enable == (param.Enable == "Y"))                    //1.4 狀態
                    && (string.IsNullOrEmpty(param.Pid) || r.Pid.ToUpper().Contains(param.Pid.ToUpper()))           //1.5 裝置編碼（1.9 模糊查詢）
                    && (string.IsNullOrEmpty(param.Name) || r.Name.ToUpper().Contains(param.Name.ToUpper()))        //1.6 裝置名稱（1.9 模糊查詢）
                    && (string.IsNullOrEmpty(param.ObjectCode) || (!string.IsNullOrEmpty(r.ObjectCode) && r.ObjectCode.ToUpper().Contains(param.ObjectCode.ToUpper())))//1.7 對象編碼（1.9 模糊查詢）
                    && (string.IsNullOrEmpty(param.ObjectName) || (!string.IsNullOrEmpty(r.ObjectName) && r.ObjectName.ToUpper().Contains(param.ObjectName.ToUpper())))//1.8 對象名稱（1.9 模糊查詢）
                    && (string.IsNullOrEmpty(param.ObjectBound) || ((param.ObjectBound == "Y" && !string.IsNullOrWhiteSpace(r.ObjectCode)) || (param.ObjectBound == "N" && string.IsNullOrWhiteSpace(r.ObjectCode))))
                 select r;

        // 排序；如果沒有 sort 條件預設使用 ModifyDate 降冪排序
        string sort = string.IsNullOrEmpty(param.sort) ? "ModifyDate:desc" : param.sort;
        string sortByField = sort.Split(":")[0];
        string sortDirection = sort.Split(":")[1];

        // 根據 sortByField 和 sortDirection 進行動態排序
        result = SortByField(result, sortByField, sortDirection.ToLower() == "desc").ToList();

        returnModel = new ReturnModel
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordList = result
            }
        };

        return Ok(returnModel);
    }

    public static IEnumerable<T> SortByField<T>(IEnumerable<T> list, string sortBy, bool isDescending)
    {
        var param = Expression.Parameter(typeof(T), "x");
        Expression property = param;

        // 支援嵌套屬性，解析點號分隔的屬性
        foreach (var member in sortBy.Split('.'))
        {
            property = Expression.Property(property, member);
        }

        // 建立 Lambda 表達式
        var lambda = Expression.Lambda<Func<T, object>>(Expression.Convert(property, typeof(object)), param);

        // 使用 OrderBy 或 OrderByDescending 排序
        return isDescending ? list.AsQueryable().OrderByDescending(lambda) : list.AsQueryable().OrderBy(lambda);
    }

    /// <summary>
    /// 更新裝置列表
    /// </summary>
    /// <param name="paramList"></param>
    /// <spec>
    /// Jira: https://tpe-jira2.fihtdc.com/browse/FSN-6213
    /// 修改畫面欄位及需求 https://tpe-jira2.fihtdc.com/secure/thumbnail/2184130/_thumb_2184130.png
    /// </spec>
    /// <returns></returns>
    [HttpPatch("devices")]
    [RequestParamListDuplicate("Pid")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateDevice([FromBody] List<InUpdateDevice> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string appCode = _user.AppCode;

        List<ReturnError> patchDeviceErrors = await _configurationService.UpdateDevice(appCode, paramList);

        // 判斷是否有錯誤
        bool addResult = patchDeviceErrors.Count == 0;

        ReturnModel returnModel = new()
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = addResult ? StatusCodes.Status200OK : StatusCodes.Status400BadRequest,
            data = patchDeviceErrors,
            result = addResult,
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpGet("GetDeviceTypes")]
    public async Task<IActionResult> GetDeviceTypes()
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        ReturnModel returnModel;

        var areaCodeList = _dataAccessService.Fetch<VwUserDeptMonInfo>(x => x.AppCode == _user.AppCode && x.UserAccount == _user.Account.ToUpper()).Select(x => x.AreaCode).Distinct().ToList();
        var deviceTypeList = await _configurationService.FetchSupportDeviceTypeList(_user.AppCode, areaCodeList);

        returnModel = new ReturnModel
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordList = deviceTypeList
            }
        };

        return Ok(returnModel);

    }
}
