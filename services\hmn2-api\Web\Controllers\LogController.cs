﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using NLog;
using NLog.Targets;
using System.Data;
using Web.Models.Controller;
using Web.Models.Controller.Log;
using Web.Models.Controller.QueryRate;
using Web.Models.Service;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;

namespace Web.Controller;

/// <summary>
/// 基站控制器
/// </summary>
[Route("[controller]")]
[Authorize]
public class LogController(IDataAccessService dataAccessService,
                            ICredentialService credentialService,
                            IRequestContextService requestContextService,
                            IUtilityService utilityService,
                            ILogService logService) : BaseController(dataAccessService, credentialService, requestContextService, logService)
{
    private readonly IUtilityService _utilityService = utilityService;

    [HttpGet("logs")]
    public async Task<IActionResult> RetrieveLog(InRetrieveLog param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 檢查參數是否為空
        if (param == null)
        {
            _logService.Logging("error", logActionName, requestUUID, "err.null.logs.Log.param");
            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, "err.null.logs.Log.param"));
        }

        string target = param.Target ?? "";

        // 获取NLog的配置对象
        var config = LogManager.Configuration;

        var targetRule = config.LoggingRules.FirstOrDefault(r => r.Targets.Cast<Target>().Any(t => t.Name == target));

        var targetFileNameLayout = targetRule?.Targets.Cast<Target>().FirstOrDefault(t => t.Name == target);
        if (targetFileNameLayout != null)
        {
            var renderer = new NLog.Layouts.SimpleLayout(targetFileNameLayout.ToString());
            //var actualPath = renderer.Render(new NLog.LogEventInfo { LoggingConfigurationResolvedEvent = config.ConfigurationResolvedEvent });
            // 現在 actualPath 包含實際的路徑
        }

        // 查找文件目标
        var fileTarget = config.FindTargetByName<FileTarget>(target);

        // 获取logFolderPath
        string logFolderPath = fileTarget?.FileName?.ToString() ?? string.Empty;
        if (!string.IsNullOrEmpty(logFolderPath))
        {
            logFolderPath = Path.GetDirectoryName(Path.GetFullPath(logFolderPath)) ?? string.Empty;
        }

        string[] logFiles = [];

        // 确保logFolderPath不为空
        if (!string.IsNullOrEmpty(logFolderPath) && Directory.Exists(logFolderPath))
        {
            logFiles = Directory.GetFiles(logFolderPath, $"*-{target}.log");
        }

        return StatusCode(StatusCodes.Status200OK, logFiles);
    }

    [HttpPost("queryRate")]
    public async Task<IActionResult> LogQueryRate([FromBody] PostQueryRate param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 開始新增查詢率資料
        _logService.Logging("info", logActionName, requestUUID, "QueryRate Data Validated, start to append.");
        _dataAccessService.BeginTransaction();

        var systemParam = _dataAccessService.Fetch<SysParameters>(e => e.ParaCode == "LogReverseDns")

                                                .FirstOrDefault();
        String LogReverseDns = systemParam?.ParaValue;
        String ClientIp = _utilityService.GetClientIp(HttpContext);
        String ClientHostName = LogReverseDns == "true" ? await _utilityService.GetClientHostNameAsync(HttpContext) : _utilityService.GetClientIp(HttpContext);

        QueryRate queryRate = new QueryRate
        {
            ClientIp = ClientIp,
            ClientHostName = ClientHostName,
            AppCode = _user.AppCode,
            AreaCode = param.AreaCode,
            UserAccount = _user.Account,
            DeptCode = param.DeptCode ?? string.Empty,
            MenuId = param.MenuId,
            HttpStatus = param.HttpStatus,
            HttpRequest = param.HttpRequest,
            CreateDate = DateTime.Now
        };

        // 新增紀錄
        await _dataAccessService.CreateAsync<QueryRate>(queryRate);

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "QueryRate Data append done.");

        ReturnModel returnModel = new()
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status201Created,
            result = true
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }


    [HttpGet("queryRate")]
    public async Task<IActionResult> RetrieveQueryRateStatistics([FromQuery] RetrieveQueryRate param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        bool pageParseResult = int.TryParse(param.page.ToString(), out int page);
        bool sizeParseResult = int.TryParse(param.size.ToString(), out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size; // 起始資料列索引值(略過幾筆)

        // 處理日期範圍
        DateTime? startDate = null;
        DateTime? endDate = null;

        DateTime.TryParse(param.startDate.Trim(), out DateTime start);
        DateTime.TryParse(param.endDate.Trim(), out DateTime end);
        startDate = start;
        endDate = end;

        // 開始查詢 QueryRate 資料
        var queryRateList = _dataAccessService.Fetch<QueryRate>(x => x.AppCode == _user.AppCode);
        var departmentList = _dataAccessService.Fetch<Department>(d => d.AppCode == _user.AppCode);
        var menuList = _dataAccessService.Fetch<Menu>();

        // 根據查詢條件過濾資料
        var filteredQuery = queryRateList.Where(x =>
            (startDate == null || x.CreateDate >= startDate) &&
            (endDate == null || x.CreateDate <= endDate) &&
            (string.IsNullOrEmpty(param.UserAccount) || x.UserAccount.Contains(param.UserAccount)) &&
            (string.IsNullOrEmpty(param.ClientIp) || x.ClientIp.Contains(param.ClientIp)) &&
            (string.IsNullOrEmpty(param.ClientHostName) || x.ClientHostName.Contains(param.ClientHostName))
        );

        // 進行分組統計
        var groupedQuery = filteredQuery
            .GroupBy(x => new
            {
                MenuId = x.MenuId,
                DeptCode = x.DeptCode
            })
            .Select(g => new
            {
                MenuId = g.Key.MenuId,
                MenuName = $"${menuList.Where(m => m.MenuId == g.Key.MenuId).Select(m => m.StringId).FirstOrDefault()}",
                DeptCode = g.Key.DeptCode,
                DeptName = string.IsNullOrEmpty(g.Key.DeptCode) ? "" : departmentList.Where(d => d.DeptCode == g.Key.DeptCode).Select(d => d.DeptName).FirstOrDefault(),
                Count = g.Count()
            })
            .Where(g =>
                (param.MenuId == 0 || g.MenuId == param.MenuId) &&
                (string.IsNullOrEmpty(param.DeptCode) || g.DeptCode == param.DeptCode)
            );

        // 取得總筆數
        int recordTotal = await groupedQuery.CountAsync();

        // 進行資料庫分頁
        var recordList = size == 0
            ? await groupedQuery.ToListAsync() // 如果 size == 0，表示不分頁，直接取回所有資料
            : await groupedQuery.Skip(skip).Take(size).ToListAsync(); // 分頁查詢
                                                                      // 添加序號 (從1開始，根據分頁遞增)
        var recordListWithId = recordList.Select((item, index) =>
        {
            // 計算序號：(當前頁碼-1) * 每頁大小 + 當前索引 + 1
            int id = (page - 1) * (size == 0 ? 0 : size) + index + 1;

            // 創建包含原始屬性和新id的匿名對象
            var props = item.GetType().GetProperties()
                .ToDictionary(p => p.Name, p => p.GetValue(item));

            // 添加id屬性
            props["Id"] = id;

            // 使用反射創建新對象
            return props.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }).Cast<object>().ToList();

        ReturnModel returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal,
                recordList = recordListWithId
            }
        };

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);
    }

    [HttpGet("queryRate/Details")]
    public async Task<IActionResult> RetrieveQueryRateDetails([FromQuery] RetrieveQueryRate param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        bool pageParseResult = int.TryParse(param.page.ToString(), out int page);
        bool sizeParseResult = int.TryParse(param.size.ToString(), out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size; // 起始資料列索引值(略過幾筆)

        // 處理日期範圍
        DateTime? startDate = null;
        DateTime? endDate = null;

        DateTime.TryParse(param.startDate.Trim(), out DateTime start);
        DateTime.TryParse(param.endDate.Trim(), out DateTime end);
        startDate = start;
        endDate = end;

        // 開始查詢 QueryRate 詳細資料
        var queryRateList = _dataAccessService.Fetch<QueryRate>(x => x.AppCode == _user.AppCode);
        var departmentList = _dataAccessService.Fetch<Department>(d => d.AppCode == _user.AppCode);
        var AccountList = _dataAccessService.Fetch<UserDatum>(u => u.AppCode == _user.AppCode);
        var menuList = _dataAccessService.Fetch<Menu>();

        // 根據查詢條件過濾資料
        var filteredQuery = queryRateList.Where(x =>
            (startDate == null || x.CreateDate >= startDate) &&
            (endDate == null || x.CreateDate <= endDate) &&
            (string.IsNullOrEmpty(param.UserAccount) || x.UserAccount.Contains(param.UserAccount)) &&
            (string.IsNullOrEmpty(param.ClientIp) || x.ClientIp.Contains(param.ClientIp)) &&
            (string.IsNullOrEmpty(param.ClientHostName) || x.ClientHostName.Contains(param.ClientHostName)) &&
            (param.MenuId == null || x.MenuId == param.MenuId) &&
            (string.IsNullOrEmpty(param.DeptCode) || x.DeptCode == param.DeptCode)
        );

        // 選擇要返回的詳細資料欄位
        var detailQuery = filteredQuery.Select(x => new
        {
            Id = x.Id,
            MenuId = x.MenuId,
            MenuName = $"${menuList.Where(m => m.MenuId == x.MenuId).Select(m => m.StringId).FirstOrDefault()}",
            UserAccount = x.UserAccount,
            UserAccountDisp = AccountList.Where(u => u.UserAccount == x.UserAccount).Select(u => u.UserAccountDisp).FirstOrDefault(),
            DeptCode = x.DeptCode,
            DeptName = string.IsNullOrEmpty(x.DeptCode) ? "" : departmentList.Where(d => d.DeptCode == x.DeptCode).Select(d => d.DeptName).FirstOrDefault(),
            CreatedDate = x.CreateDate,
            ClientHostName = x.ClientHostName ?? "",
            ClientIp = x.ClientIp,
            HttpStatus = x.HttpStatus,
            HttpRequest = x.HttpRequest
        });

        // 取得總筆數
        int recordTotal = await detailQuery.CountAsync();

        // 進行資料庫分頁
        var recordList = size == 0
            ? await detailQuery.ToListAsync() // 如果 size == 0，表示不分頁，直接取回所有資料
            : await detailQuery.Skip(skip).Take(size).ToListAsync(); // 分頁查詢

        ReturnModel returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal,
                recordList
            }
        };

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);
    }


}
