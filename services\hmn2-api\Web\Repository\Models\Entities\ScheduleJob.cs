﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class ScheduleJob
{
    public int ScheduleJobId { get; set; }

    public string AppCode { get; set; } = null!;

    public string AreaCode { get; set; } = null!;

    /// <summary>
    /// LowBatter:低電量,PwdCheck:密碼檢查
    /// </summary>
    public string JobType { get; set; } = null!;

    public bool Enable { get; set; }

    public string JobDesc { get; set; } = null!;

    public string? Threahold { get; set; }

    /// <summary>
    /// M:月,W:週,D:日
    /// </summary>
    public string ScheduleFreq { get; set; } = null!;

    /// <summary>
    /// 月:01~31,週:0~6,日:1，可多組用半形逗號隔開
    /// </summary>
    public string ScheduleDay { get; set; } = null!;

    /// <summary>
    /// 執行時間（24小時制）
    /// </summary>
    public string ScheduleTime { get; set; } = null!;

    public string? Subject { get; set; }

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }

    public virtual ICollection<ScheduleDepart> ScheduleDeparts { get; set; } = new List<ScheduleDepart>();

    public virtual ICollection<ScheduleNotify> ScheduleNotifies { get; set; } = new List<ScheduleNotify>();
}
