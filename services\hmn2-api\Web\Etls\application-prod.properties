######################################
#    System configuration            #
######################################
position.cameras.delay.seconds = 120
clone.videos.delay.seconds = 120
clone.videos.expiry.hours = 48
######################################
#    Database configuration          #
######################################
# for PostgreSQL
spring.datasource.url=***********************************************
spring.datasource.username=postgres
spring.datasource.password=Postgres**123
spring.jpa.generate-ddl=true
spring.jpa.hibernate.ddl-auto=validate
spring.jpa.properties.hibernate.show_sql=true
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.properties.hibernate.type=trace
spring.jpa.hibernate.naming.physical-strategy=org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
######################################
#           Job configuration        #
######################################
etl.videos.clone.job.schedule.fixedDelay = 300000
etl.videos.clone.job.schedule.initialDelay = 1000
etl.videos.clean.job.schedule.fixedDelay = 300000
etl.videos.clean.job.schedule.initialDelay = 300000
######################################
#    External API configuration      #
######################################
console.api.service.url = http://10.56.56.147:8000
console.api.version = v3
console.api.signatureAlgorithm = HmacSHA1
console.api.licenseCd = 6920802101149365
console.api.licenseKey = Zv4ulyLGSIV0aunYQTN9LSUMFvQos2H2
