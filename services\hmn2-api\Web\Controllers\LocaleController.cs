﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Reflection.Emit;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices.Marshalling;
using System.Text.Json;
using Web.ActionFilter;
using Web.Constant;
using Web.Models.Controller;
using Web.Models.Controller.Device;
using Web.Models.Controller.Log;
using Web.Models.Controller.Station;
using Web.Models.Service;
using Web.Models.Service.Configuration;
using Web.Models.Service.Fusion;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;
using Web.ActionFilter;
using Web.Validation;

namespace Web.Controller;

/// <summary>
/// 多國語系控制器
/// </summary>
[Route("[controller]")]
[Authorize]
public class LocaleController(IDataAccessService dataAccessService,
                              ICredentialService credentialService,
                              IRequestContextService requestContextService,
                              IUtilityService utilityService,
                              ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly IUtilityService _utilityService = utilityService;

    [HttpPost("strings")]
    [RequestParamListDuplicate("StringId")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateLocaleString(List<InCreateLocaleString> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string appCode = _user.AppCode;

        bool addResult = false;

        List<LocaleString> hmnInputList =
            paramList.Select(e => new LocaleString
            {
                Locale = e.Locale,
                StringId = e.StringId,
                StringContent = e.StringContent,
                CreatedTime = DateTime.Now
            }).ToList();

        int createCount = await _dataAccessService.CreateRangeAsync(hmnInputList);

        addResult = createCount == hmnInputList.Count;

        ReturnModel returnModel = new()
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = addResult ? StatusCodes.Status201Created : StatusCodes.Status400BadRequest,
            result = addResult
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    // 此 API 跳過驗證
    [HttpGet("strings")]
    [AllowAnonymous]
    public async Task<IActionResult> RetrieveLocaleString(InRetrieveLocaleString param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();

        var query = _dataAccessService.Fetch<LocaleString>
        (
            e => (string.IsNullOrEmpty(param.Locale) || e.Locale == param.Locale)
              && (string.IsNullOrEmpty(param.StringId) || e.StringId == param.StringId)
        ).Select(e => new { e.Locale, e.StringId, e.StringContent });

        (int recordTotal, List<object> recordList) = 
            await _utilityService.Pagination(query, param.page, param.size, param.sort);

        ReturnModel returnModel = new ()
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal,
                recordList
            }
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }
    [HttpPost("validation")]
    [RequestParamListDuplicate("StringId")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> ValidateCreateLocaleString([FromBody] List<InCreateLocaleString> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();

        // 判斷是否有錯誤
        bool validateResult = true;

        ReturnModel returnModel = new()
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = validateResult ? StatusCodes.Status200OK : StatusCodes.Status400BadRequest,
            result = validateResult,
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }
}