﻿using System.ComponentModel.DataAnnotations;

namespace Web.Models.Service;

public class UserResult
{
    public string Account { get; set; }
    public string UserName { get; set; }
    public string AppCode { get; set; }
    public string DeptCode { get; set; }
    public IEnumerable<string> DeptCodeList {get; set; }
    public string AreaCode { get; set; }
    public IEnumerable<string> AreaCodeList { get; set; }
    public string ClientIP { get; set; }
    /// <summary>
    /// IsAdmin 有三種
    /// </summary>
    public string IsAdmin { get; set; }
    /// <summary>IsDeveloper 是決定登入者是否為開發人員（true 則在程式錯誤時，會把StaceTrack輸出到SweetAlert上）</summary>
    public bool IsDeveloper { get; set; } = false;
    /// <summary>IsViewAllArea 是決定登入者是否有權限看所有院區（true 則不用判斷UserDeptMonPerm這個table）</summary>
    public bool IsViewAllArea { get; set; } = false;
    /// <summary>
    /// IsManagedDept 為USER所屬單位是否為資材管理單位
    /// </summary>
    public bool IsManagedDept { get; set; } = false;

    public bool EnableAlarmVoice { get; set; } = false; //使用者是否啟用告警音效
    public int PositionInterval { get; set; } = 0; //定位時間差
    public int AlarmInterval { get; set; } = 0; //告警音效持續秒數
    public string FontSize { get; set; } //即時事件字體大小
    public int BatteryLevel { get; set; } //低電量閥值
    public bool IsSuperAdmin { get; set; } = false; //是否為超級管理員
}