﻿using System.ComponentModel.DataAnnotations;
using Web.Constant;
using Web.Validation;

namespace Web.Models.Controller.Building;

public class RetrieveVideoCaptureTask
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    public string Id { get; set; }
    public string Active { get; set; }
    public string DeptCode { get; set; }
    public string ServiceCode { get; set; }
    public string TaskId { get; set; }
    public string TaskDataId { get; set; }
    public string TaskAction { get; set; }
    public string EventCode { get; set; }
    public string EventName { get; set; }
    public string ObjectName { get; set; }
    public string DeviceName { get; set; }
    public string DeviceType { get; set; }
    public string TaskStationSid { get; set; }
    public string TaskStartsAtBetween { get; set; }
    public string FragmentCount { get; set; }
    public string TaskStartedFragment { get; set; }
    public string TotalVideoCount { get; set; }
    public string TaskResult { get; set; }
}