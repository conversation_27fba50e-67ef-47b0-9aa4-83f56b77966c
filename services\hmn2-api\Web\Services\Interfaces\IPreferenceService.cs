﻿using Web.Models.Controller;
using Web.Models.Service.Preference;
using Web.Repository.Models.Entities;

namespace Web.Services.Interfaces
{
    public interface IPreferenceService
    {
        Task<List<SysParameters>> FetchAllGlobalParameter(string appCode);
        Task<string> FetchGlobalParameter(string appCode, string paraCode);
        Task<List<OutFetchSysParameter>> FetchSysParameter(string appCode);
        Task<string> FetchSysParameter(string appCode, string areaCode, string paraCode);
        Task<List<UserClientPara>> FetchUserClientPara(string appCode, string areaCode, string userAccount);
    }
}