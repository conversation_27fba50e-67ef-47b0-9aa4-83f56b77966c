﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using Web.Models.Controller;
using Web.Models.Controller.Inventory;
using Web.Models.Service;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Models.Service.Fusion;
using Web.Services.Interfaces;
using Web.Validation;

namespace Web.Controller;

/// <summary>
/// 
/// </summary>
[Route("[controller]")]
[Authorize]
public class InventoryController(IDataAccessService dataAccessService,
                                 IBusinessService businessService,
                                 IConfigurationService configurationService,
                                 IPreferenceService preferenceService,
                                 ICredentialService credentialService,
                                 IRequestContextService requestContextService,
                                 ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly IBusinessService _businessService = businessService;
    private readonly IConfigurationService _configurationService = configurationService;
    private readonly IPreferenceService _preferenceService = preferenceService;

    [HttpGet("InventoryChangeDept")]
    public async Task<IActionResult> InventoryChangeDept([FromQuery] InventoryChangeDept param)
    {
        //單位盤點是否顯示其它單位的裝置
        bool isShowOtherDeptDevice = await _configurationService.FetchIsShowOtherDeptDeviceOnDeptInventory(_user.AppCode);

        //取得所有對象資料
        var objectList = _dataAccessService.Fetch<ObjectDatum>(x => x.AppCode == _user.AppCode).ToList();

        //取得所有次平面與部門對應列表
        var departSectorList = _dataAccessService.Fetch<DepartSector>(x => x.AppCode == _user.AppCode).ToList();
        //取得使用者有權限的院區中所有棟別列表
        //List<Building> buildingList = _fieldService.FetchBuildingList(_userResult.AppCode, _userResult.AreaCodeList);
        List<Building> buildingList = _dataAccessService.Fetch<Building>(x => x.AppCode == _user.AppCode).ToList();

        //取得使用者有權限的院區中所有棟別代碼列表
        List<string> buildingCodeList = buildingList.Select(buildingList => buildingList.BuildingCode).ToList();

        //不能直接把buildingList 傳給FetchPlaneList 再使用buildingList.Any 做判斷，會有錯誤。印像中去年有遇過，好像是postgresql driver造成（SQL Server不會）
        //取得使用者有權限的棟別中所有樓層列表
        List<Plane> planeList = _dataAccessService.Fetch<Plane>(x => x.AppCode == _user.AppCode && buildingCodeList.Any(BuildingCode => BuildingCode == x.BuildingCode)).ToList();

        //取得所有次平面列表
        List<Sector> sectorList = _dataAccessService.Fetch<Sector>(x => x.AppCode == _user.AppCode).ToList();


        //所有單位清單(僅供查找非所屬單位之裝置所屬次平面用)
        // 先取得部門資料
        var departmentList = _dataAccessService.Fetch<Department>(x => x.AppCode == _user.AppCode && _user.AreaCodeList.Any(e => e == x.AreaCode) && x.Enable == true).ToList();

        // 然後在記憶體中組合資料
        var allDeptList = departmentList.Select(a => new
        {
            a.Id,
            Enable = (a.Enable != null && a.Enable == true) ? "Y" : "N",
            a.AppCode,
            a.AreaCode,
            a.DeptCode,
            a.CustomDeptCode,
            a.DeptName,
            a.Supervisor,
            a.DeptEmail,
            a.OrgType,
            a.IsManagedDept,
            a.IsUsageDept,
            a.BuildingCode,
            Sectors = (from ds in departSectorList
                       where ds.DeptCode == a.DeptCode && ds.AreaCode == a.AreaCode
                       join s in sectorList on ds.SectorCode equals s.SectorCode
                       select new
                       {
                           Enable = (s.Enable != null && s.Enable == true) ? "Y" : "N",
                           s.SectorCode,
                           s.SectorName,
                           s.PlaneCode,
                           PlaneName = planeList.FirstOrDefault(p => p.PlaneCode == s.PlaneCode)?.PlaneName,
                           BuildingCode = planeList.FirstOrDefault(p => p.PlaneCode == s.PlaneCode)?.BuildingCode,
                           BuildingName = buildingList.FirstOrDefault(b => b.BuildingCode == planeList.FirstOrDefault(p => p.PlaneCode == s.PlaneCode)?.BuildingCode)?.BuildingName
                       }).ToList()
        }).ToList();

        bool is250DepartControl = bool.Parse(await _preferenceService.FetchGlobalParameter(_user.AppCode, "250DepartControl"));
        var deptCodeList = new List<string>();
        if (is250DepartControl)
        {
            deptCodeList.AddRange(_user.DeptCodeList.Where(x => string.IsNullOrEmpty(param.deptCode) || x == param.deptCode).ToList());
        }
        else
        {
            if (!string.IsNullOrEmpty(param.deptCode))
                deptCodeList.Add(param.deptCode);
        }

        //取得登入者有權限單位的使用裝置（資料從HMN來）
        var deviceList = _configurationService.GetDeviceAllInfo(param.areaCode, deptCodeList, true).ToList();

        //取得傳入PID清單的裝置位置（資料從FusionNet來）
        List<Web.Models.Service.Configuration.DevicePositions> devicePositionList = await _configurationService.GetDevicePosition(string.Join(",", deviceList.Select(x => x.Pid)));

        //取得傳入PID清單的裝置資訊（資料從FusionNet來）
        //雖然GetCoreDeviceList 回傳中有Position，但不一定是即時資料，Beau 的建議取得Position還是透過 api/v3/devices/positions @20230815
        List<Devices> deviceCoreList = await _configurationService.GetCoreDeviceList(string.Join(",", deviceList.Select(x => x.Pid)));

        //取得裝置類型
        List<DeviceType> deviceTypeList = await _configurationService.GetDeviceTypeList();

        //取得所有基站資料
        List<Station> stationList = _dataAccessService.Fetch<Station>(e => e.AppCode == _user.AppCode).ToList();//, _user.AreaCodeList

        //取得所有次平面與基站對應列表
        List<SectorStation> sectorStationList = _dataAccessService.Fetch<SectorStation>(x => x.AppCode == _user.AppCode).ToList();

        //取得使用者有權限的院區中所有區域
        List<Location> locationList = _dataAccessService.Fetch<Location>(x => x.AppCode == _user.AppCode).ToList();

        //取得所有對象類型列表
        List<ObjectType> objectTypeList = _dataAccessService.Fetch<ObjectType>(x => x.AppCode == _user.AppCode).ToList();

        //取得所有對象群組資料
        List<ObjectGroup> objectGroupList = _dataAccessService.Fetch<ObjectGroup>(x => x.AppCode == _user.AppCode && x.Active == true && x.Enable == true).OrderBy(x => x.GroupName).ToList();

        //取得中文語系資源
        List<LocaleString> localeStringList = _dataAccessService.Fetch<LocaleString>(e => e.Locale == "zh-TW").ToList();

        // 只顯示 DeviceType 的 isPositioningSupported = true 的裝置
        deviceList = deviceList.Where(d => deviceTypeList.Any(dt => dt.type == d.DeviceType && dt.isPositioningSupported)).ToList();

        var returnResult = new ReturnModel(StatusCodes.Status200OK, true, new
        {
            allDeptList,
            buildingList,
            deviceCoreList,
            devicePositionList,
            deviceTypeList,
            isShowOtherDeptDevice,
            is250DepartControl,
            localeStringList,
            locationList,
            masterList = deviceList,
            objectGroupList,
            objectList,
            objectTypeList,
            planeList,
            sectorList,
            sectorStationList,
            showMasterList = deviceList,
            sourceMasterList = deviceList,
            stationList,
            userResult = _user
        });

        return Ok(returnResult);
    }

    [HttpGet("DeptInventoryMounted")]
    public async Task<IActionResult> DeptInventoryMounted()
    {
        //取得使用者有權限的單位列表
        List<Department> deptList = _businessService.FetchDepartmentList(_user.AppCode);
        deptList = deptList.OrderBy(x => x.DeptName).ToList();

        //取得使用者有權限的院區列表
        List<Area> areaList = await _dataAccessService.Fetch<Area>(e => e.AppCode == _user.AppCode && _user.AreaCodeList.Contains(e.AreaCode)).ToListAsync();

        var sysCodeList =
        await _dataAccessService.Fetch<SysCode>()
            .Where(s => s.CodeType == "EquipmentStatus" && s.Enable == true)
            .Select(s => new { s.Code, s.Name, StringId = $"${s.StringId}", s.ValueType, s.Extend, s.Sort })
            .OrderBy(s => s.Sort)
            .ToListAsync();

        var returnResult = new ReturnModel(StatusCodes.Status200OK, true, new
        {
            areaList,
            deptList,
            sysCodeList,
            userResult = _user
        });

        return Ok(returnResult);
    }
    [HttpPatch("equipmentStatus")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateEquipmentStatus([FromBody] List<UpdateEquipmentStatus> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");
        List<ReturnError> errorList = [];

        ReturnModel result = null;

        // 檢查傳入的參數是否為空
        if (paramList == null || paramList.Count == 0)
        {
            errorList.Add(new ReturnError { index = 1, code = "", errors = [new ErrorDetail { index = 1, error = "err.null.UpdateEquipmentStatus.Data.param" }] });
            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, errorList));
        }

        //檢查要修改的對象資料
        for (int i = 0; i < paramList.Count; i++)
        {
            var objectData = paramList[i];

            var errorDetailList = new List<ErrorDetail>();

            // 檢查是否有指定區域代碼
            if (string.IsNullOrEmpty(objectData.AreaCode))
            {
                errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.null.UpdateEquipmentStatus.ObjectData.AreaCode" });
            }

            // 檢查是否有指定對象代碼
            if (string.IsNullOrEmpty(objectData.ObjectCode))
            {
                errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.null.UpdateEquipmentStatus.ObjectData.ObjectCode" });
            }
            else
            {
                // 檢查對象是否存在
                bool hasObject = _dataAccessService.Fetch<ObjectDatum>(e => e.AppCode == _user.AppCode && e.AreaCode == objectData.AreaCode && e.ObjectCode == objectData.ObjectCode && e.Active == true && e.Enable == true).Any();

                // 如果對象不存在，則回傳錯誤訊息
                if (!hasObject)
                {
                    errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.notFound.UpdateEquipmentStatus.ObjectData.ObjectCode" });
                }
            }

            // 檢查是否有指定設備狀態
            if (string.IsNullOrEmpty(objectData.EquipmentStatus))
            {
                errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.null.UpdateEquipmentStatus.ObjectData.EquipmentStatus" });
            }
            else
            {
                // 檢查設備狀態是否存在
                bool hasEquipmentStatus = _dataAccessService.Fetch<SysCode>(x => x.CodeType == "EquipmentStatus" && x.Code == objectData.EquipmentStatus && x.Enable == true).Any();

                // 如果設備狀態不存在，則回傳錯誤訊息
                if (!hasEquipmentStatus)
                {
                    errorDetailList.Add(new ErrorDetail { index = errorDetailList.Count + 1, error = "err.notFound.UpdateEquipmentStatus.ObjectData.EquipmentStatus" });
                }
            }

            // 如果檢查對象存在錯誤，則回傳錯誤訊息
            if (errorDetailList.Count > 0)
            {
                errorList.Add(new ReturnError { index = errorList.Count + 1, code = "", errors = errorDetailList });
            }
        }

        // 如果有錯誤，則回傳錯誤訊息
        if (errorList.Count > 0)
        {
            _logService.Logging("error", logActionName, requestUUID, JsonSerializer.Serialize(errorList));
            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, errorList));
        }

        _dataAccessService.BeginTransaction();

        foreach (var paramObject in paramList)
        {
            var objectData = await _dataAccessService.Fetch<ObjectDatum>(e => e.AppCode == _user.AppCode && e.AreaCode == paramObject.AreaCode && e.ObjectCode == paramObject.ObjectCode).AsTracking().FirstOrDefaultAsync();

            objectData.EquipmentStatus = paramObject.EquipmentStatus;
            // 如果remark為null，則不更新remark，其餘（包含空字串）則更新remark
            if (paramObject.Remark != null)
            {
                objectData.Remark = paramObject.Remark;
            }
            objectData.ModifyUserAccount = _user.Account;
            objectData.ModifyDate = DateTime.Now;

            await _dataAccessService.UpdateAsync(objectData);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "Object EquipmentStatus update done.");

        return Ok(new ReturnModel(StatusCodes.Status200OK, true));
    }
}
