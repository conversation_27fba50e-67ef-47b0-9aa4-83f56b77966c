﻿using System.ComponentModel.Design;
using System.Text.RegularExpressions;
using Web.Models.Controller.Department;
using Web.Models.Controller;
using Web.Models.Service;
using Web.Models.Service.Business;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;

namespace Web.Services;

public class BusinessService(ICredentialService credentialService,
    IPreferenceService preferenceService,
    IDataAccessService dataAccessService) : IBusinessService
{
    private readonly IDataAccessService _dataAccessService = dataAccessService;
    private readonly IPreferenceService _preferenceService = preferenceService;
    private readonly UserResult _user = credentialService.UserResult;

    public List<ReturnError> ValidateDepartmentData(string appCode, List<CreateDepartment> paramList)
    {
        List<ReturnError> errors = [];

        // 檢查傳入的參數是否為空
        if (paramList == null || paramList.Count == 0)
        {
            errors.Add(new ReturnError { index = 1, code = "", errors = [new ErrorDetail { index = 1, error = "param is null or type error" }] });
            return errors;
        }
        var areaCode = paramList[0].AreaCode;

        var existsDeptList = _dataAccessService.Fetch<Department>(e => e.AppCode == appCode && e.AreaCode == areaCode);

        for (int i = 0; i < paramList.Count; i++)
        {
            var item = paramList[i];

            List<ErrorDetail> errorList = [];

            // 檢查部門代碼是否在傳入的參數中重複
            if (paramList.Where(e => e.DeptCode == item.DeptCode).Count() >= 2)
            {
                errorList.Add(new ErrorDetail { index = errorList.Count + 1, error = "DeptCode is duplicate" });
            }

            // 檢查部門代碼在資料庫中是否已存在
            if (existsDeptList.Where(e => e.DeptCode == item.DeptCode).Any())
            {
                errorList.Add(new ErrorDetail { index = errorList.Count + 1, error = "DeptCode is already exist" });
            }

            // 檢查部門代碼是否符合規則（只能是英文大小寫、數字、底線）
            if (!Regex.IsMatch(item.DeptCode, @"^[a-zA-Z0-9_]*$"))
            {
                errorList.Add(new ErrorDetail { index = errorList.Count + 1, error = "DeptCode is not match the rule" });
            }

            if (errorList.Count > 0)
            {
                errors.Add(new ReturnError { index = i + 1, code = item.DeptCode, errors = errorList });
            }
        }

        return errors;
    }

    #region public FetchDepartmentList 取得登入者有權限的單位列表（不分使用/資材單位）
    /// <summary>
    /// 取得登入者有權限的單位列表（不分使用/資材單位）
    /// </summary>
    /// <param name="appCode"></param>
    /// <returns></returns>
    [Obsolete("This method is obsolete. Use FetchDepartmentsByPermission instead.")]
    public List<Department> FetchDepartmentList(string appCode)
    {
        return _dataAccessService.Fetch<Department>(e => e.AppCode == appCode
                                        && (_user.IsViewAllArea || _user.AreaCodeList.Contains(e.AreaCode))
                                        && (_user.IsViewAllArea || _user.DeptCodeList.Contains(e.DeptCode))
                                        && e.Enable == true).ToList<Department>();
    }
    #endregion

    /// <summary>
    /// 根據AppCode,AreaCode和系統設置過濾部門列表。
    /// </summary>
    /// <param name="appCode">應用程式代碼</param>
    /// <param name="areaCode">區域代碼</param>
    /// <param name="isCheck250DepartControl">是否檢查250部門控制</param>
    /// <returns>過濾後的部門查詢結果</returns>
    /// <remarks>
    /// 權限控制邏輯：<br/>
    /// 1. 獲取所有啟用的部門，根據應用程式代碼過濾
    /// 2. 首先檢查是否有指定要進行250DepartControl權限判斷。<br/>
    /// 3. 如果沒有指定，則根據用戶是否為管理員或是否有指定部門代碼過濾。<br/>
    /// 4. 如果有指定，則根據 is250DepartControl 決定採用嚴格還是寬鬆的權限控制。<br/>
    /// <br/>
    /// 權限控制邏輯：<br/>
    /// - 嚴格控制（is250DepartControl 為 true）：用戶只能查看有權限的單位。<br/>
    /// - 寬鬆控制（is250DepartControl 為 false）：用戶可查看指定區域的所有單位。<br/>
    /// </remarks>
    public async Task<IQueryable<Department>> FetchDepartmentsByPermission(string appCode, string areaCode, string? check250DepartControl)
    {
        /**
         * Ann 要求此API提供的單位資料，只能是登入者有權限檢視的單位資料 @20240612 by GM
         * 
         */

        // 獲取所有啟用的部門，根據應用程式代碼過濾
        var deptList = _dataAccessService.Fetch<Department>(e => e.AppCode == appCode && e.Enable == true);

        // 如果沒有指定判斷250部門控制，則依單位或IsAdmin權限過濾
        if (check250DepartControl == null)
        {
            return deptList.Where(e => e.AreaCode == areaCode
                                // 用戶可查看所有區域，或用戶的區域代碼列表包含該部門的區域代碼
                                && (_user.IsViewAllArea || _user.AreaCodeList.Contains(e.AreaCode))
                                // 用戶可查看所有區域，或用戶的區域代碼列表包含該部門的部門代碼
                                && (_user.IsViewAllArea || _user.DeptCodeList.Contains(e.DeptCode)));
        }

        // 如果不需要檢查250部門控制，直接返回按區域代碼過濾的結果
        if (check250DepartControl == "N")
        {
            return deptList.Where(e => e.AreaCode == areaCode);
        }

        // 從全局參數中獲取250部門控制的設置
        bool is250DepartControl = bool.Parse(await _preferenceService.FetchGlobalParameter(appCode, "250DepartControl"));

        // 如果250部門控制未啟用，僅按區域代碼過濾
        if (!is250DepartControl)
        {
            return deptList.Where(e => e.AreaCode == areaCode);
        }

        // 250部門控制啟用時，應用更嚴格的權限過濾
        return deptList.Where(e => e.AreaCode == areaCode
                                // 用戶可查看所有區域，或用戶的區域代碼列表包含該部門的區域代碼
                                && (_user.IsViewAllArea || _user.AreaCodeList.Contains(e.AreaCode))
                                // 用戶可查看所有區域，或用戶的區域代碼列表包含該部門的部門代碼
                                && (_user.IsViewAllArea || _user.DeptCodeList.Contains(e.DeptCode)));
    }

    public List<ReturnError> SaveRole(string actionName, string appCode, List<InSaveRole> paramList)
    {
        List<ReturnError> errors = [];

        // 檢查傳入的參數是否為空
        if (paramList == null || paramList.Count == 0)
        {
            errors.Add(new ReturnError { index = 1, code = "", errors = [new ErrorDetail { index = 1, error = "err.null.roles.Role.param" }] });
            return errors;
        }

        var existsRoleList = _dataAccessService.Fetch<Role>().Where(x => x.AppCode == appCode);
        var existsMenuList = _dataAccessService.Fetch<Menu>();
        var existsPermissionList = _dataAccessService.Fetch<SysCode>().Where(x => x.CodeType == "PermissionType");

        // 驗證輸入的資料
        for (int i = 0; i < paramList.Count; i++)
        {
            var item = paramList[i];

            List<ErrorDetail> errorList = [];

            var role = existsRoleList.Where(x => x.RoleCode == item.RoleCode).FirstOrDefault();

            // 有指定RoleCode，代表更新資料，但是在資料庫中找不到
            if (role == null && !string.IsNullOrEmpty(item.RoleCode))
            {
                errorList.Add(new ErrorDetail { index = errorList.Count + 1, error = "err.notFound.roles.Role.Id" });
            }


        }

        return errors;
    }
}
