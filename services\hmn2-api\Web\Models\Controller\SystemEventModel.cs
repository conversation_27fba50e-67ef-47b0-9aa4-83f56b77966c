﻿
using System.ComponentModel.DataAnnotations;
using Web.Validation;
using Web.Constant;

namespace Web.Models.Controller.SystemEvent;

public class RetrieveSystemEvent
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    public string ServiceCode { get; set; }
    public string EventCode { get; set; }
    public string EventName { get; set; }
    public string DeviceType { get; set; }
    public string Enable { get; set; }
    public string Threshold { get; set; }
}

public class ExclusionPeriod
{
    public string Weekly { get; set; }
    public string StartTime { get; set; }
    public string EndTime { get; set; }
}

public class CreateSystemEvent
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ServiceCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "ServiceCode")]
    [RegularExpression(@"^(AbnormalStation|AbnormalDevice|LowBattery|LossSignal)$", ErrorMessage = Constants.ErrorCode.Pattern + "ServiceCode")]
    public string ServiceCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "EventName")]
    [StringLength(100, ErrorMessage = Constants.ErrorCode.Length + "EventName")]
    public string EventName { get; set; }

    [Required(AllowEmptyStrings = true, ErrorMessage = Constants.ErrorCode.NullOrEmpty + "DeviceType")]
    [Unique("ServiceCode", "SystemEvent", "DeviceType", ErrorMessage = Constants.ErrorCode.Unique + "DeviceType")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "DeviceType")]
    public string DeviceType { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Enable")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }
    
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "Threshold")]
    [CheckWeeklyStartTimeEndTimeList("ExclusionPeriodList", ErrorMessage = Constants.ErrorCode.Invalid + "ExclusionPeriodList")]
    public double? Threshold { get; set; }

    public List<ExclusionPeriod> ExclusionPeriodList { get; set; }
}

public class UpdateSystemEvent
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ServiceCode")]
    [RegularExpression(@"^(AbnormalStation|AbnormalDevice|LowBattery|LossSignal)$", ErrorMessage = Constants.ErrorCode.Pattern + "ServiceCode")]
    public string ServiceCode { get; set; }

    [Required(AllowEmptyStrings = true, ErrorMessage = Constants.ErrorCode.NullOrEmpty + "DeviceType")]
    [Exists("ServiceCode", "SystemEvent", "DeviceType", ErrorMessage = Constants.ErrorCode.NotFound + "DeviceType")]
    public string DeviceType { get; set; }

    [StringLength(100, ErrorMessage = Constants.ErrorCode.Length + "EventName")]
    public string EventName { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }
    
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "Threshold")]
    [CheckWeeklyStartTimeEndTimeList("ExclusionPeriodList", ErrorMessage = Constants.ErrorCode.Invalid + "ExclusionPeriodList")]
    public double? Threshold { get; set; }

    public List<ExclusionPeriod> ExclusionPeriodList { get; set; }
}

public class DeleteSystemEvent
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ServiceCode")]
    [RegularExpression(@"^(AbnormalStation|AbnormalDevice|LowBattery|LossSignal)$", ErrorMessage = Constants.ErrorCode.Pattern + "ServiceCode")]
    public string ServiceCode { get; set; }

    [Required(AllowEmptyStrings = true, ErrorMessage = Constants.ErrorCode.NullOrEmpty + "DeviceType")]
    [Exists("ServiceCode", "SystemEvent", "DeviceType", ErrorMessage = Constants.ErrorCode.NotFound + "DeviceType")]
    public string DeviceType { get; set; }
}
