﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class Sector
{
    public int SectorId { get; set; }

    public string? AppCode { get; set; }

    public string? PlaneCode { get; set; }

    public string SectorCode { get; set; } = null!;

    public string? CustomSectorCode { get; set; }

    public string? SectorName { get; set; }

    public bool? Enable { get; set; }

    public string? SectorMapPath { get; set; }

    public double? MapWidth { get; set; }

    public double? MapHeight { get; set; }

    public double? PositionX { get; set; }

    public double? PositionY { get; set; }

    public string? CreateUserAccount { get; set; }

    public DateTime? CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }
}
