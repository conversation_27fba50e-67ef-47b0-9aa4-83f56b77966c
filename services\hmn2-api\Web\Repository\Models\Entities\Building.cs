﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class Building
{
    public int BuildingId { get; set; }

    public string? AppCode { get; set; }

    public string AreaCode { get; set; } = null!;

    public bool Enable { get; set; }

    public string BuildingCode { get; set; } = null!;

    public string? CustomBuildingCode { get; set; }

    public string? BuildingName { get; set; }

    public string? BuildingImgName { get; set; }

    public string? CreateUserAccount { get; set; }

    public DateTime? CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }
}
