﻿using System.ComponentModel.DataAnnotations;
using Web.Validation;
using Web.Constant;

namespace Web.Models.Controller.Task;

public class SearchParams
{
    public string ServiceCode { get; set; }
    public string Action { get; set; }
    public string AreaCode { get; set; }
    public string BuildingCode { get; set; }
    public string PlaneCode { get; set; }
    public string LocCode { get; set; }
    public string DeptCode { get; set; }
    public string ObjectCode { get; set; }
    public string ObjectGroup { get; set; }
    public string ObjectName { get; set; }
    public string ObjectType { get; set; }
    public string DevicePid { get; set; }
    public string DeviceName { get; set; }
    public string DeviceType { get; set; }
    public string StartDate { get; set; }
    public string EndDate { get; set; }
}

public class GetTaskHistory
{
    public string page { get; set; }
    public string size { get; set; }
    public string Action { get; set; }
    public string AreaCode { get; set; }
    public string BuildingCode { get; set; }
    public string BuildingName { get; set; }
    public string DeptCode { get; set; }
    public string DeptName { get; set; }
    public string EndDate { get; set; }
    public string GroupCode { get; set; }
    public string PlaneCode { get; set; }
    public string PlaneName { get; set; }
    public string ServiceCode { get; set; }
    public string ServiceLabel { get; set; }
    public string SponsorDeviceName { get; set; }
    public string SponsorDevicePid { get; set; }
    public string SponsorDeviceType { get; set; }
    public string SponsorLocationCode { get; set; }
    public string SponsorLocationName { get; set; }
    public string SponsorObjectCode { get; set; }
    public string SponsorObjectName { get; set; }
    public string SponsorObjectType { get; set; }
    public string StartDate { get; set; }
    public string TaskId { get; set; }
}

public class RetrieveTask
{
    public string ServiceCode { get; set; }
    public string TaskAction { get; set; }
    public string AreaCode { get; set; }
    public string BuildingCode { get; set; }
    public string PlaneCode { get; set; }
    public string LocCode { get; set; }
    public string DeptCode { get; set; }
    public string ObjectCode { get; set; }
    public string ObjectGroup { get; set; }
    public string ObjectName { get; set; }
    public string ObjectType { get; set; }
    public string DevicePid { get; set; }
    public string DeviceName { get; set; }
    public string DeviceType { get; set; }
    public string StartDate { get; set; }
    public string EndDate { get; set; }
}

public class ResolveTask
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "TaskId")]
    [Exists("", "TaskDatum", "TaskId", ErrorMessage = Constants.ErrorCode.NotFound + "TaskId")]
    public int TaskId { get; set; }

    [RequiredWhenAbsentAttribute("TaskClearMethod,TaskClearMoreDesc", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "TaskHappenReason")]
    [Exists("", "CannedMessage", "CannedCode", ErrorMessage = Constants.ErrorCode.NotFound + "TaskHappenReason")]
    public string TaskHappenReason { get; set; }

    [RequiredWhenAbsentAttribute("TaskHappenReason,TaskClearMoreDesc", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "TaskClearMethod")]
    [Exists("", "CannedMessage", "CannedCode", ErrorMessage = Constants.ErrorCode.NotFound + "TaskClearMethod")]
    public string TaskClearMethod { get; set; }

    [RequiredWhenAbsentAttribute("TaskHappenReason,TaskClearMethod", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "TaskClearMoreDesc")]
    public string TaskClearMoreDesc { get; set; }
}

public class InGetFollowTask
{
    public string AreaCode { get; set; }
}