﻿using System.ComponentModel.DataAnnotations;
using System.Drawing;
using Web.Constant;
using Web.Models.Interface;
using Web.Validation;

namespace Web.Models.Controller.Device;

public class GetDeviceByStationListExUsageDept
{
    public List<string> StationList { get; set; }
    public string ExDeviceUsageDeptCode { get; set; }
    public string SectorCode { get; set; }
    public string StationOwnerDeptCode { get; set; }
    public bool? isPositioningSupported { get; set; }
}
public class RetrievePosition
{
    public string DeptCodes { get; set; }
    public string PlaneCode { get; set; }
    public string SectorCode { get; set; }
    public string ShowOtherDepartDevice { get; set; }
    public List<string> PidList { get; set; }
}

public class RetrieveTrajectory
{
    public string DeptCode { get; set; }
    public string ObjectCode { get; set; }
    public string ObjectName { get; set; }
    public string StartDate { get; set; }
    public string EndDate { get; set; }
}

public class InRetrieveDevice : IPaginationRequest
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    public string? AreaCode { get; set; }
    public string? Pid { get; set; }
    public string? ObjectCode { get; set; }
    public string? ObjectName { get; set; }
    public string? Enable { get; set; }
    public string? Name { get; set; }
    public List<string>? DeviceTypes { get; set; }
    public string? ManageDepartCode { get; set; }
    public string? UsageDepartCode { get; set; }
    public string? ObjectBound { get; set; }
    //SearchMode, CompareKind, CompareType 這是供全文檢索用的，怕忘記這個欄位叫CompareMode
    public string CompareMode { get; set; }
}

public class InUpdateDevice
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Pid")]
    [Exists("", "Device", "Pid", true, true, true, ErrorMessage = Constants.ErrorCode.NotFound + "Pid")]
    public string Pid { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }

    [StringLength(20, ErrorMessage = Constants.ErrorCode.Length + "Name")]
    public string Name { get; set; }

    [IsUsageDept("AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "UsageDepartCode")]
    public string UsageDepartCode { get; set; }
    
    [IsManagedDept("AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "ManageDepartCode")]
    public string ManageDepartCode { get; set; }
}

public class InCreateDevice
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Pid")]
    [StringLength(20, ErrorMessage = Constants.ErrorCode.Length + "Pid")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Pid")]
    [Unique("", "Device", "Pid", true, true, true, ErrorMessage = Constants.ErrorCode.Unique + "Pid")]
    public string Pid { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Enable")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; } = "Y";

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Name")]
    [StringLength(20, ErrorMessage = Constants.ErrorCode.Length + "Name")]
    public string Name { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "DeviceType")]
    [Exists("ParaCode:SupportDeviceType", "SysParameters", "ParaValue", true, ErrorMessage = Constants.ErrorCode.NotFound + "DeviceType")]
    public string DeviceType { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ManageDepartCode")]
    [IsManagedDept("AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "ManageDepartCode")]
    public string ManageDepartCode { get; set; }

    [IsUsageDept("AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "UsageDepartCode")]
    public string? UsageDepartCode { get; set; }

    [Exists("AreaCode", "Station", "SID", ErrorMessage = Constants.ErrorCode.NotFound + "StationSid")]
    public string? StationSid { get; set; }
}

public class InDeleteDevice
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Pid")]
    [Exists("", "Device", "Pid", true, true, true, ErrorMessage = Constants.ErrorCode.NotFound + "Pid")]
    public string Pid { get; set; }
}

    public class InRetrieveDeviceReport
{
    public string AreaCode { get; set; }
    public string? DeptCode { get; set; }
    public string? DeptName { get; set; }
    public List<string> ObjectCodes { get; set; }
    public string? GroupCode { get; set; }
    public string? GroupName { get; set; }
    public string StartDate { get; set; }
    public string EndDate { get; set; }
    public string? Interval { get; set; }
    public string? LineType { get; set; }
    public List<string>? DeviceTypes { get; set; }
    public List<string> ResourceIds { get; set; }
    public List<string> Pids { get; set; }
    public string page { get; set; }
    public string size { get; set; }
}

public class OutRetrieveDeviceReport
{
    public long Id { get; set; }
    public string DeptCode { get; set; }
    public string DeptName { get; set; }
    public string GroupCode { get; set; }
    public string GroupName { get; set; }
    public string ObjectCode { get; set; }
    public string ObjectName { get; set; }
    public string DeviceName { get; set; }
    public string Pid { get; set; }
    public string ResourceId { get; set; }
    public string ResourceName { get; set; }
    public string Value { get; set; }
    public long ReportTime { get; set; }
}

public class InRetrieveFusionDevice
{
    public List<string>? PidList { get; set; }
}