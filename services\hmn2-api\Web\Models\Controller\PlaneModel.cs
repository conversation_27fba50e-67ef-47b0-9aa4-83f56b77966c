﻿using System.ComponentModel.DataAnnotations;
using Web.Constant;
using Web.Validation;

namespace Web.Models.Controller.Plane;

public class RetrievePlane
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    public string AreaCode { get; set; }
    public string BuildingCode { get; set; }
    public string BuildingName { get; set; }
    public string PlaneCode { get; set; }
    public string CustomPlaneCode { get; set; }
    public string PlaneNo { get; set; }
    public string PlaneName { get; set; }
    public string Enable { get; set; }
}

public class CreatePlane
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "BuildingCode")]
    [Exists("", "Building", "BuildingCode", ErrorMessage = Constants.ErrorCode.NotFound + "BuildingCode")]
    public string BuildingCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PlaneCode")]
    [Unique("", "Plane", "PlaneCode", ErrorMessage = Constants.ErrorCode.Unique + "PlaneCode")]
    [StringLength(20, ErrorMessage = Constants.ErrorCode.Length + "PlaneCode")]
    public string PlaneCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "CustomPlaneCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "CustomPlaneCode")]
    public string CustomPlaneCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PlaneNo")]
    [StringLength(3, ErrorMessage = Constants.ErrorCode.Length + "PlaneNo")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "PlaneNo")]
    public string PlaneNo { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PlaneName")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "PlaneName")]
    public string PlaneName { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "MapWidth")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "MapWidth")]
    public double? MapWidth { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "MapHeight")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "MapHeight")]
    public double? MapHeight { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PositionX")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "PositionX")]
    public double? PositionX { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PositionY")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "PositionY")]
    public double? PositionY { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Enable")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }
}

public class UpdatePlane
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PlaneCode")]
    [Exists("", "Plane", "PlaneCode", ErrorMessage = Constants.ErrorCode.NotFound + "PlaneCode")]
    [HasReferenceWhenEquals("Enable", "N", "", "Department,Sector,Location,Fence", "PlaneCode,PlaneCode,PlaneCode,PlaneCode", ErrorMessage = Constants.ErrorCode.Reference + "PlaneCode")]
    public string PlaneCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "CustomPlaneCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "CustomPlaneCode")]
    public string CustomPlaneCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PlaneNo")]
    [StringLength(3, ErrorMessage = Constants.ErrorCode.Length + "PlaneNo")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "PlaneNo")]
    public string PlaneNo { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PlaneName")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "PlaneName")]
    public string PlaneName { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "MapWidth")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "MapWidth")]
    public double? MapWidth { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "MapHeight")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "MapHeight")]
    public double? MapHeight { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PositionX")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "PositionX")]
    public double? PositionX { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PositionY")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "PositionY")]
    public double? PositionY { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Enable")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }
}

public class UpdatePlaneMap
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PlaneCode")]
    [Exists("", "Plane", "PlaneCode", ErrorMessage = Constants.ErrorCode.NotFound + "PlaneCode")]
    public string PlaneCode { get; set; }

     [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ImageBase64")]
     public string ImageBase64 { get; set; } 

     [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ImageFileType")]
    public string ImageFileType { get; set; } 
}

public class DeletePlane
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PlaneCode")]
    [Exists("", "Plane", "PlaneCode", ErrorMessage = Constants.ErrorCode.NotFound + "PlaneCode")]
    [HasReference("", "Department,Sector,Location,Fence", "PlaneCode,PlaneCode,PlaneCode,PlaneCode", ErrorMessage = Constants.ErrorCode.Reference + "PlaneCode")]
    public string PlaneCode { get; set; }
}