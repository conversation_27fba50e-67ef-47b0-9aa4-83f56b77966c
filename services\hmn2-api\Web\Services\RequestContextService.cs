﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Web.Models.AppSettings;
using Web.Services.Interfaces;

namespace Web.Services;

public class RequestContextService(IHttpContextAccessor httpContextAccessor,
        IOptions<AppInfo> appInfoOptions) : IRequestContextService
{
    private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
    private readonly AppInfo _appInfo = appInfoOptions.Value;

    public string GetRequestUID()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext == null)
        {
            return Guid.NewGuid().ToString();
        }

        if (httpContext.Request.Headers.TryGetValue(_appInfo.RequestIdHeaderName, out var headerValues) && !string.IsNullOrEmpty(headerValues))
        {
            return headerValues.ToString();
        }

        var newUUID = Guid.NewGuid().ToString();
        httpContext.Request.Headers[_appInfo.RequestIdHeaderName] = newUUID;
        return newUUID;
    }
    public string GetClientIP()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        return httpContext?.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
    }

    public string GetUserAgent()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        return httpContext?.Request.Headers["User-Agent"].ToString() ?? "Unknown";
    }

    public bool IsSecureConnection()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        return httpContext?.Request.IsHttps ?? false;
    }

    public string GetRequestPath()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        return httpContext?.Request.Path.Value ?? "";
    }

    public IDictionary<string, string> GetRequestHeaders()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        return httpContext?.Request.Headers.ToDictionary(h => h.Key, h => h.Value.ToString())
               ?? new Dictionary<string, string>();
    }

    public void SetCustomRequestHeader(string key, string value)
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext != null)
        {
            httpContext.Request.Headers[key] = value;
        }
    }

    public string GetCorrelationId()
    {
        return GetRequestUID(); // 可能使用不同的邏輯
    }

    public DateTimeOffset GetRequestTimestamp()
    {
        return DateTimeOffset.UtcNow; // 或者從請求中獲取
    }

    public string GetRequestMethod()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        return httpContext?.Request.Method ?? "Unknown";
    }
}
