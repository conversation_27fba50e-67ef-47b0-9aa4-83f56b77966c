﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Reflection.Emit;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices.Marshalling;
using System.Text.Json;
using Web.ActionFilter;
using Web.Constant;
using Web.Models.Controller;
using Web.Models.Controller.Device;
using Web.Models.Controller.Log;
using Web.Models.Controller.Station;
using Web.Models.Controller.SystemConfig;
using Web.Models.Service;
using Web.Models.Service.Configuration;
using Web.Models.Service.Fusion;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;

namespace Web.Controller;

/// <summary>
/// 系統控制器
/// </summary>
[Route("[controller]")]
[Authorize]
public class SystemConfigController(IDataAccessService dataAccessService,
                              ICredentialService credentialService,
                              IRequestContextService requestContextService,
                              ISystemService systemService,
                              ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly ISystemService _systemService = systemService;

    [HttpGet("configurations")]
    public async Task<IActionResult> RetrieveConfigurations(InRetrieveConfiguration param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 取得所有 Configurations
        var configurationList = await _systemService.GetSystemConfigurationList(param.Search??"");

        var returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = configurationList
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }
}