﻿using System.ComponentModel.DataAnnotations;
using Web.Constant;

namespace Web.Models.Service.Preference;

public class EventNotifyPreference
{
    public string ServiceCode { get; set; }
    public bool ObjectDept { get; set; }
    public bool TaskSectorDept { get; set; }
}

public class OutFetchSysParameter
{
    public string ParaType { get; set; }
    public string ParaCode { get; set; }
    public string ParaValue { get; set; }

    public string ParaDesc { get; set; }

    // 區分數據來自 AreaSysPara 還是 GlobalSysPara
    public string Source { get; set; }

    public string? Editable { get; set; }
    public string? DescStringId { get; set; }
}


public class UpdateSysParameter
{
    public string? ParaCode { get; set; }
    public string? ParaValue { get; set; }
    public string? ImageBase64 { get; set; }
    public string? ImageFileType { get; set; }
}

public class DeleteParaCode
{
    // [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "BuildingCode")]
    // [Exists("", "Building", "BuildingCode", ErrorMessage = Constants.ErrorCode.NotFound + "BuildingCode")]
    public string ParaCode { get; set; }
}