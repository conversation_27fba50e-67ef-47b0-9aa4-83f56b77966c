﻿using Web.Models.Service.Configuration;
using Web.Repository.Models.Entities;

namespace Web.Models.Service.Monitor;

public class TaskInfo
{
    public int Id { get; set; }
    public int? TaskId { get; set; }
    public int? Action { get; set; }
    public string BuildingCode { get; set; }
    public string BuildingName { get; set; }
    public string DeptCode { get; set; }
    public string DeptName { get; set; }
    public string EventCode { get; set; }
    public string EventName { get; set; }
    public string? FinishesAt { get; set; }
    public Plane? Plane { get; set; }
    public string ServiceCode { get; set; }
    public string ServiceName { get; set; }
    public List<sector> Sector { get; set; }
    public Station? Station { get; set; }
    public List<EventCannedMessage> EventCannedMessages { get; set; }
    public string LocationCode { get; set; }
    public string LocationName { get; set; }
    public string ObjectCode { get; set; }
    public string ObjectName { get; set; }
    public string ObjectType { get; set; }
    public string GroupCode { get; set; }
    public string Pid { get; set; }
    public string DeviceName { get; set; }
    public string DeviceType { get; set; }
    public string? StartsAt { get; set; }
}

public class InTaskList
{
    public string AppCode { get; set; }
    public int Id { get; set; }
    public int TaskId { get; set; }
    public string ServiceCode { get; set; }
    public string TaskAction { get; set; }
    public string AreaCode { get; set; }
    public string BuildingCode { get; set; }
    public string PlaneCode { get; set; }
    public string LocCode { get; set; }
    public string DeptCode { get; set; }
    public string ObjectCode { get; set; }
    public string ObjectGroup { get; set; }
    public string ObjectName { get; set; }
    public string ObjectType { get; set; }
    public string DevicePid { get; set; }
    public string DeviceName { get; set; }
    public string DeviceType { get; set; }
    public string StartDate { get; set; }
    public string EndDate { get; set; }
}

public class InFollowingTaskList : InTaskList
{
    public IEnumerable<string> DeptCodeList { get; set; }
}


public class UntreatedTaskInfo
{
    public int Id { get; set; }
    public int TaskId { get; set; }
    public string ServiceCode { get; set; }
    public string EventCode { get; set; }
    public string EventName { get; set; }
    public string StartsAt { get; set; }
    public string Pid { get; set; }
    public string DeviceName { get; set; }
    public string DeviceType { get; set; }
    public string? ObjectCode { get; set; }
    public string? ObjectName { get; set; }
    public string? ObjectType { get; set; }
    public TaskLocationInfo? EventLocation { get; set; }
    public TaskLocationInfo? CurrentLocation { get; set; }

    public class TaskLocationInfo
    {
        public string PlaneCode { get; set; }
        public string PlaneName { get; set; }
        public string StationSid { get; set; }
        public string StationName { get; set; }
        public List<StationCamera>? StationCameras { get; set; }
        public string? LocationCode { get; set; }
        public string? LocationName { get; set; }

        public class StationCamera
        {
            public string CameraMac { get; set; }
            public string CameraName { get; set; }
            public string StreamUrl { get; set; }
        }
    }
}

public class InUntreatedTaskList
{
    public string AreaCode { get; set; }
    public string DeptCodes { get; set; }
}

public class ClearFusionTaskInput
{
    public int TaskId { get; set; }
    public string Description { get; set; }
}
