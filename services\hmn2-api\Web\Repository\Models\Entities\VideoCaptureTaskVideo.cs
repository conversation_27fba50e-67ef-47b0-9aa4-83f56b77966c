﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class VideoCaptureTaskVideo
{
    public int Id { get; set; }
    public int VideoCaptureTaskId { get; set; }
    public int? CameraId { get; set; }
    public string CameraCode { get; set; }
    public string CameraName { get; set; }
    public string CameraVideoFilename { get; set; }
    public bool CameraVideoFileDeleted { get; set; }
    public int Fragment { get; set; }
    public int TaskVideoResult { get; set; }
    public DateTime CreateDate { get; set; }
    public DateTime? ModifyDate { get; set; }
}
