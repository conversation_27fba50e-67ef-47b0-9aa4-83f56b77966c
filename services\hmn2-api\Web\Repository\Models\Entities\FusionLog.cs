﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class FusionLog
{
    public int LogId { get; set; }

    public string? AppCode { get; set; }

    public string ApiUrl { get; set; } = null!;

    public string RequestMethod { get; set; } = null!;

    public string? RequestParam { get; set; }

    public string? ResponseResult { get; set; }

    public string? ExecResult { get; set; }

    public int? StatusCode { get; set; }

    public string? ReasonPhrase { get; set; }

    public int ConnTiming { get; set; }

    public int ExecTiming { get; set; }

    public string? ClientIP { get; set; }

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }
}
