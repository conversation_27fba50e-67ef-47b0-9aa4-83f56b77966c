﻿using System.ComponentModel.DataAnnotations;
using Web.Validation;
using Web.Constant;

namespace Web.Models.Controller.Role;

public class InRetrieveRole
{
    public int? Id { get; set; }
    public string? page { get; set; }
    public string? size { get; set; }
    public string? RoleCode { get; set; }
    public string? RoleName { get; set; }
    public string? RoleDesc { get; set; }
    public string? Enable { get; set; }
}

public class Menu
{
    public int? MenuId { get; set; }
    public string Title { get; set; }
    public string TypeDesc { get; set; }
    public int? ParentMenuId { get; set; }
    public List<string> Permissions { get; set; }
}

public class InCreateRole
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "RoleCode")]
    [Unique("", "Role", "RoleCode", ErrorMessage = Constants.ErrorCode.Unique + "RoleCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "RoleCode")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "RoleCode")]
    [ExistsMenus("Menus", ErrorMessage = Constants.ErrorCode.NotFound + "Menus")]
    public string RoleCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "RoleName")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "RoleName")]
    public string RoleName { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "RoleName")]
    public string RoleDesc { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Enable")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }

    public List<Menu> Menus { get; set; }
}

public class InUpdateRole
{

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "RoleCode")]
    [Exists("", "Role", "RoleCode", ErrorMessage = Constants.ErrorCode.NotFound + "RoleCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "RoleCode")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "RoleCode")]
    [ExistsMenus("Menus", ErrorMessage = Constants.ErrorCode.NotFound + "Menus")]
    // [HasReferenceWhenEquals("Enable", "N", "", "UserDatum", "RoleCode", ErrorMessage = Constants.ErrorCode.Reference + "RoleCode")]
    public string RoleCode { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "RoleName")]
    public string RoleName { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "RoleDesc")]
    public string RoleDesc { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }

    public List<Menu> Menus { get; set; }
}

public class InDeleteRole
{

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "RoleCode")]
    [Exists("", "Role", "RoleCode", ErrorMessage = Constants.ErrorCode.NotFound + "RoleCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "RoleCode")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "RoleCode")]
    [HasReference("", "UserDatum", "RoleCode", ErrorMessage = Constants.ErrorCode.Reference + "RoleCode")]
    public string RoleCode { get; set; }
}

public class RoleDto
{
    public int Id { get; set; }

    public string RoleCode { get; set; }

    public string RoleName { get; set; }

    public string RoleDesc { get; set; }

    public DateTime? CreateDate { get; set; }

    public DateTime? ModifyDate { get; set; }

    public string Enable { get; set; }

    public List<Menu> Menus { get; set; }
}