﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Data;
using Web.Models.Controller;
using Web.Models.Controller.Menu;
using Web.Models.Service;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;

namespace Web.Controller;

/// <summary>
/// Menu控制器
/// </summary>
[Route("[controller]")]
[Authorize]
public class MenuController(IDataAccessService dataAccessService,
                            ICredentialService credentialService,
                            IRequestContextService requestContextService,
                            ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    [HttpGet("menus")]
    public async Task<IActionResult> RetrieveMenu(InRetrieveMenu param)
    {
        List<ReturnError> errors = [];

        // 取得所有Menu
        var menuList = await _dataAccessService.Fetch<Menu>().ToListAsync();

        if (menuList == null || menuList.Count == 0)
        {
            errors.Add(new ReturnError { index = 1, code = "", errors = [new ErrorDetail { index = 1, error = "err.notFound.users.UserData.Menu" }] });
            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, errors));
        }

        // 取得有權限的Menu
        var hasPermissionMenuList = menuList;

        if (hasPermissionMenuList == null || hasPermissionMenuList.Count() == 0)
        {
            errors.Add(new ReturnError { index = 1, code = "", errors = [new ErrorDetail { index = 1, error = "err.notFound.users.UserData.hasPermissionMenuList" }] });
            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, errors));
        }

        // 取得Menu 有那些權限可被設定
        var menuPermissionTypeList = await _dataAccessService.Fetch<MenuPermissionType>().Where(x => x.Enable == true).ToListAsync();

        if (menuPermissionTypeList == null || menuPermissionTypeList.Count() == 0)
        {
            errors.Add(new ReturnError { index = 1, code = "", errors = [new ErrorDetail { index = 1, error = "err.notFound.users.MenuPermissionType.menuPermissionTypeList" }] });
            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, errors));
        }

        // 依有權限的Menu取得最上層的Menu及Custom Menu(custome menu是自訂的Menu，且只會有一層)
        var topMenuList =
            (from m1 in hasPermissionMenuList
             join m2 in menuList on m1.ParentMenuId equals m2.MenuId
             where m2.Type == "Menu" && m2.ParentMenuId == 0
             select m2).Union
            (
             from m1 in menuList
             where m1.Type == "Custom" && m1.ParentMenuId == 0
             select m1
            )
            .Distinct();

        // 取得有權限的Menu及子Menu
        var menuPermissionList = (from m in topMenuList
                                  select new
                                  {
                                      m.MenuId,
                                      Title = m.StringId == null ? m.Title : $"${m.StringId}",
                                      Desc = m.Title,
                                      m.ParentMenuId,
                                      m.Type,
                                      m.Url,
                                      m.icon,
                                      m.Sort,
                                      m.QueryLog,
                                      m.ComponentName,
                                      ChildMenuList = (from c in hasPermissionMenuList
                                                       where c.ParentMenuId == m.MenuId && c.Type == "Menu"
                                                       select new
                                                       {
                                                           c.MenuId,
                                                           Title = string.IsNullOrWhiteSpace(c.StringId) ? c.Title : $"${c.StringId}",
                                                           Desc = c.Title,
                                                           c.ParentMenuId,
                                                           c.Type,
                                                           c.Url,
                                                           c.icon,
                                                           c.Sort,
                                                           c.ComponentName,
                                                           Permission = menuPermissionTypeList.Where(x => x.MenuId == c.MenuId).Select(x => x.PermissionType)
                                                       }).OrderByDescending(x => x.Sort)
                                  }).OrderByDescending(x => x.Sort);

        var returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = menuPermissionList.ToList()
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }
}
