﻿using Dapper;
using System.Linq.Expressions;
using System.Runtime.CompilerServices;
using Web.Repository.Models.Entities;

namespace Web.Services.Interfaces;

public interface IDataAccessService
{
    void BeginTransaction();
    Task<int> CommitAsync();
    string? ConvertExpressionToSql(LambdaExpression expression);
    Task<int?> CreateAsync<T>(T entity, [CallerMemberName] string callMethodName = null) where T : class;
    Task<int> DeleteAsync<T>(Expression<Func<T, bool>> predicate, [CallerMemberName] string callMethodName = null) where T : class;
    Task<int> DeleteAsync<T>(T entity, [CallerMemberName] string callMethodName = null) where T : class;
    IQueryable<T> ExecuteLINQ<T>(IEnumerable<T> query) where T : class;
    Task<IDictionary<string, object>> ExecuteParallelQueriesAsync(params (string Name, Func<FusionS3HMNContext, Task<object>> Query)[] queries);
    Task<List<dynamic>> ExecuteSqlQuery(string sql, DynamicParameters parameters);
    Task<List<T>> ExecuteSqlQuery<T>(string sql);
    IQueryable<T> Fetch<T>(Expression<Func<T, bool>> predicate = null) where T : class;
    string GetCompareResultLog<T>(CompareResult<T> compareResult);
    Task<TResult?> MaxAsync<TEntity, TResult>(Expression<Func<TEntity, TResult?>> selector)
        where TEntity : class
        where TResult : struct;
    int GetPendingAdds();
    int GetPendingDeletes();
    int GetPendingModifies();
    Task RollbackAsync();
    Task<int> UpdateAsync<T>(Expression<Func<T, bool>> predicate, Dictionary<string, object> updateValues, [CallerMemberName] string callMethodName = null) where T : class;
    Task<int> UpdateAsync<T>(IEnumerable<T> entities, [CallerMemberName] string callMethodName = null) where T : class;
    Task<CompareResult<T>> UpdateAsync<T>(List<T> entities, [CallerMemberName] string callMethodName = null) where T : class;
    Task<int> UpdateAsync<T>(T entity, [CallerMemberName] string callMethodName = null) where T : class;
    Task<int> UpdateAsync<T>(T entity, [CallerMemberName] string callMethodName = null, params Expression<Func<T, object>>[] updateProperties) where T : class;
    Task<int> UpdateAsync<T>(T entity, string[] updatePropertyNames, [CallerMemberName] string callMethodName = null) where T : class;
    Task<int> CreateRangeAsync<T>(IEnumerable<T> entities, [CallerMemberName] string callMethodName = null) where T : class;
    Task<int> UpdateRangeAsync<T>(IEnumerable<T> entities, [CallerMemberName] string callMethodName = null, params Expression<Func<T, object>>[] updateProperties) where T : class;
    Task<List<T>> FetchWithNewContext<T>(Expression<Func<T, bool>> predicate = null) where T : class;
    Task<List<T>> FetchAsync<T>(Expression<Func<T, bool>> predicate = null) where T : class;
}