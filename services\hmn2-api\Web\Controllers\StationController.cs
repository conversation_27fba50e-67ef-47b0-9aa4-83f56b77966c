﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Configuration;
using System.Reflection.Emit;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices.Marshalling;
using System.Text.Json;
using Web.Constant;
using Web.Models.Controller;
using Web.Models.Controller.Device;
using Web.Models.Controller.Station;
using Web.Models.Service;
using Web.Models.Service.Field;
using Web.Models.Service.Fusion;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;
using Web.Validation;
using static Web.Models.Service.Fusion.DeviceResults;

namespace Web.Controller;

/// <summary>
/// 基站控制器
/// </summary>
[Route("[controller]")]
[Authorize]
public class StationController(IDataAccessService dataAccessService,
                            IFieldService fieldService,
                            IConfigurationService configurationService,
                            IStationService stationService,
                            IConfiguration configuration,
                            ICredentialService credentialService,
                            IRequestContextService requestContextService,
                            ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly IFieldService _fieldService = fieldService;
    private readonly IConfigurationService _configurationService = configurationService;
    private readonly IStationService _stationService = stationService;
    private readonly IConfiguration _configuration = configuration;

    [HttpPatch("st3")]
    [RequestParamListDuplicate("SID")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateSt3([FromBody] List<InUpdateSt3> paramList)
    {
        string appCode = _user.AppCode;
        string? requestUUID = _requestContextService.GetRequestUID();

        string[] sids = paramList.Select(e => e.SID.ToUpper()).ToArray();

        // 將InUpdateSt3轉換成Fusion所要求的格式
        List<IDictionary<string, object>> fusionStationInputList =
            paramList.Select(e => new Dictionary<string, object>
            {
                { "sid", e.SID.ToUpper() },
                { "name", e.StationName },
                { "enable", e.Enable == "Y" }
            }).ToList<IDictionary<string, object>>();

        // 取出此次要更新的HMN Station
        List<Station> hmnStationInputList = 
            await _dataAccessService.Fetch<Station>(e => e.AppCode == appCode && sids.Contains(e.SID)).AsTracking().ToListAsync();

        // 將InUpdateSt3轉換成要更新的HMN裝置
        List<InUpdateDevice> deviceInputList = 
            paramList.Select(e=> new InUpdateDevice 
            {
                AreaCode = e.AreaCode,
                Enable = e.Enable,
                Name = e.DeviceName,
                Pid = $"{e.SID.ToUpper()}{Constants.MMWAVE_PID_SUFFIX}",
                UsageDepartCode = e.UsageDepartCode,
                ManageDepartCode = e.ManageDepartCode
            }).ToList();

        // 將InUpdateSt3 Configurations轉換成Fusion所要求的格式
        List<PatchConfigurationInput> fusionConfigurationInputList =
            paramList.Select(e => new PatchConfigurationInput
            {
                sid = e.SID.ToUpper(),
                configurations = e.Configurations?.Select(c=>new ResourceConfiguration 
                { 
                    resourceId = c.resourceId,
                    value = c.value
                }).ToList()
            }).ToList();

        // 更新Fusion Station
        List<PatchAPIResult> fusionStationResultList = await _stationService.PatchStation(fusionStationInputList);

        // 判斷每一筆的PatchAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> fusionErrorList = _stationService.GetFusionError(fusionStationResultList.Select(e => new CommonAPIResult { code = e.code, sid = e.sid, errors = e.errors, id = e.id }).ToList());

        // 取出Fusion更新成功的Station
        var successStationList = hmnStationInputList.Where(e => fusionStationResultList.Any(a => a.sid == e.SID && (a.errors == null || a.errors.Count == 0))).ToList();

        // 取出Fusion更新成功的Configuration
        var successConfigurationList = fusionConfigurationInputList.Where(e => fusionStationResultList.Any(a => a.sid == e.sid && (a.errors == null || a.errors.Count == 0) && e.configurations != null && e.configurations.Count>0)).ToList();

        // 如果有要更新Configuration
        if (successConfigurationList != null && successConfigurationList.Count>0)
        {
            // 更新Fusion Configuration
            List<PatchAPIResult> fusionConfigurationResultList = await _stationService.PatchConfiguration(fusionConfigurationInputList);

            // 判斷每一筆的PatchAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
            fusionErrorList.AddRange(_stationService.GetFusionError(fusionConfigurationResultList.Select(e => new CommonAPIResult { code = e.code, sid = e.sid, errors = e.errors, id = e.id }).ToList()));
        }

        // 將更新成功的Station，寫入資料庫
        foreach (var station in successStationList)
        {
            var newStation = paramList.FirstOrDefault(e=>e.SID.ToUpper() == station.SID);

            station.StationName = newStation.StationName;
            station.Enable = newStation.Enable == "Y";

            station.ModifyDate = DateTime.Now;
            station.ModifyUserAccount = _user.Account;

            await _dataAccessService.UpdateAsync(station);
        }

        // 將更新Configuration成功的Station重開機
        List<string> needRebot = successConfigurationList.Select(e => e.sid).ToList();

        // 重開機
        var rebootResultList = await _stationService.Reboot(needRebot);

        // 將更新Fusion失敗的ST3對應的HMN裝置移除
        var successDeviceList = deviceInputList.Where(e => fusionStationResultList.Any(a => $"{a.sid}{Constants.MMWAVE_PID_SUFFIX}" == e.Pid && (a.errors == null || a.errors.Count == 0))).ToList();

        // 更新HMN裝置
        fusionErrorList.AddRange(await _configurationService.UpdateDevice(_user.AppCode, successDeviceList));

        // 判斷是否有重開機錯誤
        fusionErrorList.AddRange(_stationService.GetFusionError(rebootResultList.Select(e => new CommonAPIResult { code = e.code, sid = e.sid, errors = e.errors, id = e.id }).ToList()));

        // 判斷是否有錯誤
        bool result = fusionErrorList.Count == 0;

        ReturnModel returnModel = new()
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = result ? StatusCodes.Status200OK : StatusCodes.Status400BadRequest,
            data = fusionErrorList,
            result = result,
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpPatch("stations")]
    [RequestParamListDuplicate("SID")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateStation([FromBody] List<InUpdateStation> paramList)
    {
        string appCode = _user.AppCode;
        string? requestUUID = _requestContextService.GetRequestUID();

        string[] sids = paramList.Select(e => e.SID.ToUpper()).ToArray();

        // 將InUpdateStation轉換成Fusion所要求的格式
        List<IDictionary<string, object>> fusionStationInputList =
            paramList.Select(e =>
            {
                var dict = new Dictionary<string, object>
                {
                    { "sid", e.SID.ToUpper() },
                };

                if (e.StationName != null)
                {
                    dict.Add("name", e.StationName);
                }

                if (e.SpaceType != null)
                {
                    dict.Add("attribute", e.SpaceType);
                }

                if (e.PlaneCode != null)
                {
                    dict.Add("planeCode", e.PlaneCode);
                    dict.Add("positionX", e.DiffPositionX??0);
                    dict.Add("positionY", e.DiffPositionY??0);
                }

                if (e.Enable != null)
                {
                    dict.Add("enable", e.Enable == "Y");
                }

                return dict;
            }).ToList<IDictionary<string, object>>();

        // 取出此次要更新的HMN Station
        List<Station> hmnStationInputList = 
            await _dataAccessService.Fetch<Station>(e => e.AppCode == appCode && sids.Contains(e.SID)).AsTracking().ToListAsync();

        // 更新Fusion Station
        List<PatchAPIResult> fusionStationResultList = await _stationService.PatchStation(fusionStationInputList);

        // 判斷每一筆的PatchAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> fusionErrorList = _stationService.GetFusionError(fusionStationResultList.Select(e => new CommonAPIResult { code = e.code, sid = e.sid, errors = e.errors, id = e.id }).ToList());

        // 取出Fusion更新成功的Station
        var successStationList = hmnStationInputList.Where(e => fusionStationResultList.Any(a => a.sid == e.SID && (a.errors == null || a.errors.Count == 0))).ToList();

        // 將更新成功的Station，寫入資料庫
        foreach (var station in successStationList)
        {
            var param = paramList.FirstOrDefault(e=>e.SID.ToUpper() == station.SID);
            // defensive code
            if (param == null)
            {
                continue;
            }

            if (param.AreaCode != null)
            {
                station.AreaCode = param.AreaCode;
            }

            if (param.LocCode != null)
            {
                station.RegionCode = param.LocCode;
            }
            
            if (param.PlaneCode != null)
            {
                station.PlaneCode = param.PlaneCode;
            }

            if (param.StationName != null)
            {
                station.StationName = param.StationName;
            }

            if (param.SpaceType != null)
            {
                station.SpaceType = param.SpaceType;
            }

            if (param.Enable != null)
            {
                station.Enable = param.Enable == "Y";
            }

            if (param.AxisX != null)
            {
                station.AxisX = param.AxisX;
            }

            if (param.AxisY != null)
            {
                station.AxisY = param.AxisY;
            }

            if (param.DiffPositionX != null)
            {
                station.DiffPositionX = param.DiffPositionX;
            }

            if (param.DiffPositionY != null)
            {
                station.DiffPositionY = param.DiffPositionY;
            }

            station.ModifyDate = DateTime.Now;
            station.ModifyUserAccount = _user.Account;

            await _dataAccessService.UpdateAsync<Station>(station);
        }

        // 判斷是否有錯誤
        bool result = fusionErrorList.Count == 0;

        ReturnModel returnModel = new()
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = result ? StatusCodes.Status200OK : StatusCodes.Status400BadRequest,
            data = fusionErrorList,
            result = result,
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpPatch("st3/configurations")]
    [RequestParamListDuplicate("SID")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateSt3Configuration([FromBody] List<InUpdateSt3Configuration> paramList)
    {
        string appCode = _user.AppCode;
        string? requestUUID = _requestContextService.GetRequestUID();

        List<ReturnError> fusionErrorList = [];
        string[] sids = paramList.Select(e => e.SID.ToUpper()).ToArray();

        // 將InUpdateSt3 Configurations轉換成Fusion所要求的格式
        List<PatchConfigurationInput> fusionConfigurationInputList =
            paramList.Select(e => new PatchConfigurationInput
            {
                sid = e.SID.ToUpper(),
                configurations = e.Configurations.Select(c=>new ResourceConfiguration 
                { 
                    resourceId = c.resourceId,
                    value = c.value
                }).ToList()
            }).ToList();

        // 如果有要更新Configuration
        if (fusionConfigurationInputList != null && fusionConfigurationInputList.Count>0)
        {
            // 更新Fusion Configuration
            List<PatchAPIResult> fusionConfigurationResultList = await _stationService.PatchConfiguration(fusionConfigurationInputList);

            // 判斷每一筆的PatchAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
            fusionErrorList.AddRange(_stationService.GetFusionError(fusionConfigurationResultList.Select(e => new CommonAPIResult { code = e.code, sid = e.sid, errors = e.errors, id = e.id }).ToList()));
        }

        // // 將更新Configuration成功的Station重開機
        // List<string> needRebot = successConfigurationList.Select(e => e.sid).ToList();

        // // 重開機
        // var rebootResultList = await _stationService.Reboot(needRebot);

        // // 判斷是否有重開機錯誤
        // fusionErrorList.AddRange(_stationService.GetFusionError(rebootResultList.Select(e => new CommonAPIResult { code = e.code, sid = e.sid, errors = e.errors, id = e.id }).ToList()));

        // 判斷是否有錯誤
        bool result = fusionErrorList.Count == 0;

        ReturnModel returnModel = new()
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = result ? StatusCodes.Status200OK : StatusCodes.Status400BadRequest,
            data = fusionErrorList,
            result = result,
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpPatch("stations/configurations")]
    [RequestParamListDuplicate("SID")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateStationConfiguration([FromBody] List<InUpdateStationConfiguration> paramList)
    {
        string appCode = _user.AppCode;
        string? requestUUID = _requestContextService.GetRequestUID();

        List<ReturnError> fusionErrorList = [];
        string[] sids = paramList.Select(e => e.SID.ToUpper()).ToArray();

        // 將InUpdateStationConfiguration轉換成Fusion所要求的格式
        List<PatchConfigurationInput> fusionConfigurationInputList =
            paramList.Select(e => new PatchConfigurationInput
            {
                sid = e.SID.ToUpper(),
                configurations = e.Configurations.Select(c=>new ResourceConfiguration 
                { 
                    resourceId = c.resourceId,
                    value = c.value
                }).ToList()
            }).ToList();

        // 如果有要更新Configuration
        if (fusionConfigurationInputList != null && fusionConfigurationInputList.Count>0)
        {
            // 更新Fusion Configuration
            List<PatchAPIResult> fusionConfigurationResultList = await _stationService.PatchConfiguration(fusionConfigurationInputList);

            // 判斷每一筆的PatchAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
            fusionErrorList.AddRange(_stationService.GetFusionError(fusionConfigurationResultList.Select(e => new CommonAPIResult { code = e.code, sid = e.sid, errors = e.errors, id = e.id }).ToList()));
        }

        // // 將更新Configuration成功的Station重開機
        // List<string> needRebot = successConfigurationList.Select(e => e.sid).ToList();

        // // 重開機
        // var rebootResultList = await _stationService.Reboot(needRebot);

        // // 判斷是否有重開機錯誤
        // fusionErrorList.AddRange(_stationService.GetFusionError(rebootResultList.Select(e => new CommonAPIResult { code = e.code, sid = e.sid, errors = e.errors, id = e.id }).ToList()));

        // 判斷是否有錯誤
        bool result = fusionErrorList.Count == 0;

        ReturnModel returnModel = new()
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = result ? StatusCodes.Status200OK : StatusCodes.Status400BadRequest,
            data = fusionErrorList,
            result = result,
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpDelete("st3")]
    [RequestParamListDuplicate("SID")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeleteSt3([FromBody] List<InDeleteSt3> paramList)
    {
        string appCode = _user.AppCode;
        string? requestUUID = _requestContextService.GetRequestUID();

        List<string> deleteFusionStationInputs = paramList.Select(e=>e.SID.ToUpper()).ToList();

        // 刪除Fusion Station
        List<DeleteAPIResult> deleteAPIResult = await _stationService.DeleteStation(string.Join(",", deleteFusionStationInputs));

        // 取出Fusion刪除成功的Station
        var successStationList = paramList.Where(e => deleteAPIResult.Any(a => a.sid == e.SID && (a.errors == null || a.errors.Count == 0))).ToList();

        // 將刪除成功的Station，從資料庫中刪除
        // DB LINQ Where 裡使用 Any，會有問題，所以改用(上面paramList 不是DB LINQ，所以不會有問題)
        foreach (var station in successStationList) 
        {
            // 刪除資料庫中的Station
            await _dataAccessService.DeleteAsync<Station>(e => e.AppCode == appCode && e.SID == station.SID.ToUpper());
        }

        // 判斷每一筆的PostAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> deleteStationErrors = _stationService.GetFusionError(deleteAPIResult.Select(e => new CommonAPIResult { code = e.code, sid = e.sid, errors = e.errors, id = e.id }).ToList());

        // 取出刪除成功的ST3對應的HMN裝置
        // var willDeleteDevice = paramList.Where(e => successStationList.Any(s => s.AreaCode == e.AreaCode && s.SID == e.SID)).Select(e => new InDeleteDevice { AreaCode = e.AreaCode, Pid = $"{e.SID}{Constants.MMWAVE_PID_SUFFIX}" }).ToList();

        // 刪除HMN裝置
        // deleteStationErrors.AddRange(await _configurationService.DeleteDevice(appCode, willDeleteDevice));

        // 判斷是否有錯誤
        bool addResult = deleteStationErrors.Count == 0;

        ReturnModel returnModel = new()
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = addResult ? StatusCodes.Status200OK : StatusCodes.Status400BadRequest,
            data = deleteStationErrors,
            result = addResult,
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpDelete("stations")]
    [RequestParamListDuplicate("SID")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeleteStation([FromBody] List<InDeleteStation> paramList)
    {
        string appCode = _user.AppCode;
        string? requestUUID = _requestContextService.GetRequestUID();

        List<string> deleteFusionStationInputs = paramList.Select(e=>e.SID.ToUpper()).ToList();

        // 刪除Fusion Station
        List<DeleteAPIResult> deleteAPIResult = await _stationService.DeleteStation(string.Join(",", deleteFusionStationInputs));

        // 取出Fusion刪除成功的Station
        var successStationList = paramList.Where(e => deleteAPIResult.Any(a => a.sid == e.SID && (a.errors == null || a.errors.Count == 0))).ToList();

        // 將刪除成功的Station，從資料庫中刪除
        // DB LINQ Where 裡使用 Any，會有問題，所以改用(上面paramList 不是DB LINQ，所以不會有問題)
        foreach (var station in successStationList) 
        {
            // 刪除資料庫中的Station
            int result = await _dataAccessService.DeleteAsync<Station>(e => e.AppCode == appCode && e.SID == station.SID.ToUpper());

            if (result > 0)
            {
                // 刪除次平面關聯
                await _dataAccessService.DeleteAsync<SectorStation>(e => e.AppCode == appCode && e.Stationsid == station.SID.ToUpper());

                // 刪除圍籬關聯
                await _dataAccessService.DeleteAsync<FenceStation>(e => e.AppCode == appCode && e.Stationsid == station.SID.ToUpper());
            }
        }

        // 判斷每一筆的DeleteAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> deleteStationErrors = _stationService.GetFusionError(deleteAPIResult.Select(e => new CommonAPIResult { code = e.code, sid = e.sid, errors = e.errors, id = e.id }).ToList());

        // 判斷是否有錯誤
        bool deleteResult = deleteStationErrors.Count == 0;

        ReturnModel returnModel = new()
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = deleteResult ? StatusCodes.Status200OK : StatusCodes.Status400BadRequest,
            data = deleteStationErrors,
            result = deleteResult,
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpPost("st3")]
    [RequestParamListDuplicate("SID")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateSt3([FromBody] List<InCreateAt3> paramList)
    {
        string appCode = _user.AppCode;
        string? requestUUID = _requestContextService.GetRequestUID();

        // 將InCreateAt3轉換成Fusion所要求的格式
        List<AddStationInput> fusionStationInputList =
            paramList.Select(e => new AddStationInput
            {
                sid = e.SID.ToUpper(),
                name = e.StationName,
                type = "fih-guard2"
            }).ToList();

        // 將InCreateAt3轉換成要新增的HMN基站
        List<Station> hmnStationInputList =
            paramList.Select(e => new Station
            {
                AppCode = appCode,
                AreaCode = e.AreaCode,
                SID = e.SID.ToUpper(),
                Enable = true,
                StationName = e.StationName,
                StationType = "fih-guard2",
                CreateDate = DateTime.Now,
                CreateUserAccount = _user.Account,
                ModifyDate = DateTime.Now
            }).ToList();

        // 將InCreateAt3轉換成要新增的HMN裝置
        List<InCreateDevice> deviceInputList =
            paramList.Select(e => new InCreateDevice
            {
                AreaCode = e.AreaCode,
                DeviceType = "mmwave",
                Enable = "Y",
                Pid = $"{e.SID.ToUpper()}{Constants.MMWAVE_PID_SUFFIX}",
                Name = e.DeviceName,
                StationSid = e.SID.ToUpper(),
                UsageDepartCode = e.UsageDepartCode,
                ManageDepartCode = e.ManageDepartCode
            }).ToList();

        // 新增Fusion Station
        List<PostAPIResult> fusionStationResultList = await _stationService.AddStation(fusionStationInputList);

        // 判斷每一筆的PostAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> fusionErrorList = _stationService.GetFusionError(fusionStationResultList.Select(e => new CommonAPIResult { code = e.code, pid = e.pid, errors = e.errors, id = e.id }).ToList());

        // 取出Fusion新增成功的Station
        var successStationList = hmnStationInputList.Where(e => fusionStationResultList.Any(a => a.sid == e.SID && (a.errors == null || a.errors.Count == 0))).ToList();

        // 將新增Fusion成功的Station，寫入HMN資料庫
        int createStationCount = await _dataAccessService.CreateRangeAsync(successStationList);

        // 將新增失敗的ST3對應的HMN裝置移除
        deviceInputList = deviceInputList.Where(e=>!fusionErrorList.Any(a=>$"{a.code}{Constants.MMWAVE_PID_SUFFIX}" == e.Pid)).ToList();
        
        // 新增HMN裝置
        fusionErrorList.AddRange(await _configurationService.CreateDevice(appCode, deviceInputList));

        // 判斷是否有錯誤
        bool addResult = fusionErrorList.Count == 0;

        ReturnModel returnModel = new()
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = addResult ? StatusCodes.Status201Created : StatusCodes.Status400BadRequest,
            data = fusionErrorList,
            result = addResult,
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpPost("stations/validate")]
    [RequestParamListDuplicate("SID")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> ValidateStation([FromBody] List<InCreateStation> paramList)
    {
        // 資料驗證通過，開始驗證可否成功新增到 console 的 Station
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        string appCode = _user.AppCode;

        // 將InCreateStation轉換成Fusion所要求的格式
        List<AddStationInput> fusionStationInputList =
            paramList.Select(e => new AddStationInput
            {
                sid = e.SID.ToUpper(),
                name = e.StationName,
                type = e.StationType,
                attribute = e.SpaceType,
                planeCode = string.IsNullOrEmpty(e.PlaneCode) ? null : e.PlaneCode,
                positionX = e.DiffPositionX.HasValue ? (float?)e.DiffPositionX.Value : null,
                positionY = e.DiffPositionY.HasValue ? (float?)e.DiffPositionY.Value : null
            }).ToList();

        // 新增Fusion Station
        List<PostAPIResult> fusionStationResultList = await _stationService.AddStationValidation(fusionStationInputList);

        // 判斷每一筆的PostAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> fusionErrorList = _stationService.GetFusionError(fusionStationResultList.Select(e => new CommonAPIResult { code = e.code, pid = e.pid, errors = e.errors, id = e.id }).ToList());

        // 判斷是否有錯誤
        bool addResult = fusionErrorList.Count == 0;

        ReturnModel returnModel = new()
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = addResult ? StatusCodes.Status200OK : StatusCodes.Status400BadRequest,
            data = fusionErrorList,
            result = addResult,
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpPost("stations")]
    [RequestParamListDuplicate("SID")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateStation([FromBody] List<InCreateStation> paramList)
    {
        string appCode = _user.AppCode;
        string? requestUUID = _requestContextService.GetRequestUID();

        // 將InCreateStation轉換成Fusion所要求的格式
        List<AddStationInput> fusionStationInputList =
            paramList.Select(e => new AddStationInput
            {
                sid = e.SID.ToUpper(),
                name = e.StationName,
                type = e.StationType,
                attribute = e.SpaceType,
                planeCode = string.IsNullOrEmpty(e.PlaneCode) ? null : e.PlaneCode,
                positionX = e.DiffPositionX.HasValue ? (float?)e.DiffPositionX.Value : null,
                positionY = e.DiffPositionY.HasValue ? (float?)e.DiffPositionY.Value : null
            }).ToList();

        // 將InCreateStation轉換成要新增的HMN基站
        List<Station> hmnStationInputList =
            paramList.Select(e => new Station
            {
                AppCode = appCode,
                AreaCode = e.AreaCode,
                RegionCode = string.IsNullOrEmpty(e.LocCode) ? null : e.LocCode,
                PlaneCode = string.IsNullOrEmpty(e.PlaneCode) ? null : e.PlaneCode,
                SID = e.SID.ToUpper(),
                Enable = true,
                StationName = e.StationName,
                StationType = e.StationType,
                SpaceType = e.SpaceType,
                AxisX = e.AxisX,
                AxisY = e.AxisY,
                DiffPositionX = e.DiffPositionX,
                DiffPositionY = e.DiffPositionY,
                CreateDate = DateTime.Now,
                CreateUserAccount = _user.Account,
                ModifyDate = DateTime.Now
            }).ToList();

        // 新增Fusion Station
        List<PostAPIResult> fusionStationResultList = await _stationService.AddStation(fusionStationInputList);

        // 判斷每一筆的PostAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> fusionErrorList = _stationService.GetFusionError(fusionStationResultList.Select(e => new CommonAPIResult { code = e.code, pid = e.pid, errors = e.errors, id = e.id }).ToList());

        // 取出Fusion新增成功的Station
        var successStationList = hmnStationInputList.Where(e => fusionStationResultList.Any(a => a.sid == e.SID && (a.errors == null || a.errors.Count == 0))).ToList();

        // 將新增Fusion成功的Station，寫入HMN資料庫
        int createStationCount = await _dataAccessService.CreateRangeAsync(successStationList);

        // 判斷是否有錯誤
        bool addResult = fusionErrorList.Count == 0;

        ReturnModel returnModel = new()
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = addResult ? StatusCodes.Status201Created : StatusCodes.Status400BadRequest,
            data = fusionErrorList,
            result = addResult,
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }
    
    [HttpGet("newFusionGuard2")]
    public async Task<IActionResult> RetrieveNewGuard2FromFusion()
    {
        string appCode = _user.AppCode;
        string? requestUUID = _requestContextService.GetRequestUID();

        // 取得Fusion所有Station
        List<Stations> fusionStationList = await _stationService.GetStationList("type eq fih-guard2");

        // 取得HMN所有Station
        List<string> hmnStationList = await _dataAccessService.Fetch<Station>(e => e.AppCode == appCode && e.StationType == "fih-guard2").Select(e => e.SID).ToListAsync();

        // 取得Fusion新增的Station
        var newStationLit = fusionStationList.Where(f => !hmnStationList.Any(sid => f.sid == sid)).Select(e => new 
        { 
            e.id,
            e.enable,
            e.sid,
            e.name,
            e.type,
            e.positionX,
            e.positionY
        });

        var returnModel = new ReturnModel
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = newStationLit
        };

        return Ok(returnModel);
    }

    [HttpGet("newFusionStation")]
    public async Task<IActionResult> RetrieveNewStationFromFusion()
    {
        string appCode = _user.AppCode;
        string? requestUUID = _requestContextService.GetRequestUID();

        // 取得Fusion所有Station
        List<Stations> fusionStationList = await _stationService.GetStationList("type in fih-smft,fih-station");

        // 取得HMN所有Station
        List<string> hmnStationList = await _dataAccessService.Fetch<Station>(e => e.AppCode == appCode && (e.StationType == "fih-station" || e.StationType == "fih-smft")).Select(e => e.SID).ToListAsync();

        // 取得Fusion新增的Station
        var newStationLit = fusionStationList.Where(f => !hmnStationList.Any(sid => f.sid == sid)).Select(e => new 
        { 
            e.id,
            e.enable,
            e.sid,
            e.name,
            e.type,
            e.positionX,
            e.positionY,
            SpaceType = e.attribute,
            PlaneCode = e.plane?.code ?? ""
        });

        var returnModel = new ReturnModel
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = newStationLit
        };

        return Ok(returnModel);
    }

    /// <summary>
    /// 同步Fusion新增的雷達基站
    /// 
    /// 需求說明 https://tpe-jira2.fihtdc.com/secure/thumbnail/2190572/_thumb_2190572.png
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    [HttpPost("newFusionGuard2")]
    [RequestParamListDuplicate("SID")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateNewGuard2FromFusion([FromBody] List<InCreateNewGuard2FromFusion> paramList)
    {
        string appCode = _user.AppCode;
        string? requestUUID = _requestContextService.GetRequestUID();

        // 將InCreateAt3轉換成要新增的HMN基站
        List<Station> hmnStationInputList =
            paramList.Select(e => new Station
            {
                AppCode = appCode,
                AreaCode = e.AreaCode,
                SID = e.SID.ToUpper(),
                Enable = true,
                StationName = e.StationName,
                StationType = "fih-guard2",
                PlaneCode = e.PlaneCode,
                AxisX = e.AxisX,
                AxisY = e.AxisY,
                DiffPositionX = e.DiffPositionX,
                DiffPositionY = e.DiffPositionY,
                CreateDate = DateTime.Now,
                CreateUserAccount = _user.Account,
                ModifyDate = DateTime.Now
            }).ToList();

        // 將InCreateAt3轉換成要新增的HMN裝置
        List<InCreateDevice> deviceInputList =
            paramList.Select(e => new InCreateDevice
            {
                AreaCode = e.AreaCode,
                DeviceType = "mmwave",
                Enable = "Y",
                Pid = $"{e.SID.ToUpper()}{Constants.MMWAVE_PID_SUFFIX}",
                Name = e.DeviceName,
                StationSid = e.SID.ToUpper(),
                ManageDepartCode = e.ManageDepartCode,
                UsageDepartCode = e.UsageDepartCode
            }).ToList();

        // 將InUpdateSt3轉換成Fusion所要求的格式
        List<IDictionary<string, object>> fusionStationInputList =
            paramList.Select(e => 
            {
                var dict = new Dictionary<string, object>
                {
                    { "sid", e.SID.ToUpper() },
                    { "name", e.StationName },
                    { "enable", e.Enable == "Y" }
                };

                if (e.PlaneCode != null)
                {
                    dict.Add("planeCode", e.PlaneCode);
                    dict.Add("positionX", e.DiffPositionX??0);
                    dict.Add("positionY", e.DiffPositionY??0);
                }

                return dict;
            }).ToList<IDictionary<string, object>>();

        // 更新Fusion Station
        List<PatchAPIResult> fusionStationResultList = await _stationService.PatchStation(fusionStationInputList);

        // 判斷每一筆的PatchAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> fusionErrorList = _stationService.GetFusionError(fusionStationResultList.Select(e => new CommonAPIResult { code = e.code, sid = e.sid, errors = e.errors, id = e.id }).ToList());

        // 取出Fusion更新成功的Station
        var successStationList = hmnStationInputList.Where(e => fusionStationResultList.Any(a => a.sid == e.SID && (a.errors == null || a.errors.Count == 0))).ToList();

        // 將Fusion更新成功的Station，寫入HMN資料庫
        int createStationCount = await _dataAccessService.CreateRangeAsync(successStationList);

        // 將更新Fusion失敗的ST3對應的HMN裝置移除
        var successDeviceList = deviceInputList.Where(e => fusionStationResultList.Any(a => $"{a.sid}{Constants.MMWAVE_PID_SUFFIX}" == e.Pid && (a.errors == null || a.errors.Count == 0))).ToList();

        // 新增HMN裝置
        List<ReturnError> fusionDeviceErrorList = await _configurationService.CreateDevice(appCode, deviceInputList, false);

        // 判斷新增HMN裝置如果有錯誤，且錯誤為"err.devicePidExists.fusion"，代表Fusion已有此裝置，則只在HMN新增裝置
        foreach(var error in fusionDeviceErrorList)
        {
            if(error != null && error.errors!=null && error.errors[0].error == "err.devicePidExists.fusion")
            {
                var param = paramList.First(e=>e.Pid == error.code);
                var device = new Repository.Models.Entities.Device 
                { 
                    AppCode = appCode,
                    AreaCode = param.AreaCode,
                    DeviceType = "mmwave",
                    Enable = true,
                    Active = true,
                    Pid = param.Pid,
                    Name = param.DeviceName,
                    StationSid = param.SID.ToUpper(),
                    ManageDepartCode = param.ManageDepartCode,
                    UsageDepartCode = param.UsageDepartCode,
                    CreateDate = DateTime.Now,
                    CreateUserAccount = _user.Account
                };

                await _dataAccessService.CreateAsync(device);
            }
        }

        // 判斷新增HMN裝置如果有錯誤，且錯誤不為"err.devicePidExists.fusion"，則加入到fusionErrorList
        fusionErrorList.AddRange(fusionDeviceErrorList.Where(e=>e.errors!=null && e.errors[0].error != "err.devicePidExists.fusion"));

        // 判斷新增HMN裝置是否有錯誤及傳入的參數數量是否等於新增的Station數量
        bool result = fusionErrorList.Count == 0;

        ReturnModel returnModel = new()
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = result ? StatusCodes.Status201Created : StatusCodes.Status400BadRequest,
            data = fusionErrorList,
            result = result
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    /// <summary>
    /// 同步Fusion新增的聯網基站
    /// 
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    [HttpPost("newFusionStation")]
    [RequestParamListDuplicate("SID")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateNewStationFromFusion([FromBody] List<InCreateNewStationFromFusion> paramList)
    {
        string appCode = _user.AppCode;
        string? requestUUID = _requestContextService.GetRequestUID();

        // 將InCreateNewStationFromFusion轉換成要新增的HMN基站
        List<Station> hmnStationInputList =
            paramList.Select(e => new Station
            {
                AppCode = appCode,
                AreaCode = e.AreaCode,
                RegionCode = e.LocCode,
                PlaneCode = e.PlaneCode,
                SID = e.SID.ToUpper(),
                Enable = true,
                StationName = e.StationName,
                StationType = e.StationType,
                SpaceType = e.SpaceType,
                AxisX = e.AxisX,
                AxisY = e.AxisY,
                DiffPositionX = e.DiffPositionX,
                DiffPositionY = e.DiffPositionY,
                CreateDate = DateTime.Now,
                CreateUserAccount = _user.Account,
                ModifyDate = DateTime.Now
            }).ToList();

        // 將InUpdateSt3轉換成Fusion所要求的格式
        List<IDictionary<string, object>> fusionStationInputList =
            paramList.Select(e => 
            {
                var dict = new Dictionary<string, object>
                {
                    { "sid", e.SID.ToUpper() },
                    { "name", e.StationName },
                    { "enable", e.Enable == "Y" }
                };

                if (e.PlaneCode != null)
                {
                    dict.Add("planeCode", e.PlaneCode);
                    dict.Add("positionX", e.DiffPositionX??0);
                    dict.Add("positionY", e.DiffPositionY??0);
                }

                return dict;
            }).ToList<IDictionary<string, object>>();

        // 更新Fusion Station
        List<PatchAPIResult> fusionStationResultList = await _stationService.PatchStation(fusionStationInputList);

        // 判斷每一筆的PatchAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> fusionErrorList = _stationService.GetFusionError(fusionStationResultList.Select(e => new CommonAPIResult { code = e.code, sid = e.sid, errors = e.errors, id = e.id }).ToList());

        // 取出Fusion更新成功的Station
        var successStationList = hmnStationInputList.Where(e => fusionStationResultList.Any(a => a.sid == e.SID && (a.errors == null || a.errors.Count == 0))).ToList();

        // 將Fusion更新成功的Station，寫入HMN資料庫
        int createStationCount = await _dataAccessService.CreateRangeAsync(successStationList);

        bool result = fusionErrorList.Count == 0;

        ReturnModel returnModel = new()
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = result ? StatusCodes.Status201Created : StatusCodes.Status400BadRequest,
            data = fusionErrorList,
            result = result
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }
    
    [HttpGet("st3")]
    public async Task<IActionResult> RetrieveSt3(InRetrieveSt3 param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        // FSN-6365[HMN2]聯網基站功能不能出現AT3，Mail Subject: 聯網基站功能不能出現 AT3 2024/10/9 (週三) 上午 09:17
        param.StationType = "fih-guard2";

        var stationList = await _fieldService.GetStationList(new InGetStationList
        {
            page = param.page,
            size = param.size,
            sort = param.sort,
            AreaCode = param.AreaCode,
            SID = param.SID?.ToUpper(),
            StationName = param.StationName,
            Enable = param.Enable,
            IsConnected = param.IsConnected,
            PlaneCode = param.PlaneCode,
            LocCode = param.LocCode,
            BuildingCode = param.BuildingCode,
            StationType = param.StationType
        });

        var result = stationList;

        returnModel = new ReturnModel
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordList = result
            }
        };

        return Ok(returnModel);
    }

    [HttpGet("stations")]
    public async Task<IActionResult> RetrieveStation(InRetrieveStation param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // 1.3 此功能針對聯網基站(StationType = fih-smft 表示一代基站；StationType=fih-station 表示二代基站)
        param.StationType = "fih-smft,fih-station";

        var stationList = await _fieldService.GetStationList(new InGetStationList
        {
            page = param.page,
            size = param.size,
            sort = param.sort,
            AreaCode = param.AreaCode,
            SID = param.SID?.ToUpper(),
            StationName = param.StationName,
            Enable = param.Enable,
            IsConnected = param.IsConnected,
            PlaneCode = param.PlaneCode,
            LocCode = param.LocCode,
            BuildingCode = param.BuildingCode,
            StationType = param.StationType,
            SpaceType = param.SpaceType
        });

        var result = stationList;

        // 下方使用CountAsync()會有問題，所以先ToList()再Count()，待解決
        var queryList = result.ToList();

        var recordTotal = queryList.Count();

        var recordList = size == 0 ? result.ToList() : queryList.Skip(skip).Take(size).ToList();

        returnModel = new ReturnModel
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal,
                recordList
            }
        };

        return Ok(returnModel);
    }

    [HttpGet("stationDatas")]
    [AllowAnonymous]
    public async Task<IActionResult> RetrieveStationData(InRetrieveStation param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // FSN-6365[HMN2]聯網基站功能不能出現AT3，Mail Subject: 聯網基站功能不能出現 AT3 2024/10/9 (週三) 上午 09:17
        param.StationType = "fih-station";

        // Anonymous User 使用配置文件（appsettings.json）中的 AppCode
        string appCode = configuration["AppInfo:AppCode"] ?? throw new ConfigurationErrorsException("appCodeNotFound");
        _user.AppCode = appCode;

        var stationList = await _fieldService.GetStationList(new InGetStationList
        {
            page = param.page,
            size = param.size,
            AreaCode = param.AreaCode,
            SID = param.SID?.ToUpper(),
            StationName = param.StationName,
            Enable = param.Enable,
            IsConnected = param.IsConnected,
            PlaneCode = param.PlaneCode,
            LocCode = param.LocCode,
            BuildingCode = param.BuildingCode,
            StationType = param.StationType
        });

        var result = stationList.Select(x => new {
            x.AreaCode,
            x.BuildingCode,
            x.BuildingName,
            x.DeviceName,
            x.DiffPositionX,
            x.DiffPositionY,
            x.Enable,
            x.Ip,
            x.IsConnected,
            x.LocCode,
            x.LocName,
            x.ManageDepartCode,
            x.ManageDepartName,
            x.ObjectCode,
            x.ObjectName,
            x.Pid,
            x.PlaneCode,
            x.PlaneName,
            x.PlanePositionX,
            x.PlanePositionY,
            x.PlaneMapPath,
            x.PlaneMapWidth,
            x.PlaneMapHeight,
            x.Registered,
            x.SceneMode,
            x.SID,
            x.StationName,
            x.SystemVersion,
            x.UsageDepartCode,
            x.UsageDepartName
        }).ToList();

        // 下方使用CountAsync()會有問題，所以先ToList()再Count()，待解決
        var queryList = result.ToList();

        var recordTotal = queryList.Count();

        var recordList = size == 0 ? result.ToList() : queryList.Skip(skip).Take(size).ToList();

        returnModel = new ReturnModel
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal,
                recordList
            }
        };

        return Ok(returnModel);
    }

    [HttpPatch("plane")]
    [RequestParamListDuplicate("SID,PlaneCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> BindingPlane([FromBody] List<InBindingPlane> paramList, [CallerMemberName] string callerName = null)
    {
        string? requestUUID = _requestContextService.GetRequestUID();

        ReturnModel returnModel;

        var sids = paramList.Select(e=>e.SID).ToArray();

        var stations = _dataAccessService.Fetch<Station>(e => e.AppCode == _user.AppCode && sids.Contains(e.SID));

        // 將InBindingPlane轉換成要修改的HMN基站
        List<Station> hmnStationInputList = await _dataAccessService.Fetch<Station>(e => e.AppCode == _user.AppCode && sids.Contains(e.SID)).AsTracking().ToListAsync();

        // 將InBindingPlane轉換成Fusion所要求的格式
        List<IDictionary<string, object>> fusionStationInputList =
            paramList.Select(e =>
            {
                var dict = new Dictionary<string, object>
                {
                    { "sid", e.SID.ToUpper() },
                    { "planeCode", e.PlaneCode },
                    { "positionX", e.DiffPositionX ?? 0 },
                    { "positionY", e.DiffPositionY ?? 0 }
                };

                return dict;
            }).ToList<IDictionary<string, object>>();

        // 更新Fusion Station
        List<PatchAPIResult> fusionStationResultList = await _stationService.PatchStation(fusionStationInputList);

        // 判斷每一筆的PatchAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> fusionErrorList = _stationService.GetFusionError(fusionStationResultList.Select(e => new CommonAPIResult { code = e.code, sid = e.sid, errors = e.errors, id = e.id }).ToList());

        // 取出Fusion更新成功的Station
        var successStationList = hmnStationInputList.Where(e => fusionStationResultList.Any(a => a.sid == e.SID && (a.errors == null || a.errors.Count == 0))).ToList();

        // 將更新成功的Station，寫入資料庫
        foreach (var station in successStationList)
        {
            var newStation = paramList.FirstOrDefault(e => e.SID.ToUpper() == station.SID);

            station.PlaneCode = newStation.PlaneCode;
            station.DiffPositionX = newStation.DiffPositionX;
            station.DiffPositionY = newStation.DiffPositionY;

            station.ModifyDate = DateTime.Now;
            station.ModifyUserAccount = _user.Account;

            await _dataAccessService.UpdateAsync(station, callMethodName: callerName,
                e => e.DiffPositionX,
                e => e.DiffPositionY,
                e => e.PlaneCode,
                e => e.ModifyDate,
                e => e.ModifyUserAccount);
        }

        // 判斷是否有錯誤
        bool result = fusionErrorList.Count == 0;

        returnModel = new()
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = result ? StatusCodes.Status200OK : StatusCodes.Status400BadRequest,
            data = fusionErrorList,
            result = result,
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }
    [HttpDelete("plane")]
    [RequestParamListDuplicate("SID")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UnBindingPlane([FromBody] List<InUnBindingPlane> paramList, [CallerMemberName] string callerName = null)
    {
        string? requestUUID = _requestContextService.GetRequestUID();

        ReturnModel returnModel;

        var sids = paramList.Select(e => e.SID).ToArray();

        var stations = _dataAccessService.Fetch<Station>(e => e.AppCode == _user.AppCode && sids.Contains(e.SID));

        // 將InUnBindingPlane轉換成要修改的HMN基站
        List<Station> hmnStationInputList = await _dataAccessService.Fetch<Station>(e => e.AppCode == _user.AppCode && sids.Contains(e.SID)).AsTracking().ToListAsync();

        // 將InBindingPlane轉換成Fusion所要求的格式
        List<IDictionary<string, object>> fusionStationInputList =
            paramList.Select(e =>
            {
                var dict = new Dictionary<string, object>
                {
                    { "sid", e.SID.ToUpper() },
                    { "planeCode", string.Empty },
                    { "positionX", 0 },
                    { "positionY", 0 }
                };

                return dict;
            }).ToList<IDictionary<string, object>>();

        // 更新Fusion Station
        List<PatchAPIResult> fusionStationResultList = await _stationService.PatchStation(fusionStationInputList);

        // 判斷每一筆的PatchAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> fusionErrorList = _stationService.GetFusionError(fusionStationResultList.Select(e => new CommonAPIResult { code = e.code, sid = e.sid, errors = e.errors, id = e.id }).ToList());

        // 取出Fusion更新成功的Station
        var successStationList = hmnStationInputList.Where(e => fusionStationResultList.Any(a => a.sid == e.SID && (a.errors == null || a.errors.Count == 0))).ToList();

        // 將更新成功的Station，寫入資料庫
        foreach (var station in successStationList)
        {
            var newStation = paramList.FirstOrDefault(e => e.SID.ToUpper() == station.SID);

            station.PlaneCode = string.Empty;
            station.DiffPositionX = 0;
            station.DiffPositionY = 0;

            station.ModifyDate = DateTime.Now;
            station.ModifyUserAccount = _user.Account;

            await _dataAccessService.UpdateAsync(station, callMethodName: callerName,
                e => e.DiffPositionX,
                e => e.DiffPositionY,
                e => e.PlaneCode,
                e => e.ModifyDate,
                e => e.ModifyUserAccount);
        }

        // 判斷是否有錯誤
        bool result = fusionErrorList.Count == 0;

        returnModel = new()
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = result ? StatusCodes.Status200OK : StatusCodes.Status400BadRequest,
            data = fusionErrorList,
            result = result,
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }
    [HttpPatch("enable")]
    [RequestParamListDuplicate("SID")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateEnable([FromBody] List<InUpdateEnable> paramList)
    {
        string appCode = _user.AppCode;
        string? requestUUID = _requestContextService.GetRequestUID();

        string[] sids = paramList.Select(e => e.SID.ToUpper()).ToArray();

        // 將InUpdateSt3轉換成Fusion所要求的格式
        List<IDictionary<string, object>> fusionStationInputList =
            paramList.Select(e => new Dictionary<string, object>
            {
                { "sid", e.SID.ToUpper() },
                { "enable", e.Enable == "Y" }
            }).ToList<IDictionary<string, object>>();

        // 取出此次要更新的HMN Station
        List<Station> hmnStationInputList =
            await _dataAccessService.Fetch<Station>(e => e.AppCode == appCode && sids.Contains(e.SID)).AsTracking().ToListAsync();

        // 將InUpdateSt3轉換成要更新的HMN裝置
        // List<InUpdateDevice> deviceInputList =
        //     paramList.Select(e => new InUpdateDevice
        //     {
        //         AreaCode = e.AreaCode,
        //         Enable = e.Enable
        //     }).ToList();

        // 更新Fusion Station
        List<PatchAPIResult> fusionStationResultList = await _stationService.PatchStation(fusionStationInputList);

        // 判斷每一筆的PatchAPIResult中的result.Count是否等於0，若大於0，則產生ReturnError
        List<ReturnError> fusionErrorList = _stationService.GetFusionError(fusionStationResultList.Select(e => new CommonAPIResult { code = e.code, sid = e.sid, errors = e.errors, id = e.id }).ToList());

        // 取出Fusion更新成功的Station
        var successStationList = hmnStationInputList.Where(e => fusionStationResultList.Any(a => a.sid == e.SID && (a.errors == null || a.errors.Count == 0))).ToList();

        // 將更新成功的Station，寫入資料庫
        foreach (var station in successStationList)
        {
            var newStation = paramList.FirstOrDefault(e => e.SID.ToUpper() == station.SID);

            station.Enable = newStation.Enable == "Y";

            station.ModifyDate = DateTime.Now;
            station.ModifyUserAccount = _user.Account;

            await _dataAccessService.UpdateAsync(station);
        }

        // 判斷是否有錯誤
        bool result = fusionErrorList.Count == 0;

        ReturnModel returnModel = new()
        {
            authorize = (Authorize)_user,
            requestUUID = requestUUID,
            httpStatus = result ? StatusCodes.Status200OK : StatusCodes.Status400BadRequest,
            data = fusionErrorList,
            result = result,
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }
    [HttpGet("configurationsCompatible")]
    public async Task<IActionResult> RetrieveConfigurationsCompatible([FromQuery] InRetrieveConfigurationsCompatible queryParam)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        string search = $"sid in {queryParam.SIDs}";
        string resourceIds = queryParam.ResourceIds;

        var result = await _stationService.GetConfigurationsCompatible(search, resourceIds);

        returnModel = new ReturnModel
        {
            requestUUID = requestUUID,
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordList = result
            }
        };

        return Ok(returnModel);
    }
}