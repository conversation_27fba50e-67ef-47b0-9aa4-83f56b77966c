﻿namespace Web.Models.AppSettings;

public class MQTTParam
{
    public string HostIp { get; set; }
    public int HostPort { get; set; }
    public string UseTLS { get; set; }
    public string? SelfCertificateFile { get; set; }
    public int Timeout { get; set; }
    public string? Enable { get; set; }
}
public class AppInfo
{
    public string RequestIdHeaderName { get; set; }
}
public class MQTTServerParam
{
    public string HostPort { get; set; }
    public int ConnectionBacklog { get; set; }
    public int MaxPendingMessagesPerClient { get; set; }
}