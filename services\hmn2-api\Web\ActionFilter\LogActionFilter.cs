﻿using DocumentFormat.OpenXml.Spreadsheet;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.AspNetCore.Mvc;
using Web.Models.Controller;
using Web.Services.Interfaces;
using Web.Models.Controller.Device;
using System.Text.Json;
using Web.Models.Service;
using DocumentFormat.OpenXml.InkML;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.ObjectPool;
using Microsoft.Extensions.Options;
using Web.Models.Interface;
using NLog.Web.LayoutRenderers;
using Web.Services;
using System.Reflection;
using Microsoft.AspNetCore.Http.Json;
using Web.Models.AppSettings;

namespace Web.ActionFilter;

public class LogActionFilter : IAsyncActionFilter
{
    private readonly ILogService _logService;
    private readonly AppInfo _appInfo;
    private static readonly JsonSerializerOptions _jsonOptions = new() { WriteIndented = false };
    // 使用Dictionary<string, string> 是為了與其它ActionFilter的 ShouldProcessAction 方法內的寫法保持一致
    private readonly Dictionary<string, string> _validationMethods;

    public LogActionFilter(ILogService logService, IOptions<AppInfo> appInfo) 
    {
        _logService = logService;
        _appInfo = appInfo.Value;

        _validationMethods = new() { 
            { "POST:Device/devices", string.Empty }, 
            { "GET:Device/devices", string.Empty }, 
            { "PATCH:Device/devices", string.Empty } 
        };
    }

    public async Task OnActionExecutionAsync(ActionExecutingContext context, ActionExecutionDelegate next)
    {
        string logActionName = context.ActionDescriptor.AttributeRouteInfo.Template;
        string methodName = context.HttpContext.Request.Method;

        var httpContext = context.HttpContext;
        string requestUUID = GetUUID(httpContext.Request);

        if (ShouldApply(context))
        {
            await _logService.LoggingAsync("info", logActionName, requestUUID, "Start");
            await _logService.LoggingAsync("info", logActionName, requestUUID, $"Method:{methodName}");
            // 記錄Request Body
            if (context.ActionArguments.Count > 0)
            {
                var json = JsonSerializer.Serialize(context.ActionArguments, _jsonOptions);
                await _logService.LoggingAsync("info", logActionName, requestUUID, $"Request: {json}");
            }
        }

        // Execute the action
        var resultContext = await next();

        if (ShouldApply(context))
        {
            if (resultContext.Result is ObjectResult objectResult && objectResult.Value is ReturnModel returnModel)
            {
                var json = JsonSerializer.Serialize(returnModel, _jsonOptions);

                await _logService.LoggingAsync(returnModel.result ? "info" : "error", logActionName, requestUUID, $"Response: {json}");
            }

            _logService.Logging("info", logActionName, requestUUID, "End");
        }
    }

    /// <summary>
    /// 從Http Header中取得x-request-id，若不存在則產生一個新的UUID
    /// </summary>
    /// <param name="request"></param>
    /// <returns></returns>
    private string GetUUID(HttpRequest request)
    {
        if (!request.Headers.TryGetValue(_appInfo.RequestIdHeaderName, out var headerValues) || string.IsNullOrEmpty(headerValues))
        {
            return GenerateFastGuid();
        }
        else
        {
            return headerValues.ToString();
        }
    }

    /// <summary>
    /// 快速產生Guid（不含連字號）
    /// 此方法效能會比Guid.NewGuid().ToString()好
    /// </summary>
    /// <returns></returns>
    private static string GenerateFastGuid()
    {
        Span<byte> bytes = stackalloc byte[16];
        Guid.NewGuid().TryWriteBytes(bytes);
        return Convert.ToHexString(bytes).ToLowerInvariant();
    }

    //private bool ShouldApply(string actionName, string methodName)
    //{
    //    // 判斷actionName 是否存在於_validationMethods中
    //    return _validationMethods.ContainsKey($"{methodName}:{actionName}");
    //}
    private bool ShouldApply(ActionExecutingContext context)
    {
        return context.ActionArguments.Values.Any(x => x is ILogRequest);
    }
}
