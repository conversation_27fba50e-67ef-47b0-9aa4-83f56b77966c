﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Diagnostics;
using System.Net.Http;
using System.Security.Cryptography;
using System.Globalization;
using System.Security.Claims;
using NLog;
using Web.Services.Interfaces;

namespace Web.Services;

public class logContent
{
    public string TimeStamp { get; set; }
    public string Level { get; set; }
    public string UserAcc { get; set; }
    public int TraceId { get; set; }
    public string RequestId { get; set; }
    public string TraceType { get; set; }
    public string Message { get; set; }
}

public class DataAccessLogService(IHttpContextAccessor httpContextAccessor, string auditedTables)
{
    private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
    private readonly NLog.ILogger _logger = LogManager.GetLogger("dataaccess");
    private readonly List<string>? _auditedTableList = auditedTables==null || auditedTables=="" ? [] :JsonSerializer.Deserialize<List<string>>(auditedTables);

    public void Logging(string logLevel, string logTraceType, string logRequestId, string logMessage)
    {
        try
        {
            // 解析 logMessage 以找出表名
            const string tablePrefix = "Table: ";
            int tableStartIndex = logMessage.IndexOf(tablePrefix);
            string tableName = null;
            if (tableStartIndex != -1)
            {
                tableStartIndex += tablePrefix.Length;
                int tableEndIndex = logMessage.IndexOf(',', tableStartIndex);
                if (tableEndIndex == -1) tableEndIndex = logMessage.Length;
                tableName = logMessage.Substring(tableStartIndex, tableEndIndex - tableStartIndex);
            }

            // 如果表名在監控清單中，則記錄日誌
            if (tableName != null && _auditedTableList != null && _auditedTableList.Contains(tableName))
            {
                DateTime saveUtcNow = DateTime.Now;
                var userAccount = _httpContextAccessor.HttpContext == null ? "" : _httpContextAccessor.HttpContext.User.Claims.FirstOrDefault(x => x.Type == "UserAccount")?.Value;
                var traceId = _httpContextAccessor.HttpContext == null ? 0 : _httpContextAccessor.HttpContext.Request.Cookies.GetHashCode();

                var content = new logContent
                {
                    // timeStamp
                    TimeStamp = saveUtcNow.ToString("s", CultureInfo.GetCultureInfo("zh-TW")),
                    // level
                    Level = logLevel,
                    //userAcc
                    UserAcc = userAccount?.ToString(),
                    //traceId
                    TraceId = traceId,
                    // traceType
                    TraceType = logTraceType,
                    // requestId
                    RequestId = logRequestId,
                    // message
                    Message = logMessage,
                };

                string logJSONInformation = JsonSerializer.Serialize(content);

                _logger.Info(logJSONInformation);
            }
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
    }
}

public class MqttLogService(IHttpContextAccessor httpContextAccessor)
{
    private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
    private readonly NLog.ILogger _logger = LogManager.GetLogger("MQTT");

    public void Logging(string logLevel,
                        string logTraceType,
                        string logRequestId,
                        string logMessage)
    {
        try
        {
            DateTime saveUtcNow = DateTime.Now;
            var userAccount = _httpContextAccessor.HttpContext == null ? "" : _httpContextAccessor.HttpContext.User.Claims.FirstOrDefault(x => x.Type == "UserAccount")?.Value;
            var traceId = _httpContextAccessor.HttpContext == null ? 0 : _httpContextAccessor.HttpContext.Request.Cookies.GetHashCode();

            var content = new logContent
            {
                // timeStamp
                TimeStamp = saveUtcNow.ToString("s", CultureInfo.GetCultureInfo("zh-TW")),
                // level
                Level = logLevel,
                //userAcc
                UserAcc = userAccount?.ToString(),
                //traceId
                TraceId = traceId,
                // traceType
                TraceType = logTraceType,
                // requestId
                RequestId = logRequestId,
                // message
                Message = logMessage,
            };
            string logJSONInformation = JsonSerializer.Serialize(content);
            _logger.Info(logJSONInformation);
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
    }
}

public class MqttServerLogService(IHttpContextAccessor httpContextAccessor)
{
    private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
    private readonly NLog.ILogger _logger = LogManager.GetLogger("MQTTServer");

    public void Logging(string logLevel,
                        string logTraceType,
                        string logRequestId,
                        string logMessage)
    {
        try
        {
            DateTime saveUtcNow = DateTime.Now;
            var userAccount = _httpContextAccessor.HttpContext == null ? "" : _httpContextAccessor.HttpContext.User.Claims.FirstOrDefault(x => x.Type == "UserAccount")?.Value;
            var traceId = _httpContextAccessor.HttpContext == null ? 0 : _httpContextAccessor.HttpContext.Request.Cookies.GetHashCode();

            var content = new logContent
            {
                // timeStamp
                TimeStamp = saveUtcNow.ToString("s", CultureInfo.GetCultureInfo("zh-TW")),
                // level
                Level = logLevel,
                //userAcc
                UserAcc = userAccount?.ToString(),
                //traceId
                TraceId = traceId,
                // traceType
                TraceType = logTraceType,
                // requestId
                RequestId = logRequestId,
                // message
                Message = logMessage,
            };
            string logJSONInformation = JsonSerializer.Serialize(content);
            _logger.Info(logJSONInformation);
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
    }
}

public class LogService(IHttpContextAccessor httpContextAccessor) : ILogService
{
    private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;
    private readonly NLog.ILogger _logger = LogManager.GetLogger("");


    public void Logging(string logLevel,
                        string logTraceType,
                        string logRequestId,
                        string logMessage)
    {
        try
        {
            DateTime saveUtcNow = DateTime.Now;
            var userAccount = _httpContextAccessor.HttpContext == null ? "" : _httpContextAccessor.HttpContext.User.Claims.FirstOrDefault(x => x.Type == "UserAccount")?.Value;
            var traceId = _httpContextAccessor.HttpContext == null ? 0 : _httpContextAccessor.HttpContext.Request.Cookies.GetHashCode();

            var content = new logContent
            {
                // timeStamp
                TimeStamp = saveUtcNow.ToString("s", CultureInfo.GetCultureInfo("zh-TW")),
                // level
                Level = logLevel.PadLeft(7,' '),
                //userAcc
                UserAcc = userAccount?.ToString(),
                //traceId
                TraceId = traceId,
                // traceType
                TraceType = logTraceType,
                // requestId
                RequestId = logRequestId,
                // message
                Message = logMessage,
            };
            string logJSONInformation = JsonSerializer.Serialize(content);
            _logger.Log(GetLogLevel(logLevel), logJSONInformation.Replace("\\u0022", "\\\""));
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
    }

    public async Task LoggingAsync(string logLevel,
                               string logTraceType,
                               string logRequestId,
                               string logMessage)
    {
        try
        {
            DateTime saveUtcNow = DateTime.Now;
            var userAccount = _httpContextAccessor.HttpContext == null ? "" : _httpContextAccessor.HttpContext.User.Claims.FirstOrDefault(x => x.Type == "UserAccount")?.Value;
            var traceId = _httpContextAccessor.HttpContext == null ? 0 : _httpContextAccessor.HttpContext.Request.Cookies.GetHashCode();

            var content = new logContent
            {
                // timeStamp
                TimeStamp = saveUtcNow.ToString("s", CultureInfo.GetCultureInfo("zh-TW")),
                // level
                Level = logLevel.PadLeft(7, ' '),
                //userAcc
                UserAcc = userAccount?.ToString(),
                //traceId
                TraceId = traceId,
                // traceType
                TraceType = logTraceType,
                // requestId
                RequestId = logRequestId,
                // message
                Message = logMessage,
            };
            string logJSONInformation = JsonSerializer.Serialize(content);
            await Task.Run(() => _logger.Log(GetLogLevel(logLevel), logJSONInformation.Replace("\\u0022", "\\\"")));
        }
        catch (Exception ex)
        {
            throw new Exception(ex.Message);
        }
    }

    private NLog.LogLevel GetLogLevel(string logLevel)
    {
        NLog.LogLevel level;
        try
        {
            level = NLog.LogLevel.FromString(logLevel);
        }
        catch (ArgumentException)
        {
            // 处理非法的日志级别，默认使用 Info
            level = NLog.LogLevel.Info;
        }
        return level;
    }
}

