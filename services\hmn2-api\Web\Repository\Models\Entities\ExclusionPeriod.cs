﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class ExclusionPeriod
{
    public int Id { get; set; }
    public string AppCode { get; set; } = null!;
    public int SystemEventId { get; set; }
    public string Weekly { get; set; }
    public string StartTime { get; set; }
    public string EndTime { get; set; }
    public string CreateUserAccount { get; set; }
    public DateTime CreateDate { get; set; }
}
