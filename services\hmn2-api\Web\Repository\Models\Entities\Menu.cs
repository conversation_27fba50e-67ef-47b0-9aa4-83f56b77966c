﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class Menu
{
    public int MenuId { get; set; }

    public string? StringId { get; set; }

    public string? Title { get; set; }

    public string? Url { get; set; }

    public int? ParentMenuId { get; set; }

    public string? Type { get; set; }

    public string? TypeDesc { get; set; }

    public string? Enabled { get; set; }

    public int? Sort { get; set; }

    public string? icon { get; set; }

    public string? ComponentName { get; set; }

    public bool QueryLog { get; set; }
}
