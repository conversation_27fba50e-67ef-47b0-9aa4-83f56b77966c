﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Reflection;
using System.Text.Json;
using System.Text.RegularExpressions;
using Web.Models.Controller.UserProfile;
using Web.Models.Controller;
using Web.Models.Service;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;
using System.Reflection.Emit;
using Web.Validation;
using Web.Constant;

namespace Web.Controller;

/// <summary>
/// 部門
/// </summary>
[Route("[controller]")]
[Authorize]
public class UserProfileController(IDataAccessService dataAccessService,
                                   ICredentialService credentialService,
                                   IRequestContextService requestContextService,
                                   ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{

    [HttpPatch("profiles")]
    [RequestParamListDuplicate("UserAccount")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateUserProfile([FromBody] List<UpdateUserProfile> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        _dataAccessService.BeginTransaction();

        foreach (var param in paramList)
        {
            var user = await _dataAccessService.Fetch<UserDatum>(e => e.AppCode == _user.AppCode).Where(x => x.UserAccount == param.UserAccount).FirstAsync();
            var userClientParaList = _dataAccessService.Fetch<UserClientPara>(e => e.AppCode == _user.AppCode && e.AreaCode == user.AreaCode && e.UserAccount == param.UserAccount).AsTracking().ToList();

            // 更新使用者資料
            // 透過反射取得UpdateUserProfile的所有屬性
            PropertyInfo[] properties = typeof(UpdateUserProfile).GetProperties();
            foreach (PropertyInfo property in properties)
            {
                // 取得屬性名稱
                string paraCode = property.Name;

                // 如果屬性名稱為AreaCode或UserAccount，則跳過
                if (paraCode == "UserAccount")
                {
                    continue;
                }

                // 依照屬性名稱取得UserClientPara
                var userClientPara = userClientParaList.FirstOrDefault(e => e.ParaCode == paraCode);

                // 依照屬性名稱取得ClientPara
                var clientPara = _dataAccessService.Fetch<ClientPara>().FirstOrDefault(e => e.ParaCode == paraCode)??new ClientPara();

                // 取得屬性值
                if (property.GetValue(param) is string paraValue)
                {
                    await UpdateOrCreateUserClientPara(user.AreaCode, userClientPara, clientPara.ParaType??"Event", paraCode, paraValue, param);
                }
            }
        }

        int updateResult = await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, $"User Data update done, updateResult:{updateResult}");

        _logService.Logging("info", logActionName, requestUUID, "End");

        return Ok(new ReturnModel
        {
            authorize = new Authorize { Account = _user.Account, AppCode = _user.AppCode, AreaCode = _user.AreaCode },
            httpStatus = StatusCodes.Status200OK,
            result = true
        });
    }

    [HttpGet("profiles")]
    public async Task<IActionResult> RetrieveUserProfile([FromQuery] RetrieveUserProfile param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        string appCode = _user.AppCode;

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // 當AreaCode為空時，表示使用登入者的AreaCode
        if (string.IsNullOrEmpty(param.AreaCode))
        {
            param.AreaCode = _user.AreaCode;
        }

        // 當UserAccount為空時，表示使用登入者的UserAccount
        if (string.IsNullOrEmpty(param.UserAccount))
        {
            param.UserAccount = _user.Account;
        }

        // 取得UserClientPara資料
        var userClientParaList = _dataAccessService.Fetch<UserClientPara>(e => e.AppCode == _user.AppCode && e.AreaCode == param.AreaCode)
            .Where(x => x.UserAccount == param.UserAccount);

        // 取得預設的ClientPara資料
        var clientParaList = _dataAccessService.Fetch<ClientPara>();

        // 取得UserClientPara和ClientPara的聯集(如果UserClientPara和ClientPara有相同的ParaType和ParaCode，則以UserClientPara為主)
        var query = userClientParaList.Select(a => new UserClientPara
        {
            AreaCode = a.AreaCode,
            UserAccount = a.UserAccount,
            ParaType = a.ParaType,
            ParaCode = a.ParaCode,
            ParaValue = a.ParaValue
        })
        .Union(clientParaList.Select(b => new UserClientPara
        {
            AreaCode = param.AreaCode,
            UserAccount = param.UserAccount,
            ParaType = b.ParaType,
            ParaCode = b.ParaCode,
            ParaValue = b.ParaValue
        })
        .Where(b => !userClientParaList.Any(a => a.ParaType == b.ParaType && a.ParaCode == b.ParaCode)));

        var resultList = await query.ToListAsync();

        var userProfile = new Dictionary<string, object>();

        // 遍历查询结果，添加属性到字典
        foreach (var item in resultList)
        {
            // 将 ParaCode 作为属性名，ParaValue 作为属性值
            userProfile[item.ParaCode] = item.ParaValue;
        }

        userProfile["ServiceAudio"] = new { Help = "Help.mp3", Leave = "Fence.mp3", Enter = "Fence.mp3" };

        ReturnModel returnModel = new(StatusCodes.Status200OK, true, new
        {
            userProfile
        });

        _logService.Logging("info", logActionName, requestUUID, JsonSerializer.Serialize(userProfile));

        return Ok(returnModel);
    }

    private async Task UpdateOrCreateUserClientPara(string areaCode, UserClientPara? userClientPara, string paraType, string paraCode, string paraValue, UpdateUserProfile param)
    {
        if (paraValue != null)
        {
            if (userClientPara == null)
            {
                userClientPara = new UserClientPara
                {
                    AppCode = _user.AppCode,
                    AreaCode = areaCode,
                    UserAccount = param.UserAccount,
                    ParaType = paraType,
                    ParaCode = paraCode,
                    ParaValue = paraValue
                };

                await _dataAccessService.CreateAsync(userClientPara);
            }
            else
            {
                userClientPara.ParaValue = paraValue;
                await _dataAccessService.UpdateAsync(userClientPara, new[] { "ParaValue" });
            }
        }
    }
}
