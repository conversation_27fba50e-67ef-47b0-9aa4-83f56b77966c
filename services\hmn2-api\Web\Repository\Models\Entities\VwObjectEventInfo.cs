﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class VwObjectEventInfo
{
    public int? ObjectEventId { get; set; }

    public string? AppCode { get; set; }

    public string? AreaCode { get; set; }

    public string? ObjectCode { get; set; }

    public string? ServiceCode { get; set; }

    public string? SddResource { get; set; }

    public string? EventCode { get; set; }

    public string? Pid { get; set; }

    public bool? Active { get; set; }

    public bool? Enable { get; set; }

    public int? EventFenceId { get; set; }

    public string? FenceCode { get; set; }

    public int? DeviceDetailId { get; set; }

    public string? SddComp { get; set; }

    public double? Threshold { get; set; }

    public int? StayOvertime { get; set; }

    public int? Duration { get; set; }

    public int? SilentInterval { get; set; }
}
