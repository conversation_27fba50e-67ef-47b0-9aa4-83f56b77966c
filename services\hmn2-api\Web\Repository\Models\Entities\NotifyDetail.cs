﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class NotifyDetail
{
    public int Id { get; set; }

    public string AppCode { get; set; } = null!;

    public string AreaCode { get; set; } = null!;

    public string NotifyCode { get; set; } = null!;

    public string NotifyType { get; set; } = null!;

    public string Source { get; set; } = null!;

    public string ContactCode { get; set; } = null!;

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }
}
