﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class VwObjectGroupInfo
{
    public int? GroupId { get; set; }

    public string? AppCode { get; set; }

    public string? AreaCode { get; set; }

    public string? GroupCode { get; set; }

    public bool? Active { get; set; }

    public bool? Enable { get; set; }

    public string? GroupName { get; set; }

    public string? CreateUserAccount { get; set; }

    public DateTime? CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }

    public string? CustomAreaCode { get; set; }

    public string? AreaName { get; set; }

    public string? OrganizationCode { get; set; }

    public string? OrganizationName { get; set; }
}
