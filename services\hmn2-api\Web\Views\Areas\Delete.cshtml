﻿@model Web.Repository.Models.Entities.Area

@{
    ViewData["Title"] = "Delete";
}

<h1>Delete</h1>

<h3>Are you sure you want to delete this?</h3>
<div>
    <h4>Area</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.AreaId)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.AreaId)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.AppCode)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.AppCode)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.OrganizationCode)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.OrganizationCode)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.CustomAreaCode)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.CustomAreaCode)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.AreaName)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.AreaName)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.AreaMapPath)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.AreaMapPath)
        </dd>
    </dl>
    
    <form asp-action="Delete">
        <input type="hidden" asp-for="AreaCode" />
        <input type="submit" value="Delete" class="btn btn-danger" /> |
        <a asp-action="Index">Back to List</a>
    </form>
</div>
