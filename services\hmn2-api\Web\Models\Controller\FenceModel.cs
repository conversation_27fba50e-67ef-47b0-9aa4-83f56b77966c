﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using Web.Validation;
using Web.Constant;

namespace Web.Models.Controller.Fence;

public class RetrieveFence
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    
    public string AreaCode { get; set; }

    public string FenceCode { get; set; }
    public string CustomFenceCode { get; set; }
    public string Enable { get; set; }
    public string FenceType { get; set; }
    public string FenceName { get; set; }
    public string PlaneCode { get; set; }
    public string BuildingCode { get; set; }
    public string SID { get; set; }
    public string StationName { get; set; }
}

public class CreateFence
{

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "FenceCode")]
    [Unique("AreaCode", "Fence", "FenceCode", ErrorMessage = Constants.ErrorCode.Unique + "FenceCode")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "FenceCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "FenceCode")]
    public string FenceCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "CustomFenceCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "CustomFenceCode")]
    public string CustomFenceCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Enable")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "FenceType")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "FenceType")]
    public string FenceType { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "FenceName")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "FenceName")]
    public string FenceName { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PlaneCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "PlaneCode")]
    [Exists("AreaCode", "Plane", "PlaneCode", ErrorMessage = Constants.ErrorCode.NotFound + "PlaneCode")]
    public string PlaneCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Width")]
    public double? Width { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Height")]
    public double? Height { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PositionX")]
    public double? PositionX { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "PositionY")]
    public double? PositionY { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "FenceColor")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "FenceColor")]
    public string FenceColor { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "RSSIDelta1")]
    public int RSSIDelta1 { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "RSSIDelta2")]
    public int RSSIDelta2 { get; set; }

    [ListAllExists("AreaCode", "Station", "SID", ErrorMessage = Constants.ErrorCode.NotFound + "StationList")]
    public List<string> StationSIDList { get; set; }

    [ListAllExists("AreaCode", "Station", "SID", ErrorMessage = Constants.ErrorCode.NotFound + "AlarmGroupList")]
    public List<string> AlarmGroupSIDList { get; set; }
}

public class UpdateFence
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "FenceCode")]
    [Exists("AreaCode", "Fence", "FenceCode", ErrorMessage = Constants.ErrorCode.NotFound + "FenceCode")]
    [HasReferenceWhenEquals("Enable", "N", "AreaCode", "EventFence", "FenceCode", ErrorMessage = Constants.ErrorCode.Reference + "FenceCode")]
    public string FenceCode { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "CustomFenceCode")]
    public string CustomFenceCode { get; set; }

    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "FenceType")]
    public string FenceType { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "FenceName")]
    public string FenceName { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "PlaneCode")]
    [Exists("AreaCode", "Plane", "PlaneCode", ErrorMessage = Constants.ErrorCode.NotFound + "PlaneCode")]
    public string PlaneCode { get; set; }
    public double? Width { get; set; }
    public double? Height { get; set; }
    public double? PositionX { get; set; }
    public double? PositionY { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "FenceColor")]
    public string FenceColor { get; set; }
    public int? RSSIDelta1 { get; set; }
    public int? RSSIDelta2 { get; set; }

    [ListAllExists("AreaCode", "Station", "SID", ErrorMessage = Constants.ErrorCode.NotFound + "StationList")]
    public List<string> StationSIDList { get; set; }

    [ListAllExists("AreaCode", "Station", "SID", ErrorMessage = Constants.ErrorCode.NotFound + "AlarmGroupList")]
    public List<string> AlarmGroupSIDList { get; set; }
}

public class DeleteFence
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "FenceCode")]
    [Exists("AreaCode", "Fence", "FenceCode", ErrorMessage = Constants.ErrorCode.NotFound + "FenceCode")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "FenceCode")]
    [HasReference("AreaCode", "EventFence", "FenceCode", ErrorMessage = Constants.ErrorCode.Reference + "FenceCode")]
    public string FenceCode { get; set; }
}
