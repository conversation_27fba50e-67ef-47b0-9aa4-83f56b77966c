﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class Fence
{
    public int FenceId { get; set; }
    public string? AppCode { get; set; }
    public string? AreaCode { get; set; }
    public string FenceCode { get; set; } = null!;
    public string? CustomFenceCode { get; set; }
    public bool? Enable { get; set; }
    public string? FenceType { get; set; }
    public string? FenceName { get; set; }
    public string? PlaneCode { get; set; }
    public double? Width { get; set; }
    public double? Height { get; set; }
    public double? PositionX { get; set; }
    public double? PositionY { get; set; }
    public string? FenceColor { get; set; }
    public string? CreateUserAccount { get; set; }
    public DateTime? CreateDate { get; set; }
    public string? ModifyUserAccount { get; set; }
    public DateTime? ModifyDate { get; set; }
    public int? RSSIDelta1 { get; set; }
    public int? RSSIDelta2 { get; set; }
}
