﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class VwDeviceInfo
{
    public int? DeviceId { get; set; }

    public string? AppCode { get; set; }

    public string? AreaCode { get; set; }

    public string? Pid { get; set; }

    public bool? Active { get; set; }

    public bool? Enable { get; set; }

    public string? Name { get; set; }

    public string? DeviceType { get; set; }

    public string? ManageDepartCode { get; set; }

    public string? UsageDepartCode { get; set; }

    public string? StationSid { get; set; }

    public string? mmWaveType { get; set; }

    public string? CreateUserAccount { get; set; }

    public DateTime? CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }

    public string? ManageDepartName { get; set; }

    public string? UsageDepartName { get; set; }

    public string? AreaName { get; set; }

    public string? ObjectCode { get; set; }

    public string? ObjectName { get; set; }

    public string? ObjectType { get; set; }

    public string? GroupCode { get; set; }
}
