﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class Station
{
    public int Id { get; set; }

    public string? AppCode { get; set; }

    public string? AreaCode { get; set; }

    public string? SID { get; set; }

    public bool? Enable { get; set; }

    public string? StationName { get; set; }

    public string? StationMac { get; set; }

    public string? StationType { get; set; }

    public string? SpaceType { get; set; }

    public string? StationIP { get; set; }

    public string? PlaneCode { get; set; }

    public string? RegionCode { get; set; }

    public double? AxisX { get; set; }

    public double? AxisY { get; set; }

    public double? DiffPositionX { get; set; }

    public double? DiffPositionY { get; set; }

    public string? CreateUserAccount { get; set; }

    public DateTime? CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }
}
