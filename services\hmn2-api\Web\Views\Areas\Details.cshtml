﻿@model Web.Repository.Models.Entities.Area

@{
    ViewData["Title"] = "Details";
}

<h1>Details</h1>

<div>
    <h4>Area</h4>
    <hr />
    <dl class="row">
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.AreaId)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.AreaId)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.AppCode)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.AppCode)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.OrganizationCode)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.OrganizationCode)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.CustomAreaCode)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.CustomAreaCode)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.AreaName)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.AreaName)
        </dd>
        <dt class = "col-sm-2">
            @Html.DisplayNameFor(model => model.AreaMapPath)
        </dt>
        <dd class = "col-sm-10">
            @Html.DisplayFor(model => model.AreaMapPath)
        </dd>
    </dl>
</div>
<div>
    <a asp-action="Edit" asp-route-id="@Model?.AreaCode">Edit</a> |
    <a asp-action="Index">Back to List</a>
</div>
