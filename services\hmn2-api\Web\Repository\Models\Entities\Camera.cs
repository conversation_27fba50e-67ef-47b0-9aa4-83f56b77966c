﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class Camera
{
    public int Id { get; set; }

    public string AppCode { get; set; } = null!;

    public string AreaCode { get; set; } = null!;

    public string CameraMAC { get; set; } = null!;

    public string CameraName { get; set; } = null!;

    public string IP { get; set; } = null!;

    public string StreamURL { get; set; } = null!;

    public string? CameraVideoPath { get; set; }

    public string? PathUserName { get; set; }

    public string? PathUserPassword { get; set; }

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }
}
