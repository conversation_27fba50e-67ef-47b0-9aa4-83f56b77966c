﻿using Web.Models.Controller;
using Web.Models.Controller.Department;
using Web.Models.Service.Business;
using Web.Repository.Models.Entities;

namespace Web.Services.Interfaces;

public interface IScheduleService
{
    List<ScheduleDepart> FetchScheduleDepartList(string appCode);
    List<ScheduleDepart> FetchScheduleDepartList(string appCode, string areaCode);
    List<ScheduleDepart> FetchScheduleDepartList(int scheduleJobId);
    List<ScheduleJob> FetchScheduleJobList(string appCode);
    List<ScheduleJob> FetchScheduleJobList(string appCode, List<string> areaCodeList);
    List<ScheduleJob> FetchScheduleJobListByDayOfMonth(string appCode, string day, int hour);
    List<ScheduleJob> FetchScheduleJobListByDayOfWeek(string appCode, int day, int hour);
    List<ScheduleJob> FetchScheduleJobListByEveryDay(string appCode, int hour);
    ScheduleJob FetchScheduleJob(int id);
    List<ScheduleNotify> FetchScheduleNotifyList(string appCode);
    List<ScheduleNotify> FetchScheduleNotifyList(string appCode, string areaCode);
    List<ScheduleNotify> FetchScheduleNotifyList(int scheduleJobId);
    Task<bool> CreateScheduleJobNNotify(string appCode, string userAccount, string areaCode, List<string> deptList, string jobType
            , string jobDesc, bool enable, string threahold, string scheduleFreq
            , string scheduleDay, string scheduleTime, string subject
            , dynamic scheduleNotifyList, bool endOfSave);
    Task<bool> UpdateScheduleJobNNotify(
            int scheduleJobId, string appCode, string userAccount
            , string areaCode, List<string> deptList, string jobType
            , string jobDesc, bool enable, string threahold, string scheduleFreq
            , string scheduleDay, string scheduleTime, string subject
            , dynamic scheduleNotifyList);
    Task<bool> DeleteScheduleJobNNotify(int scheduleJobId, bool endOfSave);
    Task<bool> SaveScheduleJobEnable(string userAccount, List<int> jobIdList, bool enable);
}