﻿namespace Web.Services.Interfaces;

public interface IRequestContextService
{
    string GetRequestUID();
    string GetClientIP();
    string GetUserAgent();
    bool IsSecureConnection();
    string GetRequestPath();
    IDictionary<string, string> GetRequestHeaders();
    void SetCustomRequestHeader(string key, string value);
    string GetCorrelationId();
    DateTimeOffset GetRequestTimestamp();
    string GetRequestMethod();
}
