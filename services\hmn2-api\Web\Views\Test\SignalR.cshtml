﻿<script src="https://cdnjs.cloudflare.com/ajax/libs/microsoft-signalr/6.0.1/signalr.js"></script>
<script>

    document.addEventListener('DOMContentLoaded', async () => {
        debugger;
        const search = location.search;
        const params = new URLSearchParams(search);

        const WebAlias = params.get('WebAlias')??"";
        const AreaCode = params.get('AreaCode');

        const connection = new signalR.HubConnectionBuilder()
            .withUrl(`${WebAlias}/messagehub?AreaCode=${AreaCode}`)
            //.configureLogging(signalR.LogLevel.Debug)
            .withAutomaticReconnect()
            .build();

        connection.keepAliveIntervalInMilliseconds = 1000 * 15; // 每15秒向server發訊息
        connection.serverTimeoutInMilliseconds = 1000 * 60 * 1; // 如果server超過6分鐘沒有發出ping,則切斷連線

        connection.on("taskOccurred", (message) => {
            console.log("taskOccurred", message);
        });

        connection.on("position", (message) => {
            console.log("position",message);
        });
        
        await connection.start().then(() => {
            console.log('Connection started');
        }).catch(err => {
            console.log('Error while starting connection: ' + err);
        });
    });

</script>