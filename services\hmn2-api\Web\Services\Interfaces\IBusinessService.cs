﻿using Web.Models.Controller;
using Web.Models.Controller.Department;
using Web.Models.Service.Business;
using Web.Repository.Models.Entities;

namespace Web.Services.Interfaces;

public interface IBusinessService
{
    List<Department> FetchDepartmentList(string appCode);
    Task<IQueryable<Department>> FetchDepartmentsByPermission(string appCode, string areaCode, string? check250DepartControl);
    List<ReturnError> SaveRole(string actionName, string appCode, List<InSaveRole> paramList);
    List<ReturnError> ValidateDepartmentData(string appCode, List<CreateDepartment> paramList);
}