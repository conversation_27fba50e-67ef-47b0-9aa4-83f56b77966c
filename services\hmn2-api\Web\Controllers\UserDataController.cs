﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using System.Text.RegularExpressions;
using Web.Constant;
using Web.Models.Controller;
using Web.Models.Controller.Auth;
using Web.Models.Controller.UserData;
using Web.Models.Service;
using Web.Repository.Models.Entities;
using Web.Services;
using Web.Services.Interfaces;
using Web.Validation;

namespace Web.Controller;

/// <summary>
/// 部門
/// </summary>
[Route("[controller]")]
[Authorize]
public class UserDataController : BaseController
{
    private readonly MqttBackgroundService _mqttService; // 新增私有欄位
    private readonly IConfiguration _configuration;     // 修正：新增私有欄位
    private readonly IAuthService _authService;         // 修正：新增私有欄位

    public UserDataController(
        IDataAccessService dataAccessService,
        ICredentialService credentialService,
        IRequestContextService requestContextService,
        ILogService logService,
        MqttBackgroundService mqttService,// 注入 MqttBackgroundService
        IConfiguration configuration,                   // 修正：注入 IConfiguration
        IAuthService authService)
        : base(dataAccessService, credentialService, requestContextService, logService)
    {
        _mqttService = mqttService;       //將注入的服務指派給私有欄位
        _configuration = configuration;   //修正：指派 IConfiguration
        _authService = authService;       //修正：指派 IAuthService
    }

    [HttpPost("users")]
    [RequestParamListDuplicate("UserAccount")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateUserData([FromBody] List<InCreateUserData> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        string appCode = _user.AppCode;

        var areaCode = paramList[0].AreaCode;

        for (int i = 0; i < paramList.Count; i++)
        {
            var item = paramList[i];

            // 將 userAccount 轉大寫
            item.UserAccount = item.UserAccount.Trim().ToUpper();

            // 將UserName去除前後空白
            item.UserName = item.UserName.Trim();

            // 檢查使用者Email不為空時
            if (!string.IsNullOrEmpty(item.UserEmail))
            {
                // 將UserEmail去除前後空白
                item.UserEmail = item.UserEmail.Trim();
            }
        }

        _logService.Logging("info", logActionName, requestUUID, "User Data Validated, start append.");
        _dataAccessService.BeginTransaction();

        foreach (InCreateUserData param in paramList)
        {
            // 新增使用者資料
            UserDatum userData = new()
            {
                AppCode = appCode,
                AreaCode = param.AreaCode,
                DeptCode = param.DeptCode,
                Enable = param.Enable == "Y",
                IsSupervisor = param.IsSupervisor == "Y",
                IsAdmin = param.IsAdmin,
                IsSuperAdmin = param.IsSuperAdmin == "Y",
                NeedChangePwd = param.NeedChangePwd == "Y",
                PwdExpType = param.PwdExpType,
                PwdExpStartDate = param.PwdExpStartDate,
                PwdExpEndDate = param.PwdExpEndDate,
                RoleCode = param.RoleCode,
                ThirdPartyAuth = param.ThirdPartyAuth == "Y",
                UserAccount = param.UserAccount.ToUpper(),
                UserAccountDisp = param.UserAccount,
                UserEmail = param.UserEmail,
                UserName = param.UserName,
                UserPassword = _authService.EncryptPwd(param.UserPassword),
                CreateDate = DateTime.Now,
                CreateUserAccount = _user.Account,
                ModifyDate = DateTime.Now
            };

            // 新增使用者資料
            await _dataAccessService.CreateAsync(userData);

            // 刪除使用者的部門監控權限（理論上這行是多的）
            await _dataAccessService.DeleteAsync<UserDeptMonPerm>(u => u.AppCode == _user.AppCode && u.AreaCode == userData.AreaCode && u.UserAccount == userData.UserAccount);

            // 新增使用者的部門監控權限（前端預設會有一筆，就是自己的部門）
            foreach (InUserDeptMonPerm userDeptMonPerm in param.UserDeptMonPermList)
            {
                UserDeptMonPerm userDeptMonPermData = new()
                {
                    AppCode = appCode,
                    AreaCode = userDeptMonPerm.AreaCode,
                    UserAccount = userDeptMonPerm.UserAccount.ToUpper(),
                    UsageDeptCode = userDeptMonPerm.UsageDeptCode,
                    CreateUserAccount = _user.Account,
                    CreateDate = DateTime.Now,
                    ModifyDate = DateTime.Now
                };

                await _dataAccessService.CreateAsync<Repository.Models.Entities.UserDeptMonPerm>(userDeptMonPermData);
            }

            // 刪除使用者的聯絡人資料（理論上這行是多的）
            await _dataAccessService.DeleteAsync<ContactDatum>(c => c.AppCode == _user.AppCode && c.AreaCode == userData.AreaCode && c.ContactCode == userData.UserAccount);

            // 新增聯絡人資料
            if (!string.IsNullOrWhiteSpace(param.Phone) || !string.IsNullOrWhiteSpace(param.LineToken) || !string.IsNullOrWhiteSpace(param.UserEmail))
            {
                ContactDatum contactData = new()
                {
                    AppCode = appCode,
                    AreaCode = param.AreaCode,
                    Source = "1",
                    ContactCode = param.UserAccount,
                    ContactName = param.UserName,
                    DeptCode = param.DeptCode,
                    Email = param.UserEmail,
                    Phone = param.Phone,
                    LineToken = param.LineToken,
                    CreateUserAccount = _user.Account,
                    CreateDate = DateTime.Now,
                    ModifyDate = DateTime.Now
                };

                await _dataAccessService.CreateAsync(contactData);

                // 新增 ContactDetail
                if (!string.IsNullOrWhiteSpace(param.Phone))
                {
                    ContactDetail contactDetail = new()
                    {
                        AppCode = appCode,
                        ContactCode = param.UserAccount,
                        ContactType = "SMS",
                        ContactValue = param.Phone,
                        CreateUserAccount = _user.Account,
                        CreateDate = DateTime.Now
                    };

                    await _dataAccessService.CreateAsync(contactDetail);
                }

                if (!string.IsNullOrWhiteSpace(param.LineToken))
                {
                    ContactDetail contactDetail = new()
                    {
                        AppCode = appCode,
                        ContactCode = param.UserAccount,
                        ContactType = "Line",
                        ContactValue = param.LineToken,
                        CreateUserAccount = _user.Account,
                        CreateDate = DateTime.Now
                    };

                    await _dataAccessService.CreateAsync(contactDetail);
                }

                if (!string.IsNullOrWhiteSpace(param.UserEmail))
                {
                    ContactDetail contactDetail = new()
                    {
                        AppCode = appCode,
                        ContactCode = param.UserAccount,
                        ContactType = "Email",
                        ContactValue = param.UserEmail,
                        CreateUserAccount = _user.Account,
                        CreateDate = DateTime.Now
                    };

                    await _dataAccessService.CreateAsync(contactDetail);
                }
            }
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "User Data append done.");

        var returnModel = new ReturnModel
        {
            authorize = new Authorize { Account = _user.Account, AppCode = _user.AppCode, AreaCode = _user.AreaCode },
            httpStatus = StatusCodes.Status201Created,
            result = true
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpDelete("users")]
    [RequestParamListDuplicate("UserAccount")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeleteUser([FromBody] List<InDeleteUserData> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 開始刪除帳號資料
        _logService.Logging("info", logActionName, requestUUID, "User Data Validated, start delete.");
        _dataAccessService.BeginTransaction();

        foreach (var param in paramList)
        {
            var userData = _dataAccessService.Fetch<UserDatum>().Where(x => x.UserAccount == param.UserAccount.ToUpper()).FirstOrDefault();

            await _dataAccessService.DeleteAsync(userData);
            await _dataAccessService.DeleteAsync<UserDeptMonPerm>(u => u.AppCode == _user.AppCode && u.AreaCode == userData.AreaCode && u.UserAccount == userData.UserAccount);
            await _dataAccessService.DeleteAsync<ContactDatum>(c => c.AppCode == _user.AppCode && c.ContactCode == userData.UserAccount);
            await _dataAccessService.DeleteAsync<ContactDetail>(c => c.AppCode == _user.AppCode && c.ContactCode == userData.UserAccount);
            await _dataAccessService.DeleteAsync<UserClientPara>(u => u.AppCode == _user.AppCode && u.UserAccount == userData.UserAccount);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "User Data delete done.");

        return Ok(new ReturnModel(StatusCodes.Status200OK, true));
    }

    [HttpPatch("users")]
    [RequestParamListDuplicate("UserAccount")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateUserData([FromBody] List<InUpdateUserData> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        string appCode = _user.AppCode;
        ReturnModel returnModel;

        // 驗證輸入的User資料
        for (int i = 0; i < paramList.Count; i++)
        {
            var item = paramList[i];

            // 將 userAccount 轉大寫
            item.UserAccount = item.UserAccount.Trim().ToUpper();

            // 檢查使用者名稱是否為空
            if (!string.IsNullOrWhiteSpace(item.UserName))
            {
                // 將UserName去除前後空白
                item.UserName = item.UserName.Trim();
            }

            // 檢查使用者Email不為空時
            if (!string.IsNullOrEmpty(item.UserEmail))
            {
                // 將UserEmail去除前後空白
                item.UserEmail = item.UserEmail.Trim();
            }
        }

        _logService.Logging("info", logActionName, requestUUID, "User Data Validated, start update.");

        _dataAccessService.BeginTransaction();

        foreach (InUpdateUserData param in paramList)
        {
            UserDatum userDatum = _dataAccessService.Fetch<UserDatum>(x => x.UserAccount == param.UserAccount.ToUpper()).AsTracking().FirstOrDefault();

            // 先刪除使用者的部門監控權限及聯絡人資料，底下再依照傳入的參數新增
            await _dataAccessService.DeleteAsync<UserDeptMonPerm>(u => u.AppCode == _user.AppCode && u.AreaCode == userDatum.AreaCode && u.UserAccount == userDatum.UserAccount);
            await _dataAccessService.DeleteAsync<ContactDatum>(c => c.AppCode == _user.AppCode && c.ContactCode == userDatum.UserAccount);
            await _dataAccessService.DeleteAsync<ContactDetail>(c => c.AppCode == _user.AppCode && c.ContactCode == userDatum.UserAccount);

            userDatum.AreaCode = param.AreaCode;
            userDatum.DeptCode = param.DeptCode;
            userDatum.Enable = param.Enable == "Y";
            userDatum.IsSupervisor = param.IsSupervisor == "Y";
            userDatum.IsAdmin = param.IsAdmin;
            userDatum.IsSuperAdmin = param.IsSuperAdmin == "Y";
            userDatum.NeedChangePwd = param.NeedChangePwd == "Y";
            userDatum.PwdExpType = param.PwdExpType;
            userDatum.PwdExpStartDate = param.PwdExpStartDate;
            userDatum.PwdExpEndDate = param.PwdExpEndDate;
            userDatum.RoleCode = param.RoleCode;
            userDatum.ThirdPartyAuth = param.ThirdPartyAuth == "Y";
            userDatum.UserName = param.UserName;
            userDatum.UserEmail = param.UserEmail;
            userDatum.UserPassword = !string.IsNullOrWhiteSpace(param.UserPassword) && !_user.IsSuperAdmin ? _authService.EncryptPwd(param.UserPassword) : userDatum.UserPassword;
            userDatum.ModifyDate = DateTime.Now;
            userDatum.ModifyUserAccount = _user.Account;

            if (param.Enable == "N")
            {
                await _mqttService.NotifyUserAccountDisable(requestUUID, userDatum, "user");
            }

            if (param.UserDeptMonPermList != null)
            {
                foreach (InUserDeptMonPerm userDeptMonPerm in param.UserDeptMonPermList)
                {
                    UserDeptMonPerm userDeptMonPermData = new()
                    {
                        AppCode = appCode,
                        AreaCode = userDeptMonPerm.AreaCode,
                        UserAccount = userDeptMonPerm.UserAccount.ToUpper(),
                        UsageDeptCode = userDeptMonPerm.UsageDeptCode,
                        CreateUserAccount = _user.Account,
                        CreateDate = DateTime.Now,
                        ModifyDate = DateTime.Now
                    };

                    await _dataAccessService.CreateAsync<Repository.Models.Entities.UserDeptMonPerm>(userDeptMonPermData);
                }
            }

            if (!string.IsNullOrWhiteSpace(param.Phone) || !string.IsNullOrWhiteSpace(param.LineToken) || !string.IsNullOrWhiteSpace(param.UserEmail))
            {
                ContactDatum contactData = new()
                {
                    AppCode = appCode,
                    AreaCode = param.AreaCode,
                    Source = "1",
                    ContactCode = param.UserAccount,

                    ContactName = param.UserName,
                    DeptCode = param.DeptCode,
                    Email = param.UserEmail,
                    Phone = param.Phone,
                    LineToken = param.LineToken,
                    CreateUserAccount = _user.Account,
                    CreateDate = DateTime.Now,
                    ModifyDate = DateTime.Now
                };

                await _dataAccessService.CreateAsync(contactData);

                // 新增 ContactDetail
                if (!string.IsNullOrWhiteSpace(param.Phone))
                {
                    ContactDetail contactDetail = new()
                    {
                        AppCode = appCode,
                        ContactCode = param.UserAccount,
                        ContactType = "SMS",
                        ContactValue = param.Phone,
                        CreateUserAccount = _user.Account,
                        CreateDate = DateTime.Now
                    };

                    await _dataAccessService.CreateAsync(contactDetail);
                }

                if (!string.IsNullOrWhiteSpace(param.LineToken))
                {
                    ContactDetail contactDetail = new()
                    {
                        AppCode = appCode,
                        ContactCode = param.UserAccount,
                        ContactType = "Line",
                        ContactValue = param.LineToken,
                        CreateUserAccount = _user.Account,
                        CreateDate = DateTime.Now
                    };

                    await _dataAccessService.CreateAsync(contactDetail);
                }

                if (!string.IsNullOrWhiteSpace(param.UserEmail))
                {
                    ContactDetail contactDetail = new()
                    {
                        AppCode = appCode,
                        ContactCode = param.UserAccount,
                        ContactType = "Email",
                        ContactValue = param.UserEmail,
                        CreateUserAccount = _user.Account,
                        CreateDate = DateTime.Now
                    };

                    await _dataAccessService.CreateAsync(contactDetail);
                }
            }
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "User Data update done.");

        returnModel = new ReturnModel
        {
            authorize = new Authorize { Account = _user.Account, AppCode = _user.AppCode, AreaCode = _user.AreaCode },
            httpStatus = StatusCodes.Status200OK,
            result = true
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpGet("users")]
    public async Task<IActionResult> RetrieveUserData([FromQuery] InRetrieveUserData param)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // if (string.IsNullOrEmpty(param.AreaCode))
        // {
        //     param.AreaCode = _user.AreaCode;
        // }

        var areaList = _dataAccessService.Fetch<Area>(e => e.AppCode == _user.AppCode);
        var contactDataList = _dataAccessService.Fetch<ContactDatum>(e => e.AppCode == _user.AppCode);
        var deptList = _dataAccessService.Fetch<Department>(e => e.AppCode == _user.AppCode && (e.Enable == true || e.Enable == false)).OrderByDescending(x => x.ModifyDate).ThenByDescending(x => x.CreateDate);
        var roleList = _dataAccessService.Fetch<Role>(e => e.AppCode == _user.AppCode);
        var userDataList = _dataAccessService.Fetch<UserDatum>(e => e.AppCode == _user.AppCode).OrderByDescending(e => e.ModifyDate).ThenByDescending(x => x.CreateDate); ;
        var departSectorList = await _dataAccessService.Fetch<DepartSector>(e => e.AppCode == _user.AppCode).ToListAsync();
        var sectorList = await _dataAccessService.Fetch<Sector>(e => e.AppCode == _user.AppCode).ToListAsync();
        var userDeptMonPermList = await _dataAccessService.Fetch<UserDeptMonPerm>(e => e.AppCode == _user.AppCode).ToListAsync();

        var departSectors = from ds in departSectorList
                            join s in sectorList on ds.SectorCode equals s.SectorCode
                            select new { ds.AreaCode, ds.DeptCode, s.SectorName };

        var outUserDeptMonPermList = (from u in userDeptMonPermList
                                      join d1 in userDataList on new { u.AreaCode, u.UserAccount } equals new { d1.AreaCode, d1.UserAccount } into ud
                                      from d1 in ud.DefaultIfEmpty()
                                      join d2 in deptList on new { u.AreaCode, u.UsageDeptCode } equals new { d2.AreaCode, UsageDeptCode = d2.DeptCode } into dd
                                      from d2 in dd.DefaultIfEmpty()
                                      join ds in departSectors on new { u.AreaCode, u.UsageDeptCode } equals new { ds.AreaCode, UsageDeptCode = ds.DeptCode } into dsg
                                      from ds in dsg.DefaultIfEmpty()
                                      select new
                                      {
                                          u.Id,
                                          u.AreaCode,
                                          DeptName = d2 != null ? d2.DeptName : null,
                                          Enable = d2 != null && d2.Enable == true ? "Y" : "N",
                                          IsManagedDept = d2 != null && d2.IsManagedDept == true ? "Y" : "N",
                                          IsUsageDept = d2 != null && d2.IsUsageDept == true ? "Y" : "N",
                                          Sectors = string.Join(",", dsg.Select(ds => ds.SectorName)),
                                          Supervisors = string.Join(",", ud.Where(ud => ud.IsSupervisor == true).Select(ud => ud.UserAccountDisp)),
                                          u.UserAccount,
                                          UserAccountDisp = d1 != null ? d1.UserAccountDisp : null,
                                          u.UsageDeptCode
                                      }).Distinct().ToList();

        var query = (from a in userDataList
                     join b in deptList on new { a.AreaCode, a.DeptCode } equals new { b.AreaCode, b.DeptCode } into temp
                     from t in temp.DefaultIfEmpty()
                     join c in areaList on a.AreaCode equals c.AreaCode into temp2
                     from t2 in temp2.DefaultIfEmpty()
                     join e in roleList on a.RoleCode equals e.RoleCode into temp3
                     from t3 in temp3.DefaultIfEmpty()
                     join f in contactDataList on new { a.AreaCode, ContactCode = a.UserAccount } equals new { f.AreaCode, f.ContactCode } into temp4
                     from t4 in temp4.DefaultIfEmpty()
                     select new
                     {
                         a.Id,
                         a.AppCode,
                         a.AreaCode,
                         t2.AreaName,
                         a.UserName,
                         a.UserEmail,
                         a.UserAccount,
                         a.UserAccountDisp,
                         a.DeptCode,
                         t.DeptName,
                         a.RoleCode,
                         t3.RoleName,
                         a.IsAdmin,
                         IsSuperAdmin = (a.IsSuperAdmin != null && a.IsSuperAdmin == true) ? "Y" : "N",
                         IsSupervisor = (a.IsSupervisor != null && a.IsSupervisor == true) ? "Y" : "N",
                         ThirdPartyAuth = (a.ThirdPartyAuth != null && a.ThirdPartyAuth == true) ? "Y" : "N",
                         a.PwdExpType,
                         a.PwdExpStartDate,
                         a.PwdExpEndDate,
                         NeedChangePwd = ((a.NeedChangePwd != null && a.NeedChangePwd == true) || (a.PwdExpEndDate.HasValue && a.PwdExpEndDate.Value < DateTime.Now)) ? "Y" : "N",
                         t4.Phone,
                         t4.LineToken,
                         ClientIP = (a.UserAccount == _user.Account) ? _user.ClientIP : "",
                         Enable = (a.Enable != null && a.Enable == true) ? "Y" : "N",
                         AutoLoginUrl = _user.IsSuperAdmin ? $"token={_authService.EncryptPwd(a.UserAccount)}{a.UserPassword}" : ""
                     });

        // 篩選條件，如果沒有指定比對模式，預設為Like
        if (string.IsNullOrWhiteSpace(param.CompareMode) || param.CompareMode.ToLower() == "like")
        {
            query = query.Where(x => (param.DeptName == null || x.DeptName.ToUpper().Contains(param.DeptName.ToUpper()))
                                    && (param.DeptCode == null || x.DeptCode.ToUpper().Contains(param.DeptCode.ToUpper()))
                                    && (param.AreaCode == null || x.AreaCode.ToUpper().Contains(param.AreaCode.ToUpper()))
                                    && (param.UserAccount == null || x.UserAccount.ToUpper().Contains(param.UserAccount.ToUpper()))
                                    && (param.UserAccountDisp == null || x.UserAccountDisp.ToUpper().Contains(param.UserAccountDisp.ToUpper()))
                                    && (param.RoleCode == null || x.RoleCode.ToUpper().Contains(param.RoleCode.ToUpper()))
                                    && (param.Enable == null || x.Enable == param.Enable)
                               );
        }
        else
        {
            query = query.Where(x => (param.DeptName == null || x.DeptName.ToUpper() == param.DeptName.ToUpper())
                                    && (param.DeptCode == null || x.DeptCode.ToUpper() == param.DeptCode.ToUpper())
                                    && (param.AreaCode == null || x.AreaCode.ToUpper() == param.AreaCode.ToUpper())
                                    && (param.UserAccount == null || x.UserAccount.ToUpper() == param.UserAccount.ToUpper())
                                    && (param.UserAccountDisp == null || x.UserAccountDisp.ToUpper() == param.UserAccountDisp.ToUpper())
                                    && (param.RoleCode == null || x.RoleCode.ToUpper() == param.RoleCode.ToUpper())
                                    && (param.Enable == null || x.Enable == param.Enable)
                                  );
        }

        var queryResult = query.ToList();

        /**
         * 這裡的DeptCode及DeptName 有可能會跟 User/loginInfo 的 DeptCode及DeptName 不同（因為IsSuperAdmin的關係）
         * 原因參考 https://tpe-jira2.fihtdc.com/secure/thumbnail/2184298/_thumb_2184298.png
         */
        var result = from q in queryResult
                     select new
                     {
                         q.Id,
                         q.AppCode,
                         q.AreaCode,
                         q.AreaName,
                         q.UserName,
                         q.UserEmail,
                         q.UserAccount,
                         q.UserAccountDisp,
                         q.DeptCode,
                         q.DeptName,
                         q.RoleCode,
                         q.RoleName,
                         q.IsAdmin,
                         q.IsSuperAdmin,
                         q.IsSupervisor,
                         q.ThirdPartyAuth,
                         q.PwdExpType,
                         q.PwdExpStartDate,
                         q.PwdExpEndDate,
                         q.NeedChangePwd,
                         q.Phone,
                         q.LineToken,
                         q.ClientIP,
                         q.Enable,
                         q.AutoLoginUrl,
                         UserDeptMonPermList = q.IsAdmin == "A" ? [] : outUserDeptMonPermList.Where(u => u.AreaCode == q.AreaCode && u.UserAccount == q.UserAccount).ToList()
                     };

        // 下方使用CountAsync()會有問題，所以先ToList()再Count()，待解決
        var queryList = result.ToList();

        var recordTotal = queryList.Count();

        var recordList = size == 0 ? result.ToList() : queryList.Skip(skip).Take(size).ToList();

        //// 總筆數
        //var recordTotal = await query.CountAsync();

        //// 判斷是否分頁決定回傳資料
        //var recordList = size == 0 ? await query.ToListAsync() : await query.Skip(skip).Take(size).ToListAsync();

        returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new
            {
                recordTotal,
                recordList
            }
        };

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);
    }

    [HttpGet("menuPermission")]
    public async Task<IActionResult> GetMenuPermission()
    {
        List<ReturnError> errors = [];

        string account = _user.Account;

        // 取得登入者的角色代碼
        var userData = await _dataAccessService.Fetch<UserDatum>(e => e.AppCode == _user.AppCode && e.UserAccount == account).FirstOrDefaultAsync();
        var roleCode = userData?.RoleCode;

        if (string.IsNullOrWhiteSpace(roleCode))
        {
            errors.Add(new ReturnError { index = 1, code = account, errors = [new ErrorDetail { index = 1, error = "err.null.users.UserData.RoleCode" }] });
            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, errors));
        }

        // 取得登入者的角色資訊
        var role = await _dataAccessService.Fetch<Role>(e => e.AppCode == _user.AppCode && e.AppCode == _user.AppCode && e.Enable == true && e.RoleCode == roleCode).FirstOrDefaultAsync();

        // 如果角色不存在或未啟用
        if (role == null)
        {
            errors.Add(new ReturnError { index = 1, code = roleCode, errors = [new ErrorDetail { index = 1, error = "err.notFound.users.UserData.Role" }] });
            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, errors));
        }

        // 取得登入角色有權限的Menu List
        var roleMenuPermissionList = _user.IsSuperAdmin ? await _dataAccessService.Fetch<MenuPermissionType>(x => x.Enable == true).Select(x => new { MenuId = (int?)x.MenuId, x.PermissionType }).ToListAsync()
            : await _dataAccessService.Fetch<RoleMenuPermission>(x => x.AppCode == _user.AppCode && x.RoleCode == roleCode && x.IsAllowed == true).Select(x => new { x.MenuId, x.PermissionType }).ToListAsync();

        // 取得登入角色有權限的MenuId
        var roleMenuSet = new HashSet<int?>(roleMenuPermissionList.Select(x => x.MenuId));

        // 取得所有Menu
        var menuList = await _dataAccessService.Fetch<Menu>().ToListAsync();

        if (menuList == null || menuList.Count == 0)
        {
            errors.Add(new ReturnError { index = 1, code = "", errors = [new ErrorDetail { index = 1, error = "err.notFound.users.UserData.Menu" }] });
            return BadRequest(new ReturnModel(StatusCodes.Status400BadRequest, false, errors));
        }

        // 取得有權限的Menu
        var hasPermissionMenuList = menuList.Where(x => _user.IsSuperAdmin || roleMenuSet.Contains(x.MenuId));

        // 依有權限的Menu取得最上層的Menu及Custom Menu(custome menu是自訂的Menu，且只會有一層)
        var topMenuList =
            (from m1 in hasPermissionMenuList
             join m2 in menuList on m1.ParentMenuId equals m2.MenuId
             where m2.Type == "Menu" && m2.ParentMenuId == 0
             select m2).Union
            (
             from m1 in menuList
             where m1.Type == "Custom" && m1.ParentMenuId == 0
             select m1
            )
            .Distinct();

        // 取得HMNAlpha的WebUrl
        string alphaWebUrl = _configuration.GetSection("HMNAlpha")["WebUrl"] ?? "";

        // 如果alphaWebUrl 字串結尾有"/"，則移除
        if (alphaWebUrl.EndsWith("/"))
        {
            alphaWebUrl = alphaWebUrl.Substring(0, alphaWebUrl.Length - 1);
        }

        // 取得有權限的Menu及子Menu
        var menuPermissionList = (from m in topMenuList
                                  select new
                                  {
                                      m.MenuId,
                                      Title = m.StringId == null ? m.Title : $"${m.StringId}",
                                      Desc = m.Title,
                                      m.ParentMenuId,
                                      m.Type,
                                      m.Url,
                                      icon = alphaWebUrl + m.icon,
                                      m.Sort,
                                      m.ComponentName,
                                      ChildMenuList = (from c in hasPermissionMenuList
                                                       where c.ParentMenuId == m.MenuId && c.Type == "Menu"
                                                       select new
                                                       {
                                                           c.MenuId,
                                                           c.QueryLog,
                                                           Title = c.StringId == null ? c.Title : $"${c.StringId}",
                                                           Desc = c.Title,
                                                           c.ParentMenuId,
                                                           c.Type,
                                                           c.Url,
                                                           c.icon,
                                                           c.Sort,
                                                           c.ComponentName,
                                                           Permission = roleMenuPermissionList.Where(x => x.MenuId == c.MenuId).Select(x => x.PermissionType)
                                                       }).OrderByDescending(x => x.Sort)
                                  }).OrderByDescending(x => x.Sort);

        var result = new ReturnModel
        {
            authorize = new Authorize { Account = _user.Account },
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = menuPermissionList.ToList()
        };

        return Ok(result);
    }

    [HttpGet("loginInfo")]
    public async Task<IActionResult> GetLoginInfo()
    {
        var userName = (await _dataAccessService.Fetch<UserDatum>(x => x.UserAccount == _user.Account).FirstOrDefaultAsync())?.UserName;
        var deptName = (await _dataAccessService.Fetch<Department>(x => x.AppCode == _user.AppCode && x.DeptCode == _user.DeptCode).FirstOrDefaultAsync())?.DeptName;

        /**
         * 這裡的DeptCode及DeptName 有可能會跟 User/users 的 DeptCode及DeptName 不同（因為IsSuperAdmin的關係）
         * 原因參考 https://tpe-jira2.fihtdc.com/secure/thumbnail/2184298/_thumb_2184298.png
         */
        var returnData = new
        {
            _user.Account,
            UserName = userName,
            _user.AppCode,
            _user.DeptCode,
            DeptName = deptName,
            _user.IsManagedDept,
            _user.DeptCodeList,
            _user.AreaCode,
            _user.AreaCodeList,
            _user.ClientIP,
            _user.IsAdmin,
            _user.IsViewAllArea,
            _user.IsDeveloper
        };

        ReturnModel returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = returnData
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    [HttpPatch("changePwd")]
    [RequestParamNotNullOrEmpty]
    public async Task<IActionResult> ChangeUserPwd([FromBody] ChangeUserPwd userPwd)
    {
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, Guid.NewGuid().ToString(), "Start");

        UserDatum accountData = _dataAccessService.Fetch<UserDatum>(x => x.UserAccount == _user.Account).FirstOrDefault();

        var encryptPwd = _authService.EncryptPwd(userPwd.OrginPassword);
        if (accountData.UserPassword != encryptPwd)
        {
            _logService.Logging("error", logActionName, Guid.NewGuid().ToString(), Constants.ErrorCode.Invalid + "OrginPassword");
            return Unauthorized(new ReturnModel(StatusCodes.Status401Unauthorized, false, Constants.ErrorCode.Invalid + "OrginPassword"));
        }

        accountData.UserPassword = _authService.EncryptPwd(userPwd.NewPassword);
        await _dataAccessService.UpdateAsync(accountData);

        _logService.Logging("info", logActionName, Guid.NewGuid().ToString(), "User Password change done.");
        return Ok(new ReturnModel(StatusCodes.Status200OK, true));
    }
}
