﻿using System.ComponentModel.DataAnnotations;
using Web.Models.Interface;
using Web.Validation;

namespace Web.Models.Controller.Log;

public class InRetrieveLocaleString: ILogRequest
{
    public string? Locale { get; set; }
    public string? StringId { get; set; }
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; } = "StringId:desc";
}

//[ListUnique("Locale", "StringId", ErrorMessage = "err.duplicate.param.LocalNStringId")]
// [ListUnique("StringId", ErrorMessage = "err.duplicate.param.StringId")]
public class InCreateLocaleString : ILogRequest
{
    // 這裡的AreaCode是為了驗證ExistsAttribute而加的，實際上不會用到，也不用傳值
    public string? AreaCode { get; set; }
    [Required(ErrorMessage = "err.null.param.Locale")]
    [Exists("AreaCode", "Locale", "Locale1", ErrorMessage = "err.notfound.param.Locale")]
    public string Locale { get; set; }
    [Required(ErrorMessage = "err.null.param.StringId")]
    [Unique("AreaCode", "LocaleString", "StringId", ErrorMessage = "err.uniqe.param.StringId")]
    public string StringId { get; set; }
    public string StringContent { get; set; }
}

public class InCreateLocaleString1 : ILogRequest
{
    // 這裡的AreaCode是為了驗證ExistsAttribute而加的，實際上不會用到，也不用傳值
    public string? AreaCode { get; set; }
    [Required(ErrorMessage = "err.null.param.Locale")]
    [Exists("AreaCode", "Locale", "Locale1", ErrorMessage = "err.notfound.param.Locale")]
    public string Locale { get; set; }
    [Required(ErrorMessage = "err.null.param.StringId")]
    [Unique("AreaCode", "LocaleString", "StringId", ErrorMessage = "err.uniqe.param.StringId")]
    public string StringId { get; set; }
    public string StringContent { get; set; }
    public List<InCreateLocaleString2?>? DetailList2 { get; set; }
}

public class InCreateLocaleString2 : ILogRequest
{
    // 這裡的AreaCode是為了驗證ExistsAttribute而加的，實際上不會用到，也不用傳值
    public string? AreaCode { get; set; }
    [Required(ErrorMessage = "err.null.param.Locale")]
    [Exists("AreaCode", "Locale", "Locale1", ErrorMessage = "err.notfound.param.Locale")]
    public string Locale { get; set; }
    [Required(ErrorMessage = "err.null.param.StringId")]
    [Unique("AreaCode", "LocaleString", "StringId", ErrorMessage = "err.uniqe.param.StringId")]
    public string StringId { get; set; }
    public string StringContent { get; set; }
}