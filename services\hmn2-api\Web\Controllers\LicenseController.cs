﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Reflection.Emit;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices.Marshalling;
using System.Text.Json;
using Web.ActionFilter;
using Web.Constant;
using Web.Models.Controller;
using Web.Models.Controller.Device;
using Web.Models.Controller.Log;
using Web.Models.Controller.Station;
using Web.Models.Service;
using Web.Models.Service.Configuration;
using Web.Models.Service.Fusion;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;

namespace Web.Controller;

/// <summary>
/// 授權控制器
/// </summary>
[Route("[controller]")]
[Authorize]
public class LicenseController(IDataAccessService dataAccessService,
                              ICredentialService credentialService,
                              IRequestContextService requestContextService,
                              ILicenseService licenseService,
                              ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly ILicenseService _licenseService = licenseService;

    [HttpGet("services")]
    public async Task<IActionResult> RetrieveServices()
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 取得所有 Services
        var serviceList = await _licenseService.GetServiceList("active+eq+true+and+code+eq+LossSignal");

        var returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = serviceList
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }


    [HttpGet("notifyConfigs")]
    public async Task<IActionResult> RetrieveNotifyConfigs()
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");
        
        // 取得所有 Notify configs
        var notifyConfigList = await _licenseService.GetNotifyConfigList("");

        var returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = notifyConfigList
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }
}