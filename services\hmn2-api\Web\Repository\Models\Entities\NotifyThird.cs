﻿using System;
using System.Collections.Generic;

namespace Web.Repository.Models.Entities;

public partial class NotifyThird
{
    public int Id { get; set; }

    public string AppCode { get; set; } = null!;

    public string AreaCode { get; set; } = null!;

    public string NotifyType { get; set; } = null!;

    public string ThirdCode { get; set; } = null!;

    public string ThirdName { get; set; } = null!;

    public string URL_MAC { get; set; } = null!;

    public string CreateUserAccount { get; set; } = null!;

    public DateTime CreateDate { get; set; }

    public string? ModifyUserAccount { get; set; }

    public DateTime? ModifyDate { get; set; }
}
