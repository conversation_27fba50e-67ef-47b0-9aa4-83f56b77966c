﻿using System.ComponentModel.DataAnnotations;
using Web.Validation;
using Web.Constant;
using System.Text.Json.Serialization;

namespace Web.Models.Controller.NotifyHeader;

public class RetrieveNotifyHeader
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    public string AreaCode { get; set; }
    public string NotifyCode { get; set; }
    public string NotifyName { get; set; }
    public string Enable { get; set; }
    public string NotifyType { get; set; }
}

public class CreateNotifyDetail
{
    [JsonIgnore]
    public string NotifyCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "NotifyType")]
    [RegularExpression(@"^(Line|Email|SMS)$", ErrorMessage = Constants.ErrorCode.Pattern + "NotifyType")]
    public string NotifyType { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Source")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "Source")]
    [Range(0, 2, ErrorMessage = Constants.ErrorCode.Pattern + "Source")]
    public string Source { get; set; } = null!;

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ContactCode")]
    [ExistsWhenEquals("Source", "0,1,2", "", "ContactDatum,ContactDatum,ContactDatum", "ContactCode,ContactCode,ContactCode", true, "false,false,false", ErrorMessage = Constants.ErrorCode.NotFound + "ContactCode")]
    public string ContactCode { get; set; }
}

public class CreateNotifyHeader
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "NotifyCode")]
    [RegularExpression(@"^[a-zA-Z0-9_:@-]+$", ErrorMessage = Constants.ErrorCode.Pattern + "NotifyCode")]
    [StringLength(20, ErrorMessage = Constants.ErrorCode.Length + "NotifyCode")]
    [Unique("", "NotifyHeader", "NotifyCode", true, ErrorMessage = Constants.ErrorCode.Unique + "NotifyCode")]
    [SetToList("NotifyCode", "NotifyDetailList")]
    [ListDuplicate("NotifyDetailList", "NotifyType", "Source", "ContactCode", ErrorMessage = Constants.ErrorCode.Duplicate + "ObjectDeviceList.Pid")]
    public string NotifyCode { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "NotifyName")]
    [StringLength(64, ErrorMessage = Constants.ErrorCode.Length + "NotifyName")]
    public string NotifyName { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Enable")]
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }

    public List<CreateNotifyDetail> NotifyDetailList { get; set; }
}

public class UpdateNotifyHeader
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }
    
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "NotifyCode")]
    [Exists("", "NotifyHeader", "NotifyCode", ErrorMessage = Constants.ErrorCode.NotFound + "NotifyCode")]
    [SetToList("NotifyCode", "NotifyDetailList")]
    [ListDuplicate("NotifyDetailList", "NotifyType", "Source", "ContactCode", ErrorMessage = Constants.ErrorCode.Duplicate + "ObjectDeviceList.Pid")]
    public string NotifyCode { get; set; }
    
    [StringLength(64, ErrorMessage = Constants.ErrorCode.Length + "NotifyName")]
    public string NotifyName { get; set; }
    
    [RegularExpression(@"^[YN]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Enable")]
    public string Enable { get; set; }

    public List<CreateNotifyDetail> NotifyDetailList { get; set; }
}

public class DeleteNotifyHeader
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "NotifyCode")]
    [Exists("", "NotifyHeader", "NotifyCode", ErrorMessage = Constants.ErrorCode.NotFound + "NotifyCode")]
    public string NotifyCode { get; set; }
}