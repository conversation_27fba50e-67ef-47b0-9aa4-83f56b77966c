{
  "DetailedErrors": true,
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.AspNetCore.SignalR": "Warning",
      "Microsoft.AspNetCore.Http.Connections.Internal.Transports.WebSocketsTransport": "Warning",
      "Microsoft.EntityFrameworkCore.ChangeTracking": "Warning",
      "Microsoft.EntityFrameworkCore.Database": "Warning"
    }
  },
  "ConnectionStrings": {
    "FusionS3HMNConnection": "server=************;port=8260;database=FusionS3HMN;uid=postgres;pwd=*************;"
  },
  "FusionNetParam": {
    "ApiUrl": "http://************:8000",
    "ApiVersion": "v3",
    "SecretKey": "T9qiXWHR6CpPhFzDONBc24CnU44qfflG"
  },
  "Cors": {
    "AllowOrigin": "*"
  },
  "MQTTParam": {
    "HostIp": "************",
    "HostPort": "8204", //default: if 域名 9002 else 8201 
    "UseTLS": "N", //default: if 域名 Y else N 
	"Enable": "N", //default: Y
    "Timeout": 5000,
    "UserName": "",
    "Password": ""
  },
  "MQTTServerParam": {
    "HostPort": "8888",
	"ConnectionBacklog": 100,
	"MaxPendingMessagesPerClient": 1000
  },
  "AppInfo": {
    "AppCode": "hmn",
    "RequestIdHeaderName": "X-Request-ID"
  },
  "MapInfo": {
    "SectorUrl": "https://pcrm.fusionnet.io/hmnweb/upload/img/sector/",
    "PlaneUrl": ""
  },
  "HMNAlpha": {
    "WebUrl": "http://************/hmnweb/"
  },
  "FusionHmnVasAppInfo": {
    "Name": "FusionHMN VAS",
    "Version": "0.0.4",
    "FileName": "FusionHMN VAS v0.0.4.zip"
  },
  "CameraEtl": {
    "Enable": "N",
    "WorkingDirectory": "Etls",
    "WorkingFileName": "fnc-application-hmn-camera-etl-v0.0.1.jar"
  }
}