﻿using Web.Services.Interfaces;

public class RequestLoggingMiddleware(RequestDelegate next, ILogService logService)
{
    private readonly RequestDelegate _next = next;
    private readonly ILogService _logService = logService;

    public async Task InvokeAsync(HttpContext context)
    {
        string requestUUID = string.IsNullOrEmpty(context.Request.Headers["x-request-id"])
            ? Guid.NewGuid().ToString()
            : context.Request.Headers["x-request-id"];

        context.Items["RequestUUID"] = requestUUID;

        await _next(context);
    }
}