﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Data;
using Web.Models.Controller;
using Web.Models.Controller.Contact;
using Web.Models.Service;
using Web.Repository.Models.Entities;
using Web.Validation;
using Web.Services;
using Web.Services.Interfaces;

namespace Web.Controller;

public class ContactVO
{
    public string AreaCode { get; set; }
    public string DeptCode { get; set; }
    public string DeptName { get; set; }
    public string Source { get; set; }
    public string ContactCode { get; set; }
    public string ContactName { get; set; }
    public string Phone { get; set; }
    public string Email { get; set; }
    public string LineToken { get; set; }
    public DateTime? CreateDate { get; set; }
    public string CreateUserAccount { get; set; }
    public DateTime? ModifyDate { get; set; }
    public string ModifyUserAccount { get; set; }
}

public class ContactDetailVO
{
    public string AreaCode { get; set; }
    public string DeptCode { get; set; }
    public string DeptName { get; set; }
    public string Source { get; set; }
    public string ContactCode { get; set; }
    public string ContactName { get; set; }
    public string ContactType { get; set; }
    public string ContactValue { get; set; }
    public DateTime? CreateDate { get; set; }
    public string CreateUserAccount { get; set; }
}

/// <summary>
/// 聯絡人控制器
/// </summary>
[Authorize]
[Route("[controller]")]
public class ContactController(IDataAccessService dataAccessService,
                            ICredentialService credentialService,
                            IRequestContextService requestContextService,
                            IObjectService objectService,
                            ILogService logSercice) : BaseController(dataAccessService, credentialService, requestContextService, logSercice)
{
    private readonly IObjectService _objectService = objectService;

    [HttpGet("contacts")]
    public async Task<IActionResult> RetrieveContact(RetrieveContact queryParam)
    {
        RetrieveContact param = queryParam;
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // 排序；如果沒有 sort 條件預設使用 ModifyDate 降冪排序
        string sort = string.IsNullOrEmpty(param.sort) ? "ModifyDate:desc" : param.sort;
        string sortByField = sort.Split(":")[0];
        string sortDirection = sort.Split(":")[1];

        var contactList = _dataAccessService.Fetch<ContactDatum>(x => x.AppCode == _user.AppCode);
        var departmentList = _dataAccessService.Fetch<Department>(e=>e.AppCode == _user.AppCode);

        var query = (from a in contactList
             join b in departmentList on a.DeptCode equals b.DeptCode into temp
             from t in temp.DefaultIfEmpty()
             select new ContactVO
             {
                 AreaCode = a.AreaCode,
                 DeptCode = a.DeptCode,
                 DeptName = t.DeptName,
                 Source = a.Source,
                 ContactCode = a.ContactCode,
                 ContactName = a.ContactName,                 
                 Phone = a.Phone,
                 Email = a.Email,
                 LineToken = a.LineToken,
                 CreateDate = a.CreateDate,
                 CreateUserAccount = a.CreateUserAccount,
                 ModifyDate = a.ModifyDate,
                 ModifyUserAccount = a.ModifyUserAccount
             })
            .Where(x => (param.AreaCode == null || x.AreaCode == param.AreaCode)
                    && (param.Source == null || x.Source == param.Source)
                    && (param.DeptCode == null || (x.DeptCode != null && x.DeptCode.ToUpper().Contains(param.DeptCode.ToUpper())))
                    && (param.ContactName == null || x.ContactName.ToUpper().Contains(param.ContactName.ToUpper()))
                    && (param.ContactCode == null || x.ContactCode.ToUpper().Contains(param.ContactCode.ToUpper()))
                );
        
        // 取得總筆數
        int recordTotal = await query.CountAsync();

        // 根據 sortByField 和 sortDirection 進行動態排序
        query = sortDirection.ToLower() == "desc"
            ? query.OrderByDescending(x => EF.Property<object>(x, sortByField))
            : query.OrderBy(x => EF.Property<object>(x, sortByField));

        // 進行資料庫分頁
        var recordList = size == 0 
            ? await query.ToListAsync() // 如果 size == 0，表示不分頁，直接取回所有資料
            : await query.Skip(skip).Take(size).ToListAsync(); // 分頁查詢

        ReturnModel returnModel = new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = new
                    {
                        recordTotal,
                        recordList
                    }
                };

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);
    }

    [HttpGet("details")]
    public async Task<IActionResult> RetrieveContactDetail(RetrieveContactDetail queryParam)
    {
        RetrieveContactDetail param = queryParam;
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        bool pageParseResult = int.TryParse(param.page, out int page);
        bool sizeParseResult = int.TryParse(param.size, out int size);

        // 當page或size為0或沒有傳時，表示不分頁
        page = pageParseResult ? page : 1;
        size = sizeParseResult ? size : 0;

        int skip = (page - 1) * size;//起始資料列索引值(略過幾筆)

        // 排序；如果沒有 sort 條件預設使用 ModifyDate 降冪排序
        string sort = string.IsNullOrEmpty(param.sort) ? "CreateDate:desc" : param.sort;
        string sortByField = sort.Split(":")[0];
        string sortDirection = sort.Split(":")[1];

        // 處理參數 ContactTypes 參數為 ContactTypeList
        List<string> contactTypeList = param.ContactTypes?.Split(',').Distinct().ToList() ?? new List<string>();

        var contactList = _dataAccessService.Fetch<ContactDatum>(x => x.AppCode == _user.AppCode);
        var contactDetailList = _dataAccessService.Fetch<ContactDetail>(x => x.AppCode == _user.AppCode);
        var departmentList = _dataAccessService.Fetch<Department>(e=>e.AppCode == _user.AppCode);

        var query = (from a in contactDetailList
             join b in contactList on a.ContactCode equals b.ContactCode into temp
             from t in temp.DefaultIfEmpty()
             join c in departmentList on t.DeptCode equals c.DeptCode into temp2
             from t2 in temp2.DefaultIfEmpty()
             select new ContactDetailVO
             {
                 AreaCode = t.AreaCode,
                 DeptCode = t.DeptCode,
                 DeptName = t2.DeptName,
                 Source = t.Source,
                 ContactCode = a.ContactCode,
                 ContactName = t.ContactName,                 
                 ContactType = a.ContactType,
                 ContactValue = a.ContactValue,
                 CreateDate = a.CreateDate,
                 CreateUserAccount = a.CreateUserAccount,
             })
            .Where(x => (param.AreaCode == null || x.AreaCode == param.AreaCode)
                    && (param.Source == null || x.Source == param.Source)
                    && (!contactTypeList.Any() || contactTypeList.Any(y => x.ContactType.ToUpper().Contains(y.ToUpper())))
                    && (param.DeptCode == null || (x.DeptCode != null && x.DeptCode.ToUpper().Contains(param.DeptCode.ToUpper())))
                    && (param.ContactName == null || x.ContactName.ToUpper().Contains(param.ContactName.ToUpper()))
                    && (param.ContactCode == null || x.ContactCode.ToUpper().Contains(param.ContactCode.ToUpper()))
                );
        
        // 取得總筆數
        int recordTotal = await query.CountAsync();

        // 根據 sortByField 和 sortDirection 進行動態排序
        query = sortDirection.ToLower() == "desc"
            ? query.OrderByDescending(x => EF.Property<object>(x, sortByField))
            : query.OrderBy(x => EF.Property<object>(x, sortByField));

        // 進行資料庫分頁
        var recordList = size == 0 
            ? await query.ToListAsync() // 如果 size == 0，表示不分頁，直接取回所有資料
            : await query.Skip(skip).Take(size).ToListAsync(); // 分頁查詢

        ReturnModel returnModel = new ReturnModel
                {
                    authorize = (Authorize)_user,
                    httpStatus = StatusCodes.Status200OK,
                    result = true,
                    data = new
                    {
                        recordTotal,
                        recordList
                    }
                };

        _logService.Logging("info", logActionName, requestUUID, recordTotal.ToString());

        return Ok(returnModel);
    }

    /// <summary>
    /// 驗證新增聯絡資料
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    // 有在method 定義route，在Controller記得也要定義route，否則會404
    [HttpPost("contacts/validate")]
    [RequestParamListDuplicate("ContactCode")]
    [RequestParamListNotNullOrEmpty]
    public IActionResult ValidateContact([FromBody] List<CreateContact> paramList)
    {
        // 進到 controller 代表驗證通過，回傳空的錯誤列表
        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = new List<ReturnError>()
        });
    }

    /// <summary>
    /// 新增聯絡資料
    /// </summary>
    /// <param name="paramList"></param>
    /// <returns></returns>
    // 有在method 定義route，在Controller記得也要定義route，否則會404
    [HttpPost("contacts")]
    [RequestParamListDuplicate("ContactCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> CreateContact([FromBody] List<CreateContact> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        ReturnModel returnModel;

        string appCode = _user.AppCode;
        _logService.Logging("info", logActionName, requestUUID, "Contact Data Validated, start to append.");

        _dataAccessService.Fetch<ObjectDatum>(x => x.AppCode == _user.AppCode);

        _dataAccessService.BeginTransaction();

        foreach (CreateContact param in paramList)
        {
            ContactDatum contact = new ContactDatum
            {
                AppCode = _user.AppCode,
                AreaCode = param.AreaCode,
                DeptCode = param.DeptCode,
                ContactCode = param.ContactCode,
                Source = param.Source,
                ContactName = param.ContactName,
                Phone = param.Phone,
                Email = param.Email,
                LineToken = param.LineToken,
                CreateUserAccount = _user.Account,
                CreateDate = DateTime.Now,
                ModifyDate = DateTime.Now
            };

            // 新增 Contact
            await _dataAccessService.CreateAsync<ContactDatum>(contact);

            // 更新 ContactDetail
            await UpdateContactDetailsAsync(contact.ContactCode, contact.Phone, contact.Email, contact.LineToken);

            // 如果聯絡資料名稱有修改就同步更新來源資料
            await UpdateSourceDataNameIfChangedAsync(contact);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "Contact Data append done.");

        returnModel = new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status201Created,
            result = true
        };

        return StatusCode(returnModel.httpStatus, returnModel);
    }

    private async Task UpdateContactDetailsAsync(string contactCode, string? phone, string? email, string? lineToken)
    {
        // 刪除原本 ContactCode 關聯的 ContactDetails 資料
        await _dataAccessService.DeleteAsync<ContactDetail>(e => e.AppCode == _user.AppCode && e.ContactCode == contactCode);

        // 新增 ContactDetails 資料
        List<ContactDetail> contactDetails = new List<ContactDetail>();

        if (!string.IsNullOrWhiteSpace(phone))
        {
            contactDetails.Add(new ContactDetail
            {
                AppCode = _user.AppCode,
                ContactCode = contactCode,
                ContactType = "SMS",
                ContactValue = phone,
                CreateUserAccount = _user.Account,
                CreateDate = DateTime.Now
            });
        }

        if (!string.IsNullOrWhiteSpace(email))
        {
            contactDetails.Add(new ContactDetail
            {
                AppCode = _user.AppCode,
                ContactCode = contactCode,
                ContactType = "Email",
                ContactValue = email,
                CreateUserAccount = _user.Account,
                CreateDate = DateTime.Now
            });
        }

        if (!string.IsNullOrWhiteSpace(lineToken))
        {
            contactDetails.Add(new ContactDetail
            {
                AppCode = _user.AppCode,
                ContactCode = contactCode,
                ContactType = "Line",
                ContactValue = lineToken,
                CreateUserAccount = _user.Account,
                CreateDate = DateTime.Now
            });
        }

        if (contactDetails.Count > 0)
        {
            await _dataAccessService.CreateRangeAsync(contactDetails);
        }
    }

    /// <summary>
    /// 更新聯絡資料來源的對象資料
    /// </summary>
    /// <param name="contact"></param>
    /// <returns></returns>
    private async Task UpdateSourceDataNameIfChangedAsync(ContactDatum contact)
    {
        if (contact.Source == "0") // source 為 0，來源為對象資料
        {
            ObjectDatum? objectData = _dataAccessService.Fetch<ObjectDatum>(e => e.AppCode == _user.AppCode && e.AreaCode == contact.AreaCode && e.UsageDepartCode == contact.DeptCode && e.ObjectCode == contact.ContactCode).Where(x => x.Name != contact.ContactName).AsTracking().FirstOrDefault();
            if (objectData != null)
            {
                // 更新 Fusion 對象資料名稱
                var fusionResult = await _objectService.PatchObject([new() { code = objectData.ObjectCode, name = contact.ContactName }]);
                bool updateFusionResult = fusionResult.Any(e => e.errors == null || e.errors.Count == 0);

                // 成功更新 Fusion，開始更新對象資料
                if (updateFusionResult)
                {
                    // 更新對象資料名稱
                    List<string> updateField = new List<string>();

                    updateField.Add("Name");
                    objectData.Name = contact.ContactName;

                    updateField.Add("ModifyDate");
                    updateField.Add("ModifyUserAccount");
                    objectData.ModifyDate = DateTime.Now;
                    objectData.ModifyUserAccount = _user.Account;

                    await _dataAccessService.UpdateAsync<ObjectDatum>(objectData, updateField.ToArray());
                }
            }
        }
        else if (contact.Source == "1") // source 為 1，來源為帳號資料
        {
            UserDatum? userData = _dataAccessService.Fetch<UserDatum>(e => e.AppCode == _user.AppCode && e.AreaCode == contact.AreaCode && e.DeptCode == contact.DeptCode && e.UserAccount == contact.ContactCode).Where(x => x.UserName != contact.ContactName).AsTracking().FirstOrDefault();
            if (userData != null)
            {
                // 更新帳號資料名稱
                List<string> updateField = new List<string>();

                updateField.Add("UserName");
                userData.UserName = contact.ContactName;

                updateField.Add("ModifyDate");
                updateField.Add("ModifyUserAccount");
                userData.ModifyDate = DateTime.Now;
                userData.ModifyUserAccount = _user.Account;

                await _dataAccessService.UpdateAsync<UserDatum>(userData, updateField.ToArray());
            }
        }
    }

    [HttpPatch("contacts")]
    [RequestParamListDuplicate("ContactCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> UpdateContact([FromBody] List<UpdateContact> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        _dataAccessService.BeginTransaction();

        foreach (UpdateContact param in paramList)
        {
            ContactDatum contact = _dataAccessService.Fetch<ContactDatum>(e => e.AppCode == _user.AppCode && e.ContactCode == param.ContactCode).AsTracking().First();

            List<string> updateField = new List<string>();
            if (param.ContactName != null)
            {
                updateField.Add("ContactName");
                contact.ContactName = param.ContactName;
            }

            if (param.Phone != null)
            {
                updateField.Add("Phone");
                contact.Phone = param.Phone;
            }

            if (param.Email != null)
            {
                updateField.Add("Email");
                contact.Email = param.Email;
            }

            if (param.LineToken != null)
            {
                updateField.Add("LineToken");
                contact.LineToken = param.LineToken;
            }

            updateField.Add("ModifyDate");
            updateField.Add("ModifyUserAccount");
            contact.ModifyDate = DateTime.Now;
            contact.ModifyUserAccount = _user.Account;

            await _dataAccessService.UpdateAsync<ContactDatum>(contact, updateField.ToArray());

            // 更新 ContactDetail
            await UpdateContactDetailsAsync(contact.ContactCode, contact.Phone, contact.Email, contact.LineToken);

            // 如果聯絡資料名稱有修改就同步更新來源資料
            await UpdateSourceDataNameIfChangedAsync(contact);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "Contact Data update done.");

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = true
        });
    }

    [HttpDelete("contacts")]
    [RequestParamListDuplicate("ContactCode")]
    [RequestParamListNotNullOrEmpty]
    public async Task<IActionResult> DeleteContact([FromBody] List<DeleteContact> paramList)
    {
        string? requestUUID = _requestContextService.GetRequestUID();
        string logActionName = GetType().Name + "/" + this.ControllerContext.ActionDescriptor.ActionName;
        _logService.Logging("info", logActionName, requestUUID, "Start");

        // 開始刪除聯絡資料
        _logService.Logging("info", logActionName, requestUUID, "Contact Data Validated, start to delete.");
        _dataAccessService.BeginTransaction();

        foreach (DeleteContact param in paramList)
        {
            // 刪除聯絡資料
            await _dataAccessService.DeleteAsync<ContactDatum>(e => e.AppCode == _user.AppCode && e.ContactCode == param.ContactCode);

            // 刪除聯絡資料明細
            await _dataAccessService.DeleteAsync<ContactDetail>(e => e.AppCode == _user.AppCode && e.ContactCode == param.ContactCode);
        }

        await _dataAccessService.CommitAsync();

        _logService.Logging("info", logActionName, requestUUID, "Contact Data delete done.");

        return Ok(new ReturnModel
        {
            authorize = (Authorize)_user,
            httpStatus = StatusCodes.Status200OK,
            result = true,
            data = true
        });
    }
}
