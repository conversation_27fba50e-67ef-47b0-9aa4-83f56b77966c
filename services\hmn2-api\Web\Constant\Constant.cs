﻿namespace Web.Constant;

public static class Constants
{
    public const string MMWAVE_PID_SUFFIX = "_M";
    public static class Command
    {
        public const string Reboot = "/1029/0/4";
    }
    public static class ResourceId
    {
        public const string Battery = "/1035/0/2";
    }

    public static class ErrorCode
    {
        public const string InternalError = "err.internalerror.system"; // 系統內部錯誤搭配 500 status
        public const string FusionError = "err.internalerror.fusion"; // 系統內部 Fusion 呼叫錯誤
        public const string NotFound = "err.notfound.param."; // 欄位資料在 DB 中找不到
        public const string NullOrEmpty = "err.null.param."; // 欄位資料是 null 或是空
        public const string Unique = "err.unique.param."; // 欄位資料在資料庫中已存在
        public const string Pattern = "err.pattern.param."; // 欄位資料格式錯誤
        public const string Length = "err.length.param."; // 欄位資料長度超過限制
        public const string Reference = "err.reference.param."; // 欄位資料在 DB 中存在資料關聯此欄位
        public const string Duplicate = "err.duplicate.param."; // 欄位資料在 request 列表中重複
        public const string RequestNullOrEmpty = "err.null.param"; // request 列表為 null 或是空列表
        public const string Invalid = "err.invalid.param."; // 欄位資料不正確
    }
}
