﻿namespace Web.Middlewares;

using System;
using System.Diagnostics;
using System.Net;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Web.Exceptions;
using Web.Models.Controller;

public class ErrorHandlingMiddleware(RequestDelegate next, ILogger<ErrorHandlingMiddleware> logger)
{
    private readonly RequestDelegate _next = next;
    private readonly ILogger<ErrorHandlingMiddleware> _logger = logger;

    public async Task InvokeAsync(HttpContext context)
    {
        try
        {
            await _next(context);
        }
        catch (Exception ex)
        {
            await HandleExceptionAsync(context, ex);
        }
    }

    private Task HandleExceptionAsync(HttpContext context, Exception exception)
    {
        var (className, methodName, lineNumber) = GetExceptionDetails(exception);

        _logger.LogError(exception,
            "An unhandled exception has occurred. Class: {ClassName}, Method: {MethodName}, Line: {LineNumber}",
            className, methodName, lineNumber);

        context.Response.ContentType = "application/json";
        context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;

        object response;
        if (exception is CustomException customException)
        {
            context.Response.StatusCode = customException.returnModel.httpStatus; // 或其他適當的狀態碼
            response = new
            {
                error = new
                {
                    message = customException.Message,
                    data = customException.returnModel
                }
            };
        }
        else
        {
            context.Response.StatusCode = StatusCodes.Status500InternalServerError;
            response = new
            {
                error = new
                {
                    message = exception.Message,
                    data = (object)null
                }
            };
        }

        return context.Response.WriteAsJsonAsync(response);

        //var result = JsonSerializer.Serialize(new
        //{
        //    data = exception is CustomException exception1 ? exception1.returnModel : exception.Message,
        //    error = "An error occurred while processing your request.",
        //    details = $"Class: {className}, Method: {methodName}, Line: {lineNumber}"
        //});
    }

    private static (string ClassName, string MethodName, int LineNumber) GetExceptionDetails(Exception exception)
    {
        var stackTrace = new StackTrace(exception, true);
        var frame = stackTrace.GetFrame(0); // Get the top stack frame

        string className = "Unknown";
        string methodName = "Unknown";
        int lineNumber = 0;

        if (frame != null)
        {
            var method = frame.GetMethod();
            if (method != null)
            {
                className = method.DeclaringType?.FullName ?? "Unknown";
                methodName = method.Name;
            }
            lineNumber = frame.GetFileLineNumber();
        }

        return (className, methodName, lineNumber);
    }
}
