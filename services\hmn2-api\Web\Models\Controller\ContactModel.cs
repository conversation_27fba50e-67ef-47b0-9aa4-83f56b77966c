﻿using System.ComponentModel.DataAnnotations;
using Web.Validation;
using Web.Constant;

namespace Web.Models.Controller.Contact;

public class RetrieveContact
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    public string? AreaCode { get; set; }
    public string? DeptCode { get; set; }
    public string? Source { get; set; }
    public string? ContactName { get; set; }
    public string? ContactCode { get; set; }
}

public class RetrieveContactDetail
{
    public string page { get; set; }
    public string size { get; set; }
    public string sort { get; set; }
    public string? AreaCode { get; set; }
    public string? DeptCode { get; set; }
    [RegularExpression(@"^(Line|SMS|Email)(,(Line|SMS|Email))*$", ErrorMessage = Constants.ErrorCode.Pattern + "ContactTypes")]
    public string? ContactTypes { get; set; }
    public string? Source { get; set; }
    public string? ContactName { get; set; }
    public string? ContactCode { get; set; }
}

public class CreateContact
{

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "AreaCode")]
    [Exists("AreaCode", "Area", "AreaCode", ErrorMessage = Constants.ErrorCode.NotFound + "AreaCode")]
    public string AreaCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "DeptCode")]
    [Exists("AreaCode", "Department", "DeptCode", ErrorMessage = Constants.ErrorCode.NotFound + "DeptCode")]
    public string DeptCode { get; set; } = null!;

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ContactCode")]
    [ExistsWhenEquals("Source", "0,1", "AreaCode", "ObjectDatum,UserDatum", "ObjectCode,UserAccount", true, "false,true", ErrorMessage = Constants.ErrorCode.NotFound + "ContactCode")]
    [Unique("", "ContactDatum", "ContactCode", ErrorMessage = Constants.ErrorCode.Unique + "ContactCode")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "ContactCode")]
    public string ContactCode { get; set; }

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Source")]
    [Numeric(ErrorMessage = Constants.ErrorCode.Pattern + "Source")]
    [Range(0, 2, ErrorMessage = Constants.ErrorCode.Pattern + "Source")]
    public string Source { get; set; } = null!;

    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ContactName")]
    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "ContactName")]
    public string ContactName { get; set; }

    [StringLength(20, ErrorMessage = Constants.ErrorCode.Length + "FenceType")]
    [RegularExpression(@"^[0-9+\-()\s]{7,20}$", ErrorMessage = Constants.ErrorCode.Pattern + "Phone")]
    [RequiredWhenAbsentAttribute("Email,LineToken", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Phone")]
    public string Phone { get; set; }

    [StringLength(100, ErrorMessage = Constants.ErrorCode.Length + "Email")]
    [RegularExpression(@"^[^@\s]+@[^@\s]+\.[^@\s]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Email")]
    [RequiredWhenAbsentAttribute("Phone,LineToken", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "Email")]
    public string Email { get; set; }

    [StringLength(100, ErrorMessage = Constants.ErrorCode.Length + "LineToken")]
    [RequiredWhenAbsentAttribute("Phone,Email", ErrorMessage = Constants.ErrorCode.NullOrEmpty + "LineToken")]
    public string LineToken { get; set; }
}

public class UpdateContact
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ContactCode")]
    [Exists("", "ContactDatum", "ContactCode", ErrorMessage = Constants.ErrorCode.NotFound + "ContactCode")]
    public string ContactCode { get; set; }

    [StringLength(50, ErrorMessage = Constants.ErrorCode.Length + "ContactName")]
    public string ContactName { get; set; }

    [StringLength(20, ErrorMessage = Constants.ErrorCode.Length + "FenceType")]
    [RegularExpression(@"^[0-9+\-()\s]{7,20}$", ErrorMessage = Constants.ErrorCode.Pattern + "Phone")]
    public string Phone { get; set; }

    [StringLength(100, ErrorMessage = Constants.ErrorCode.Length + "Email")]
    [RegularExpression(@"^[^@\s]+@[^@\s]+\.[^@\s]+$", ErrorMessage = Constants.ErrorCode.Pattern + "Email")]
    public string Email { get; set; }

    [StringLength(100, ErrorMessage = Constants.ErrorCode.Length + "LineToken")]
    public string LineToken { get; set; }
}

public class DeleteContact
{
    [Required(ErrorMessage = Constants.ErrorCode.NullOrEmpty + "ContactCode")]
    [Exists("", "ContactDatum", "ContactCode", ErrorMessage = Constants.ErrorCode.NotFound + "ContactCode")]
    public string ContactCode { get; set; }
}
