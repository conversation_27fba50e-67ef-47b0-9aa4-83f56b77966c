using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Web.Repository.Models.Entities;
using Web.Services.Interfaces;
using Web.Models.Controller;

namespace Web.ActionFilter
{
    public class CustomAuthorizeFilter : IAsyncAuthorizationFilter
    {
        private readonly IDataAccessService _dataAccessService;
        private readonly ICredentialService _credentialService; // 注入 CredentialService

        public CustomAuthorizeFilter(IDataAccessService dataAccessService, ICredentialService credentialService)
        {
            _dataAccessService = dataAccessService;
            _credentialService = credentialService;
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {

            // 檢查當前動作是否有 [Authorize] 特性
            var hasAuthorizeAttribute = context.ActionDescriptor.EndpointMetadata
                .Any(em => em is AuthorizeAttribute);

            // 如果沒有 [Authorize] 特性，直接返回，不執行後續邏輯
            if (!hasAuthorizeAttribute)
            {
                return;
            }

            if (context.HttpContext.User.Identity?.IsAuthenticated == true)
            {
                // 使用 CredentialService 獲取用戶帳號
                var userAccount = _credentialService.UserResult.Account;
                var userData = await _dataAccessService.Fetch<UserDatum>(x => x.UserAccount == userAccount).FirstOrDefaultAsync();
                var roleData = await _dataAccessService.Fetch<Role>(x => x.RoleCode == userData.RoleCode).FirstOrDefaultAsync();


                if (userData?.Enable == false)
                {
                    //用戶為停用狀態
                    context.Result = new BadRequestObjectResult(new ReturnModel { httpStatus = StatusCodes.Status400BadRequest, result = false, title = "userDisable" });
                    return;
                }
                else if (roleData?.Enable == false)
                {
                    //角色為停用狀態
                    context.Result = new BadRequestObjectResult(new ReturnModel { httpStatus = StatusCodes.Status400BadRequest, result = false, title = "roleDisable" });
                    return;
                }
                else
                {
                    return;
                }
            }
        }
    }
}